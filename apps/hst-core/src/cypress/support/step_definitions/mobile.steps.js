import { Given, When, Then } from "@badeball/cypress-cucumber-preprocessor"

Then('The {string} element should {string} on mobile', (element, assertion) => {
  cy.viewport(375, 812)
  cy.get(`[data-test="${element.replace(/ /g, '-')}"]`).should(assertion.replace(/ /g, '.'))
})

When('I click the {string} element on mobile', (element) => {
  cy.viewport(375, 812)
  cy.get(`[data-test="${element.replace(/ /g, '-')}"]`).click()
})
