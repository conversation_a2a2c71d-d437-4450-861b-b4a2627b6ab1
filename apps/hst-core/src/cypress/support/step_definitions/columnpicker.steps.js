import { Given, When, Then } from "@badeball/cypress-cucumber-preprocessor"

let columnPickerItems

When('I count the items from {string} column picker', (columnPickerName) => {
  columnPickerItems = cy.get(`[data-test="${columnPickerName.replace(/ /g, '-')}"] span`).its('length')
})

When('I recount the items from {string} column picker to make sure it is the same', (columnPickerName) => {
  const sizeOfColumPickerItems = Object.keys(columnPickerItems).length
  cy.get(`[data-test="${columnPickerName.replace(/ /g, '-')}"] span`).its('length').should('be.gte', sizeOfColumPickerItems)
})
