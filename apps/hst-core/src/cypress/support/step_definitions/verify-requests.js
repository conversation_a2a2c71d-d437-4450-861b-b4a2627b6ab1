import { Given, When, Then } from "@badeball/cypress-cucumber-preprocessor"

When('I verify the Google Analytics ID {string}', (gaID) => {
  cy.get('@ga')
    .should('be.calledWith', 'create', gaID)
})

When('I setup spying with request {string} and {string} method', (requestURL, requestMethod) => {
  cy.server()
  cy.route({
    method: requestMethod,
    url: requestURL,
  })
    .as('request')
})

When('I verify the request with {string} status', (requestStatus) => {
  cy.wait('@request').then((xhr) => {
    expect(xhr.status).to.be.eq(Number(requestStatus))
  })
})

When('I verify the request with {string} value on {string} location', (value, location) => {
  cy.get('@request').then((xhr) => {
    // convert location from xhr object and then check it with the input value
    expect(location.split('.').reduce((o, i) => o[i], xhr)).to.eq(value)
  })
})

When('I verify the request with no {string} value on {string} location', (value, location) => {
  cy.get('@request').then((xhr) => {
    expect(location.split('.').reduce((o, i) => o[i], xhr)).to.not.eq(value)
  })
})

When('I verify the request that have been call {string} time', (numberOfCall) => {
  cy.get('@request.all').then((xhrs) => {
    expect(xhrs.length).to.eq(Number(numberOfCall))
  })
})
