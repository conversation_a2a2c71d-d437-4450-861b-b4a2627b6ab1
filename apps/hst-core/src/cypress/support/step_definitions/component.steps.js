import { Given, When, Then } from "@badeball/cypress-cucumber-preprocessor"

// Criteria -------------------------------------------------

Then('The {string} criteria should be {string}', (criteriaName, criteriaValue) => {
  cy.get(`[data-test="criteria-${criteriaName.replace(/ /g, '-')}"] [data-test="criteria-ok-${criteriaValue}"]`)
})

// Toast ----------------------------------------------------

Then('The Toast should have the {string} message as {string}', (messageType, message) => {
  const toasts = cy.get(`.test--toast-${messageType} .test--toast-body:contains("${message}")`, { timeout: 50000 }).should('be.visible')
  toasts.each((toast) => toast.click())
})

Then('The toast {string} should {string}', (messageType, assertion) => {
  cy.get(`.test--toast-${messageType}`, { timeout: 10000 }).should(assertion.replace(/ /g, '.'))
})
