import { Given, When, Then } from "@badeball/cypress-cucumber-preprocessor"

Then('The {string} element at row {int} of the {string} table should {string}', (elementName, row, tableName, assertion) => {
  cy.get('.spinner').should('not.exist')
  cy.get(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"]`).scrollIntoView()
  cy.get(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"] tr:nth-child(${row}) button[data-test="${elementName.replace(/ /g, '-')}"]`, { timeout: 20000 }).should(assertion.replace(/ /g, '.'))
})

When('I change page size of the {string} table to {int}', (tableName, pageSize) => {
  cy.get(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"] [data-test="${tableName}-pagination-select"]`).click()
  cy.wait(1000)
  cy.get('li[class="ant-select-dropdown-menu-item"], div[class="ant-select-item-option-content"]').contains(pageSize.toString()).scrollIntoView().click({ timeout: 50000 })
})

When('I click the {string} element at row {int} of the {string} table', (elementName, row, tableName) => {
  const unUseRow = Cypress.$(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"] tbody tr[aria-hidden="true"]:first`)
  const extraCount = +(unUseRow.length > 0)

  cy.get('.spinner').should('not.exist')
  cy.get(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"] tbody tr:nth-child(${row + extraCount}) button[data-test="${elementName.replace(/ /g, '-')}"]`, { timeout: 10000 }).scrollIntoView({ duration: 1000 })
  cy.wait(300)
  cy.get(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"] tbody tr:nth-child(${row + extraCount}) button[data-test="${elementName.replace(/ /g, '-')}"]`).click()
})

When('I click the {string} item from more menu at the row {int} of the {string} table', (buttonName, row, tableName) => {
  cy.get('.ant-card-loading-content', { timeout: 1000 })
    .should('not.exist')
  cy.get(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"] tr:nth-child(${row}) button[data-test="button-more-menu"]`, { timeout: 10000 })
    .scrollIntoView()
    .click()
  cy.get(`div.ant-dropdown:not(.ant-dropdown-hidden) [data-test="button-${buttonName.replace(/ /g, '-')}"]`, { timeout: 10000 }).as('theElement')
  cy.get('@theElement', { timeout: 10000 })
    .scrollIntoView()
    .should('be.visible')
  cy.get('@theElement', { timeout: 10000 })
    .click()
})

When('I click the {string} element in the {string} table', (element, tableName) => {
  cy.get('.spinner').should('not.exist')
  cy.get(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"] [data-test="${element.replace(/ /g, '-')}"]`).first().as('theElement')
  cy.get('@theElement')
    .should('be.visible')
    .invoke('width')
    .should('be.greaterThan', 0)
  cy.get('@theElement')
    .invoke('height')
    .should('be.greaterThan', 0)
  cy.get('@theElement')
    .click()
})

Then('The row number {int} in {string} table should {string} the text {string}', (row, tableName, action, elementText) => {
  cy.get(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"] tr:nth-child(${row}) td`, { timeout: 10000 }).should(action.replace(/ /g, '.'), elementText)
})

Then('The row with text {string} in table {string} should {string} the text {string}', (elementtext, tableName, action, text) => {
  cy.get(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"]`).contains('td', elementtext).siblings()
    .should(action.replace(/ /g, '.'), text, { timeout: 30000 })
})

Then('The {string} table should be empty', (tableName) => {
  cy.get('.-with-loading__overlaySpinnerWrapper').should('not.exist')
  cy.get(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"] [data-test="no-data"]`)
})

Then('The {string} table should contain {int} rows', (tableName, rowNumber) => {
  cy.get('.-with-loading__overlaySpinnerWrapper').should('not.exist')
  cy.get(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"] tbody tr:not([aria-hidden="true"])`).should('have.length', rowNumber, { timeout: 50000 })
})

Then('The {string} table should {string} {string}', (table, assertion, value) => {
  cy.get('.-with-loading__overlaySpinnerWrapper').should('not.exist')
  cy.get(`[data-test="mm-table-${table.replace(/ /g, '-')}"]`).should(assertion, value, { timeout: 10000 })
})

Then('The {string} table should have page size {int}', (tableName, pageSize) => {
  cy.get('.-with-loading__overlaySpinnerWrapper').should('not.exist')
  cy.get(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"] div[class="ant-select-selection-selected-value"],
  [data-test="mm-table-${tableName.replace(/ /g, '-')}"] span[class="ant-select-selection-item"]
  `, { timeout: 20000 }).should('have.text', pageSize.toString())
})

Then('The {string} table should display items from {int} to {int} of {int}', (tableName, from, to, total) => {
  cy.get('.-with-loading__overlaySpinnerWrapper').should('not.exist')
  cy.get(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"] [data-test="${tableName}-pagination-pageInfo"]`).should('have.text', `${from} - ${to} of ${total}`)
})

Then('The row number {int} in {string} table should have the below values:', (rowIndex, tableName, data) => {
  cy.get('.-with-loading__overlaySpinnerWrapper').should('not.exist')
  const [, ...columns] = data.rawTable
  columns.forEach(([, colValue]) => {
    cy.get(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"] tbody tr:nth-child(${rowIndex}) td`, { timeout: 120000 }).should('contain', colValue)
  })
})

Then('The row number {int} in {string} table with horizontal scroll should have the below values:', (rowIndex, tableName, data) => {
  cy.get('.-with-loading__overlaySpinnerWrapper').should('not.exist')
  const [, ...columns] = data.rawTable
  columns.forEach(([, colValue]) => {
    cy.get(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"] tbody tr:nth-child(${rowIndex + 1}) td`, { timeout: 120000 }).should('contain', colValue)
  })
})

Then('The {string} table should include the columns {string}', (tableName, headers) => {
  const colHeaders = headers.split(',')
  colHeaders.forEach((header) => {
    cy.get(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"] thead tr th`, { timeout: 120000 }).should('contain', header.trim())
  })
})

Then('The {string} element in the {string} table should {string}', (element, tableName, assertion) => {
  cy.get(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"] [data-test="${element.replace(/ /g, '-')}"]`, { timeout: 10000 }).should(assertion.replace(/ /g, '.'))
})

When('I change page size of the {string} ant table to {string}', (tableName, pageSize) => {
  cy.get(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"] [data-test="${tableName}-pagination-select"],
  [data-test="mm-table-${tableName.replace(/ /g, '-')}"] [class="ant-pagination-options"]`).click()
  cy.get('li[class="ant-select-dropdown-menu-item"], div[class="ant-select-item-option-content"]').contains(pageSize, { matchCase: false }).scrollIntoView().click({ timeout: 50000 })
})

When('I toggle all selection of ant table {string}', (tableName) => {
  cy.get('.ant-spin-spinning').should('not.exist')
  cy
    .get(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"] thead tr input[type=checkbox]`)
    .then((checkboxElement) => {
      checkboxElement.click()
    })
})

When('I click the {string} element at row {int} of the {string} ant table', (elementName, row, tableName) => {
  cy.get('.-with-loading__overlaySpinnerWrapper').should('not.exist')

  // improve to avoid flaky tests following suggestion from https://github.com/cypress-io/cypress/issues/695
  cy.get(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"] tr:nth-child(${row}) [data-test="${elementName.replace(/ /g, '-')}"]`).scrollIntoView({ duration: 500 }).should('be.visible', { timeout: 20000 })
  cy.get(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"] tr:nth-child(${row}) [data-test="${elementName.replace(/ /g, '-')}"]`).invoke('width').should('be.gt', 0)
  cy.wait(500)
  cy.get(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"] tr:nth-child(${row}) [data-test="${elementName.replace(/ /g, '-')}"]`).click()
})

When('I click the item at row {int} of the {string} table', (row, tableName) => {
  cy.get('.-with-loading__overlaySpinnerWrapper').should('not.exist')

  cy.get(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"] tr:nth-child(${row}) a`).then((theElement) => {
    cy.get(theElement).should('be.visible')
    cy.get(theElement).invoke('width').should('be.gt', 0)
    cy.wait(500)
    cy.get(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"] tr:nth-child(${row}) a`).click({ timeout: 10000 })
  })
})

When('I click the {string} element in the {string} ant table', (element, tableName) => {
  cy.get('.-with-loading__overlaySpinnerWrapper').should('not.exist')
  cy.get(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"] .${element}`).first().as('theElement')
  cy.get('@theElement')
    .should('be.exist')
    .invoke('width')
    .should('be.greaterThan', 0)
  cy.get('@theElement')
    .invoke('height')
    .should('be.greaterThan', 0)
  cy.get('@theElement')
    .click()
})

Then('The {string} ant table should have page size {int}', (tableName, pageSize) => {
  cy.get(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"] [class="ant-select-selection-selected-value"],
    [data-test="mm-table-${tableName.replace(/ /g, '-')}"] [class="ant-select-selection-item"]
  `).should('have.text', `${pageSize.toString()} items per page`)
})

Then('The {string} element in the {string} ant table should {string}', (element, tableName, assertion) => {
  cy.get(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"] .${element}`).should(assertion.replace(/ /g, '.'))
})

Then('The {string} ant table should be on page {int}', (tableName, page) => {
  cy.get(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"] .ant-pagination-item-active`).should('have.text', page.toString())
})

Then('The {string} element in the {string} ant table should have attribute {string} equals to {string}', (elementSelector, tableName, attribute, value) => {
  cy.get(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"] .${elementSelector}`)
    .should('have.attr', attribute, value)
})

Then('The row number {int} in {string} table should be disabled', (row, tableName) => {
  cy.get(`[data-test="mm-table-${tableName.replace(/ /g, '-')}"] tr:nth-child(${row}) td a`).should('not.exist')
})