import { Given, When, Then } from "@badeball/cypress-cucumber-preprocessor"

// For ant design

When('I click the ant date range selector of {string}', (dateRange<PERSON>abel) => {
  cy.get('span[class="ant-tag ant-tag-blue"]')
    .contains(dateRange<PERSON>abel, { timeout: 5000 }).should('be.visible')
    .click()
})

When('I select the date {int} in ant date picker {string} side panel', (date, side) => {
  let index
  let countElementIndex = 0
  if (side === 'left') { index = 0 } else if (side === 'right') { index = 1 }

  cy.wait(1000)
  cy.get('div[class="ant-calendar-body"], div[class="ant-picker-body"]').eq(index)
    .find('div[class="ant-calendar-date"], div[class="ant-picker-cell-inner"]')
    .filter(`:contains('${date}')`)
    .each(($eachElement) => {
      if ($eachElement.text() === date.toString() && countElementIndex < 1) {
        cy.get($eachElement)
          .click({ force: true })
        countElementIndex += 1
      }
    })
})

When('I navigate to the {string} in ant date picker', (time) => {
  const timeClassMap = {
    'previous year': 'ant-calendar-prev-year-btn',
    'previous month': 'ant-calendar-prev-month-btn',
    'next month': 'ant-calendar-next-month-btn',
    'next year': 'ant-calendar-prev-year-btn',
  }
  const v4timeClassMap = {
    'previous year': 'ant-picker-header-super-prev-btn',
    'previous month': 'ant-picker-header-prev-btn',
    'next month': 'ant-picker-header-next-btn',
    'next year': 'ant-picker-header-super-next-btn',
  }

  cy.get(`.${timeClassMap[time]}, .${v4timeClassMap[time]}`)
    .first()
    .click()
})

When('I enter the time as {string} in the {string} field', (time, fieldName) => {
  cy.get(`input[placeholder = "${fieldName}"]`)
    // .not('[readonly]')
    // TODO: remove this step to use the generic one above
    // .scrollIntoView()
    .click({ force: true })
    .focused({ force: true })
    .clear()
    .type(time)
})

When('I enter the time as {string} in the {string} time picker input', (time, element) => {
  // TODO: Antd v4 after upgrade, remove the class selector
  cy.get('body')
    .then(($body) => {
      if ($body.find('.ant-time-picker-panel-input-wrap').length) {
        return '.ant-time-picker-panel-input-wrap'
      }

      return `[data-test="input-timepicker-${element}"]`
    }).then((selector) => cy.get(selector).find('input')
      .click()
      .clear({ force: true })
      .type(time)
      .blur({ force: true }))
})

Then('The ant date picker should contain following items:', (dataList) => {
  const dateRangeLabels = dataList.rawTable
  dateRangeLabels.forEach(([eachDateLabel]) => {
    cy.get('div span[class = "ant-tag ant-tag-blue"]', { timeout: 12000 }).should('contain', eachDateLabel)
  })
})
