import { Given, When, Then } from "@badeball/cypress-cucumber-preprocessor"
import featureSet from '../../../../tools/feature-set/feature-set-mapping'

const domain = Cypress.env('APP_HOSTNAME') || 'localhost'
const webUrl = Cypress.env('WEB_URL') || `http://${domain}:3015/`
const directSmsUrl = Cypress.env('DIRECTSMS_URL') || `http://directsms.${domain}:8008/`
const mtxtUrl = Cypress.env('MULTITXT_URL') || `http://multitxt.${domain}:8008/`
const streetDataUrl = Cypress.env('STREETDATA_URL') || `http://streetdata.${domain}:8008/`
const wholeSaleSmsUrl = Cypress.env('WHOLESALE_URL') || `http://wholesalesms.${domain}:8008/`
const smsbUrl = Cypress.env('SMSB_URL') || `http://smsbroadcast.${domain}:8008/`
const messagenetUrl = Cypress.env('MESSAGENET_URL') || `http://messagenet.${domain}:8008/`
const mobipostUrl = Cypress.env('MOBIPOST_URL') || `http://mobipost.${domain}:8008/`
const smscentralUrl = Cypress.env('SMSCENTRAL_URL') || `http://smscentral.${domain}:8008/`
const twodegreesUrl = Cypress.env('2DEGREES_URL') || `http://2degrees.${domain}:8008/`
const notifynowUrl = Cypress.env('NOTIFYNOW_URL') || `http://notify-now.${domain}:8008/`
const etxtUrl = Cypress.env('ETXT_URL') || `http://etxt.${domain}:8008/`
const vendorUrls = {
  messagemedia: webUrl,
  multitxt: mtxtUrl,
  directsms: directSmsUrl,
  streetdata: streetDataUrl,
  wholesalesms: wholeSaleSmsUrl,
  smsbroadcast: smsbUrl,
  messagenet: messagenetUrl,
  mobipost: mobipostUrl,
  smscentral: smscentralUrl,
  twodegrees: twodegreesUrl,
  notifynow: notifynowUrl,
  etxt: etxtUrl,
}
const vendorRegex = /([A-Za-z]+) - /

Given('I am on the {string} page with {string} cookie', (rawPageName, cookieValue) => {
  cy.log(`${cookieValue}`)
  cy.accessPageNameBasedCookie(rawPageName, cookieValue, vendorUrls, vendorRegex)
})

Given('I am on the {string} page with {string} cookie and features of {string}', (rawPageName, sessionCookieValue, featuresCookieValue) => {
  const cookieValue = `${sessionCookieValue}[features:${featuresCookieValue}]`
  cy.log(`${cookieValue}`)
  cy.accessPageNameBasedCookie(rawPageName, cookieValue, vendorUrls, vendorRegex)
})

Given('I am logged in', () => {
  window.localStorage.setItem('REFRESH_TOKEN', JSON.stringify('logged_in'))
  cy.visit(`${webUrl}login`)
  cy.setCookie('ci_session', 'functional-test-session', { path: '/', domain })
})

Given('I am logged in with cookie {string}', (cookieValue) => {
  cy.visit(`${webUrl}login`)
  window.localStorage.setItem('REFRESH_TOKEN', JSON.stringify(cookieValue))
  cy.setCookie('ci_session', cookieValue, { path: '/', domain })
})

Given('I am on the {string} page', (page) => {
  window.localStorage.setItem('REFRESH_TOKEN', JSON.stringify('logged_in'))
  cy.visit(`${webUrl}${page === '/' ? page : page.replace(/ /g, '-')}`)
  cy.get('.spinner', { timeout: 20000 }).should('not.exist')
})

Then('I should be on the {string} page', (page) => {
  cy.location('pathname').should('eq', page === '/' ? page : `/${page.replace(/ /g, '-')}`, { timeout: 40000 })
})

Then('I should be on {string} page with params of {string}', (page, params) => {
  cy.location('pathname').should('eq', `/${page.replace(/ /g, '-')}`)
  cy.location('search').should('contain', `${params}`)
})

Given('I am on the {string} page with {string} as {string}', (pageName, paramName, paramValue) => {
  cy.visit(`${webUrl}${pageName}?${paramName}=${paramValue}`)
  cy.get('.spinner', { timeout: 20000 }).should('not.exist')
})

Then('I pause a while after page load', () => {
  cy.wait(4000)
})

When('I reload', () => {
  cy.reload()
  cy.get('.spinner', { timeout: 20000 }).should('not.exist')
})

Given('I wait {int} milliseconds', (time) => {
  cy.wait(time)
})

Given('I clean cookie session', () => {
  cy.clearCookie('ci_session')
  cy.clearLocalStorage('REFRESH_TOKEN')
})

Then('I logout', () => {
  cy.get('[data-test="user-navigation-menu"]', { timeout: 20000 }).should('be.visible').click()
  cy.get('[data-test="log-out"]', { timeout: 20000 }).click({ force: true })
  cy.clearCookie('ci_session')
})

Given('I am on the {string} page with {string} cookie and default features of {string}', (rawPageName, sessionCookieValue, defaultFeatureSet) => {
  cy.fixture(`${featureSet[defaultFeatureSet]}`).then((defaultFeatures) => {
    const cookieValue = `${sessionCookieValue}[features:${defaultFeatures}]`
    cy.log(`${cookieValue}`)
    cy.accessPageNameBasedCookie(rawPageName, cookieValue, vendorUrls, vendorRegex)
  })
})

Given('I am on the {string} page with {string} cookie and default features of {string} and new AMS {string} features', (rawPageName, sessionCookieValue, defaultFeatureSet, selectedNewFeatures) => {
  cy.fixture(`${featureSet[defaultFeatureSet]}`).then((defaultFeatures) => {
    const cookieValue = `${sessionCookieValue}[features:${defaultFeatures},${selectedNewFeatures}]`
    cy.log(`${cookieValue}`)
    cy.accessPageNameBasedCookie(rawPageName, cookieValue, vendorUrls, vendorRegex)
  })
})

Given('I am on the {string} page with {string} cookie and default features of {string} and remove {string} features and new AMS {string} features', (rawPageName, sessionCookieValue, defaultFeatureSet, removeFeatures, addFeatures) => {
  cy.fixture(`${featureSet[defaultFeatureSet]}`).then((defaultFeatures) => {
    const removeArray = removeFeatures.split(',')
    removeArray.forEach((removeItem) => {
      const index = defaultFeatures.indexOf(removeItem)
      if (index > -1) {
        defaultFeatures.splice(index, 1)
      }
    })
    const cookieValue = `${sessionCookieValue}[features:${defaultFeatures},${addFeatures}]`
    cy.log(`${cookieValue}`)
    cy.accessPageNameBasedCookie(rawPageName, cookieValue, vendorUrls, vendorRegex)
  })
})
