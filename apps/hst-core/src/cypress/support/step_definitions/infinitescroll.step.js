import { Given, When, Then } from "@badeball/cypress-cucumber-preprocessor"

const domain = Cypress.env('APP_HOSTNAME') || 'localhost'
const nextGenApiUrl = Cypress.env('NEXTGEN_API_URL') || `http://${domain}:3000/nextgen`

Then('I scroll to the {string} element number {int} should have the text {string}', (element, index, text) => {
  cy.get(`[data-test="${element.replace(/ /g, '-')}"]`).eq(index - 1).as('theElement')
  cy.get('@theElement')
    .scrollIntoView({ duration: 3000 })
    .contains(text)
    .should('be.visible')
})

Then('I scroll to the {string} element number {int}', (element, index) => {
  cy.get(`[data-test="${element.replace(/ /g, '-')}"]`).eq(index - 1).as('theElement')
  cy.get('@theElement')
    .scrollIntoView({ duration: 4000 })
})

Then('I scroll to the {string} element', (element) => {
  cy.get(`[data-test="${element.replace(/ /g, '-')}"]`).as('theElement')
  cy.get('@theElement')
    .scrollIntoView({ duration: 1000 })
})

Then('I scroll to the last {string} element', (element) => {
  cy.get(`[data-test="${element.replace(/ /g, '-')}"]`).last().as('theElement')
  cy.get('@theElement')
    .scrollIntoView({ duration: 1000 })
})

Then('I scroll to the element having class {string}', (className) => {
  cy.get(`.${className}`, { timeout: 150000 }).as('theElement')
  cy.get('@theElement')
    .scrollIntoView({ duration: 4000 })
})

When('The customer with phone number {string} send reply {string} with text type {string} to the number {string}', (sourcePhone, messageContent, messageType, destinationPhone) => {
  cy.request({
    method: 'POST',
    url: `${nextGenApiUrl}/mo`,
    timeout: 30000,
    headers: {
      accept: 'application/json',
      'content-type': 'application/json',
    },
    body: {
      destinationAddress: destinationPhone,
      content: messageContent,
      format: messageType,
      sourceAddress: sourcePhone,
    },
    json: true,
  }).then((response) => {
    expect(response.status).to.eq(201)
    expect(response.body).to.have.property('status', 'DELIVERED')
  })
})

When('The customer with phone number {string} send {int} times reply {string} with text type {string} to the number {string}', (sourcePhone, numOfTimes, messageContent, messageType, destinationPhone) => {
  for (let i = 1; i <= numOfTimes; i += 1) {
    cy.request({
      method: 'POST',
      url: `${nextGenApiUrl}/mo`,
      timeout: 30000,
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
      },
      body: {
        destinationAddress: destinationPhone,
        content: messageContent,
        format: messageType,
        sourceAddress: sourcePhone,
      },
      json: true,
    }).then((response) => {
      expect(response.body).to.have.property('status', 'DELIVERED')
    })
  }
})
