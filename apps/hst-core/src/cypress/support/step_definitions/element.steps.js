import moment from 'moment'
import { Given, When, Then } from "@badeball/cypress-cucumber-preprocessor"

When('I click the element having class {string}', (className) => {
  cy.get(`.${className}`).scrollIntoView().click({ timeout: 30000 })
})

When('I click the {string} element', (element) => {
  cy.get(`[data-test="${element.replace(/ /g, '-')}"], [data-test="${element}"]`).first().as('theElement')
  cy.get('@theElement')
    .scrollIntoView()
    .should('be.exist')
    .invoke('width')
  cy.get('@theElement')
    .invoke('height')
  cy.get('@theElement').click({ timeout: 30000 })
})

When('I click the origin {string} element without replacement', (element) => {
  cy.get(`[data-test="${element}"], [data-test="${element}"]`).first().as('theElement')
  cy.get('@theElement')
    .scrollIntoView()
    .should('be.exist')
    .invoke('width')
  cy.get('@theElement')
    .invoke('height')
  cy.get('@theElement').click({ timeout: 30000 })
})

When('I click the {string} element in element number {int}', (element, number) => {
  cy.get(`[data-test="${element.replace(/ /g, '-')}"]`).as('theElement').eq(number).scrollIntoView()
    .should('be.visible')
    .click({ timeout: 30000 })
})

When('I force click the {string} element', (element) => {
  cy.get(`[data-test="${element.replace(/ /g, '-')}"]`).first().as('theElement')
  cy.get('@theElement')
    .scrollIntoView()
    .should('be.visible')
    .invoke('width')
  cy.get('@theElement')
    .invoke('height')
  cy.get('@theElement').click({ force: true })
})

When('I hover the {string} element', (element) => {
  cy.get(`[data-test="${element.replace(/ /g, '-')}"]`).first().as('theElement')
  cy.get('@theElement')
    .scrollIntoView()
    .should('be.visible')
    .invoke('width')
  cy.get('@theElement')
    .invoke('height')
  cy.get('@theElement').trigger('mouseover', { timeout: 10000 })
})

When('I click the element {string}', (element) => {
  cy.get(`[data-test="${element.replace(/ /g, '-')}"]`).first().as('theElement')
  cy.get('@theElement')
    .scrollIntoView()
  cy.get('@theElement')
    .should('be.exist')
    .invoke('width')
    .should('be.greaterThan', 0)
  cy.get('@theElement')
    .invoke('height')
    .should('be.greaterThan', 0)
  cy.get('@theElement')
    .should('be.exist')
    .click({ timeout: 30000 })
})

When('I click the {string} element {int} times', (element, times) => {
  for (let i = 0; i < times; i += 1) {
    cy.get('.ant-card-loading-content').should('not.exist')
    cy.get(`[data-test="${element.replace(/ /g, '-')}"]`).first().as('theElement')
    cy.get('@theElement')
      .should('be.visible')
      .invoke('width')
      .should('be.greaterThan', 0)
    cy.get('@theElement')
      .invoke('height')
      .should('be.greaterThan', 0)
    cy.get('@theElement')
      .scrollIntoView()
      .should('be.visible')
      .click({ timeout: 30000 })
  }
})

When('I click the {string} element number {int}', (element, index) => {
  cy.get('.ant-card-loading-content').should('not.exist')
  cy.get(`[data-test="${element.replace(/ /g, '-')}"]`).eq(index - 1)
    .should('be.visible')
    .click()
})

When('I click the link {string} element', (element) => {
  cy.get('.ant-card-loading-content').should('not.exist')
  cy.get(`[data-test="${element.replace(/ /g, '-')}"]`, { timeout: 10000 })
    .should('be.visible')
    .click({ force: true })
})

Then('I click the element with text {string}', (text) => {
  cy.contains(text).click({ timeout: 100000 })
})

Then('I click the element having class {string} with text {string}', (className, text) => {
  cy.get(`.${className}`, { timeout: 150000 }).contains(text).click({ timeout: 100000 })
})

When('I click the element with {string} class containing {string} text', (className, text) => {
  cy.get(`.${className}`).contains(text).as('theElement')
  cy.get('@theElement').scrollIntoView().click()
})
// data-test -------------------------------------------------

Then('The element having class {string} should {string}', (className, assertion) => {
  cy.get(`.${className}`, { timeout: 150000 }).should(assertion.replace(/ /g, '.'), { timeout: 150000 })
})

Then('The element with class containing {string} should {string}', (className, assertion) => {
  cy.get(`[class*="${className}"]`, { timeout: 40000 }).should(assertion.replace(/ /g, '.'))
})

Then('The element having class {string} should {string} {string}', (className, assertion, inputValue) => {
  cy.get(`.${className}`).should(assertion.replace(/ /g, '.'), inputValue, { timeout: 5000 })
})

Then('The element having class {string} {int} should have text {string}', (className, index, text) => {
  cy.get(`.${className}`).eq(index - 1).contains(text, { timeout: 5000 })
})

Then('The {string} element should {string}', (element, assertion) => {
  cy.get(`[data-test="${element.replace(/ /g, '-')}"]`, { timeout: 100000 }).should(assertion.replace(/ /g, '.'))
})

Then('The origin {string} element without replacement should {string}', (element, assertion) => {
  cy.get(`[data-test="${element}"]`, { timeout: 100000 }).should(assertion.replace(/ /g, '.'))
})

Then('The element with text {string} should {string}', (text, assertion) => {
  cy.contains(text).should(assertion.replace(/ /g, '.'), { timeout: 100000 })
})

Then('The {string} dropDown element should {string} {string}', (element, assertion, inputValue) => {
  cy.get(`[data-test="${element.replace(/ /g, '-')}"]`).click()
  cy.get('.ant-select-dropdown-menu-item, .ant-select-item-option-content').should(assertion.replace(/ /g, '.'), inputValue, { timeout: 50000 })
})

Then('The {string} credit expire element should {string} {string}', (element, assertion, inputValue) => {
  const date = new Date()
  date.setDate(date.getDate() + 20)

  cy.get(`[data-test="${element.replace(/ /g, '-')}"]`)
    .should(assertion.replace(/ /g, '.'), inputValue
      .replace('{expired_date}', moment(date).format('Do MMM')), { timeout: 50000 })
})

Then('The {string} element {int} should {string}', (element, number, assertion) => {
  cy.get(`[data-test="${element.replace(/ /g, '-')}"]`).eq(number).should(assertion.replace(/ /g, '.'), { timeout: 20000 })
})

Then('The {string} element should {string} {string}', (element, assertion, value) => {
  cy.get(`[data-test="${element.replace(/ /g, '-')}"]`, { timeout: 20000 }).should(assertion, value)
})

Then('The {string} element should {string} the {string}', (element, assertion, value) => {
  cy.get(`[data-test="${element}"]`, { timeout: 20000 }).should(assertion, value)
})

Then('The {string} element number {int} should {string} {string}', (element, number, assertion, value) => {
  cy.get(`[data-test="${element.replace(/ /g, '-')}"],
          [data-test="${element.replace(/ /g, '-')}"] input`)
    .eq(number).should(assertion, value)
})

Then('The {string} element number {int} should have the text {string}', (element, index, text) => {
  cy.get(`[data-test="${element.replace(/ /g, '-')}"]`).eq(index - 1).contains(text, { timeout: 5000 })
})

Then('The {string} element number {int} should have the {string} text', (element, index, text) => {
  cy.get(`[data-test="${element.replace(/ /g, '-')}"]`).eq(index - 1).should('contain', text, { timeout: 5000 })
})

Then('The {string} element should have the {string} {string}', (element, attribute, text) => {
  cy.get(`[data-test="${element.replace(/ /g, '-')}"]`).should('have.attr', attribute, text, { timeout: 5000 })
})

Then('All the {string} elements should have the text {string}', (elements, text) => {
  cy.get(`[data-test="${elements.replace(/ /g, '-')}"] h3`).each(($index) => {
    cy.wrap($index).should('have.attr', 'data-test').and('include', text, { timeout: 5000 })
  })
})

Then('All the {string} elements should {string} {string}', (elements, assertion, value) => {
  cy.get(`[data-test^="${elements.replace(/ /g, '-')}"]`).each(($index) => {
    cy.wrap($index).should(assertion, value)
  })
})

Then('The {string} element number {int} should not have the text {string}', (element, index, text) => {
  cy.get(`[data-test="${element.replace(/ /g, '-')}"]`).eq(index - 1).should('not.contain', text)
})

Then('The {string} element number {int} should contain the {string} element', (parent, index, child) => {
  cy.get(`[data-test="${parent.replace(/ /g, '-')}"]`).eq(index - 1).find(`[data-test="${child.replace(/ /g, '-')}"]`)
})

Then('The {string} banner should {string}', (type, assertion) => {
  cy.get(`[data-test="banner-${type}"]`).should(assertion.replace(/ /g, '.'))
})

// Generic ---------------------------------------------------

Then('I should see {int} {string} elements', (length, element) => {
  cy.get(`[data-test="${element.replace(/ /g, '-')}"]`, { timeout: 5000 }).should('have.length', length)
})

Then('I should see more than {int} {string} elements', (length, element) => {
  cy.get(`[data-test="${element.replace(/ /g, '-')}"]`).its('length').should('be.gte', length)
})

When('I click the {string} dropdown', (name) => {
  cy.get(`[data-test="${name.replace(/ /g, '-')}"]`).scrollIntoView()
  cy.get(`[data-test="${name.replace(/ /g, '-')}"]`).should('be.visible').click()
})

When('I expand {string} dropdown', (name) => {
  cy.get(`[data-test="${name.replace(/ /g, '-')}"]`).click()
})

When('I select the {string} item inside {string}', (text, name) => {
  cy.get(`[data-test="${name.replace(/ /g, '-')}"]`).contains(text).click()
  cy.get('.ant-card-loading-content').should('not.exist')
})

When('I select the {string} option from {string} dropdown', (text, name) => {
  cy.wait(100)
  cy.get(`[data-test="${name.replace(/ /g, '-')}-${text.toLowerCase().replace(/ /g, '-')}"]`).contains(text, { timeout: 5000 }).click({ force: true })
})

When('I select the {string} item from tree select', (text) => {
  cy.get(`[data-test="tree-node-${text}"]`)
    .then((nodes) => {
      // if the node is already selected, 2 nodes would be found.
      // which we will select the second one
      if (nodes.length === 1) {
        cy.get(nodes).click({ force: true })
      } else {
        cy.get(nodes[1]).click({ force: true })
      }
    })
  cy.get('.ant-card-loading-content').should('not.exist')
})

When('I search in field {string} and select the {string} item from dropdown', (element, text) => {
  cy.get(`[data-test="${element.replace(/ /g, '-')}"], [data-test="select-${element.replace(/ /g, '-')}"]`).first()
    .scrollIntoView()
    .should('be.visible', { timeout: 5000 })
    .find('input')
    .trigger('click', { force: true })
    .type(text, { timeout: 500, force: true })
  cy.get('.ant-select-dropdown-menu-item, .ant-select-item-option-content').contains(text).click({ force: true }, { timeout: 5000 })
})

When('I search in field {string} and select the {string} item from treeSelect', (element, text) => {
  cy.get(`[data-test="${element.replace(/ /g, '-')}"]`).first().click({ timeout: 30000 })
  cy.focused().not('[readonly]').type(text, { timeout: 500 })
  cy.get('.ant-select-tree-title').contains(text).click({ force: true }, { timeout: 5000 })
})

When('I search {string} from {string} treeselect', (text, name) => {
  cy.get(`[data-test="tree-select-${name}"]`).click()
  cy.get('[aria-label=\'filter select\'], [aria-controls=\'rc_select_0_list\']').not('[readonly]').type(text, { timeout: 500 })
})

// antd tree select
When('I click the {string} tree select', (name) => {
  cy.get(`[data-test=tree-select-${name.replace(/ /g, '-')}]`).click()
})

Then('The {string} tree select should open', (name) => {
  cy.get(`[data-test=tree-select-${name.replace(/ /g, '-')}] .ant-select-open`, { timeout: 5000 }).should('exist')
})

Then('The {string} tree select should have label as {string}', (name, label) => {
  cy.get(`[data-test=tree-select-${name.replace(/ /g, '-')}]`, { timeout: 5000 }).should('contain', label)
})

Then('The {string} tree select should {string} {string}', (tree, assertion, inputValue) => {
  cy.get('div.ant-select-dropdown:not(.ant-select-dropdown-hidden)', { timeout: 5000 }).should(assertion.replace(/ /g, '.'), inputValue)
})

Then('The active item of the tree select should have the text {string}', (label) => {
  cy.get('.ant-select-tree-node-selected .ant-select-tree-title', { timeout: 5000 }).should('contain', label)
})

Then('The tree select menu should contain following items:', (dataList) => {
  const treeSelectValues = dataList.hashes()
  treeSelectValues.forEach((listItem) => {
    cy.get('.ant-select-tree-dropdown, .ant-select-tree-list').should('contain', listItem['account-name'])
  })
})

Then('The {string} select dropdown menu should contain following items:', (selectName, dataList) => {
  const selectValues = dataList.rawTable
  cy.get(`[data-test="select-${selectName}"], [data-test="dropdown-${selectName}"]`, { timeout: 5000 }).click()
  selectValues.forEach(([eachValue]) => {
    cy.contains(eachValue, { matchCase: false }).first().should('be.visible', { timeout: 12000 })
  })
})

Given('take the snapshot of window {string}', (name) => {
  cy.get('.spinner', { timeout: 20000 }).should('not.exist')
  cy.get('.ant-card-loading-content').should('not.exist', { timeout: 30000 })
  cy.get('.ant-skeleton-paragraph').should('not.exist')
  cy.get('.ant-spin-spinning').should('not.exist')
  cy.get('.-with-loading__overlaySpinnerWrapper').should('not.exist')
  cy.wait(3000)
  cy.percySnapshot(name)
})

Given('take a quick snapshot of window {string}', (name) => {
  cy.wait(1000)
  cy.percySnapshot(name, { widths: [1080, 1920], minHeight: 1080, enableJavaScript: false })
})

Then('The {string} badge element should be empty', (element) => {
  cy.get(`[data-test="badge-${element.replace(/ /g, '-')}"]`).should('be.empty', { timeout: 30000 })
})

Then('The {string} badge element should contain {string}', (element, content) => {
  if (parseInt(content, 10) > 9) {
    const [firstDigit, sendcondDigit] = content
    cy.get(`[data-test="badge-${element.replace(/ /g, '-')}"] .current`, { timeout: 30000 })
      .then(([firstBadgeNumber, secondBadgeNumber]) => {
        cy.get(firstBadgeNumber).should('contain', firstDigit, { timeout: 30000 })
        cy.get(secondBadgeNumber).should('contain', sendcondDigit, { timeout: 30000 })
      })
  } else {
    cy.get(`[data-test="badge-${element.replace(/ /g, '-')}"] .current`).should('contain', content, { timeout: 20000 })
  }
})

Then('The {string} badge element should contain over {string}', (element, content) => {
  cy.get(`[data-test="badge-${element.replace(/ /g, '-')}"]`, { timeout: 20000 }).should('contain', `${content}+`)
})

Then('There should have {int} elements with {string} class after {string} class', (inputValue, nextElement, element) => {
  cy.get(`div[class*="${element}"] ~ div[class*="${nextElement}"]`).should(($lis) => {
    expect($lis).to.have.length(inputValue)
  })
})

Then('The element having {string} class inside {string} element should {string}', (element, parent, assertion) => {
  cy.get(`[data-test*="${parent}"] [class*="${element}"]`).should(assertion.replace(/ /g, '.'))
})

When('I click the element having {string} class inside {string} class', (element, parent) => {
  cy.get(`[class*="${parent}"] [class*="${element}"]`).as('theElement')
  cy.get('@theElement').scrollIntoView().click({ timeout: 20000 })
})

When('I click the element having {string} class number {int}', (element, index) => {
  cy.get(`[class*="${element}"]`).eq(index - 1)
    .scrollIntoView()
    .click()
})

When('I click the icon {string} in the element {string}', (icon, element) => {
  cy.get(`[data-test="${element}"]`)
    .find(`[data-icon="${icon}"]`)
    .scrollIntoView()
    .click()
})

When('I click the element containing {string} class inside {string} element', (element, parent) => {
  cy.get(`[data-test="${parent}"] .${element}`).as('theElement')
  cy.get('@theElement').scrollIntoView().click()
})

Then('The element having {string} name should {string} {string}', (name, assertion, inputName) => {
  cy.get(`[name*="${name}"]`).should(assertion.replace(/ /g, '.'), inputName, { timeout: 5000 })
})

When('I click the link having {string} attribute', (linkContent) => {
  cy.get(`a[href*="${linkContent}"]`, { timeout: 20000 }).invoke('removeAttr', 'target').click()
  cy.url().should('include', `${linkContent}`, { timeout: 20000 })
})

Then('The element with {string} class should {string} the {string} value', (className, assertion, inputValue) => {
  cy.get(`[class*="${className}"]`, { timeout: 20000 }).invoke('val').as('theElement')
  cy.get('@theElement').should(assertion.replace(/ /g, '.'), inputValue)
})

Then('The element with {string} number {int} should {string} {string}', (className, index, assertion, inputValue) => {
  cy.get(`.${className}`).eq(index - 1)
    .scrollIntoView()
    .should('be.visible', { timeout: 5000 })
    .should(assertion.replace(/ /g, '.'), inputValue, { timeout: 5000 })
})

Then('The element with {string} class should have the {string} attribute with {string} value', (className, attribute, attributeValue) => {
  cy.get(`.${className}`).should('have.attr', `${attribute}`, `${attributeValue}`)
})

Then('The element with {string} class number {int} should have the {string} attribute with {string} value', (className, index, attribute, attributeValue) => {
  cy.get(`.${className}`).eq(index - 1).should('have.attr', `${attribute}`, `${attributeValue}`)
})

When('I force click the {string} element within the {string} element number {int}', (element, parentElement, number) => {
  cy.get(`[data-test="${parentElement.replace(/ /g, '-')}"]`).eq(number).within(() => {
    cy.get(`[data-test="${element.replace(/ /g, '-')}"]`).click({ force: true })
  })
})

Then('The tab element {string} should be {string}', (element, assertion) => {
  let flag
  if (assertion === 'disabled') { flag = 'true' } else if (assertion === 'enabled') { flag = 'false' }
  cy.get(`[data-test="${element.replace(/ /g, '-')}"]`).parent().should('have.attr', 'aria-disabled', flag, { timeout: 5000 })
})

Then('The {string} of ant date picker should contain the date {string}', (input, value) => {
  cy.get(`input[placeholder="${input}"]`).should('have.attr', 'value', value)
})

When('The element with {string} class will be hidden to prevent dynamic data', (className) => {
  cy.get(`[class*=${className}]`).invoke('attr', 'style', 'display: none').should('have.attr', 'style', 'display: none')
})

When('The element with {string} class number {int} will be hidden to prevent dynamic data', (className, index) => {
  cy.get(`[class*=${className}]`).eq(index - 1).invoke('attr', 'style', 'display: none').should('have.attr', 'style', 'display: none')
})

When('The {string} element will be hidden to prevent dynamic data', (element) => {
  cy.get(`[data-test=${element}]`).invoke('attr', 'style', 'display: none').should('have.attr', 'style', 'display: none')
})

When('The {string} element number {int} will be hidden to prevent dynamic data', (element, index) => {
  cy.get(`[data-test=${element}]`).eq(index - 1).invoke('attr', 'style', 'display: none').should('have.attr', 'style', 'display: none')
})

When('The hidden {string} element number {int} will be re-diplayed properly', (element, index) => {
  cy.get(`[data-test=${element}]`).eq(index - 1).invoke('attr', 'style', 'display: initial').should('have.attr', 'style', 'display: initial')
})

When('The hidden element with {string} class number {int} will be re-diplayed properly', (className, index) => {
  cy.get(`[class*=${className}]`).eq(index - 1).invoke('attr', 'style', 'display: initial').should('have.attr', 'style', 'display: initial')
})
