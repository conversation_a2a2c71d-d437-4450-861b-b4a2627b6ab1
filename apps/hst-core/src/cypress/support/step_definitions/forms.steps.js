import 'cypress-file-upload'
import { Given, When, Then } from "@badeball/cypress-cucumber-preprocessor"

When('I enter the {string} select tag as {string}', (inputName, inputValue) => {
  cy.get(`[data-test="${inputName}"] input, [data-test="${inputName}"] textarea`)
    .scrollIntoView()
    .clear()
    .type(inputValue, { timeout: 5000 })
})

When('I enter the {string} as {string}', (inputName, inputValue) => {
  cy.get(`
  input[data-test="input-${inputName}"],
    [data-test="input-${inputName}"] input,
    [data-test="input-${inputName}"] textarea,
    [data-test="${inputName}"] input,
    [data-test="${inputName}"] textarea
  `)
    .not('[readonly]')
    .clear({ force: true })
    .clear({ force: true })
    .type(inputValue, { timeout: 500 })
})

When('I enter the {string} as {string} and do tabout', (inputName, inputValue) => {
  cy.get(`
  input[data-test="input-${inputName}"],
    [data-test="input-${inputName}"] input,
    [data-test="input-${inputName}"] textarea,
    [data-test="${inputName}"] input,
    [data-test="${inputName}"] textarea
  `)
    .clear({ force: true })
    .clear({ force: true })
    .type(inputValue, { timeout: 500 })
    .tab()
})

When('I enter the {string} field as {string} text without blur a focused element', (inputName, inputValue) => {
  cy.get(`
    input[data-test="input-${inputName}"],
    [data-test="input-${inputName}"] input,
    [data-test="input-${inputName}"] textarea,
    [data-test*="select-tags"] input,
    [data-test*="select-tags"] textarea
  `)
    // .clear()
    .type(inputValue, { timeout: 500 })
})

When('I enter the {string} with special charater as {string}', (inputName, inputValue) => {
  cy.get(`
    input[data-test="input-${inputName}"],
    [data-test="input-${inputName}"] input,
    [data-test="input-${inputName}"] textarea,
    [data-test*="select-tags"] input,
    [data-test*="select-tags"] textarea
  `)
    .clear()
    .clear()
    .type(inputValue, { timeout: 500, parseSpecialCharSequences: false })
    .blur({ force: true })
})

When('I enter the {string} input as {string}', (inputName, inputValue) => {
  cy.get(`[data-test="input-${inputName}"] input`)
    .clear()
    .clear()
    .type(inputValue, { timeout: 5000 })
    .blur({ force: true })
})

When('I enter the {string} as {string} in element number {int}', (inputName, inputValue, number) => {
  cy.get(`
      input[data-test="input-${inputName}"],
      [data-test="input-${inputName}"] input,
      [data-test="input-${inputName}"] textarea,
      [data-test*="select-tags"] textarea,
      [data-test*="select-tags"] input
  `)
    .clear({ force: true })
    .eq(number)
    .type(inputValue, { timeout: 500, force: true })
    .blur({ force: true })
})

When('I enter the {string} tags as {string}', (inputName, inputValue) => {
  const items = inputValue.split(',')
  const backspaceActions = items.reduce((acc) => `{backspace}${acc}`, '')

  cy.get(`[data-test="input-${inputName}"] input`)
    .type(backspaceActions + inputValue)
    .blur({ force: true })
})

// code input only
When('I enter the {string} code input as {string}', (inputName, inputValue) => {
  for (let index = 0; index < inputValue.length; index += 1) {
    cy.get(`[data-test="input-${inputName}"] input`).eq(index).clear().type(inputValue.charAt(index))
  }
})

When('I clear the {string} field', (inputName) => {
  cy.get(`[data-test="input-${inputName}"] input`)
    .not('[readonly]')
    .clear()
    .clear()
    .blur({ force: true })
})

When('I clear the {string} select tag', (selectTagName) => {
  cy.get(`[data-test="${selectTagName}"]`).not('[readonly]')
    .click()
})

Then('The {string} field should {string}', (inputName, assertion) => {
  cy.get(`[data-test="input-${inputName}"] input, [data-test="input-${inputName}"] textarea`).should(assertion.replace(/ /g, '.'), { timeout: 10000 })
})

Then('The {string} field should {string} {string}', (inputName, assertion, inputValue) => {
  cy.get(`[data-test="input-${inputName}"] input, [data-test="input-${inputName}"] textarea`).should(assertion.replace(/ /g, '.'), inputValue)
})

Then('The {string} field inside {string} element should {string} {string}', (insideTag, outsideElement, assertion, inputValue) => {
  cy.get(`[data-test="${outsideElement}"] ${insideTag}`).should(assertion.replace(/ /g, '.'), inputValue)
})

Then('The {string} input should have the {string} {string} {string}', (inputName, attribute, assertion, value) => {
  cy.get(`[data-test="input-${inputName}"] input`).should('have.attr', attribute).and(assertion.replace(/ /g, '.'), value)
})

Then('The {string} element number {int} should have the {string} {string}', (element, index, attribute, text) => {
  cy.get(`[data-test="${element.replace(/ /g, '-')}"]`).eq(index - 1).should('have.attr', attribute).and('include', text)
})

When('I click on the {string} field', (inputName) => {
  cy.get(`[data-test="input-${inputName}"] input`).click().blur()
})

When('I click the {string} tag inside {string} element', (selectTagName, parentElement) => {
  cy.get(`[data-test="${parentElement}"] ${selectTagName}`).first()
    .click()
})

// this is for chrome
When('I upload a file with name {string} and type {string}', (fileName, fileType) => {
  cy.fixture(fileName, 'binary').then(Cypress.Blob.binaryStringToBlob).then((fileContent) => {
    cy.get('input[type=file]').attachFile({
      fileContent, fileName, mimeType: fileType,
    })
  })
})

When('I upload a file with name {string} and type {string} in element {int}', (fileName, fileType, number) => {
  cy.fixture(fileName, 'binary').then(Cypress.Blob.binaryStringToBlob).then((fileContent) => {
    cy.get('input[type=file]').eq(number).attachFile({
      fileContent, fileName, mimeType: fileType,
    })
  })
})
// Textarea

When('I enter the {string} textarea inside {string} as {string}', (inputName, parentElement, inputValue) => {
  cy.get(`[data-test="${parentElement}"] [data-test="input-${inputName}"] textarea`)
    .clear()
    .type(inputValue)
    .blur({ force: true })
})

When('I enter the {string} textarea as {string}', (inputName, inputValue) => {
  cy.get(`#${inputName}, [data-test="input-${inputName}"] textarea, [data-test="${inputName}"]`)
    .scrollIntoView()
    .clear()
    .clear()
    .type(inputValue, { parseSpecialCharSequences: false })
    .blur({ force: true })
})

When('I enter the {string} textarea as {string} on element {int}', (inputName, inputValue, number) => {
  cy.get(`#${inputName}, [data-test="input-${inputName}"] textarea`, { timeout: 40000 }).eq(number)
    .scrollIntoView()
    .clear()
    .clear()
    .type(inputValue)
    .blur({ force: true })
})

When('I clear the {string} textarea', (inputName) => {
  cy.get(`#${inputName}`)
    .clear()
    .blur({ force: true })
})

When('I force clear the {string} textarea', (inputName) => {
  cy.get(`#${inputName}`)
    .clear({ force: true })
    .blur({ force: true })
})

Then('The {string} textarea should {string} {string}', (inputName, assertion, inputValue) => {
  cy.get(`[data-test="input-${inputName}"] textarea`).should(assertion.replace(/ /g, '.'), inputValue)
})

Then('The {string} Message box should contain {string}', (value, text) => {
  cy.get(`[class="${value}"] div`).contains(text)
})

// Checkbox

When('I check the {string} checkbox', (checkboxName) => {
  cy.get(`[data-test="checkbox-${checkboxName}"]`).scrollIntoView({ timeout: 15000 })
  // re-get again to resolve flaky element is detached from the DOM
  cy.get(`[data-test="checkbox-${checkboxName}"]`, { timeout: 15000 }).first().focus().check()
})

When('I uncheck the {string} checkbox', (checkboxName) => {
  cy.get(`[data-test="checkbox-${checkboxName}"]`).scrollIntoView().uncheck()
})

Then('The {string} checkbox should {string}', (checkboxName, assertion) => {
  cy.get(`[data-test="checkbox-${checkboxName}"]`).should(assertion.replace(/ /g, '.'))
})

Then('All checkboxes in the {string} table should be {string}', (tableName, selectionStatus) => {
  let assertion = 'have.checked'
  if (selectionStatus !== 'selected') {
    assertion = 'have.checked=false'
  }
  cy.get(`#mm-table-${tableName} input`).should(assertion, 'checked')
})

Then('The {string} checkbox in the {string} element number {int} should {string}', (checkboxName, element, index, assertion) => {
  cy.get(`[data-test="${element}"]`).eq(index - 1).find(`[data-test="checkbox-${checkboxName}"]`).should(assertion, 'checked')
})

Then('I {string} the {string} checkbox in the {string} element number {int}', (action, checkboxName, element, index) => {
  const status = action === 'check' ? 'unchecked' : 'checked'
  if (status !== 'unchecked') {
    cy.get(`[data-test="${element}"]`).eq(index - 1).find(`[data-test="checkbox-${checkboxName}"]`).click()
  }
})

// Radio

Then('The radio button for {string} should {string}', (name, selectionStatus) => {
  cy.get(`[data-test="${name}"] input[type="radio"]`).should(selectionStatus.replace(/ /g, '.'))
})

Then('The radio button in the {string} element number {int} should {string}', (element, index, selectionStatus) => {
  cy.get(`[data-test="${element}"] input[type="radio"]`).eq(index - 1).should(selectionStatus.replace(/ /g, '.'))
})

When('I select the radio button in the {string} element number {int}', (element, index) => {
  cy.get(`[data-test="${element}"]`).eq(index - 1).click()
})

When('I select the {string} radio button', (name) => {
  cy.get(`[data-test="${name.replace(/ /g, '-')}"]`).should('be.exist').click({ timeout: 1000 })
})

// Select

When('I select the {string} in the dropdown {string}', (text, selectName) => {
  cy.get(`[data-test="select-${selectName}"]`).click()
  cy.focused().type(text, { force: true })
  cy.focused().type('{enter}')
})

When('I press {string} key', (key) => {
  cy.focused().type(`{${key}}`, { force: true })
})

// Select -----------------------------------------

When('I select {string} element from the {string} select', (element, selectName) => {
  cy.get(`[data-test="select-${selectName}"], [data-test="dropdown-${selectName}"]`, { timeout: 5000 }).click()
  cy.get(`[data-test="${element}"], [datatest="${element}"]`).last().scrollIntoView().click({ force: true }, { timeout: 20000 })
})

When('I select {string} from the {string} select', (selectValue, selectName) => {
  cy.get(`[data-test="select-${selectName}"], [data-test="dropdown-${selectName}"]`, { timeout: 5000 }).click()
  cy.wait(50)
  cy.contains(selectValue, { matchCase: false }).first().scrollIntoView().click({ force: true }, { timeout: 20000 })
})

When('I select {string} from the {string} select in {string} container', (selectValue, selectName, container) => {
  cy.get(`[data-test="${container}"]`).find(`[data-test="select-${selectName}"], [data-test="dropdown-${selectName}"], [data-test="${selectName}"]`, { timeout: 5000 }).click()
  cy.contains(selectValue, { matchCase: false }).first().scrollIntoView().click({ force: true }, { timeout: 20000 })
})

When('I select {string} from the {string} dropDown', (selectValue, selectName) => {
  cy.get(`[data-test="select-${selectName}"], [data-test="${selectName}"]`, { timeout: 5000 }).click()
  cy.get('.ant-select-dropdown-menu-item, .ant-select-item-option-content').contains(selectValue, { matchCase: false }).scrollIntoView().click({ force: true })
})

// Buttons -------------------------------------------------

Then('The {string} button should {string}', (buttonName, assertion) => {
  cy.get(`button[data-test="button-${buttonName.replace(/ /g, '-')}"]`, { timeout: 5000 }).should(assertion.replace(/ /g, '.'))
})

Then('I click {string} button', (buttonName) => {
  cy.get(`[class="${buttonName}"]`).click({ timeout: 30000 })
})

// Error Messages -------------------------------------------

Then('I should see an error message {string} on the {string} field', (errorMessage, inputName) => {
  cy.get('body')
    .then(($body) => {
      if ($body.find(`
      [data-test="input-${inputName}"] label,
      [data-test="-${inputName}"] label
      `).text().includes(errorMessage)) {
        return (`
        [data-test="input-${inputName}"] label,
        [data-test="-${inputName}"] label
      `)
      }

      return `[data-test="validator-message-${inputName}"]`
    }).then((selector) => cy.get(selector).contains(errorMessage))
})

Then('The error message {string} should be displayed on {string} field', (errorMessage, inputName) => {
  cy.get('body')
    .then(($body) => {
      if ($body.find(`
      [class="-${inputName}"] label
      `).text().includes(errorMessage)) {
        return (`
        [class="-${inputName}"] label
      `)
      }

      return `[class="${inputName}"]`
    }).then((selector) => cy.get(selector).contains(errorMessage))
})
// End Error Messages -------------------------------------------

When('I select {string} item from ant dropdown', (itemLabel) => {
  cy.get('.ant-select-dropdown .ant-select-tree-title', { timeout: 15000 })
    .contains(itemLabel)
    .click()
})

When('I click {string} button from ant dropdown', (selectType) => {
  cy.get(`.ant-select-dropdown .ant-select-tree-title [data-test="link-span-${selectType}"]`, { timeout: 15000 })
    .click({ force: true })
})

When('I enter the {string} as {string} inside {string} textbox', (inputName, inputValue, parentElement) => {
  cy.get(`
    [data-test="input-${inputName}"] input,
    [data-test="input-${inputName}"] textarea,
    [data-test="${parentElement}"] [data-test*="select-tags"] input,
    [data-test="${parentElement}"] [data-test*="select-tags"] textarea
  `)
    .scrollIntoView()
    .clear()
    .type(inputValue, { timeout: 500 })
    .blur({ force: true })
})

When('I clear the {string} select tag inside {string} container', (selectTagName, parentElement) => {
  cy.get(`[data-test="${parentElement}"] [data-test="${selectTagName}"] svg[data-icon="close-circle"]`).click()
})

// Browser prompt
When('I click {string} on browser prompt', (inputValue) => {
  cy.on('window:confirm', () => (inputValue === 'OK'))
})

When('The window confirm is display with text {string} after clicking {string} element number {int}', (promptText, element, number) => {
  cy.get(`[data-test="${element.replace(/ /g, '-')}"]`).eq(number).scrollIntoView()
    .should('be.visible')
    .click({ timeout: 30000 })
  const stub = cy.stub()
  cy.on('window:confirm', stub).then(() => {
    expect(stub.getCall(0)).to.be.calledWith(promptText)
  })
})
