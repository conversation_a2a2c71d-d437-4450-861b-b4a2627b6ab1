import { faker } from '@faker-js/faker'
import moment from 'moment'
import { Given, When, Then } from "@badeball/cypress-cucumber-preprocessor"

let userName
let phoneNumber
let propertyValue
let groupName
let elementValue
let randomString
let broadcastName

Then('The {string} element should be available in {int} minutes', (element, number) => {
  cy.get(`[data-test="${element.replace(/ /g, '-')}"]`, { timeout: number * 60000 }).should('be.visible')
})

// Account Automation - End --------------------

Then('The {string} element should {string} {string} {string}', (element, assertion, value) => {
  switch (value) {
    case 'phoneNumber':
      cy.get(`[data-test="${element.replace(/ /g, '-')}"]`)
        .then(() => {
          cy.should(assertion, phoneNumber, { timeout: 20000 })
        })
      break
    case 'randomElementValue':
      cy.get(`[data-test="${element.replace(/ /g, '-')}"]`).invoke('val')
        .then(() => {
          cy.should(assertion, elementValue, { timeout: 20000 })
        })
      break
    default:
      break
  }
})

Then('The element with {string} class inside {string} should contain the {string} {string}', (className, parent, value) => {
  switch (value) {
    case 'phoneNumber':
      cy.get(`[data-test="${parent.replace(/ /g, '-')}"] [class="${className.replace(/ /g, '-')}"]`).invoke('text', { timeout: 500 })
        .then((text) => {
          phoneNumber = text
        })
      break
    default:
      break
  }
})

When('I enter the element {string} {string} a {string} string with {int} characters', (element, attribute, characterType, length) => {
  randomString = faker.helpers.repeatString('a', length)
  if (characterType === 'unicode') {
    randomString = `${randomString.substr(1)}ệ`
  }
  cy.get(`${element}[data-test="${attribute}"]`)
    .clear()
    .type(randomString, { timeout: 500 })
})

When('I click the checkbox with text {string} and class {string}', (text, className) => {
  // cy.get(`.${className}`).contains(text).click()
  cy.get('div').contains(text).prevUntil(`input[class="${className}"]`).click({ multiple: true })
})

When('I set the clock to {string}', (humanReadableTimeString) => {
  const now = Date.parse(humanReadableTimeString)
  cy.clock(now)
})

When('I restore the clock', () => {
  cy.clock().invoke('restore')
})
