import moment from 'moment'
import { faker } from '@faker-js/faker'
import 'cypress-wait-until'
// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add("login", (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add("drag", { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add("dismiss", { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This is will overwrite an existing command --
// Cypress.Commands.overwrite("visit", (originalFn, url, options) => { ... })

// Divide ci_session value into two parts when the ci_session value lengh is reached to the limit on Google Chrome
// Maximum cookies 4096 bytes - Refer https://docs.devexpress.com/AspNet/11912/common-concepts/cookies-support
function seperateCookieValue(cookieValue) {
  const featuresListIndex = cookieValue.indexOf('[')
  const sessionValue = cookieValue.substring(0, featuresListIndex)
  const featuresIndex = cookieValue.indexOf(':')
  const featuresList = cookieValue.substring(featuresIndex + 1, cookieValue.length - 1)
  const featuresListArr = featuresList.split(',')
  const featuresList1Length = featuresListArr.length / 2
  const featuresList1 = featuresListArr.slice(0, featuresList1Length).toString()
  const featureList1Str = sessionValue.concat('[features:'.concat(featuresList1).concat(']'))
  const featuresList2 = featuresListArr.slice(featuresList1Length).toString()
  const featuresList2Str = '[features:'.concat(featuresList2).concat(']')
  return [featureList1Str, featuresList2Str]
}

const MENU_PATH = {
  messages: {
    dataTest: 'menu-messages',
    children: {
      send: {
        dataTest: 'link-Send-a-Message',
      },
      broadcasts: {
        dataTest: 'link-Broadcasts',
      },
      newMessage: {
        dataTest: 'link-new-message',
      },
      scheduledMessage: {
        dataTest: 'scheduled-messages',
      },
    },
  },
  account: {
    dataTest: 'menu-account',
    children: {
      subAccounts: {
        dataTest: 'link-subAccounts',
      },
      users: {
        dataTest: 'link-usersManagement',
      },
      settings: {
        dataTest: 'link-accountSettings',
      },
      numbers: {
        dataTest: 'sender-ids',
      },
    },
  },
  contacts: {
    dataTest: 'menu-contacts',
    children: {
      contactsGroup: {
        dataTest: 'contact-groups',
      },
      contacts: {
        dataTest: 'all-contacts',
      },
    },
  },
}

Cypress.on('window:before:load', (win) => {
  win.ga = cy.stub().as('ga')
})

Cypress.Commands.add('goTo', (pageName) => {
  const webUrl = Cypress.env('WEB_URL')

  return cy.location()
    .then((loc) => {
      if (!loc.pathname || loc.pathname !== `/${pageName.replace(/ /g, '-')}`) {
        return cy.visit(`${webUrl}${pageName.replace(/ /g, '-')}`)
      }
      return cy.reload()
    })
})

Cypress.Commands.add('navigateTo', (route) => {
  if (route === '/') return

  const [mainRoute, nestedRoute] = route.split('/')

  cy.get(`[data-test=${MENU_PATH[mainRoute].dataTest}] > .ant-menu-submenu-title`)
    .invoke('attr', 'aria-expanded')
    .then((isExpanded) => {
      if (isExpanded === 'true') {
        cy.get(`[data-test=${MENU_PATH[mainRoute].children[nestedRoute].dataTest}]`)
          .click()
      } else {
        cy.get(`[data-test=${MENU_PATH[mainRoute].dataTest}]`)
          .click()
          .get(`[data-test=${MENU_PATH[mainRoute].children[nestedRoute].dataTest}]`)
          .click()
      }
    })
})

Cypress.Commands.add('getUsers', (userType) => {
  if (['local', 'multitxt', 'directsms', 'wholesalesms', 'streetdata', 'smsb', 'messagenet', 'mobipost', 'smscentral', '2degrees'].includes(Cypress.env('CONFIG_FILE'))) {
    return cy.fixture(`users.${Cypress.env('CONFIG_FILE')}.json`)
      .then((users) => users[userType] || {})
  }

  return {}
})

Cypress.Commands.add('getToken', (mwUrl, userName, passWord) => {
  cy.request({ // Get TOKEN
    method: 'POST',
    url: `${mwUrl}/middleware/iam/v1/jwt/auth`,
    timeout: 120000,
    headers: {
      accept: 'application/json',
      'content-type': 'application/json',
    },
    body: {
      grant_type: 'password',
      username: userName,
      password: passWord,
      client_id: '',
      scope: '',
    },
    json: true,
  }).then((response) => response.body.access_token)
})

Cypress.Commands.add('getWorkFlowList', (mwUrl, restApi, token) => {
  cy.request({ // Get WorkFlow list
    method: 'GET',
    url: `${restApi}/wfp-bff/workflows`,
    timeout: 120000,
    headers: {
      accept: 'application/json',
      'content-type': 'application/json',
      Origin: mwUrl,
      Authorization: `Bearer ${token}`,
    },
    json: true,
  }).then((response) => {
    const workflowList = []
    response.body.workflows.forEach((wf) => {
      workflowList.push(wf.id)
    })
    return workflowList
  })
})

Cypress.Commands.add('deleteWorkFlow', (mwUrl, restApi, token, workflowId) => {
  cy.request({ // DELETE WorkFlow with WorkFlow ID
    method: 'DELETE',
    url: `${restApi}/wfp-bff/workflows/${workflowId}`,
    timeout: 120000,
    headers: {
      accept: 'application/json',
      'content-type': 'application/json',
      Origin: mwUrl,
      Authorization: `Bearer ${token}`,
    },
    json: true,
  }).then((response) => response.body.workflowId)
})

Cypress.Commands.add('getAssignedNumList', (mwUrl, restApi, token) => {
  cy.request({ // Get Assigned Dedicated Numbers list
    method: 'GET',
    url: `${restApi}/v1/messaging/numbers/dedicated/assignments`,
    timeout: 120000,
    headers: {
      accept: 'application/json',
      'content-type': 'application/json',
      Origin: mwUrl,
      Authorization: `Bearer ${token}`,
    },
    params: {
      page_size: '100',
    },
    json: true,
  }).then((response) => {
    const dedicatedNumList = []
    response.body.data.forEach((element) => {
      dedicatedNumList.push(element.assignment.number_id)
    })
    return dedicatedNumList
  })
})

Cypress.Commands.add('disassocicateNum', (mwUrl, restApi, token, dedicatedNumId) => {
  cy.request({ // Disassociate Dedicated Number with Number ID
    method: 'DELETE',
    url: `${restApi}/v1/messaging/numbers/dedicated/${dedicatedNumId}/assignment`,
    timeout: 120000,
    headers: {
      accept: 'application/json',
      'content-type': 'application/json',
      Origin: mwUrl,
      Authorization: `Bearer ${token}`,
    },
    json: true,
  }).then((response) => response.status)
})

Cypress.Commands.add('getDedicatedNumList', (numUrl) => {
  cy.request({ // Get 100 Dedicated Numbers using Internal API
    method: 'GET',
    url: `${numUrl}/v1/numbers?pageSize=100`,
    timeout: 120000,
    headers: {
      accept: 'application/json',
      'content-type': 'application/json',
    },
  }).then((response) => response.body.numbers)
})

Cypress.Commands.add('deleteDedicatedNum', (numUrl, dedicatedNumId) => {
  cy.request({ // Delete Dedicated Number with Number ID using Internal API
    method: 'DELETE',
    url: `${numUrl}/v1/numbers/${dedicatedNumId}`,
    timeout: 120000,
    headers: {
      accept: 'application/json',
      'content-type': 'application/json',
    },
  }).then((response) => response.status)
})

Cypress.Commands.add('getBroadcastList', (mwUrl, token, broadcastName, broadcastStatuses) => {
  const broadcastStatusArr = broadcastStatuses.toUpperCase().split(',')
  const statusArrayQry = broadcastStatusArr.map((el, idx) => `statuses[${idx}]=${el}`).join('&')
  cy.request({ // Get all broadcasts with selected status
    method: 'GET',
    url: `${mwUrl}/middleware/messaging/v1/broadcasts?name=${broadcastName}&${statusArrayQry}&pageSize=100`,
    timeout: 120000,
    headers: {
      accept: 'application/json',
      'content-type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  }).then((response) => {
    const broadcastIdList = []
    response.body.broadcasts.forEach((element) => {
      broadcastIdList.push(element.id)
    })
    return broadcastIdList
  })
})

Cypress.Commands.add('deleteAllBroadcasts', (mwUrl, token, broadcastId) => {
  cy.request({ // Delete broadcast with selected Id
    method: 'PATCH',
    url: `${mwUrl}/middleware/messaging/v1/broadcasts/${broadcastId}`,
    timeout: 120000,
    headers: {
      accept: 'application/json',
      'content-type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: {
      status: 'CANCELLED',
    },
    json: true,
  }).then((response) => response.status)
})

Cypress.Commands.add('sendMTMessage', (mwUrl, token, msgContent, phoneNum) => {
  cy.request({ // Send MT message to the selected phone number
    method: 'POST',
    url: `${mwUrl}/middleware/messaging/v1/messages`,
    timeout: 300000,
    headers: {
      accept: 'application/json',
      'content-type': 'application/json',
      Origin: mwUrl,
      Authorization: `Bearer ${token}`,
    },
    body: {
      destinationAddress: `${phoneNum}`,
      content: `${msgContent}`,
    },
    json: true,
  }).then((response) => response.status)
})

Cypress.Commands.add('replyMOMessage', (nextGenID, vendorID, msgContent, sourcePhoneNum) => {
  const createTimeStamp = moment().toISOString()
  cy.request({ // Use Kinesis to reply MO message
    method: 'PUT',
    url: 'https://a8e88t7mja.execute-api.ap-southeast-2.amazonaws.com/dev/kinesis/stream',
    timeout: 30000,
    headers: {
      accept: 'application/json',
      'content-type': 'application/json',
      'x-api-key': '27LTjq6dRx3z0qNlVtDLfapLie6BcqSMaVYz040C',
      STREAM_NAME: 'web-ugapps-EventNotification-WPFNCS9RU2S5',
    },
    body: {
      payloadId: faker.random.uuid(),
      payloadCreationTimestamp: `${createTimeStamp}`,
      priority: 'PRIORITY_0',
      trace: {
        workerHops: [],
      },
      messageType: 'EN',
      event: 'MO_PROCESSED',
      datetime: `${createTimeStamp}`,
      retryCount: 0,
      workerInformation: {
        workerName: 'gw-prioritisation-worker',
        workerVersion: '4.37.0-829',
        buildVersion: '829',
        region: 'ap-southeast-2',
        instanceId: 'i-0492e8b177de04dc1',
      },
      component: 'INGEST',
      fromPayload: {
        messageType: 'MO',
        gatewayMessageId: faker.random.uuid(),
        payloadCreationTimestamp: `${createTimeStamp}`,
        gatewayAccountId: `${nextGenID}`,
        gatewayVendorId: `${vendorID}`,
        content: `${msgContent}`,
        source: {
          address: `${sourcePhoneNum}`,
        },
        destination: {
          address: '+***********',
        },
        serviceType: 'SMS',
        sourceAllocatedFromRotary: false,
        inResponseTo: {
          messageType: 'MT',
          gatewayMessageId: faker.random.uuid(),
          payloadId: faker.random.uuid(),
          payloadCreationTimestamp: `${createTimeStamp}`,
          internalClientMetadata: {
            workerInstanceId: 'i-001713a0cf4bbda04',
            apiVersion: '0.37.0-832',
            apiRegion: 'ap-southeast-2',
            'Account-Features-1': '2178',
            apiStackName: 'at-restapi-web',
            creditsDeducted: '{"allocationType":"PARENT_ALLOCATED","deductedCredits":{"MT_SMS":1}}',
            restAccessKey: 'SysTestAccount_1_user',
            Forwarded: 'Forwarded: for=************ Forwarded: for=************ Forwarded: for=*********** Forwarded: for=*************:38668 ',
            'X-Forwarded-For': '************, ************, ***********, *************:38668',
            clientAccountId: 'f5d11333543a4fe2b45f3d929902c170_QAG_0001',
            sourceApi: 'REST',
            remoteIpAddress: '************ ->  ************ ->  ***********',
          },
          externalClientMetadata: {},
          routingAttempts: [],
          providerWeights: [{
            type: 'SERVICE_TYPE',
            presence: 'REQUIRED',
            value: 'SMS',
          },
          {
            type: 'SOURCE_ADDRESS',
            presence: 'REQUIRED',
            value: '+***********',
            addressType: 'INTERNATIONAL',
          },
          {
            type: 'DELIVERY_RECEIPT',
            presence: 'REQUIRED',
            addressType: 'INTERNATIONAL',
          },
          ],
          deliveryProperties: [],
          messageFlags: [],
          mediaContentList: [],
          priority: 'PRIORITY_0',
          trace: {
            workerHops: [],
          },
          retryCount: 0,
          gatewayAccountId: `${nextGenID}`,
          gatewayVendorId: `${vendorID}`,
          content: `${msgContent}`,
          source: {
            address: '+***********',
            addressType: 'INTERNATIONAL',
          },
          destination: {
            address: '+***********',
          },
          serviceType: 'SMS',
          providerMetadata: [],
          inboundMatchingRetryCount: 0,
          sourceAllocatedFromRotary: false,
          dedicatedNumbers: [],
          submissionAttempts: [],
          sendingMode: 'SEND_TO_PROVIDER',
          originalBatchSize: 1,
          deliveryReceiptRequested: true,
          clientDeliveryReceiptRequested: false,
          shouldIgnoreHoldFilters: false,
          ignoreHoldFilters: [],
          payloadVersion: '4.23',
        },
      },
    },
  }).then((response) => response.status)
})

Cypress.Commands.add('updateContactStatus', (mwUrl, token, status, contactID) => {
  const subscriptionStatus = (status === 'unsubscribed')
  cy.request({ // Update the contact's subscription status
    method: 'PATCH',
    url: `${mwUrl}/middleware/contacts/v1/contacts/${contactID}`,
    timeout: 300000,
    headers: {
      accept: 'application/json',
      'content-type': 'application/json',
      Origin: mwUrl,
      Authorization: `Bearer ${token}`,
    },
    body: {
      unsubscribed: subscriptionStatus,
    },
    json: true,
  }).then((response) => response.status)
})

Cypress.Commands.add('findContactID', (mwUrl, token, phoneNum) => {
  cy.request({ // Find contact id by phone number
    method: 'GET',
    url: `${mwUrl}/middleware/contacts/v1/contacts/by-phone-number/${phoneNum}`,
    timeout: 300000,
    headers: {
      'content-type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    json: true,
  }).then((response) => {
    const contactID = response.body.id
    return contactID
  })
})

Cypress.Commands.add('sendMMS', (mwUrl, token, msgSubject, msgContent, broadcast, mediaID, phoneNum) => {
  const scheduledTime = moment().add(1, 'months').toISOString()
  cy.request({ // Send MMS to phone number
    method: 'POST',
    url: `${mwUrl}/middleware/messaging/v1/broadcasts`,
    timeout: 300000,
    headers: {
      accept: 'application/json',
      'content-type': 'application/json',
      Origin: mwUrl,
      Authorization: `Bearer ${token}`,
    },
    body: {
      recipients: {
        numbers: [
          `${phoneNum}`,
        ],
      },
      format: 'MMS',
      mms: {
        subject: `${msgSubject}`,
        message: `${msgContent}`,
        media: [
          `${mediaID}`,
        ],
      },
      name: `${broadcast}`,
      enableDeliveryReceipts: true,
      scheduled: `${scheduledTime}`,
      scheduledDisplayTimezone: 'Australia/Melbourne',
    },
    json: true,
  }).then((response) => response.status)
})

Cypress.Commands.add('setConversationStatus', (restApi, phoneNum, token, openStatus) => {
  const phoneNumWithoutPlus = phoneNum.slice(1)
  cy.request({ // Send MMS to phone number
    method: 'PUT',
    url: `${restApi}/v1/inbox/conversations/%2B${phoneNumWithoutPlus}/status`,
    timeout: 300000,
    headers: {
      'content-type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: {
      open: openStatus,
    },
    json: true,
  }).then((response) => response.status)
})

Cypress.Commands.add('getSenderId', (mwUrl, filterText, type, status, token) => {
  cy.request({
    method: 'GET',
    url: `${mwUrl}/middleware/messaging/v1/senders?type=${type.toUpperCase()}&filter=${filterText}&statuses[]=${status}`,
    timeout: 300000,
    headers: {
      'content-type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    json: true,
  }).then((response) => {
    const senderId = response.body.senders[0]
    return senderId
  })
})

Cypress.Commands.add('approveBusinessName', (mwUrl, groupId, status) => {
  const userName = 'MessageMediaTestingUser!#'
  const passWord = 'The3key3is2this4.'
  cy.request({
    method: 'POST',
    url: `${mwUrl}/middleware/internal/v1/senders/${groupId}`,
    timeout: 300000,
    headers: {
      accept: 'application/json',
      'content-type': 'application/json',
    },
    auth: {
      user: userName,
      pass: passWord,
    },
    body: {
      status: `${status}`,
    },
    json: true,
  }).then((response) => response.status)
})

Cypress.Commands.add('accessPageNameBasedCookie', (rawPageName, cookieValue, vendorUrls, vendorRegex) => {
  const auth = cookieValue !== 'no'

  // Use rawPageName = "vendor - page name" for accessing a page under a vendor
  // For example: "directsms - sign up"
  const vendorMatches = rawPageName.match(vendorRegex)
  const vendor = vendorMatches ? vendorMatches[1] : 'messagemedia'
  const pageNameWithoutVendor = rawPageName.replace(vendorRegex, '')
  const pageName = pageNameWithoutVendor === '/' ? '' : pageNameWithoutVendor
  const pageUrl = `${vendorUrls[vendor]}${pageName.replace(/ /g, '-')}`

  // Apply to remove ',' in features set
  if (cookieValue.endsWith(',')) {
    cookieValue.slice(0, cookieValue.lastIndexOf(','))
  }
  // Determine to split the ci_session value into 2 parts if the value length is more than 4000 characters
  const maxCiSessionChars = 4000
  let cookieValue1
  let cookieValue2
  if (cookieValue.includes('features:') && cookieValue.length > maxCiSessionChars) {
    cookieValue1 = seperateCookieValue(cookieValue)[0].toString()
    cookieValue2 = seperateCookieValue(cookieValue)[1].toString()
    cy.log('cookie1', cookieValue1)
    cy.log('cookie2', cookieValue2)
  }

  let cookieChange = false
  cy.log('routing')
  cy.waitUntil(() => cy.getCookie('ci_session')
    .then((cookie) => {
      let differentCookie = false
      if (cookie) {
        if (cookie.value !== cookieValue) {
          differentCookie = true
        }
      } else {
        differentCookie = true
      }
      if (auth && differentCookie) {
        cookieChange = true
        if (cookieValue.includes('features:') && cookieValue.length > maxCiSessionChars) {
          cy.waitUntil(() => cy.setCookie('ci_session', cookieValue1, { path: '/', pageUrl }))
          cy.waitUntil(() => cy.setCookie('ci_session_1', cookieValue2, { path: '/', pageUrl }))
          cy.waitUntil(() => cy.getCookie('ci_session').should('have.property', 'value', cookieValue1))
          cy.waitUntil(() => cy.getCookie('ci_session_1').should('have.property', 'value', cookieValue2))
        } else {
          cy.waitUntil(() => cy.setCookie('ci_session', cookieValue, { path: '/', pageUrl }))
          cy.waitUntil(() => cy.getCookie('ci_session').should('have.property', 'value', cookieValue))
        }
      }
      if (!auth) {
        cy.clearCookie('ci_session')
        cy.clearLocalStorage('REFRESH_TOKEN')
      }
    }), {
    errorMsg: 'Error message about get cookie', // overrides the default error message
    timeout: 10000, // waits up to 2000 ms, default to 5000
    interval: 500, // performs the check every 500 ms, default to 200
  }).then(() => {
    cy.location()
      .then((loc) => {
        if (!loc.pathname || loc.pathname !== pageUrl || cookieChange) {
          if (cookieValue !== 'no') {
            window.localStorage.setItem('REFRESH_TOKEN', JSON.stringify(cookieValue))
          }
          cy.waitUntil(() => cy.visit(pageUrl))
          cy.get('.spinner', { timeout: 20000 }).should('not.exist')
        }
      }).then(() => {
        if (auth) {
          if (pageName.includes('?beta-features=off')) {
            const pageNameWithOffFeature = pageName.replace('?beta-features=off', '')
            cy.location('pathname').should('eq', `/${pageNameWithOffFeature.replace(/ /g, '-')}`, { timeout: 40000 })
          } else if (pageName.includes('?beta-features=on')) {
            const pageNameWithOffFeature = pageName.replace('?beta-features=on', '')
            cy.location('pathname').should('eq', `/${pageNameWithOffFeature.replace(/ /g, '-')}`, { timeout: 40000 })
          } else {
            cy.location('pathname').should('eq', `/${pageName.replace(/ /g, '-')}`, { timeout: 40000 })
          }
          if (cookieValue.includes('features:') && cookieValue.length > maxCiSessionChars) {
            cy.getCookie('ci_session')
              .should('have.property', 'value', cookieValue1, { timeout: 20000 })
            cy.getCookie('ci_session_1')
              .should('have.property', 'value', cookieValue2, { timeout: 20000 })
          } else {
            cy.getCookie('ci_session')
              .should('have.property', 'value', cookieValue, { timeout: 20000 })
          }
        }
      })
  })
})
