Feature: Support Dashboard (MFE)
  This is to test support dashboard's accounts table

  Scenario: List and filter accounts
    Given I am on the "support/" page with "any" cookie and features of "support"
    And I wait 400 milliseconds
    Then The "no-data" element should "be visible"
    When I enter the "search" as "<PERSON><PERSON>{enter}"
    Then The "account-details-MMGP-39658" element should "be visible"
    And The "account-details-Parent-MMGP-66974" element should "be visible"
    When I click the "account-details-MMGP-39658" element
    And I wait 400 milliseconds
    Then I should be on the "support/MMGP-39658" page

  Scenario: Error Messages on Account Search
    Given I am on the "support/" page with "any" cookie and features of "support"
    When I click the "input-search" element
    Then The element with text "Minimum 3 characters required" should "be visible"

    When I click the "select searchOptions" element
    And I click the element with "ant-select-item-option-content" class containing "User Email (Exact)" text
    Then The element with text "This field is required" should "be visible"
    When I enter the "search" as "invalid@email{enter}"
    Then The element with text "Email not valid. Hint: <EMAIL>" should "be visible"

    # Search by User Email Domain
    Then I click the "select searchOptions" element
    Then I click the element with "ant-select-item-option-content" class containing "User Email Domain" text
    And I enter the "search" as "{enter}"
    Then The element with text "This field is required" should "be visible"

    # Search by Phone Number
    Then I click the "select searchOptions" element
    Then I click the element with "ant-select-item-option-content" class containing "User Phone Number (Exact)" text
    When I enter the "search" as "+612"
    Then The element with text "Phone number not valid. Hint: +sign, Country code, Local/Area code and Number" should "be visible"

    # Search by Billing Account ID
    Then I click the "select searchOptions" element
    Then I click the element with "ant-select-item-option-content" class containing "Billing Account ID" text
    And I enter the "search" as "{enter}"
    Then The element with text "This field is required" should "be visible"

  Scenario: Verify accounts table
    Given I am on the "support/" page with "any" cookie and features of "support"
    When I enter the "search" as "Super Unique{enter}"
    Then The row number 2 in "supportOverview" table should be disabled

  Scenario: Verify Account verification page
    Given I am on the "support/verify" page with "any" cookie and features of "support,support.verification-report"
    And The row number 1 in "verificationTable" table should have the below values:
      | column       | value             |
      | Account Name | Luettgen - Harris |
      | ACCOUNT ID   | MMGP-95565        |
    And The "verificationTable" table should contain 28 rows
    When I click the element having class "ant-pagination-options"
    And I click the element having class "ant-select-item-option-content" with text "25 items per page"
    Then The "verificationTable" table should contain 25 rows
    When I click the element having class "ant-pagination-options"
    And I click the element having class "ant-select-item-option-content" with text "10 items per page"
    Then The "verificationTable" table should contain 10 rows
    When I click the element having class "ant-pagination-options"
    And I click the element having class "ant-select-item-option-content" with text "100 items per page"
    Then The "verificationTable" table should contain 28 rows

  Scenario: Search an Account ID on the Account verification page
    Given I am on the "support/verify" page with "any" cookie and features of "support,support.verification-report"
    When I enter the "accountId" as "Flatley{enter}"
    Then The row number 1 in "verificationTable" table should have the below values:
      | column       | value                    |
      | Account Name | Flatley - Halvorson      |
      | ACCOUNT ID   | MMGP-62848               |
      | Created      | 2020-03-24T02:54:23.301Z |

    # Search By Account ID
    When I enter the "accountId" as "MMGP-98687{enter}"
    Then The row number 1 in "verificationTable" table should have the below values:
      | column       | value                    |
      | Account Name | Russel LLC               |
      | ACCOUNT ID   | MMGP-98687               |
      | Created      | 2020-03-13T23:17:23.600Z |

  Scenario: Verify the page Account Summary - Account Details
    Given I am on the "support/MMGP-10502" page with "any" cookie and features of "support,support.verification-report"
    And I wait 2000 milliseconds
    Then The "panel-heading-account-details" element should "be visible"
    And The "Account Status" element should "contain" the "UNVERIFIED"
    And The "Account Name" element should "contain" the "Hackett, Fadel and Windler"
    And The "Parent Account Name" element should "contain" the "Collier - Olson"
    And The "Timezone" element should "contain" the "Australia/Sydney"
    And The "Kibana Search" element should "contain" the "Last 7 days messages(open in kibana)"
    And The "Verification Status" element should "contain" the "VERIFIED"
    And The "Access" element should "contain" the "UNRESTRICTED"

    And The "Date Created" element should "contain" the "March 8th 2020"
    And The "Account ID" element should "contain" the "MMGP-10502"
    And The "Parent Account ID" element should "contain" the "Parent-MMGP-52078"
    And The "Vendor" element should "contain" the "MessageMedia"
    And The "Salesforce ID" element should "contain" the "001900000219vhbsho"

    And The "sub-accounts" table should include the columns "Account Name,Date Created"

  Scenario: Verify the tab Users
    Given I am on the "support/MMGP-10502" page with "any" cookie and features of "support,support.verification-report,support.user.profile,profile.update.sub-users"
    And I wait 2000 milliseconds
    Then The "tab-users" element should "be visible"
    When I click the "tab-users" element
    Then The "users" table should include the columns "id,Name,email,Accounts,Last Login"
    And The row number 1 in "users" table with horizontal scroll should have the below values:
      | column   | value                             |
      | Name     | Elwin Aufderhar                   |
      | Email    | <EMAIL>         |
      | Accounts | Leuschke, Blick and Purdy (admin) |
    And The row number 2 in "users" table with horizontal scroll should have the below values:
      | column   | value                    |
      | Name     | Henderson Kirlin         |
      | Email    | <EMAIL>     |
      | Accounts | Waelchi - Sawayn (admin) |

    # Filter by username and roles
    When I enter the "userName" as "Arvid{enter}"
    Then The row number 1 in "users" table with horizontal scroll should have the below values:
      | column   | value                 |
      | Name     | Arvid Mohr            |
      | Email    | <EMAIL>  |
      | Accounts | Funk and Sons (admin) |

    When I search in field "tree select roles" and select the "User" item from treeSelect
    Then I click the "roles" tree select
    And I enter the "userName" as "{enter}"
    Then The row number 1 in "users" table with horizontal scroll should have the below values:
      | column   | value                    |
      | Name     | Scot Brekke              |
      | Email    | <EMAIL>  |
      | Accounts | Beahan - Hegmann (admin) |
    And The row number 2 in "users" table with horizontal scroll should have the below values:
      | column   | value                      |
      | Name     | Raul Kautzer               |
      | Email    | <EMAIL> |
      | Accounts | Collins - O'Hara (user)    |

  Scenario: Verify the tab Message Details
    Given I am on the "support/MMGP-10502" page with "any" cookie and features of "support,support.verification-report"
    Then The "tab-message-details" element should "be visible"
    When I click the "tab-message-details" element
    Then The "panel-heading-messaging-details" element should "be visible"
    And The "detailed messages listing" table should include the columns "Source,Date,Recipient,Content,Message Id,Format,Direction,Status Description,To Country,Account Id,Status Code,Status"
    And The element with text "Outbound Messages" should "be visible"
    And The row number 1 in "detailed messages listing" table with horizontal scroll should have the below values:
      | column      | value                                |
      | Source      | +**********                          |
      | Recipient   | +**********                          |
      | Message ID  | a2f19b11-193c-4830-8b46-219a94580e1e |
      | Format      | MMS                                  |
      | Direction   | MT                                   |
      | To Country  | AU                                   |
      | Account ID  | MMGP-10502                           |
      | Status Code | 0                                    |
      | Status      | Delivered                            |
    And The row number 2 in "detailed messages listing" table with horizontal scroll should have the below values:
      | column      | value                                |
      | Source      | +**********                          |
      | Recipient   | +**********                          |
      | Message ID  | 615d75c7-aa52-4b56-85c6-ed4be3b8526d |
      | Format      | SMS                                  |
      | Direction   | MT                                   |
      | To Country  | AU                                   |
      | Account ID  | MMGP-10502                           |
      | Status Code | 0                                    |
      | Status      | Delivered                            |

    # Filter by Status
    When I search in field "tree-select-drop-statuses" and select the "Submitted" item from treeSelect
    Then I wait 1000 milliseconds
    Then I click the element with text "Apply Filters"
    And The row number 1 in "detailed messages listing" table with horizontal scroll should have the below values:
      | column      | value                                |
      | Source      | +**********                          |
      | Recipient   | +**********                          |
      | Message ID  | e63dc7f3-d866-484a-8259-17e5964fe607 |
      | Format      | MMS                                  |
      | Direction   | MT                                   |
      | To Country  | AU                                   |
      | Account ID  | MMGP-10502                           |
      | Status Code | 0                                    |
      | Status      | Submitted                            |
    And The row number 2 in "detailed messages listing" table with horizontal scroll should have the below values:
      | column      | value                                |
      | Source      | +**********                          |
      | Recipient   | +**********                          |
      | Message ID  | c63700c6-3294-4ced-8dfe-4718b25347b7 |
      | Format      | TTS                                  |
      | Direction   | MT                                   |
      | To Country  | AU                                   |
      | Account ID  | MMGP-10502                           |
      | Status Code | 0                                    |
      | Status      | Submitted                            |

  #Scenario: Verify the tab Numbers - Sender ID
  #  Given I am on the "support/MMGP-10502" page with "any" cookie and features of "support,support.verification-report"
  #  Then The "tab-numbers" element should "be visible"
  #  When I click the "tab-numbers" element
  #  When I click the element with text "Sender IDs"
  #  Then The "senders listing alphanumeric" table should include the columns "Id,Source Address,Status"
  #  And The row number 1 in "senders listing alphanumeric" table should have the below values:
  #    | column         | value                                |
  #    | ID             | 7431966b-a103-498e-a183-8204e6aac5bc |
  #    | Source Address | CMS7RFRBUZK                          |
  #    | Status      | Approved                             |

  Scenario: Verify the tab Numbers - User's Own Number
    Given I am on the "support/MMGP-10502" page with "any" cookie and features of "support,support.verification-report"
    When I click the "tab-numbers" element
    And I click the element with text "User's Own Number"
    Then The "senders listing international" table should include the columns "Id,Label,Source Address,Status"
    And The row number 1 in "senders listing international" table should have the below values:
      | column         | value                                |
      | ID             | 7431966b-a103-498e-a183-8204e6aac5bc |
      | Label          | intuitive Croatian Kuna              |
      | Source Address | +61491367274                         |
      | Status         | Pending                              |

  # Scenario: Verify the tab Numbers - Dedicated Number
  #   Given I am on the "support/MMGP-10994" page with "any" cookie and features of "support,support.verification-report"
  #   Then I wait 3000 milliseconds
  #   When I click the "tab-numbers" element
  #   And I click the "tab-dedicated-numbers" element
  #   Then The "dedicated numbers listing" table should include the columns "Id,Country,Source Address,Number Status,Name,Sending Capabilities,Classification,Price"
  #   And The row number 1 in "dedicated numbers listing" table with horizontal scroll should have the below values:
  #     | column               | value                                     |
  #     | Country              | US                                        |
  #     | Source Address       | +6109225148                               |
  #     | Number Status        | Pending                                   |
  #     | Name                 | Indigo dynamic focused practical vertical |
  #     | Sending Capabilities | SMS                                       |
  #     | Classification       | Silver                                    |
  #     | Price                | $25                                       |
  #   And The row number 2 in "dedicated numbers listing" table with horizontal scroll should have the below values:
  #     | column               | value                           |
  #     | Country              | US                              |
  #     | Source Address       | +6193250264                     |
  #     | Number Status        | Assigned                        |
  #     | Name                 | Ouguiya thx content thx dynamic |
  #     | Sending Capabilities | MMS                             |
  #     | Classification       | Silver                          |
  #     | Price                | $25                             |

  #   # Filter by Status
  #   When I click the "select-status" element
  #   Then I click the element having class "ant-select-item-option-content" with text "Assigned"
  #   Then I wait 1000 milliseconds
  #   And I enter the "number" as "{enter}"
  #   Then The row number 1 in "dedicated numbers listing" table with horizontal scroll should have the below values:
  #     | column               | value                           |
  #     | Country              | US                              |
  #     | Source Address       | +6193250264                     |
  #     | Number Status        | Assigned                        |
  #     | Name                 | Ouguiya thx content thx dynamic |
  #     | Sending Capabilities | MMS                             |
  #     | Classification       | Silver                          |
  #     | Price                | $25                             |
  #   And The row number 2 in "dedicated numbers listing" table with horizontal scroll should have the below values:
  #     | column               | value                                         |
  #     | Country              | NZ                                            |
  #     | Source Address       | +**********                                   |
  #     | Number Status        | Assigned                                      |
  #     | Name                 | Keyboard exploit central bedfordshire schemas |
  #     | Sending Capabilities | TTS                                           |
  #     | Classification       | Bronze                                        |
  #     | Price                | TBD                                           |

  Scenario: Verify the page Account Summary - Billing details
    Given I am on the "support/MMGP-8415" page with "any" cookie and features of "support,support.automated-broadcasts,support.verification-report,support.billing.details"
    Then The "panel-heading-billing-details" element should "be visible"
    And The "Billing Type" element should "contain" the "Prepaid Credit"
    And The "Plan" element should "contain" "Conversation"
    And The "Currency" element should "contain" "AUD"
    And The "Billing Account ID" element should "contain" the "237530 (open in zuora)"
    And The "MTD Volume" element should "contain" the "94,118"
    And The "Prepaid Balance" element should "contain" the "399"
    Then The "invoices" table should include the columns "Date,Invoice Number,Amount"

  Scenario: Verify the page Account Summary - Product details
    Given I am on the "support/MMGP-8415" page with "any" cookie and features of "support,support.automated-broadcasts,support.verification-report,support.billing.details"
    Then The "panel-heading-product-details" element should "be visible"
    And The "tab-email-address" element should "exist"
    Then The "email2sms listing" table should include the columns "Email Addresses"
    And The "tab-email-domains" element should "exist"
    When I click the "tab-email-domains" element
    Then The "email2sms domains listing" table should include the columns "Email Domains"

    Then The "tab-api-basic" element should "exist"
    Then The "api credentials basic" table should include the columns "Api Keys,Date Created"
    And The "tab-api-hmac" element should "exist"
    Then I click the "tab-api-hmac" element
    Then The "api credentials hmac" table should include the columns "Api Keys,Date Created"

    And The "tab-automated-broadcasts" element should "exist"
    Then The "automated broadcasts listing" table should include the columns "Broadcast name,Recurrence,Broadcast ID,File,Actions"

  Scenario: Verify Features & Limits
    Given I am on the "support/MMGP-8415" page with "any" cookie and features of "support,support.automated-broadcasts,support.verification-report,support.billing.details"
    And The "panel-heading-features-limits-details" element should "be visible"
    And The element with text "Hourly Sending Limit" should "be visible"
    And The element with text "Daily Sending Limit" should "be visible"
    And The element with text "Monthly Sending Limit" should "be visible"
    And The element with text "Weekly Sending Limit" should "be visible"
    And The element with text "Features Enabled" should "be visible"

  # Scenario: Verify navigation details to support dashboard
  #   Given I am on the "" page with "any" cookie and features of "support,support.verification-report"
  #   When I click the "menu-user-navigation" element
  #   And I click the "support-dashboard" element
  #   Then The "support-dashboard" element should "exist"

  Scenario: Verify account status change
    Given I am on the "support/MMGP-10502" page with "any" cookie and features of "support,support.verification-report"
    Then The "Account Status" element should "contain" the "UNVERIFIED"
    When I click the "button-accountStatus" element
    And I select "VERIFIED" element from the "metaStatus" select
    And I click the "button-modal-confirm-verification-ok" element
    Then The "Account Status" element should "contain" the "ACTIVE"

    Given I am on the "support/MMGP-10994" page with "any" cookie and features of "support,support.verification-report"
    When I click the "button-accountStatus" element
    And I select "SUSPENDED" element from the "metaStatus" select
    And I select "FRAUD" element from the "reason" select
    And I click the "button-modal-confirm-verification-ok" element
    Then The "Account Status" element should "contain" the "SUSPENDED"

    Given I am on the "support/MMGP-13931" page with "any" cookie and features of "support,support.verification-report"
    When I click the "button-accountStatus" element
    And I select "CANCELLED" element from the "metaStatus" select
    And I select "SPAMMER" element from the "reason" select
    And I click the "button-modal-confirm-verification-ok" element
    Then The "Account Status" element should "contain" the "CANCELLED"

  Scenario: Schedule and process an automated broadcast
    Given I am on the "support/broadcast/MMGP-10994" page with "any" cookie and features of "support,support.verification-report"
    When I click the "select-template" element
    And I wait 2000 milliseconds
    And I select "1 Selected template name" from the "template" select
    And I click the element having class "ant-picker-input"
    And I click the element having class "ant-picker-now-btn"
    And I wait 2000 milliseconds
    And I click the "checkbox-Monday" element
    And I enter the "sftpPath" as "contactfiles"
    And I enter the "sftpUsername" as "junsample"
    And I enter the "sftpPassword" as "test"
    And I click the "select-sftpHost" element
    And I wait 2000 milliseconds
    And I select "host-sftp-broadcasts.messagemedia.com" element from the "sftpHost" select
    And I enter the "email" as "<EMAIL>"
    And I click the "radio-customFilename" element
    And I enter the "fileName" as "nas-contacts-content.csv"
    And I enter the "phoneHeader" as "mobilenumber"
    And I enter the "accountHeader" as "accountheader"
    And I enter the "defaultHeader" as "content"
    And I click the "button-add-header" element
    And I enter the "headers[0].key" as "firstname"
    And I click the "button-add-header" element
    And I enter the "headers[1].key" as "mobilenumber"
    And I click the "button-add-header" element
    And I enter the "headers[2].key" as "lastname"
    And I click the "button-add-header" element
    And I enter the "headers[3].key" as "location"
    And I click the "button-create-broadcast" element
    And I wait 2000 milliseconds

  Scenario: Schedule and process a staggered automated broadcast
    And I wait 2000 milliseconds
    Given I am on the "support/broadcast/MMGP-10994" page with "any" cookie and features of "support,support.verification-report"
    When I click the "select-template" element
    And I wait 2000 milliseconds
    And I select "1 Selected template name" from the "template" select
    And I click the element having class "ant-picker-input"
    And I click the element having class "ant-picker-now-btn"
    And I wait 2000 milliseconds
    And I click the "checkbox-Monday" element
    And I click the "checkbox-Tuesday" element
    And I click the "checkbox-Wednesday" element
    And I click the "checkbox-Thursday" element
    And I click the "checkbox-Friday" element
    And I click the "checkbox-Saturday" element
    And I click the "checkbox-Sunday" element
    And I click the "staggerOptionsOn" element
    And I enter the "input-stagger-window" as "2"
    And I enter the "sftpPath" as "contactfiles"
    And I enter the "sftpUsername" as "junsample"
    And I enter the "sftpPassword" as "test"
    And I click the "select-sftpHost" element
    And I wait 2000 milliseconds
    And I select "host-sftp-broadcasts.messagemedia.com" element from the "sftpHost" select
    And I enter the "email" as "<EMAIL>"
    And I click the "radio-customFilename" element
    And I enter the "fileName" as "nas-contacts-content.csv"
    And I enter the "phoneHeader" as "mobilenumber"
    And I enter the "accountHeader" as "accountheader"
    And I click the "checkbox-fileHeaderRow" element
    And I click the "button-create-broadcast" element
    And I wait 2000 milliseconds

  Scenario: Validate Error Messages in add automated broadcast page
    Given I am on the "support/broadcast/MMGP-10994" page with "any" cookie and features of "support,support.verification-report"
    When I click the "select-template" element
    And I select "1 Selected template name" from the "template" select
    And I wait 2000 milliseconds
    And I click the element having class "ant-picker-input"
    And I click the element having class "ant-picker-now-btn"
    And I click the "checkbox-Monday" element
    And I click the "staggerOptionsOn" element
    And I enter the "input-stagger-window" as " "
    Then The error message "Send period is required" should be displayed on "ant-legacy-form-explain" field
    And I enter the "input-stagger-window" as "12"
    And I enter the "sftpPath" as " "
    Then The error message "path is required" should be displayed on "ant-legacy-form-explain" field
    And I enter the "sftpPath" as "jun"
    And I enter the "sftpUsername" as " "
    Then The error message "sftp username is required" should be displayed on "ant-legacy-form-explain" field
    And I enter the "sftpUsername" as "username"
    And I enter the "sftpPassword" as " "
    Then The error message "Your password is required" should be displayed on "ant-legacy-form-explain" field
    And I enter the "sftpPassword" as "sample"
    And I click the "select-sftpHost" element
    And I select "host-sftp-broadcasts.messagemedia.com" element from the "sftpHost" select
    And I enter the "email" as " "
    Then I should see an error message "At least one email is required" on the "emails" field
    And I enter the "email" as "<EMAIL>"
    And I click the "staticFilename" element
    And I enter the "fileName" as " "
    Then The error message "filename is required" should be displayed on "ant-legacy-form-explain" field
    And I enter the "fileName" as "jun.csv"
    And I enter the "phoneHeader" as " "
    Then The error message "phone header is required" should be displayed on "ant-legacy-form-explain" field
    And I enter the "phoneHeader" as "header"
    And I enter the "accountHeader" as " "
    And I enter the "defaultHeader" as " "
    Then The error message "header is required" should be displayed on "ant-legacy-form-explain" field
    And I enter the "defaultHeader" as "defaultheader"
    And I wait 2000 milliseconds

  Scenario: Edit automated broadcast
    Given I am on the "support/MMGP-10994" page with "any" cookie and features of "support,support.automated-broadcasts"
    When I click the "button-more-menu" element
    And I click the element "button-edit-automated-broadcast"
    And I wait 2000 milliseconds
    And The "button-create-broadcast" element should "be.disabled"
    And The "sftpUsername" field should "have.value" "Seth_Murphy"
    And I enter the "sftpUsername" as "Seth_Murphy_updated"
    And The "button-create-broadcast" element should "not.be.disabled"

  Scenario: Delete automated broadcast
    Given I am on the "support/MMGP-10994" page with "any" cookie and features of "support,support.automated-broadcasts"
    And I wait 2000 milliseconds
    And I click the "button-more-menu" element
    And I click the element "button-open-modal-delete-automated-broadcast"
    And The "ant-modal-body" Message box should contain "Are you sure you want to delete the automated broadcast"
    And I click "ant-btn" button
    And I wait 2000 milliseconds
    And I click the "button-more-menu" element
    And I click the element "button-open-modal-delete-automated-broadcast"
    And I click "ant-btn ant-btn-primary" button
    Then I click the "menu support dashboard v2" element
    Then I click the "user navigation menu" element
    And I click the "log-out" element

  Scenario: Verify Workflow table in Account Summary page
    Given I am on the "support/MMGP-10502" page with "any" cookie and features of "support,support.verification-report"
    And The "Workflow-Table" element should "contain" the "ID"
    And The "Workflow-Table" element should "contain" the "Name"
    And The "Workflow-Table" element should "contain" the "Status"
    And The "Workflow-Table" element should "contain" the "Type"
    When I click the "workflow-detail-link" element
    And The "update-workflow-page" element should "exist"

  Scenario: Update workflow
    Given I am on the "support/MMGP-10502/workflows/mock-workflow-id" page with "any" cookie
    And The "update-workflow-page" element should "exist"
    And The "workflow-information-panel" element should "exist"
    And The "workflow-information-panel" element should "contain" the "mock-workflow-id"
    And The "workflow-information-panel" element should "contain" the "Id"
    And The "workflow-information-panel" element should "contain" the "Name"
    And The "workflow-information-panel" element should "contain" the "Status"
    And The "workflow-information-panel" element should "contain" the "Type"
    And The "workflow-json-editor" element should "exist"
    And The "workflow-json-editor" element should "contain" the "definition"
    And I enter the "sms-text-area" textarea as "new sms message"
    And The "workflow-json-editor" element should "contain" the "new sms message"
    When I click the "button-show-confirm-workflow-modal" element
    And The "button-save-workflow" element should "exist"
    And The "button-cancel-save-workflow" element should "exist"
    When I click the "button-save-workflow" element
    Then The toast "success" should "be visible"

  Scenario: Cancel change while updating workflow
    Given I am on the "support/MMGP-10502/workflows/mock-workflow-id" page with "any" cookie
    And The "update-workflow-page" element should "exist"
    And The "workflow-information-panel" element should "exist"
    And The "workflow-information-panel" element should "contain" the "mock-workflow-id"
    And The "workflow-information-panel" element should "contain" the "Id"
    And The "workflow-information-panel" element should "contain" the "Name"
    And The "workflow-information-panel" element should "contain" the "Status"
    And The "workflow-information-panel" element should "contain" the "Type"
    And The "workflow-json-editor" element should "exist"
    And The "workflow-json-editor" element should "contain" the "definition"
    And I enter the "sms-text-area" textarea as "new sms message"
    And The "workflow-json-editor" element should "contain" the "new sms message"
    And The "changing-workflow-alert" element should "exist"
    When I click the "button-reset-workflow" element
    And The "workflow-json-editor" element should "not.contain" the "new sms message"

  Scenario: Approve Sender ID Request
    Given I am on the "support/sender-id-verification" page with "any" cookie
    Then The "senderIdVerificationTable" table should include the columns "Account Name,Account ID,Account Country,Account Created Date,Alpha Tag,Email Address,Time requested,Time Actioned,Last Modified By,Status,Action"
    When I enter the "input-accountId" as "MMGP{enter}"
    Then The row number 1 in "senderIdVerificationTable" table with horizontal scroll should have the below values:
      | column          | value                     |
      | Account Name    | Walter, Dickinson and Toy |
      | ACCOUNT ID      | MMGP-76262                |
      | Account Country | GB                        |
      | Alpha Tag       | 85rv30gik8                |
      #| Email Address   | <EMAIL> |
      | Status          | Submitted                 |
    When I click the "approve-sender-id-btn" element at row 1 of the "senderIdVerificationTable" table
    And I enter the "reason" as "Approved"
    Then I click the "button modal confirm verification ok" element
    Then The element with text "Successfully updated sender id status to APPROVE" should "be visible"
    When I enter the "input-accountId" as "MMGP{enter}"
    And I click the "reject-sender-id-btn" element at row 1 of the "senderIdVerificationTable" table
    And I enter the "reason" as "Rejected"
    Then I click the "button modal confirm verification ok" element
    Then The element with text "Successfully updated sender id status to REJECT" should "be visible"

  Scenario: Filter Sender ID by Status and Account ID
    Given I am on the "support/sender-id-verification" page with "any" cookie
    Then The element having class "ant-select-selection-item" 2 should have text "SUBMITTED"
    And I enter the "input-accountId" as "MMGP{enter}"
    Then The row number 1 in "senderIdVerificationTable" table with horizontal scroll should have the below values:
      | column | value     |
      | Status | Submitted |
    And The row number 2 in "senderIdVerificationTable" table with horizontal scroll should have the below values:
      | column | value     |
      | Status | Submitted |

    # Filter the PENDING sender ID request
    When I click the "select-statuses" element
    And I click the element with "ant-select-item-option-content" class containing "PENDING" text
    And I click the element with text "Search"
    Then The element having class "ant-select-selection-item" 2 should have text "PENDING"
    Then The row number 1 in "senderIdVerificationTable" table with horizontal scroll should have the below values:
      | column | value   |
      | STATUS | Pending |
    And The row number 2 in "senderIdVerificationTable" table with horizontal scroll should have the below values:
      | column | value   |
      | STATUS | Pending |

    # Filter the APPROVED sender ID request
    When I click the "select-statuses" element
    And I click the element with "ant-select-item-option-content" class containing "APPROVED" text
    And I click the element with text "Search"
    Then The element having class "ant-select-selection-item" 2 should have text "APPROVED"
    Then The row number 1 in "senderIdVerificationTable" table with horizontal scroll should have the below values:
      | column | value    |
      | STATUS | Approved |
    And The row number 2 in "senderIdVerificationTable" table with horizontal scroll should have the below values:
      | column | value    |
      | STATUS | Approved |

    # Filter the REJECTED sender ID request
    When I click the "select-statuses" element
    And I click the element with "ant-select-item-option-content" class containing "REJECTED" text
    And I click the element with text "Search"
    Then The element having class "ant-select-selection-item" 2 should have text "REJECTED"
    Then The row number 1 in "senderIdVerificationTable" table with horizontal scroll should have the below values:
      | column | value    |
      | STATUS | Rejected |
    And The row number 2 in "senderIdVerificationTable" table with horizontal scroll should have the below values:
      | column | value    |
      | STATUS | Rejected |

  Scenario: Approve and Reject the on-hold broadcasts
    Given I am on the "support/campaign-monitoring" page with "any" cookie
    And The row number 1 in "campaignHoldFilterTable" table with horizontal scroll should have the below values:
      | column           | value          |
      | Account Name     | Grant - Hirthe |
      | Account ID       | MMGP-17264     |
      | Account Timezone | Europe/London  |
      | Broadcast Name   | Micah ad       |
      | From             | Shared number  |
      | Recipients       | 14589          |
      | Time Actioned    | -              |
      | Status           | Held           |

    # Block sending and then reject a campaign
    When I click the "chf-block-sending-btn" element at row 1 of the "campaignHoldFilterTable" table
    Then The "button-modal-confirm-verification-ok" element should "be visible"
    Then I click the "button-modal-confirm-verification-ok" element
    And I wait 2000 milliseconds
    Then The element with text "Successfully blocked sending MMGP-17264" should "be visible"

    When I click the "chf-reject-btn" element at row 1 of the "campaignHoldFilterTable" table
    Then The element with text "Successfully updated campaign status to REJECTED" should "be visible"

    # Approve a campaign
    And I wait 3000 milliseconds
    When I click the "chf-approve-btn" element at row 1 of the "campaignHoldFilterTable" table
    Then The element with text "Successfully updated campaign status to APPROVED" should "be visible"

  Scenario: Filter the broadcasts by status and Account ID
    Given I am on the "support/campaign-monitoring" page with "any" cookie
    And I wait 2000 milliseconds
    Then The element having class "ant-select-selection-item-content" 1 should have text "HELD"
    Then The row number 1 in "campaignHoldFilterTable" table with horizontal scroll should have the below values:
      | column        | value |
      | Time Actioned | -     |
      | Status        | Held  |
    And The row number 3 in "campaignHoldFilterTable" table should have the below values:
      | column        | value |
      | Time Actioned | -     |
      | Status        | Held  |

    # Filter the Approved broadcasts
    When I search in field "tree-select-statuses" and select the "APPROVED" item from treeSelect
    And I click the element with "ant-select-tree-title" class containing "HELD" text
    And I click the element with text "Search"
    And I wait 2000 milliseconds
    Then The element having class "ant-select-selection-item-content" 1 should have text "APPROVED"
    Then The row number 1 in "campaignHoldFilterTable" table with horizontal scroll should have the below values:
      | column | value    |
      | STATUS | Approved |
    And The row number 2 in "campaignHoldFilterTable" table with horizontal scroll should have the below values:
      | column | value    |
      | STATUS | Approved |

    # Filter the Rejected broadcasts
    When I search in field "tree-select-statuses" and select the "REJECTED" item from treeSelect
    And I click the element with "ant-select-tree-title" class containing "APPROVED" text
    And I click the element with text "Search"
    And I wait 2000 milliseconds
    Then The element having class "ant-select-selection-item-content" 1 should have text "REJECTED"
    Then The row number 1 in "campaignHoldFilterTable" table with horizontal scroll should have the below values:
      | column | value    |
      | STATUS | Rejected |
    And The row number 2 in "campaignHoldFilterTable" table with horizontal scroll should have the below values:
      | column | value    |
      | STATUS | Rejected |

#    # Filter by the Account ID
#    When I search in field "tree-select-statuses" and select the "HELD" item from treeSelect
#    And I click the element with "ant-select-tree-title" class containing "APPROVED" text
#    And I enter the "input-accountId" as "MMGP-83980"
#    And I click the element with text "Search"
#    #And I wait 2000 milliseconds
#    Then The row number 1 in "campaignHoldFilterTable" table with horizontal scroll should have the below values:
#      | column           | value                |
#      | Account Name     | Kessler Group        |
#      | Account ID       | MMGP-72346           |
#      | Account Timezone | Pacific/Auckland     |
#      | Broadcast Name   | Tristian repellendus |
#      | From             | autem                |
#      | Recipients       | 13466                |
#      | STATUS           | Approved             |

  Scenario: Ecosystem search - validate value input and search button
    Given I am on the "support/ecosystems" page with "any" cookie
    When I click the "select integration" element
    And I click the element with "ant-select-item-option-content" class containing "SHOPIFY" text
    And I click the "input-searchValue" element
    Then The element with text "This field is required" should "be visible"
    When I enter the "input-searchValue" as "be"
    Then The element with text "Minimum 3 characters required" should "be visible"
    And The "button-search" element should "be.disabled"

  Scenario: Ecosystem search - search shopify id and verify pagination
    Given I am on the "support/ecosystems" page with "any" cookie
    When I click the "select integration" element
    And I click the element with "ant-select-item-option-content" class containing "SHOPIFY" text
    And I click the "input-searchValue" element
    And I enter the "input-searchValue" as "banana"
    Then The "button-search" element should "be.enabled"

    When I click the "button-search" element
    Then The "ecosystemsTable" table should contain 10 rows
    When I click the element having class "ant-pagination-item-2"
    Then The "ecosystemsTable" table should contain 10 rows
    When I click the element having class "ant-pagination-next"
    Then The "ecosystemsTable" table should contain 10 rows
    When I click the element having class "ant-pagination-options"
    And I click the element having class "ant-select-item-option-content" with text "25 items per page"
    Then The "ecosystemsTable" table should contain 25 rows
    When I click the element having class "ant-pagination-item-2"
    Then The "ecosystemsTable" table should contain 25 rows
    When I click the element having class "ant-pagination-prev"
    Then The "ecosystemsTable" table should contain 25 rows

  Scenario: Ecosystem search - search shopify id and no data found
    Given I am on the "support/ecosystems" page with "error-404" cookie
    When I click the "select integration" element
    And I click the element with "ant-select-item-option-content" class containing "SHOPIFY" text
    And I click the "input-searchValue" element
    And I enter the "input-searchValue" as "banana"
    Then The "button-search" element should "be.enabled"
    When I click the "button-search" element
    Then The "mm-table-ecosystemsTable" element should "contain" the "No accounts to show"