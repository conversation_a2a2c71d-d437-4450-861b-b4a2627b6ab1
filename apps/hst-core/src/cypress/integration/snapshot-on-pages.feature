Feature: snapshot on pages
  Visual test on pages to ensure things such as logo, theme and color, etc... are correct

  Scenario: Verify UI of Account page
    Given I am on the "support/" page with "any" cookie and features of "support"
    Then take the snapshot of window "MM-Support-Dashboard"

    When I enter the "search" as "<PERSON><PERSON>{enter}"
    Then take the snapshot of window "MM-Support-Account-Search-List"

    Given I am on the "support/MMGP-39658" page with "any" cookie and features of "support,support.verification-report"

    Then I should be on the "support/MMGP-39658" page
    Then take the snapshot of window "MM-Support-Account-Summary-Tab"

    When I click the "tab-users" element
    Then take the snapshot of window "MM-Support-Account-Users-Tab"

    When I click the "tab-numbers" element
    Then take the snapshot of window "MM-Support-Account-Numbers-Tab-Dedicated-Numbers-Subtab"

    When I click the element with text "Sender IDs"
    Then take the snapshot of window "MM-Support-Account-Numbers-Tab-SenderId-Subtab"

    When I click the element with text "User's Own Number"
    Then take the snapshot of window "MM-Support-Account-Numbers-Tab-User-Owner-Number-Subtab"

    #When I click the "tab-message-details" element
    #Then take the snapshot of window "MM-Support-Account-Message-Details-Tab"

  Scenario: Verify UI of Account Verification
    Given I am on the "support/verify" page with "any" cookie and features of "support,support.verification-report"
    Then take the snapshot of window "MM-Support-Account-Verification-Page"

  Scenario: Verify UI of Sender ID Verification
    Given I am on the "support/sender-id-verification" page with "any" cookie
    Then take the snapshot of window "MM-Support-SendID-Verification-Page"

  Scenario: Verify UI of Campaign Monitoring
    Given I am on the "support/campaign-monitoring" page with "any" cookie
    Then take the snapshot of window "MM-Support-Campaign-Monitoring-Page"  

  Scenario: Verify UI of Toll-free Numbers
    Given I am on the "support/toll-free-numbers" page with "any" cookie
    Then take the snapshot of window "MM-Support-Toll-free-Numbers-Page"  
