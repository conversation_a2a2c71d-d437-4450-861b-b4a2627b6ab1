{"datadog-client-token": "pub3eab2f4cdc216d1d1e745c918f0394ee", "datadog-application-id": "2f61fa70-c1d9-4ef7-8683-0631ce5664f2", "support-countries": ["au"], "ga-id": "UA-********-1", "ga4-id": {"production": "G-D92X8PRV76", "dev": "G-WK6FYQY49C", "qa": "G-WK6FYQY49C", "staging": "G-WK6FYQY49C", "dev-functional": "G-WK6FYQY49C"}, "brand-label": "SMSBroadcast", "short-brand-label": "SB", "registration-show": true, "registration-layout": "smsbregister", "support-link": "https://support.smsbroadcast.com.au/", "accounts-email": "<EMAIL>", "support-email2sms-email": "@e2s.smsbroadcast.com.au", "sharing-contact-groups": null, "sharing-template": "https://messagemedia3531.zendesk.com/hc/en-us/articles/*************", "unicode-support": "https://smsb.zendesk.com/hc/en-us/articles/*************", "message-status-definitions": "https://smsb.zendesk.com/hc/en-us/articles/*************", "login-validations-username": "email", "login-placeholder-username": "Email", "dev-portal": "https://developers.messagemedia.com/code/messages-api-documentation", "username-email-label": "emailOrUsername", "username-email-placeholder": "Username or Email", "username-help-text": null, "login-layout": "smsbLogin", "auth-error-message-invalid-password": "Your email or password is not valid. Please try again.", "username-field-name": "username", "password-field-name": "password", "show-status-report-help": true, "report-hlr-help": true, "status-report-chart-size": 16, "show-upgrade-paragraph": false, "browser-notification-icon": "<EMAIL>", "favicon": "SMSBroadcast_favicon.png", "developer-invitation": true, "favicon-badge": "SMSBroadcast_favicon-badge.png", "mms": "messaging.send.mms", "mms-tab-label": "Attachment message (MMS)", "paypal-client-sdk": {"production": "https://www.paypal.com/sdk/js?client-id=ARYjDTwrtllwLkH1-GIYP2FkSfAlGEWxQWKM00CTaEh8bt9k2h8n-sFuuB6U5e1K7WZk6O-33Qk4CeeC&disable-funding=credit,card,venmo&vault=true&locale=en_AU", "staging": "https://www.paypal.com/sdk/js?client-id=AV1257PzurnPOz2QEHpFrnR9FuCgfkzg2DKNUhxlrYvBKWJQftuKwdpCeranKFPkTfT4W-1jGivVo-Xl&disable-funding=credit,card,venmo&vault=true&locale=en_AU&debug=true", "dev": "https://www.paypal.com/sdk/js?client-id=AV1257PzurnPOz2QEHpFrnR9FuCgfkzg2DKNUhxlrYvBKWJQftuKwdpCeranKFPkTfT4W-1jGivVo-Xl&disable-funding=credit,card,venmo&vault=true&locale=en_AU&debug=true", "dev-functional": "https://www.paypal.com/sdk/js?client-id=AV1257PzurnPOz2QEHpFrnR9FuCgfkzg2DKNUhxlrYvBKWJQftuKwdpCeranKFPkTfT4W-1jGivVo-Xl&disable-funding=credit,card,venmo&vault=true&locale=en_AU&debug=true"}, "article-set-up-calendar-for-calendar-automation": "https://messagemedia3531.zendesk.com/hc/en-us/articles/*************", "article-creating-new-calendar-automation": "https://messagemedia3531.zendesk.com/hc/en-us/articles/*************", "article-how-to-create-mlp": "https://messagemedia3531.zendesk.com/hc/en-us/articles/*************", "hlr-lookup-url": "https://support.smsbroadcast.com.au", "email-configured": "This email is used for invoice, low balance and credit expiry notifications.", "payment-contacts-info": [{"title": "Phone", "displayNumber": "1300 667 405", "number": "1300667405"}], "billing-lookup-url": "https://www.smsbroadcast.com.au/pricing", "delivery-receipts": "https://smsb.zendesk.com/hc/en-us/articles/*************/", "hubspot-uninstall-url": "https://messagemedia3531.zendesk.com/hc/en-us/articles/*************", "dedicated-pricing-gst": "All prices are inclusive of GST", "upsell-message": "Hello!\nWant to send a test message? Send yourself a test message and then reply to see replies on the Received page.\n\nClick the 'Send now' button to try it out.", "connect-hubspot-header": "Select an account to connect to HubSpot.", "default-geo-ip-answer": {"country_code": "AU", "country_name": "Australia", "countryCode": "AU", "countryName": "Australia", "time_zone": "", "phone": "+61"}, "privacy-policy-url": "https://www.smsbroadcast.com.au/privacy", "terms-of-service-url": "https://www.smsbroadcast.com.au/terms-and-conditions/", "contact-url": "https://support.smsbroadcast.com.au/", "sale-contact-url": "<EMAIL>", "contact-phone-number": "1300 667 405", "example-email": "<EMAIL>", "logo": {"src": "SMSbroadcast.svg", "alt": "SMS Broadcast logo"}, "logo-login": {"src": "logo-smsb.jpg", "alt": "SMS Broadcast logo"}, "logo-sidebar-bottom": {"src": "logo-sidebar-bottom-mm.png", "alt": "MessageMedia logo"}, "logo-login-powered-by": {"src": "logo-login-powered-by-mm.svg", "alt": "MessageMedia logo"}, "login-register-navigator": "login-register", "password-validation": {"rule": "^(?=.*[\\d])(?=.*[a-zA-Z]).{8,50}$", "hint": "Must contain at least 8 characters using both numbers and letters", "hasDynamicHint": true}, "dedicated-number-support-countries": ["AU"], "dedicated-number-prices": {"AU": {"BRONZE": "$179"}}, "dedicated-number-recurring": "year", "sso-login": true, "activecampaign-setup-url": "https://messagemedia3531.zendesk.com/hc/en-us/categories/4413568178831-ActiveCampaign-App-Support", "zendesk": {"show": true, "source": "https://static.zdassets.com/ekr/snippet.js?key=b3d0b58b-cc88-4540-b718-b5ab4fdd3631", "id": "ze-snippet"}, "broadcast-title": "Campaign", "messages": "Campaigns", "message-name": "Campaign name", "broadcast-reports": "Campaign Reports", "message-name-updated": "Campaign name has been updated.", "search-message-names": "Search campaign names", "new-message": "New Campaign", "name-your-message": "Name your campaign to track and manage outcomes.", "no-broadcast": "You do not have any campaign, once created your campaign will show here.", "campaign-monitoring": {"production": true, "dev": true, "qa": true, "staging": true, "dev-functional": true}, "sender-id-verification": {"production": true, "dev": true, "qa": true, "staging": true, "dev-functional": false}, "link-purchasing-dedicated-number": "", "sender-address-auth": true, "azure-login": true, "ams-settings": true, "ams-api-settings": true, "social-channels": true, "support-log": {"production": true, "dev": true, "qa": true, "staging": true, "dev-functional": false}, "roles-permissions": {"production": true, "dev": true, "qa": true, "staging": true, "dev-functional": false}, "new-sender-id-app": true, "new-account-search": true}