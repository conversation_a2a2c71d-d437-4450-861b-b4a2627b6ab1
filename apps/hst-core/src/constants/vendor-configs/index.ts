import commonConfigs from './common.json'
import mmConfigs from './mm.json'
import sincheuConfigs from './sincheu.json'
import vodanzConfigs from './vodanz.json'
import directSMSConfigs from './directsms.json'
import streetdataConfigs from './streetdata.json'
import twodegreesConfigs from './2degrees.json'
import bulletinConfigs from './bulletin.json'
import smsbConfigs from './smsb.json'
import wholesaleConfigs from './wholesale.json'
import messagenetConfigs from './messagenet.json'
import mobipostConfigs from './mobipost.json'
import smscentralConfigs from './smscentral.json'
import etxtConfigs from './etxt.json'
import tpgConfigs from './tpg.json'
import simpletextingConfigs from './simpletexting.json'
import { getData } from '../../helpers/storage'

type VendorConfigKey = keyof typeof configs;

const configs = {
  common: commonConfigs,
  mm: mmConfigs,
  sincheu: sincheuConfigs,
  vodanz: vodanzConfigs,
  directSMS: directSMSConfigs,
  streetdata: streetdataConfigs,
  '2degrees': twodegreesConfigs,
  bulletin: bulletinConfigs,
  smsb: smsbConfigs,
  wholesale: wholesaleConfigs,
  messagenet: messagenetConfigs,
  mobipost: mobipostConfigs,
  smscentral: smscentralConfigs,
  etxt: etxtConfigs,
  tpgtelecom: tpgConfigs,
  simpletexting: simpletextingConfigs,
} as const

export default () => {
  const partnerConfigs = getData('partnerConfigs')

  return {
    ...configs[VENDOR_LABEL as VendorConfigKey],
    ...(partnerConfigs ?? {}),
  }
}

// TODO:
// Fix ga id
// Replace browser-notification-icon bulletin with notification size
// Replace favicon-badge bulletin with badge size
// Check article-set-up-calendar-for-calendar-automation streetdata url
// Check article-creating-new-calendar-automation streetdata url
// Check article-how-to-create-mlp streetdata url
// Update hlr-lookup-url streetdata url
// Update hubspot-uninstall-url of the rest vendors
// Remove dedicated-number-prices after MW implementates the dedicated numbers endpoint for each vendor
