{"datadog-client-token": "pub3eab2f4cdc216d1d1e745c918f0394ee", "datadog-application-id": "2f61fa70-c1d9-4ef7-8683-0631ce5664f2", "support-countries": ["au"], "ga-id": {"production": "UA-*********-1", "dev": "UA-*********-4", "qa": "UA-*********-4", "staging": "UA-*********-3", "dev-functional": "UA-*********-4"}, "ga4-id": {"production": "G-DPNPPE0EDW", "dev": "G-WK6FYQY49C", "qa": "G-WK6FYQY49C", "staging": "G-WK6FYQY49C", "dev-functional": "G-WK6FYQY49C"}, "brand-label": "TPGTelecom", "short-brand-label": "tpg", "registration-show": false, "registration-layout": "mmregister", "support-link": "http://support.messaging.tpgtelecom.com.au/", "accounts-email": "<EMAIL>", "support-email": "<EMAIL>", "support-email2sms-email": "@e2s.messaging.tpgtelecom.com.au", "sharing-contact-groups": "https://messagemedia3531.zendesk.com/hc/en-us/articles/*************", "sharing-template": "https://messagemedia3531.zendesk.com/hc/en-us/articles/*************", "unicode-support": "https://messagemedia3531.zendesk.com/hc/en-us/articles/*************", "message-status-definitions": "https://messagemedia3531.zendesk.com/hc/en-us/articles/*************", "login-validations-username": "email", "login-placeholder-username": "Email", "dev-portal": "https://support.messaging.tpgtelecom.com.au/hc/en-us/categories/*************-Developer-Guides", "username-email-label": "emailOrUsername", "username-email-placeholder": "Email or username", "username-help-text": null, "login-layout": "tpgtelecomLogin", "auth-error-message-invalid-password": "Your email, username or password is not valid. Please try again.", "username-field-name": "username", "password-field-name": "password", "show-status-report-help": true, "report-hlr-help": false, "status-report-chart-size": 16, "show-upgrade-paragraph": false, "browser-notification-icon": "<EMAIL>", "favicon": "TPGTelecom_favicon.png", "developer-invitation": true, "favicon-badge": "TPGTelecom_favicon-badge.png", "mms": "messaging.send.mms", "mms-tab-label": "Picture message (MMS)", "paypal-client-sdk": null, "article-set-up-calendar-for-calendar-automation": "https://messagemedia3531.zendesk.com/hc/en-us/articles/*************", "article-creating-new-calendar-automation": "https://messagemedia3531.zendesk.com/hc/en-us/articles/*************", "article-how-to-create-mlp": "https://messagemedia3531.zendesk.com/hc/en-us/articles/*************", "article-sso-single-sign-on-configuration": "https://messagemedia3531.zendesk.com/hc/en-us/articles/*************", "article-idp-setup-for-sso-azure": "https://messagemedia3531.zendesk.com/hc/en-us/articles/*************", "hlr-lookup-url": "https://messaging.tpgtelecom.com.au/products/lookups", "email-configured": "This email is used for invoice, low balance and credit expiry notifications.", "payment-contacts-info": [{"title": "Phone Australia", "displayNumber": "1800 959 291", "number": "1800959291"}], "billing-lookup-url": "https://messaging.tpgtelecom.com.au/us/pricing", "delivery-receipts": "https://messagemedia3531.zendesk.com/hc/en-us/articles/*************/", "hubspot-uninstall-url": "https://messagemedia3531.zendesk.com/hc/en-us/articles/*************", "dedicated-pricing-gst": "All prices are exclusive of GST", "upsell-message": "Hello!\nWant to send a test message? Send yourself a test message and then reply to see replies in the Inbox.\n\nClick the 'Send now' button to try it out.", "connect-hubspot-header": "Select an account to connect to HubSpot.", "default-geo-ip-answer": {"country_code": "AU", "country_name": "Australia", "countryCode": "AU", "countryName": "Australia", "time_zone": "", "phone": "+61"}, "privacy-policy-url": {"AU": "https://messaging.tpgtelecom.com.au/legal/privacy-policy", "default": "https://messaging.tpgtelecom.com.au/legal/privacy-policy"}, "terms-of-service-url": {"AU": "https://messaging.tpgtelecom.com.au/legal/terms-of-service", "default": "https://messaging.tpgtelecom.com.au/legal/terms-of-service"}, "contact-url": {"AU": "https://support.messaging.tpgtelecom.com.au/hc/en-us/articles/*************", "default": "https://support.messaging.tpgtelecom.com.au/hc/en-us/articles/*************"}, "sale-contact-url": {"AU": "<EMAIL>", "default": "<EMAIL>"}, "contact-phone-number": {"AU": "1800 959 291", "default": "1844 912 2350"}, "example-email": {"AU": "<EMAIL>", "default": "<EMAIL>"}, "logo": {"src": "logo-tpgtelecom.svg", "alt": "TPGTelecom logo"}, "logo-login": {"src": "logo-login-tpg.jpg", "alt": "TPGTelecom logo"}, "logo-sidebar-bottom": {"src": "logo-sidebar-bottom-tpg.png", "alt": "TPGTelecom logo"}, "logo-login-powered-by": {"src": "logo-login-powered-by-tpg.svg", "alt": "TPGTelecom logo"}, "login-register-navigator": "login-only", "password-validation": {"rule": "^(?=.*[\\d])(?=.*[a-zA-Z]).{8,50}$", "hint": "Must contain at least 8 characters using both numbers and letters", "hasDynamicHint": true}, "dedicated-number-support-countries": ["ALL_COUNTRIES", "AU", "GB", "US"], "dedicated-number-prices": {"US_DEFAULT": {"GOLD": "$50", "SILVER": "$25", "BRONZE": "$25"}, "US_TFN": {"GOLD": "$50", "SILVER": "$50", "BRONZE": "$50"}, "US_MMS": {"GOLD": "$50", "SILVER": "$50", "BRONZE": "$50"}, "AU": {"GOLD": "$99", "SILVER": "$59", "BRONZE": "$29"}, "GB": {"GOLD": "£50", "SILVER": "£15", "BRONZE": "£10"}}, "dedicated-number-recurring": "month", "sso-login": true, "activecampaign-setup-url": "https://messagemedia3531.zendesk.com/hc/en-us/categories/4413568178831-ActiveCampaign-App-Support", "zendesk": {"show": true, "source": "https://static.zdassets.com/ekr/snippet.js?key=bc043523-b8ba-429a-af95-2fbfb0e88b64", "id": "ze-snippet"}, "broadcast-title": "Campaign", "messages": "Campaigns", "message-name": "Campaign name", "broadcast-reports": "Campaign Reports", "message-name-updated": "Campaign name has been updated.", "search-message-names": "Search campaign names", "new-message": "New Campaign", "name-your-message": "Name your campaign to track and manage outcomes.", "no-broadcast": "You do not have any campaign, once created your campaign will show here.", "campaign-monitoring": {"production": false, "dev": false, "qa": false, "staging": false, "dev-functional": false}, "sender-id-verification": {"production": true, "dev": true, "qa": true, "staging": true, "dev-functional": false}, "link-purchasing-dedicated-number": "", "sender-address-auth": true, "azure-login": true, "ams-settings": false, "ams-api-settings": false, "social-channels": false, "support-log": {"production": true, "dev": true, "qa": true, "staging": true, "dev-functional": false}, "roles-permissions": {"production": false, "dev": false, "qa": false, "staging": false, "dev-functional": false}, "new-sender-id-app": true, "new-account-search": false}