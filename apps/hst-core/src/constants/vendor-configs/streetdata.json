{"datadog-client-token": "pub3533f00f7c5b2496cdad4740b2623402", "datadog-application-id": "139533e9-b6e8-493b-b9a9-4d2c6dfb41ae", "support-countries": ["au", "us", "ca", "nz", "gb"], "ga-id": "UA-*********-2", "ga4-id": {"production": "G-X6KSFKBQZK", "dev": "G-WK6FYQY49C", "qa": "G-WK6FYQY49C", "staging": "G-WK6FYQY49C", "dev-functional": "G-WK6FYQY49C"}, "brand-label": "Streetdata", "short-brand-label": "SD", "registration-show": false, "registration-layout": "mmregister", "support-link": "https://support.streetdata.com.au/", "accounts-email": "<EMAIL>", "support-email2sms-email": "@e2s.streetdata.com.au", "sharing-contact-groups": "https://streetdata.zendesk.com/hc/en-us/articles/*************", "sharing-template": "https://messagemedia3531.zendesk.com/hc/en-us/articles/*************", "unicode-support": "https://streetdata.zendesk.com/hc/en-us/articles/*************", "message-status-definitions": "https://streetdata.zendesk.com/hc/en-us/articles/*************", "login-validations-username": "email", "login-placeholder-username": "Email", "dev-portal": "https://developers.messagemedia.com/code/messages-api-documentation", "username-email-label": "emailOrUsername", "username-email-placeholder": "Email or username", "username-help-text": "Please enter your email address or username (without leading 0's)", "login-layout": "streetdataLogin", "auth-error-message-invalid-password": "Your email, username or password is not valid. Please try again.", "username-field-name": "lf[nluser]", "password-field-name": "lf[nlpass]", "show-status-report-help": true, "report-hlr-help": true, "status-report-chart-size": 16, "show-upgrade-paragraph": false, "browser-notification-icon": "<EMAIL>", "favicon": "StreetData_favicon.png", "developer-invitation": true, "favicon-badge": "StreetData_favicon-badge.png", "mms": "messaging.send.mms", "mms-tab-label": "Attachment message (MMS)", "paypal-client-sdk": null, "article-set-up-calendar-for-calendar-automation": "https://support.streetdata.com.au/articles/46323-📄-setting-up-your-calendar-for-calendar-automation", "article-creating-new-calendar-automation": "https://support.streetdata.com.au/articles/46320-📄-creating-a-new-calendar-automation", "article-how-to-create-mlp": "https://support.streetdata.com.au/articles/46299-📄-sending-your-first-mobile-landing-page", "hlr-lookup-url": "https://support.streetdata.com.au", "email-configured": "This email is used for invoice, low balance and credit expiry notifications.", "payment-contacts-info": [{"title": "Phone Australia", "displayNumber": "1800 155 228", "number": "1800155228"}, {"title": "Phone UK", "displayNumber": "0808 234 8246", "number": "***********"}], "billing-lookup-url": "http://www2.streetdata.com.au/pricing.php", "delivery-receipts": "https://support.streetdata.com.au/articles/57114-delivery-receipts/", "hubspot-uninstall-url": "https://messagemedia3531.zendesk.com/hc/en-us/articles/*************", "dedicated-pricing-gst": "All prices are exclusive of GST", "upsell-message": "Hello!\nWant to send a test message? Send yourself a test message and then reply to see replies in the Inbox.\n\nClick the 'Send now' button to try it out.", "connect-hubspot-header": "Select an account to connect to HubSpot.", "default-geo-ip-answer": {"country_code": "AU", "country_name": "Australia", "countryCode": "AU", "countryName": "Australia", "time_zone": "", "phone": "+61"}, "privacy-policy-url": {"NZ": "https://www.messagemedia.com.au/legal/privacy-policy", "AU": "https://www.messagemedia.com.au/legal/privacy-policy", "GB": "https://www.messagemedia.co.uk/legal/privacy-policy", "ETZ": "https://www.messagemedia.co.uk/legal/privacy-policy", "default": "https://www.messagemedia.com/legal/privacy-policy"}, "terms-of-service-url": {"NZ": "https://www.messagemedia.com.au/legal/terms-of-service", "AU": "https://www.messagemedia.com.au/legal/terms-of-service", "GB": "https://www.messagemedia.co.uk/legal/terms-of-service", "ETZ": "https://www.messagemedia.co.uk/legal/terms-of-service", "default": "https://www.messagemedia.com/legal/terms-of-service"}, "contact-url": "https://support.streetdata.com.au/", "sale-contact-url": "<EMAIL>", "contact-phone-number": "+61 (0)2 9130 1997", "example-email": "<EMAIL>", "logo": {"src": "streetdata.svg", "alt": "Streetdata logo"}, "logo-login": {"src": "logo-login-streetdata.png", "alt": "Streetdata logo"}, "logo-sidebar-bottom": {"src": "logo-sidebar-bottom-mm.png", "alt": "MessageMedia logo"}, "logo-login-powered-by": {"src": "logo-login-powered-by-mm.svg", "alt": "MessageMedia logo"}, "login-register-navigator": "login-only", "password-validation": {"rule": "^(?=.*[\\d])(?=.*[a-zA-Z]).{8,50}$", "hint": "Must contain at least 8 characters using both numbers and letters", "hasDynamicHint": true}, "dedicated-number-support-countries": ["ALL_COUNTRIES", "AU", "GB", "US"], "dedicated-number-prices": {"US_DEFAULT": {"GOLD": "$50", "SILVER": "$25", "BRONZE": "$25"}, "US_TFN": {"GOLD": "$50", "SILVER": "$50", "BRONZE": "$50"}, "US_MMS": {"GOLD": "$50", "SILVER": "$50", "BRONZE": "$50"}, "AU": {"GOLD": "$99", "SILVER": "$59", "BRONZE": "$29"}, "GB": {"GOLD": "£50", "SILVER": "£15", "BRONZE": "£10"}}, "dedicated-number-recurring": "month", "sso-login": true, "activecampaign-setup-url": "https://messagemedia3531.zendesk.com/hc/en-us/categories/4413568178831-ActiveCampaign-App-Support", "zendesk": {"show": false}, "broadcast-title": "Campaign", "messages": "Campaigns", "message-name": "Campaign name", "broadcast-reports": "Campaign Reports", "message-name-updated": "Campaign name has been updated.", "search-message-names": "Search campaign names", "new-message": "New Campaign", "name-your-message": "Name your campaign to track and manage outcomes.", "no-broadcast": "You do not have any campaign, once created your campaign will show here.", "campaign-monitoring": {"production": false, "dev": false, "qa": false, "staging": false, "dev-functional": false}, "sender-id-verification": {"production": true, "dev": true, "qa": true, "staging": true, "dev-functional": false}, "link-purchasing-dedicated-number": "", "sender-address-auth": true, "azure-login": true, "ams-settings": true, "ams-api-settings": true, "social-channels": false, "support-log": {"production": true, "dev": true, "qa": true, "staging": true, "dev-functional": false}, "roles-permissions": {"production": true, "dev": true, "qa": true, "staging": true, "dev-functional": false}, "new-sender-id-app": true, "new-account-search": true}