{"datadog-client-token": "pub3eab2f4cdc216d1d1e745c918f0394ee", "datadog-application-id": "2f61fa70-c1d9-4ef7-8683-0631ce5664f2", "support-countries": ["au"], "ga-id": "UA-********-1", "ga4-id": {"production": "G-6P3V64PJ6K", "dev": "G-WK6FYQY49C", "qa": "G-WK6FYQY49C", "staging": "G-WK6FYQY49C", "dev-functional": "G-WK6FYQY49C"}, "brand-label": "SMSCentral", "short-brand-label": "SMSC", "registration-show": true, "registration-layout": "mmregister", "support-link": "https://support.smscentral.com.au/", "accounts-email": "<EMAIL>", "support-email2sms-email": "@sms.smscentral.com.au", "sharing-contact-groups": "https://smscentral.helpsite.com/articles/61613-sharing-contact-groups", "sharing-template": "https://messagemedia3531.zendesk.com/hc/en-us/articles/*************", "unicode-support": "https://smscentral.helpsite.com/articles/61592-how-many-characters-can-i-send-in-an-sms", "message-status-definitions": "https://smscentral.helpsite.com/articles/61587-message-status-definitions", "login-validations-username": "email", "login-placeholder-username": "Email", "dev-portal": "https://www.smscentral.com.au/sms-api/rest-api/", "username-email-label": "emailOrUsername", "username-email-placeholder": "Email or username", "username-help-text": null, "login-layout": "smscentralLogin", "auth-error-message-invalid-password": "Your email, username or password is not valid. Please try again.", "username-field-name": "username", "password-field-name": "password", "show-status-report-help": true, "report-hlr-help": true, "status-report-chart-size": 16, "show-upgrade-paragraph": false, "browser-notification-icon": "<EMAIL>", "favicon": "smscentral_favicon.png", "developer-invitation": true, "favicon-badge": "smscentral_favicon-badge.png", "mms": "messaging.send.mms", "mms-tab-label": "Attachment message (MMS)", "paypal-client-sdk": null, "article-set-up-calendar-for-calendar-automation": "https://messagemedia3531.zendesk.com/hc/en-us/articles/4413627051919", "article-creating-new-calendar-automation": "https://messagemedia3531.zendesk.com/hc/en-us/articles/4413627051919", "article-how-to-create-mlp": "https://messagemedia3531.zendesk.com/hc/en-us/articles/4413569388303", "hlr-lookup-url": "https://support.smscentral.com.au", "email-configured": "This email is used for invoice.", "payment-contacts-info": [{"title": "Phone", "displayNumber": "1300 971 093", "number": "1300971093"}], "billing-lookup-url": "https://messagemedia.com/us/pricing", "delivery-receipts": "https://smscentral.helpsite.com/articles/61590-delivery-receipts", "hubspot-uninstall-url": "https://messagemedia3531.zendesk.com/hc/en-us/articles/*************", "dedicated-pricing-gst": "All prices are exclusive of GST", "upsell-message": "Hello!\nWant to send a test message? Send yourself a test message and then reply to see replies in the Inbox.\n\nClick the 'Send now' button to try it out.", "connect-hubspot-header": "Select an account to connect to HubSpot.", "default-geo-ip-answer": {"country_code": "AU", "country_name": "Australia", "countryCode": "AU", "countryName": "Australia", "time_zone": "", "phone": "+61"}, "privacy-policy-url": "https://www.smscentral.com.au/privacy", "terms-of-service-url": "https://www.smscentral.com.au/terms-and-conditions/", "contact-url": "https://www.smscentral.com.au/contact-us/", "sale-contact-url": "<EMAIL>", "contact-phone-number": "1300 971 093", "example-email": "<EMAIL>", "logo": {"src": "logo-smscentral.svg", "alt": "SMSCentral logo"}, "logo-login": {"src": "logo-smscentral.svg", "alt": "SMSCentral logo"}, "logo-sidebar-bottom": {"src": "logo-sidebar-bottom-smscentral.svg", "alt": "SMSCentral logo"}, "logo-login-powered-by": {"src": "logo-login-powered-by-smscentral.svg", "alt": "SMSCentral logo"}, "login-register-navigator": "login-only", "password-validation": {"rule": "**********************************", "hint": "Must contain at least 8 characters using both numbers and letters", "hasDynamicHint": true}, "dedicated-number-support-countries": ["ALL_COUNTRIES", "AU", "GB", "US"], "dedicated-number-prices": {"US_DEFAULT": {"GOLD": "$50", "SILVER": "$25", "BRONZE": "$25"}, "US_TFN": {"GOLD": "$50", "SILVER": "$50", "BRONZE": "$50"}, "US_MMS": {"GOLD": "$50", "SILVER": "$50", "BRONZE": "$50"}, "AU": {"GOLD": "$99", "SILVER": "$59", "BRONZE": "$29"}, "GB": {"GOLD": "£50", "SILVER": "£15", "BRONZE": "£10"}}, "dedicated-number-recurring": "month", "sso-login": true, "activecampaign-setup-url": "https://messagemedia3531.zendesk.com/hc/en-us/categories/4413568178831-ActiveCampaign-App-Support", "zendesk": {"show": false}, "broadcast-title": "Campaign", "messages": "Campaigns", "message-name": "Campaign name", "broadcast-reports": "Campaign Reports", "message-name-updated": "Campaign name has been updated.", "search-message-names": "Search campaign names", "new-message": "New Campaign", "name-your-message": "Name your campaign to track and manage outcomes.", "no-broadcast": "You do not have any campaign, once created your campaign will show here.", "campaign-monitoring": {"production": false, "dev": false, "qa": false, "staging": false, "dev-functional": false}, "sender-id-verification": {"production": true, "dev": true, "qa": true, "staging": true, "dev-functional": false}, "link-purchasing-dedicated-number": "", "sender-address-auth": true, "azure-login": true, "ams-settings": true, "ams-api-settings": true, "social-channels": false, "support-log": {"production": true, "dev": true, "qa": true, "staging": true, "dev-functional": false}, "roles-permissions": {"production": true, "dev": true, "qa": true, "staging": true, "dev-functional": false}, "new-sender-id-app": true, "new-account-search": true}