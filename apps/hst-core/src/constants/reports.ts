const GROUPBY = {
  DAY: 'DAY',
  DESTINATION_ADDRESS: 'DESTINATION_ADDRESS',
  DESTINATION_ADDRESS_COUNTRY: 'DESTINATION_ADDRESS_COUNTRY',
  HOUR: 'HOUR',
  METADATA_KEY: 'METADATA_KEY',
  METADATA_VALUE: 'METADATA_VALUE',
  MINUTE: 'MINUTE',
  MONTH: 'MONTH',
  SOURCE_ADDRESS: 'SOURCE_ADDRESS',
  SOURCE_ADDRESS_COUNTRY: 'SOURCE_ADDRESS_COUNTRY',
  STATUS: 'STATUS',
  STATUS_CODE: 'STATUS_CODE',
  YEAR: 'YEAR',
  ACCOUNT: 'ACCOUNT',
}

export const getReportStatusOptions = ({ deliveredExcl, readExcl }: { deliveredExcl: boolean; readExcl: boolean }) => [
  ...deliveredExcl ? [] : [{
    value: 'DELIVERED',
    key: '0',
    label: 'Delivered',
  }],
  {
    value: 'SUBMITTED',
    key: '2-0',
    label: 'Submitted',
  },
  {
    value: 'FAILED,REJECTED,EXPIRED',
    key: '1',
    label: 'Undelivered',
    children: [
      {
        value: 'FAILED',
        key: '0-0',
        label: 'Failed',
      },
      {
        value: 'REJECTED',
        key: '0-1',
        label: 'Rejected',
      },
      {
        value: 'EXPIRED',
        key: '0-2',
        label: 'Expired',
      },
    ],
  },
  {
    value: 'SUBMITTED,ENROUTE,HELD,UNDEFINED',
    key: '2',
    label: 'Pending',
    children: [
      {
        value: 'ENROUTE',
        key: '2-1',
        label: 'Enroute',
      },
      {
        value: 'HELD',
        key: '2-2',
        label: 'Held',
      },
      {
        value: 'UNDEFINED',
        key: '2-3',
        label: 'Undefined',
      },
    ],
  },
  ...readExcl ? [] : [{
    value: 'READ',
    key: '3',
    label: 'Read',
  }],
]

export default GROUPBY
