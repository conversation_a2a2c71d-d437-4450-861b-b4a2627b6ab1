import * as StorageHelpers from '../helpers/storage/storage';
import {
  ETXT,
  MESSAGEMEDIA,
  MULTITXT,
  SIMPLE_TEXTING,
  SMSCENTRAL,
  TPGTELECOM,
  TWO_DEGREES,
} from './app-config';
import {
  isBrandMM,
  isBrandSMSB,
  isBrandSMSC,
  isBrandSimpleTexting,
  isBrandTPG,
  isNZCarriers,
  labelLookup,
  messageLabelLookup,
} from './vendor-labels';

jest.mock('../helpers/endpoint', () => ({
  BUILD_ENV: null,
}));

describe('vendor-label', () => {
  it('labelLookup brand-label', () => {
    expect(labelLookup('brand-label')).toEqual({
      dev: 'Sinch MessageMedia',
      'dev-functional': 'Sinch MessageMedia',
      production: 'Sinch MessageMedia',
      staging: 'Sinch MessageMedia',
      qa: 'Sinch MessageMedia',
    });
  });

  it('labelLookup unknown property', () => {
    expect(labelLookup('unknown')).toBeFalsy();
  });

  describe('mm vendor', () => {
    it('isBrandMM', () => {
      expect(isBrandMM()).toEqual(true);
    });

    it('isBrandTPG', () => {
      expect(isBrandTPG()).toEqual(false);
    });

    it('isBrandSMSC', () => {
      expect(isBrandSMSC()).toEqual(false);
    });

    it('isBrandSMSB', () => {
      expect(isBrandSMSB()).toEqual(false);
    });

    it('isBrandSimpleTexting', () => {
      expect(isBrandSimpleTexting()).toEqual(false);
    });
  });

  describe('smscentral vendor', () => {
    beforeAll(() => {
      global.VENDOR_LABEL = TPGTELECOM;
    });
    afterAll(() => {
      global.VENDOR_LABEL = MESSAGEMEDIA;
    });

    it('isBrandMM', () => {
      expect(isBrandMM()).toEqual(false);
    });

    it('isBrandTPG', () => {
      expect(isBrandTPG()).toEqual(true);
    });

    it('isBrandSMSC', () => {
      expect(isBrandSMSC()).toEqual(false);
    });

    it('isBrandSimpleTexting', () => {
      expect(isBrandSimpleTexting()).toEqual(false);
    });
  });

  describe('TPGTelecom vendor', () => {
    beforeAll(() => {
      global.VENDOR_LABEL = SMSCENTRAL;
    });
    afterAll(() => {
      global.VENDOR_LABEL = MESSAGEMEDIA;
    });

    it('isBrandMM', () => {
      expect(isBrandMM()).toEqual(false);
    });

    it('isBrandTPG', () => {
      expect(isBrandTPG()).toEqual(false);
    });

    it('isBrandSMSC', () => {
      expect(isBrandSMSC()).toEqual(true);
    });
  });

  describe('SIMPLE_TEXTING vendor', () => {
    beforeAll(() => {
      global.VENDOR_LABEL = SIMPLE_TEXTING;
    });
    afterAll(() => {
      global.VENDOR_LABEL = MESSAGEMEDIA;
    });

    it('isBrandSimpleTexting', () => {
      expect(isBrandSimpleTexting()).toEqual(true);
    });

    it('isBrandTPG', () => {
      expect(isBrandTPG()).toEqual(false);
    });

    it('isBrandSMSC', () => {
      expect(isBrandSMSC()).toEqual(false);
    });
  });

  describe('NZ Carriers vendor', () => {
    it('should return true if vendor is ETXT', () => {
      global.VENDOR_LABEL = ETXT;
      expect(isNZCarriers()).toEqual(true);
    });
    it('should return true if vendor is MULTITXT', () => {
      global.VENDOR_LABEL = MULTITXT;
      expect(isNZCarriers()).toEqual(true);
    });
    it('should return true if vendor is TWO_DEGREES', () => {
      global.VENDOR_LABEL = TWO_DEGREES;
      expect(isNZCarriers()).toEqual(true);
    });
  });

  describe('label lookup with partner', () => {
    it('lookup mlp support link without partner', () => {
      expect(labelLookup('article-how-to-create-mlp')).toEqual(
        'https://messagemedia3531.zendesk.com/hc/en-us/articles/4413569388303'
      );
    });

    it('lookup mlp support link with partner', () => {
      // eslint-disable-next-line no-import-assign, import/namespace
      StorageHelpers.getData = jest.fn(() => ({
        'article-how-to-create-mlp': 'mlp-support-link-of-partner',
      }));
      expect(labelLookup('article-how-to-create-mlp')).toEqual(
        'https://messagemedia3531.zendesk.com/hc/en-us/articles/4413569388303'
      );
    });
  });

  describe('message label lookup', () => {
    beforeAll(() => {
      global.VENDOR_LABEL = MESSAGEMEDIA;
    });

    it('return value if type is string', () => {
      expect(messageLabelLookup('message-name-updated')).toEqual(
        'Campaign name has been updated.'
      );
    });

    it('return dedicated country value', () => {
      expect(messageLabelLookup('new-message', 'NZ')).toEqual('New Message');
    });

    it('return default value', () => {
      expect(messageLabelLookup('messages', 'AU')).toEqual('Campaigns');
    });
  });
});
