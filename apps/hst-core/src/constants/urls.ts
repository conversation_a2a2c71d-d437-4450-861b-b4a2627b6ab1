import Endpoint from '../helpers/endpoint'

export const ACCOUNT_URLS = {
  END_POINT: `${Endpoint.API_URL}/iam/v1/account`,
}

export const USAGE_TRACKING = {
  END_POINT: `${Endpoint.API_URL}/v1/usage-tracking`,
}

export const WORKFLOW_API = 'wfp-bff/workflows'
export const WORKFLOW_CALENDARS_API = `${WORKFLOW_API}/calendars`
export const WORKFLOW_API_V2 = 'wfp-bff/automations'
export const WORKFLOW_AUTOMATIONS_API = 'wfp-bff/workflow-automations'
export const WORKFLOW_API_V3 = 'wfp-bff/v3/automations'

export const SUPPORT_URLS = {
  PORTAL: '',
  INDEX: '/',
  VERIFY_ACCOUNT: '/verify',
  CAMPAIGN_MONITORING: '/campaign-monitoring',
  SENDER_ID_VERIFICATION: '/sender-id-verification',
  DETAIL: '/:accountId',
  AUTOMATED_BROADCAST: '/broadcast',
  NEW_CUSTOMER: '/new-customer',
  TOLL_FREE_NUMBERS: '/toll-free-numbers',
  ECOSYSTEMS_SUPPORT: '/ecosystems',
}

export const PARTNERS_URLS = {
  INDEX: '/customer-accounts',
  ADD: '/customer-accounts/add',
}

export const SUB_ACCOUNTS_URLS = {
  INDEX: '/sub-accounts',
}

export const UPGRADE_URLS = {
  BILLING_POSTPAID_PLAN_SELECTION: '/billing/postpaid-plan-selection',
  PARTNER_PLAN_SELECTION: '/plan-selection',
}

export const MESSAGES = {
  NEW_MESSAGE: '/campaigns/new-campaign',
  NEW_MESSAGE_POST_SIGNUP: '/messages/post-signup',
  BROADCASTS: '/campaigns/sent',
  SCHEDULED: '/campaigns/scheduled',
  DRAFTS: '/campaigns/drafts',
  RECEIVED: '/campaigns/received',
  CAMPAIGNS_SENT: '/campaigns/sent',
}

export const AUTOMATION = '/automations'

export const REPORTING_V2_URLS = {
  GET_REPORT: `${Endpoint.NEXTGEN_API_URL}/v2-preview/reporting/messages/detail`,
  POST_ASYNC_DETAIL_REPORT: `${Endpoint.NEXTGEN_API_URL}/v2-preview/reporting/messages/async/detail`,
  POST_ASYNC_DETAIL_REPORT_FIELDS: `${Endpoint.NEXTGEN_API_URL}/v2-preview/reporting/messages/async/detail/fields`,
  POST_ASYNC_SUMMARY_REPORT: `${Endpoint.NEXTGEN_API_URL}/v2-preview/reporting/messages/async/summary`,
  GET_SUMMARY_REPORT: `${Endpoint.NEXTGEN_API_URL}/v2-preview/reporting/messages/summary`,
  GET_INSIGHTS_REPORT: `${Endpoint.NEXTGEN_API_URL}/v2-preview/reporting/messages/insights`,
  GET_METADATA_KEY_LIST: `${Endpoint.NEXTGEN_API_URL}/v2-preview/reporting/messages/metakeys`,
  GET_SCHEDULED_REPORTS: `${Endpoint.NEXTGEN_API_URL}/v2-preview/reporting/scheduled`,
  POST_SCHEDULED_DETAIL_REPORTS: `${Endpoint.NEXTGEN_API_URL}/v2-preview/reporting/detail/scheduled`,
  PUT_SCHEDULED_DETAIL_REPORTS: `${Endpoint.NEXTGEN_API_URL}/v2-preview/reporting/detail/scheduled`,
  DELETE_SCHEDULED_DETAIL_REPORTS: `${Endpoint.NEXTGEN_API_URL}/v2-preview/reporting/scheduled`,
  POST_SCHEDULED_SUMMARY_REPORTS: `${Endpoint.NEXTGEN_API_URL}/v2-preview/reporting/summary/scheduled`,
  PUT_SCHEDULED_SUMMARY_REPORTS: `${Endpoint.NEXTGEN_API_URL}/v2-preview/reporting/summary/scheduled`,
}

export const INVOICE = {
  INVOICES_HISTORY: '/billing/invoices',
  INVOICES_HISTORY_CHECKOUT: '/invoice-history/checkout',
}
