import {
  ACTIVE,
  SUSPENDED,
  CANC<PERSON>LED,
} from './account-verification'

export const ACCOUNT_NAME_AND_ID_SEARCH = {
  name: 'accountNameAndIdSearch',
  value: 'Account Name/ Account ID',
}

export const USER_EMAIL_EXACT_SEARCH = {
  name: 'userEmailExactSearch',
  value: 'User Email (Exact)',
}

export const USER_EMAIL_WILDCARD_SEARCH = {
  name: 'userEmailDomainSearch',
  value: 'User Email Domain',
}

export const USER_PHONE_NUMBER_EXACT_SEARCH = {
  name: 'userPhoneNumberSearch',
  value: 'User Phone Number (Exact)',
}

export const DEFAULT_SEARCH = {
  name: 'defaultSearch',
  value: 'Account Name/ Account ID/ Username',
}
export const EMAIL2SMS_SEARCH = {
  name: 'email2SmsSearch',
  value: 'Email Address/ Domain',
}
export const CARRIER_BILLING_NUMBER_SEARCH = {
  name: 'carrierBillingNumberSearch',
  value: 'Carrier Billing Number',
}

export const BILLING_ACCOUNT_ID_SEARCH = {
  name: 'billingAccountIDSearch',
  value: 'Billing Account ID',
}

export const STATUSES = [
  { name: 'Active', value: ACTIVE },
  { name: 'Suspended', value: SUSPENDED },
  { name: 'Cancelled', value: CANCELLED },
]

export const SEARCH_OPTIONS = [ACCOUNT_NAME_AND_ID_SEARCH, USER_EMAIL_EXACT_SEARCH, USER_EMAIL_WILDCARD_SEARCH, USER_PHONE_NUMBER_EXACT_SEARCH, BILLING_ACCOUNT_ID_SEARCH, DEFAULT_SEARCH, EMAIL2SMS_SEARCH, CARRIER_BILLING_NUMBER_SEARCH]
