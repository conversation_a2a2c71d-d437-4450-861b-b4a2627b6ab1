import LeadConversion from '../assets/img/icons/lead-conversion.svg'
import PersonalisedSMS from '../assets/img/icons/personalised-sms.svg'
import WorkSmarter from '../assets/img/icons/work-smarter.svg'

/* eslint-disable import/prefer-default-export */
export const PARTNER_WHITELIST_DOMAIN = [
  'preview-notify-now.macquarieview.com',
  'notify-now.macquarieview.com',
  'now.macquarieview.com',
  'notify-now.dev.messagemedia.com',
  'notify-now.qa.messagemedia.com',
  'notify-now.stg.messagemedia.com',
  'notify-now.messagemedia.com',
  'notify-now.localhost:8008',
  'notify-now.localhost',
]

export const PARTNER_CONFIG_KEYS_MAPPING = {
  sharingContactGroups: 'sharing-contact-groups',
  sharingTemplate: 'sharing-template',
  unicodeSupport: 'unicode-support',
  messageStatusDefinitions: 'message-status-definitions',
  setUpCalendarForCalendarAutomation: 'article-set-up-calendar-for-calendar-automation',
  creatingNewCalendarAutomation: 'article-creating-new-calendar-automation',
  creatingFirstMobileLandingPage: 'article-how-to-create-mlp',
  ssoConfiguration: 'article-sso-single-sign-on-configuration',
  ssoIdentityProviderSetup: 'article-idp-setup-for-sso-azure',
  deliveryReceipts: 'delivery-receipts',
  hubspotUninstall: 'hubspot-uninstall-url',
  allowLoginUsingSso: 'sso-login',
  documentationUrl: 'support-link',
}

export const E2S_BASE = {
  'support-email2sms-email': '@E2S_DOMAIN',
  'example-email': {
    NZ: '02XXXXXXXXX@E2S_DOMAIN, +642XXXXXXXXX@E2S_DOMAIN',
    AU: '611900654321@E2S_DOMAIN',
    US: '12025550183@E2S_DOMAIN',
    GB: '07983085566@E2S_DOMAIN, +447983085566@E2S_DOMAIN',
    CA: '12025550183@E2S_DOMAIN',
    default: '61491570156@E2S_DOMAIN',
  },
}

export const PARTNER_SLUGS = {
  HUBSPOT: 'hubspot',
  BIGCOMMERCE: 'bigcommerce',
  SALESFORCE: 'salesforce',
  ZOHO: 'zoho-crm',
  SHOPIFY: 'shopify',
}

// Subheading is the content after the partner description
export const PARTNER_SUBHEADING = {
  BIGCOMMERCE: 'With approximately 90% of text messages being read within 90 seconds of sending SMS is a powerful addition to any communication workflow.',
  HUBSPOT: 'With approximately 90% of text messages being read within 90 seconds of sending SMS is a powerful addition to any communication workflow.',
  SALESFORCE: 'Take control of your customer messaging directly from Salesforce.',
  ZOHO: 'Take control of your customer messaging directly from Zoho - CRM.',
}

// Add bullet points here of every partner
export const PARTNER_BULLET_POINTS = [
  {
    slug: PARTNER_SLUGS.SALESFORCE,
    Icon: PersonalisedSMS,
    title: 'Personalised SMS communication',
    detail: 'Send a bulk SMS to a list view or campaign members. Use merge fields to personalise communication to each recipient.',
  }, {
    slug: PARTNER_SLUGS.SALESFORCE,
    Icon: LeadConversion,
    title: 'Drive lead conversion',
    detail: 'Send SMS messages to your prospects and customers from within Salesforce. Your message history is stored against each record in one place.',
  }, {
    slug: PARTNER_SLUGS.SALESFORCE,
    Icon: WorkSmarter,
    title: 'Work smarter and faster',
    detail: 'Use SMS templates, keywords and workflows to reduce manual workload and communicate to your customers faster.',
  },
  {
    slug: PARTNER_SLUGS.ZOHO,
    Icon: PersonalisedSMS,
    title: 'Personalised SMS communication',
    detail: 'Send a bulk SMS to a list view or campaign members. Use merge fields to personalise communication to each recipient.',
  }, {
    slug: PARTNER_SLUGS.ZOHO,
    Icon: LeadConversion,
    title: 'Drive lead conversion',
    detail: 'Send SMS messages to your prospects and customers from within Zoho CRM. Your message history is stored against each record in one place.',
  }, {
    slug: PARTNER_SLUGS.ZOHO,
    Icon: WorkSmarter,
    title: 'Work smarter and faster',
    detail: 'Use SMS templates, keywords and workflows to reduce manual workload and communicate to your customers faster.',
  },
]

export const PARTNER_ECOSYSTEM = {
  [PARTNER_SLUGS.HUBSPOT]: 'HubSpot',
  [PARTNER_SLUGS.BIGCOMMERCE]: 'BigCommerce',
  [PARTNER_SLUGS.SALESFORCE]: 'Salesforce CRM',
  [PARTNER_SLUGS.ZOHO]: 'Zoho',
  shopify: 'Shopify',
  netsuite: 'NetSuite',
  activecampaign: 'Active Campaign',
  adobe: 'Adobe',
  microsoftdynamics: 'Microsoft Dynamics',
  magento: 'Magento',
  oracleerpcloud: 'Oracle ERP Cloud',
  acumatica: 'Acumatica',
  braze: 'Braze',
  breezyhr: 'BreezyHR',
  emarsys: 'Emarsys',
  epic: 'Epic',
  epicor: 'Epicor',
  freshdesk: 'Freshdesk',
  genesyscloud: 'Genesys Cloud',
  infor: 'Infor',
  marketo: 'Marketo',
  mautic: 'Mautic',
  myob: 'MYOB',
  prontosoftware: 'Pronto Software',
  quickbooks: 'QuickBooks',
  sage: 'Sage',
  salesforcemarketing: 'Salesforce Marketing',
  sap: 'SAP',
  servicenow: 'ServiceNow',
  serviceworks: 'ServiceWorks',
  squarespace: 'SquareSpace',
  sugarcrm: 'SugarCRM',
  woocommerce: 'WooCommerce',
  xero: 'Xero',
  klaviyo: 'Klaviyo',
  mailchimp: 'Mailchimp',
}
