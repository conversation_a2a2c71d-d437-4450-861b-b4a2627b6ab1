export const SEGMENT_TRACK_EVENTS = {
  // old events
  dedicatedNumberPurchased: 'dedicated-number-purchased',
  newMessage: 'new-message',
  quickSend: 'quick-send',
  sendTest: 'send-test',
  inboxMessage: 'inbox-message',
  postpaidConversion: 'postpaid-conversion',
  integrationPage: 'integrations-page',
  goCardlessConfigured: 'go-cardless-configured',
  selfServeCheckboxChecked: 'self-serve-checkbox-checked',
  callableNumberPurchased: 'callable-number-purchased',
  partnerCustomerGeneratedApiKey: 'partner-customer-generated-api-key',
  'multi-channel': 'multi-channel',
  'mobile-landing-page-added': 'mobile-landing-page-added',
  'tfn-verification': 'tfn-verification',
}

// new events - First_letter_uppercase naming convention
export const SEGMENT_TRACK_EVENTS_V2 = {
  Account_close_requested: 'Account_close_requested',
  Account_social_sending_updated: 'Account_social_sending_updated',
  Account_default_sender_id_updated: 'Account_default_sender_id_updated',
  Account_sms_limits_updated: 'Account_sms_limits_updated',
  Account_default_template_updated: 'Account_default_template_updated',
  Alpha_tag_requested: 'Alpha_tag_requested',
  Billing_contact_updated: 'Billing_contact_updated',
  Payment_method_updated: 'Payment_method_updated',
  Invoice_downloaded: 'Invoice_downloaded',
  Account_allow_duplicate_contacts: 'Account_allow_duplicate_contacts',
  Account_delivery_receipts_updated: 'Account_delivery_receipts_updated',
  Ecosystem_sign_up: 'Ecosystem_sign_up',
  Sso_settings_updated: 'Sso_settings_updated',
  Plan_type_clicked: 'Plan_type_clicked',
  Term_clicked: 'Term_clicked',
  Dedicated_number_verified: 'Dedicated_number_verified',
  Plan_clicked: 'Plan_clicked',
  Plan_confirmation_submitted: 'Plan_confirmation_submitted',
  Support_search_integration: 'Support_search_integration',
  Click_disconnect_integration: 'Click_disconnect_integration',
  Click_oneportal_page: 'Click_oneportal_page',
  Click_account_search: 'Click_account_search',
  Action_sender_id: 'Action_sender_id',
}

export const ANONYMOUS_SEGMENT_TRACK_EVENTS = {
  Registration_form_submitted: 'Registration_form_submitted',
  Registration_attempt_blocked: 'Registration_attempt_blocked',
  Pin_verified: 'Pin_verified',
  Hub_login: 'Hub_login',
}
