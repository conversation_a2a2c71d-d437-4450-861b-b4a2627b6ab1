import Endpoint from '../helpers/endpoint';

export const KIBANA_SEARCH_PARAMS = (accountId: string) =>
  `${Endpoint.KIBANA_DASHBOARD_URL}/app/kibana#/discover?_g=(refreshInterval:(pause:!t,value:0),time:(from:now-7d,mode:quick,to:now))&_a=(columns:!(message,content,deliveryReportStatus,gatewayAccountId,providerName,billingUnits),index:da68cc10-5216-11e9-8274-7d9568c9dee5,interval:auto,query:(language:lucene,query:%22${accountId}%22),sort:!('@timestamp',desc))`;
export const ZUORA_SEARCH_PARAMS = (zuoraId: string) =>
  `${Endpoint.ZUORA_URL}CustomerAccount.do?method=view&id=${zuoraId}`;

export default KIBANA_SEARCH_PARAMS;
