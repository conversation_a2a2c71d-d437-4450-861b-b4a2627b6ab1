import _get from 'lodash/get'
import Endpoint from '../helpers/endpoint'
import {
  ETXT,
  MESSAGEMEDIA,
  MULTITXT,
  SIMPLE_TEXTING,
  SINCH_EU,
  SMSB,
  SMSCENTRAL,
  TPGTELECOM,
  TWO_DEGREES,
} from './app-config'
import getVendorConfigs from './vendor-configs/index'

type VendorConfig = ReturnType<typeof getVendorConfigs>
type VendorConfigKey = keyof VendorConfig

type ConfigWithDefault = { default: string }

export const labelLookup = (val: VendorConfigKey) => {
  const vendorConfigs = getVendorConfigs()

  return _get(vendorConfigs[val], Endpoint?.BUILD_ENV, vendorConfigs[val])
}

export const isBrandMM = () => VENDOR_LABEL === MESSAGEMEDIA

export const isBrandTPG = () => VENDOR_LABEL === TPGTELECOM

export const isBrandSMSC = () => VENDOR_LABEL === SMSCENTRAL

export const isBrandSMSB = () => VENDOR_LABEL === SMSB

export const isBrandSimpleTexting = () => VENDOR_LABEL === SIMPLE_TEXTING

export const isNZCarriers = () =>
  VENDOR_LABEL === ETXT
  || VENDOR_LABEL === MULTITXT
  || VENDOR_LABEL === TWO_DEGREES
  || VENDOR_LABEL === TPGTELECOM

export const messageLabelLookup = (val: VendorConfigKey, countryCode = '') => {
  const vendorConfigs = getVendorConfigs()
  const config = vendorConfigs[val]

  if (!config) return ''
  if (typeof config === 'string') return config
  if (!config || typeof config !== 'object' || !('default' in config)) return ''

  return _get(config, countryCode, (config as ConfigWithDefault).default)
}

export const isBrandSE = () => VENDOR_LABEL === SINCH_EU

export default labelLookup
