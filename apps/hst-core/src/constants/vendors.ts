const vendors = [
  {
    name: '2Degrees',
    domains: [
      'localhost:3015/2degrees',
      'syd.support.saas.sinch.com/2degrees',
      'syd.stg.support.saas.sinch.com/2degrees',
      'syd.qa.support.saas.sinch.com/2degrees',
    ],
    themeName: '2degrees.css',
    title: '2Degrees',
  },
  {
    name: 'DirectSMS',
    domains: [
      'localhost:3015/directsms',
      'syd.support.saas.sinch.com/directsms',
      'syd.stg.support.saas.sinch.com/directsms',
      'syd.qa.support.saas.sinch.com/directsms',
    ],
    themeName: 'directSMS.css',
    title: 'directSMS',
    label: 'directSMS',
  },
  {
    name: 'MessageMedia',
    themeName: 'mm.css',
    domains: [
      'localhost:3015/messagemedia',
      'syd.support.saas.sinch.com/messagemedia',
      'syd.stg.support.saas.sinch.com/messagemedia',
      'syd.qa.support.saas.sinch.com/messagemedia',
    ],
    title: 'MessageMedia Hub',
    label: 'mm',
  },
  {
    name: 'VodaNZ',
    themeName: 'vodanz.css',
    domains: [
      'localhost:3015/onenz',
      'syd.support.saas.sinch.com/onenz',
      'syd.stg.support.saas.sinch.com/onenz',
      'syd.qa.support.saas.sinch.com/onenz',
    ],
    title: 'One MultiTXT',
  },
  {
    name: 'MessageNet',
    domains: [
      'localhost:3015/messagenet',
      'syd.support.saas.sinch.com/messagenet',
      'syd.stg.support.saas.sinch.com/messagenet',
      'syd.qa.support.saas.sinch.com/messagenet',
    ],
    themeName: 'messagenet.css',
    title: 'MessageNet',
  },
  {
    name: 'SMSBroadcast',
    domains: [
      'localhost:3015/smsbroadcast',
      'syd.support.saas.sinch.com/smsbroadcast',
      'syd.stg.support.saas.sinch.com/smsbroadcast',
      'syd.qa.support.saas.sinch.com/smsbroadcast',
    ],
    themeName: 'smsb.css',
    title: 'SMS Broadcast',
    label: 'smsb',
  },
  {
    name: 'Streetdata',
    domains: [
      'localhost:3015/streetdata',
      'syd.support.saas.sinch.com/streetdata',
      'syd.stg.support.saas.sinch.com/streetdata',
      'syd.qa.support.saas.sinch.com/streetdata',
    ],
    themeName: 'streetdata.css',
    title: 'Streetdata',
  },
  {
    name: 'WholesaleSMS',
    domains: [
      'localhost:3015/wholesalesms',
      'syd.support.saas.sinch.com/wholesalesms',
      'syd.stg.support.saas.sinch.com/wholesalesms',
      'syd.qa.support.saas.sinch.com/wholesalesms',
    ],
    themeName: 'wholesale.css',
    title: 'WholesaleSMS',
    label: 'wholesale',
  },
  {
    name: 'Mobipost',
    domains: [
      'localhost:3015/mobipost',
      'syd.support.saas.sinch.com/mobipost',
      'syd.stg.support.saas.sinch.com/mobipost',
      'syd.qa.support.saas.sinch.com/mobipost',
    ],
    themeName: 'mobipost.css',
    title: 'Mobipost',
  },
  {
    name: 'SMSCentral',
    domains: [
      'localhost:3015/smscentral',
      'syd.support.saas.sinch.com/smscentral',
      'syd.stg.support.saas.sinch.com/smscentral',
      'syd.qa.support.saas.sinch.com/smscentral',
    ],
    themeName: 'smscentral.css',
    title: 'SMSCentral',
  },
  {
    name: 'bulletin',
    domains: [
      'localhost:3015/bulletin',
      'syd.support.saas.sinch.com/bulletin',
      'syd.stg.support.saas.sinch.com/bulletin',
      'syd.qa.support.saas.sinch.com/bulletin',
    ],
    themeName: 'bulletin.css',
    title: 'Bulletin',
  },
  {
    name: 'eTXT',
    domains: [
      'localhost:3015/etxt',
      'syd.support.saas.sinch.com/etxt',
      'syd.stg.support.saas.sinch.com/etxt',
      'syd.qa.support.saas.sinch.com/etxt',
    ],
    themeName: 'etxt.css',
    title: 'eTXT',
  },
  {
    name: 'TPGTelecom',
    domains: [
      'localhost:3015/tpgtelecom',
      'syd.support.saas.sinch.com/tpgtelecom',
      'syd.stg.support.saas.sinch.com/tpgtelecom',
      'syd.qa.support.saas.sinch.com/tpgtelecom',
    ],
    themeName: 'tpg.css',
    title: 'TPG Telecom Messaging Hub',
  },
  // pdx airport
  {
    name: 'SimpleTexting',
    domains: [
      'localhost:3015/simpletexting',
      'dub.support.saas.sinch.com/simpletexting',
      'dub.stg.support.saas.sinch.com/simpletexting',
      'dub.qa.support.saas.sinch.com/simpletexting',
    ],
    themeName: 'simpletexting.css',
    title: 'Simple Texting Messaging Hub',
    region: 'eu',
  },
  // dub airport
  {
    name: 'SinchEU',
    domains: [
      'localhost:3015/sincheu',
      'dub.support.saas.sinch.com/sincheu',
      'dub.stg.support.saas.sinch.com/sincheu',
      'dub.qa.support.saas.sinch.com/sincheu',
    ],
    themeName: 'mm.css',
    title: 'Sinch EU Hub',
    region: 'eu',
  },
  // sincheu common
  {
    name: 'SinchEU',
    themeName: 'mm.css',
    domains: [
      'dub.support.saas.sinch.com',
      'dub.stg.support.saas.sinch.com',
      'dub.qa.support.saas.sinch.com',
    ],
    title: 'Sinch EU Hub',
    label: 'sincheu',
  },
  // default
  {
    // todo: replace after update vendorID for common page
    name: 'MessageMedia',
    themeName: 'mm.css',
    domains: [
      'localhost:3015',
      'syd.support.saas.sinch.com',
      'syd.stg.support.saas.sinch.com',
      'syd.qa.support.saas.sinch.com',
    ],
    title: 'Common Page',
    label: 'common',
  },
];

const mmVendor = {
  name: 'MessageMedia',
  themeName: 'mm.css',
  domains: [
    'localhost:3015/messagemedia',
    'syd.support.saas.sinch.com/messagemedia',
    'syd.stg.support.saas.sinch.com/messagemedia',
    'syd.qa.support.saas.sinch.com/messagemedia',
  ],
  title: 'MessageMedia Hub',
  label: 'mm',
};

const euVendor = {
  name: 'SinchEU',
  domains: [
    'localhost:3015/sincheu',
    'dub.support.saas.sinch.com/sincheu',
    'dub.stg.support.saas.sinch.com/sincheu',
    'dub.qa.support.saas.sinch.com/sincheu',
  ],
  themeName: 'mm.css',
  title: 'Sinch EU Hub',
  region: 'eu',
};

export const hostname = window && window.location && window.location.hostname;
export const origin = window && window.location && window.location.origin;
const href = window && window.location && window.location.href;
const defaultFallbackVendor = IS_EU_VENDOR ? euVendor : mmVendor;
export const curVendor =
  vendors.find((vendor) =>
    vendor.domains.some((domain) => href.includes(domain))
  ) || defaultFallbackVendor;
const defaultFallbackVendorId = IS_EU_VENDOR ? 'SinchEU' : 'MessageMedia';
export const vendorId = curVendor?.name || defaultFallbackVendorId;
export default vendors;
