export const BROADCASTS = 'broadcasts'
export const TEMPLATES = 'templates'
export const TEMPLATES_CREATE = 'templates.create'
export const TEMPLATES_DELETE = 'templates.delete'
export const TEMPLATES_UPDATE = 'templates.update'
export const TEMPLATES_RESTRICTED = 'templates.restricted'
export const TEMPLATES_RESTRICTED_CREATE = 'templates.restricted.create'
export const TEMPLATES_RESTRICTED_UPDATE = 'templates.restricted.update'
export const TEMPLATES_RESTRICTED_DELETE = 'templates.restricted.delete'
export const MESSAGING_SEND = 'messaging.send'
export const MESSAGING_SEND_URL = 'messaging.send.url'
export const MESSAGING_SEND_PERIOD = 'messaging.send.period'
export const MESSAGING_SEND_SCHEDULED = 'messaging.send.scheduled'
export const MESSAGING_SEND_BULK = 'messaging.send.bulk'
export const MESSAGING_SEND_SCHEDULED_RECURRING = 'messaging.send.scheduled.recurring'
export const MESSAGING_SEND_MMS = 'messaging.send.mms'
export const MESSAGING_SEND_MMS_NZ = 'messaging.send.mms-nz'
export const MESSAGING_SEND_MMS_IMAGE = 'messaging.send.mms.image'
export const MESSAGING_SEND_MMS_AUDIO = 'messaging.send.mms.audio'
export const MESSAGING_SEND_MMS_VIDEO = 'messaging.send.mms.video'
export const MESSAGING_SEND_MMS_DOCUMENT = 'messaging.send.mms.document'
export const MESSAGING_SEND_OPT_OUT_LINK = 'messaging.send.opt-out.link'
export const MESSAGING_SEND_CUSTOM_MESSAGE = 'messaging.send.custom-message'
export const MESSAGING_SEND_DELIVERY_RECEIPT = 'messaging.send.delivery-receipt'
export const MESSAGING_CONTENT_TRANSLATE = 'messaging.content.translate'
export const MESSAGING_SOURCE_NUMBER = 'messaging.source.number'
export const MESSAGING_SOURCE_ALPHA = 'messaging.source.alpha'
export const MESSAGING_SOURCE_ALPHA_REQUEST = 'messaging.source.alpha.request'
export const MESSAGING_SOURCE_MY_NUMBER = 'messaging.source.my-number'
export const MESSAGING_SOURCE_MY_NUMBER_REQUEST = 'messaging.source.my-number.request'
export const MESSAGING_SEND_RICH_LINK = 'messaging.send.rich-link'
export const MESSAGING_LANDING_PAGE = 'messaging.landing-pages'
export const MESSAGING_LANDING_PAGE_SEND = 'messaging.landing-pages.send'
export const MESSAGING_SOURCE_NUMBER_REQUEST = 'messaging.source.number.request'
export const MESSAGING_LANDING_PAGE_TEMPLATE_LIST = 'messaging.landing-pages.templates.list'
export const MESSAGING_SEND_VERIFY = 'messaging.send.prompt.phoneNumberVerification'
export const MESSAGING_SETTINGS = 'messaging.settings'
export const MESSAGING_SETTINGS_UPDATE = 'messaging.settings.update'
export const MESSAGING_DEFAULT_SENDER = 'messaging.source.default'
export const MESSAGING_SETTINGS_DEFAULT_SENDER = 'messaging.source.default.update'
export const CONTACTS = 'contacts'
export const CONTACTS_CREATE = 'contacts.create'
export const CONTACTS_UPDATE = 'contacts.update'
export const CONTACTS_DELETE = 'contacts.delete'
export const CONTACTS_RESUBSCRIBE = 'contacts.resubscribe'
export const CONTACTS_UNSUBSCRIBE = 'contacts.unsubscribe'
export const CONTACTS_EXPORT = 'contacts.export'
export const CONTACTS_IMPORT = 'contacts.import'
export const CONTACTS_GROUPS = 'contacts.groups'
export const CONTACTS_GROUPS_CREATE = 'contacts.groups.create'
export const CONTACTS_GROUPS_UPDATE = 'contacts.groups.update'
export const CONTACTS_GROUPS_DELETE = 'contacts.groups.delete'
export const CONTACTS_GROUP_SHARING = 'contacts.group-sharing'
export const CONTACTS_GROUP_SHARING_VIEW = 'contacts.group-sharing.view'
export const CONTACTS_GROUP_SHARING_ADD_GROUP = 'contacts.group-sharing.add-group'
export const CONTACTS_GROUP_SHARING_REMOVE_GROUP = 'contacts.group-sharing.remove-group'
export const CONTACTS_SETTINGS_UPDATE = 'contacts.settings.update'
export const REPORTING = 'reporting'
export const REPORTING_REPORT_DETAILED = 'reporting.report.detailed'
export const REPORTING_REPORT_CAMPAIGN = 'reporting.report.campaign'
export const REPORTING_EXPORT_TO_EMAIL = 'reporting.export-to-email'
export const REPORTING_REPORT_USAGE = 'reporting.report.usage'
export const REPORTING_REPORT_DELIVERY_STATUS = 'reporting.report.delivery-status'
export const REPORTING_REPORT_SUBACCOUNTS = 'reporting.report.sub-accounts'
export const REPORTING_REPORT_SCHEDULED = 'reporting.report.schedule'
export const REPORTING_REPORT_SCHEDULED_CREATE = 'reporting.report.schedule.create'
export const REPORTING_REPORT_SCHEDULED_UPDATE = 'reporting.report.schedule.update'
export const REPORTING_REPORT_SCHEDULED_DELETE = 'reporting.report.schedule.delete'
export const REPORTING_REPORT_USERS = 'reporting.report.users'
export const REPORTING_REPORT_USERS_API_USERS = 'reporting.report.users.api-users'
export const REPORTING_REPORT_FILTER_METADATA = 'reporting.report.filter.metadata'
export const REPORTING_ACTIVITY_LOG = 'reporting.activity-log'
export const REPORTING_REPORT_SHORT_URLS = 'reporting.report.short-urls'
export const REPORTING_EXPORT_TO_EMAIL_VERIFY = 'reporting.export-to-email.prompt.phoneNumberVerification'
export const REPORTING_REPORT_SCHEDULED_CREATE_VERIFY = 'reporting.report.schedule.create.prompt.phoneNumberVerification'
export const REPORTING_REPORT_PARTNER_CUSTOMERS = 'reporting.report.partner-customers'
export const REPORTING_ABANDONEDCART = 'reporting.abandoned-cart'
export const REPORTING_REPORT_DETAILED_ALL = 'reporting.report.detailed.all'
export const REPORTING_REPORT_DETAILED_MINE = 'reporting.report.detailed.mine'
export const REPORTING_REPORT_INSIGHT_ALL = 'reporting.report.insight.all'
export const REPORTING_REPORT_INSIGHT_MINE = 'reporting.report.insight.mine'
export const BILLING = 'billing'
export const BILLING_DETAILS = 'billing.details'
export const BILLING_HISTORY = 'billing.history'
export const BILLING_TOPUP_CREDIT = 'billing.top-up.credit'
export const BILLING_TOPUP_MONEY = 'billing.top-up.money'
export const BILLING_TOPUP_AUTO_TOPUP = 'billing.top-up.auto-top-up'
export const BILLING_TOPUP_AUTO_TOPUP_SOE = 'billing.top-up.auto-top-up.for-sub-accounts'
export const BILLING_TOPUP_NOTIFICATION = 'billing.top-up.notification'
export const BILLING_TOPUP_SHOW_CTA = 'billing.top-up.show-call-to-action'
export const BILLING_BALANCE_USAGE = 'billing.balance.usage'
export const BILLING_BALANCE_MONEY = 'billing.balance.money'
export const BILLING_BALANCE_CREDIT = 'billing.balance.credit'
export const BILLING_BALANCE_CREDIT_SUB_ACCOUNTS = 'billing.balance.credit.sub-accounts'
export const BILLING_FIRST_TIME_DISCOUNT = 'billing.top-up.first-time-discount'
export const BILLING_NO_EXPIRY = 'billing.top-up.credit.no-expiry-date'
export const BILLING_PAYMENT_METHOD_PAYPAL = 'billing.payment-method.paypal'
export const BILLING_PAYMENT_METHOD_BANK_TRANSFER = 'billing.payment-method.bank-transfer'
export const BILLING_TRANSFER_CREDITS_TO_SUB_ACCOUNTS = 'billing.transfer-credits.to-sub-accounts'
export const BILLING_TRANSFER_CREDITS_FROM_SUB_ACCOUNTS = 'billing.transfer-credits.from-sub-accounts'
export const BILLING_ALLOCATE_CREDITS_TO_SUB_ACCOUNTS = 'billing.allocate-credits.to-sub-accounts'
export const BILLING_ALLOCATE_CREDITS_FROM_SUB_ACCOUNTS = 'billing.allocate-credits.from-sub-accounts'
export const UPGRADE = 'upgrade'
export const UPGRADE_BANNER = 'upgrade.banner'
export const ACCOUNT_SETTINGS = 'account-settings'
export const ACCOUNT_SETTINGS_USAGE_TRACKING = 'account-settings.usage-tracking'
export const ACCOUNT_SETTINGS_SOCIAL_SENDING = 'account-settings.social-sending'
export const ACCOUNT_SETTINGS_FAMILIAR_SENDER = 'account-settings.familiar-sender'
export const ACCOUNT_SETTINGS_PARTNER_UPDATE = 'account-settings.partner.update'
export const ACCOUNT_SETTINGS_CHANGE_CANCEL_REQUEST = 'account-settings.change-or-cancel.request'
export const ACCOUNT_SETTINGS_TIMEZONE = 'account-settings.timezone'
export const ACCOUNT_SETTINGS_SSO = 'account-settings.sso'
export const API_KEYS_BASIC = 'api-keys.basic'
export const API_KEYS_BASIC_CREATE = 'api-keys.basic.create'
export const API_KEYS_BASIC_UPDATE = 'api-keys.basic.update'
export const API_KEYS_BASIC_DELETE = 'api-keys.basic.delete'
export const API_KEYS_BASIC_CREATE_PROMPT_PHONE_NUMBER_VERIFICATION = 'api-keys.basic.create.prompt.phoneNumberVerification'
export const API_KEYS_HMAC = 'api-keys.hmac'
export const API_KEYS_HMAC_CREATE = 'api-keys.hmac.create'
export const API_KEYS_HMAC_UPDATE = 'api-keys.hmac.update'
export const API_KEYS_HMAC_DELETE = 'api-keys.hmac.delete'
export const API_KEYS_HMAC_CREATE_PROMPT_PHONE_NUMBER_VERIFICATION = 'api-keys.hmac.create.prompt.phoneNumberVerification'
export const API_KEYS_LEGACY = 'api-keys.legacy'
export const API_KEYS_LEGACY_CREATE = 'api-keys.legacy.create'
export const API_KEYS_LEGACY_UPDATE = 'api-keys.legacy.update'
export const API_KEYS_LEGACY_DELETE = 'api-keys.legacy.delete'
export const API_KEYS_LEGACY_CREATE_PROMPT_PHONE_NUMBER_VERIFICATION = 'api-keys.legacy.create.prompt.phoneNumberVerification'
export const WEB_HOOKS = 'web-hooks'
export const WEB_HOOKS_CREATE = 'web-hooks.create'
export const WEB_HOOKS_UPDATE = 'web-hooks.update'
export const WEB_HOOKS_DELETE = 'web-hooks.delete'
export const WEB_HOOKS_CREATE_VERIFY = 'web-hooks.create.prompt.phoneNumberVerification'
export const EMAIL2SMS = 'email2sms'
export const EMAIL2SMS_CREATE = 'email2sms.create'
export const EMAIL2SMS_DELETE = 'email2sms.delete'
export const EMAIL2SMS_SETTINGS = 'email2sms.settings'
export const EMAIL2SMS_CREATE_VERIFY = 'email2sms.create.prompt.phoneNumberVerification'
export const WORKFLOWS = 'workflows'
export const WORKFLOWS_RULES_AND_TRIGGERS = 'workflows.rules-and-triggers'
export const WORKFLOWS_CREATE = 'workflows.create'
export const WORKFLOWS_CREATE_VERIFY = 'workflows.create.prompt.phoneNumberVerification'
export const WORKFLOWS_UPDATE = 'workflows.update'
export const WORKFLOWS_DELETE = 'workflows.delete'
export const WORKFLOWS_ZAPIER = 'workflows.zapier'
export const WORKFLOWS_LIMIT = 'workflows.create.has-limits'
export const MANAGEMENT_USERS = 'management.users'
export const MANAGEMENT_USERS_INVITE = 'management.users.invite'
export const MANAGEMENT_USERS_INVITE_HAS_LIMIT = 'management.users.invite.has-limits'
export const MANAGEMENT_USERS_ADMIN = 'management.users.invite.admin'
export const MANAGEMENT_USERS_ADVANCED = 'management.users.invite.advanced'
export const MANAGEMENT_USERS_BASIC = 'management.users.invite.basic'
export const MANAGEMENT_USERS_UPDATE_ROLE = 'management.users.update.role'
export const MANAGEMENT_USERS_ROLE_ADMIN = 'management.users.role.admin'
export const MANAGEMENT_USERS_ROLE_USER = 'management.users.role.user'
export const MANAGEMENT_USERS_ROLE_BASIC = 'management.users.role.basic'

export const MANAGEMENT_USERS_PASSWORD_RESET = 'management.users.password-reset'
export const MANAGEMENT_USERS_PASSWORD_RESET_LINK = 'management.users.password-reset-link'
export const MANAGEMENT_USERS_REMOVE = 'management.users.remove'
export const MANAGEMENT_SUB_ACCOUNTS = 'management.sub-accounts'
export const MANAGEMENT_SUB_ACCOUNTS_LIST = 'management.sub-accounts.list'
export const MANAGEMENT_SUB_ACCOUNTS_CREATE = 'management.sub-accounts.create'
export const MANAGEMENT_SUB_ACCOUNTS_CREATE_VERIFY = 'management.sub-accounts.create.prompt.phoneNumberVerification'
export const MANAGEMENT_SUB_ACCOUNTS_CREATE_BILLING_TYPE_PREPAID = 'management.sub-accounts.create.billing-type.prepaid'
export const MANAGEMENT_SUB_ACCOUNTS_CREATE_BILLING_TYPE_POSTPAID = 'management.sub-accounts.create.billing-type.postpaid'
export const MANAGEMENT_SUB_ACCOUNTS_CREATE_INVOICING_TYPE_BILL_TO_PARENT = 'management.sub-accounts.create.invoicing-type.bill-to-parent'
export const MANAGEMENT_SUB_ACCOUNTS_CREATE_INVOICING_TYPE_BILL_TO_PARENT_COST_CENTRE = 'management.sub-accounts.create.invoicing-type.bill-to-parent.cost-centre'
export const MANAGEMENT_SUB_ACCOUNTS_UPDATE = 'management.sub-accounts.update'
export const MANAGEMENT_SUB_ACCOUNTS_DELETE = 'management.sub-accounts.disable'
export const MANAGEMENT_USERS_UNLOCK = 'management.users.unlock'
export const MANAGEMENT_USERS_INVITE_VERIFY = 'management.users.invite.prompt.phoneNumberVerification'
export const MANAGEMENT_USERS_FROM_SUB_ACCOUNTS = 'management.users.from-sub-accounts'

export const MANAGEMENT_DEDICATED_NUMBERS = 'management.dedicated-numbers.list'
export const MANAGEMENT_DEDICATED_NUMBERS_ASSOCIATE = 'management.dedicated-numbers.associate'
export const MANAGEMENT_DEDICATED_NUMBERS_ASSOCIATE_SMS_ONLY = 'management.dedicated-numbers.associate.sms-only'
export const MANAGEMENT_DEDICATED_NUMBERS_DISASSOCIATE = 'management.dedicated-numbers.disassociate'
export const MANAGEMENT_DEDICATED_NUMBERS_UPDATE = 'management.dedicated-numbers.update'
export const MANAGEMENT_DEDICATED_SENDER_IDS = 'management.sender-ids'
export const DEDICATED_NUMBERS = 'dedicated-numbers.list'

export const PROFILE_UPDATE_EMAIL = 'profile.update.email'
export const PROFILE_UPDATE_PHONE = 'profile.update.phone'
export const PROFILE_UPDATE_SUB_USERS = 'profile.update.sub-users'
export const PROFILE_UPDATE_PASSWORD = 'profile.update.password'
export const SUPPORT = 'support'
export const SUPPORT_ACTIVE = 'support.active'
export const SUPPORT_MULTITXT = 'support.multitxt'
export const SUPPORT_VERIFICATION = 'support.verification-report'
export const SUPPORT_CREATE_CARRIER_ACCOUNTS = 'support.carrier-accounts.create'

export const ADDON = 'addon'
export const ADDON_HLR_LOOKUP = 'addon.hlr-lookup'
export const ADDON_NUMBER_TYPE_LOOKUP = 'addon.number-type-lookup'
export const ADDON_CARRIER_LOOKUP = 'addon.carrier-lookup'
export const ADDON_DEDICATED_NUMBER = 'addon.dedicated-number'
export const ADDON_FAMILIAR_SENDER = 'addon.familiar-sender'
export const ADDON_ALPHA_TAG = 'addon.alpha-tag'
export const ADDON_SHORT_CODE = 'addon.short-code'
export const ADDON_SHORT_TRACKABLE_LINKS = 'addon.short-trackable-links'
export const ADDON_LINK_PREVIEWS = 'addon.link-previews'
export const ADDON_MOBILE_LANDING_PAGES = 'addon.mobile-landing-pages'
export const ADDON_CALENDAR_AUTOMATION = 'addon.calendar-automation'

export const LOOKUP_BULK = 'lookup.bulk'
export const LOOKUP_BULK_CREATE = 'lookup.bulk.create'
export const URL_SHORTENER = 'url-shortener'
export const INBOX = 'inbox'
export const INBOX_ADVANCED = 'inbox.advanced'
export const WORKFLOWS_CALENDARS = 'workflows.calendars'
export const WORKFLOWS_CALENDARS_CREATE = 'workflows.calendars.create'
export const WORKFLOWS_CALENDARS_CREATE_VERIFY = 'workflows.calendars.create.prompt.phoneNumberVerification'
export const WORKFLOWS_CALENDARS_DELETE = 'workflows.calendars.delete'
export const WORKFLOWS_CALENDARS_LIST = 'workflows.calendars.list'
export const WORKFLOWS_CALENDARS_PURCHASE = 'workflows.calendars.purchase'
export const WORKFLOWS_CALENDARS_UPDATE = 'workflows.calendars.update'
export const WORKFLOWS_ABANDONEDCART = 'workflows.abandoned-cart'
export const PRODUCTS_DRAFT_EXISTS = 'products.draft-exists'
export const PRODUCTS_SELECTION = 'products.selection'
export const SELF_POST_PAID_REQUEST = 'self-serve.post-paid.request'
export const SELF_SERVE_POST_PAID_PLAN_ACTIVATE = 'self-serve.post-paid.activate'
export const WORKFLOWS_DR_TRIGGER = 'workflows.trigger.dr'

export const INTEGRATIONS_HUBSPOT_SETTINGS_LIST = 'integrations.hubspot.settings.list'

export const INTEGRATION_BIGCOMMERCE_CONNECT = 'integrations.bigcommerce.connect'

export const PROFILE_DEFAULT_SENDING_COUNTRY = 'profile.default-sending-country'
export const BILLING_TOPUP_AUTO_TOPUP_UPDATE = 'billing.top-up.auto-top-up.update'
export const PARTNERS_LIST_CUSTOMER_ACCOUNT = 'partners.customer-accounts.list'
export const PARTNERS_ACTIVE_CUSTOMER_ACCOUNT = 'partners.customer-accounts.active'
export const PARTNERS_LOGIN_CUSTOMER_ACCOUNT = 'partners.customer-accounts.login'
export const PARTNERS_CUSTOMER_ACCOUNT_CREATE = 'partners.customer-accounts.create'
export const MESSAGING_SEND_MY_NUMBER = 'messaging.recipient.my-number'

export const TEMPLATES_SHARING_ADD_ACCOUNT = 'templates.sharing.add-account'
export const TEMPLATES_SHARING_REMOVE_ACCOUNT = 'templates.sharing.remove-account'

export const WEB_WIDGET = 'web-widget'
export const WEB_WIDGET_CREATE = 'web-widget.create'
export const WEB_WIDGET_UPDATE = 'web-widget.update'

export const CONFIG_GBM = 'multi-channel-messaging.google-business-messaging.request'

export const MULTI_CHANNEL = 'inbox.multi-channel.connect'
export const INBOX_MULTI_CHANNEL = 'inbox.multi-channel'

export const TFN_TRIAL_RESTRICTIONS = 'tfn.trial.restrictions'

export const URL_SHORTENER_NAME = 'URL_SHORTENER'
