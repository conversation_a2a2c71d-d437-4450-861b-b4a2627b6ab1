import {
  TenDlcBrandIdentityStatus,
  TenDlcCampaignPhoneStatus,
  type TenDlcCampaignStatus,
} from './constants';

export type TenDlcBrand = {
  id: string;
  account: {
    accountId: string;
    vendorId: string;
  };
  entityType: string;
  identityStatus?: TenDlcBrandIdentityStatus;
  vertical?: string;
  firstName?: string;
  lastName?: string;
  displayName: string;
  companyName?: string;
  ein?: string | null;
  phone: string;
  email: string;
  website?: string;
  street?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country: 'US' | 'CA';
  stockExchange?: string | null;
  stockSymbol?: string | null;
  createDate: string;
  sourceCode?: string | null;
  inherited: boolean | null;
};

export type TenDlcPhoneNumber = {
  type: string;
  status: TenDlcCampaignPhoneStatus;
  phoneNumber: string;
};

export type TenDlcCampaign = {
  id: string;
  sourceCode?: string | null;
  inherited: boolean | null;
  account: {
    accountId: string;
    vendorId: string;
  };
  brand?: TenDlcBrand;
  brandId: string;
  usecase: string;
  subUsecases: string[] | null;
  status: TenDlcCampaignStatus;
  phones: TenDlcPhoneNumber[];
  sampleMessage1: string;
  sampleMessage2: string;
  description: string;
  messageFlow: string;
  messageFrequency: number;
  optInMethod: string;
  optInKeyword?: string | null;
  otherOptInMethod?: string | null;
  directLending?: boolean;
  ageGated?: boolean;
  numberPool?: boolean;
  affiliateMarketing?: boolean;
  rateLimit?: number;
  dailyLimit?: {
    sent: number;
    max: number;
  };
};

export type TenDlcUseCase = {
  id: string;
  classification: string;
  description: string;
  displayName: string;
  maxSubUsecases: number;
  minSubUsecases: number;
  validSubUsecase: boolean;
};

export type TenDlcBrandIndustry = {
  type: string;
  label: string;
};
