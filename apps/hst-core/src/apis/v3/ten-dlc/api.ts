import {
  loadTenDlcCampaigns,
  loadTenDlcFetchSearchInfo,
  loadTenDlcCampaignsByAccount,
  createCampaign,
  updateCampaign,
  deleteCampaign,
  submitToTCR,
} from './endpoints/campaigns';
import { loadUseCases } from './endpoints/use-cases';
import {
  deleteBrand,
  getBrand,
  syncBrand,
  updateBrand,
  reassignBrand,
  fetchBrandLinkAccounts,
  linkBrand,
  unlinkBrand,
} from './endpoints/brand';
import { loadBrandIndustries } from './endpoints/industries';
import { getApplication, saveApplication } from './endpoints/application';
import { loadPhoneNumbers, updatePhoneNumbers } from './endpoints/phones';
import { loadAccountKeywords } from './endpoints/account-keywords';

export const TenDlcApi = {
  loadTenDlcCampaigns,
  loadTenDlcFetchSearchInfo,
  loadTenDlcCampaignsByAccount,
  createCampaign,
  updateCampaign,
  deleteCampaign,
  loadUseCases,
  getBrand,
  syncBrand,
  updateBrand,
  deleteBrand,
  reassignBrand,
  fetchBrandLinkAccounts,
  linkBrand,
  unlinkBrand,
  submitToTCR,
  loadBrandIndustries,
  getApplication,
  saveApplication,
  loadPhoneNumbers,
  updatePhoneNumbers,
  loadAccountKeywords,
};
