export enum TenDlcBrandIdentityStatus {
  SelfDeclared = 'SELF_DECLARED',
  Unverified = 'UNVERIFIED',
  Verified = 'VERIFIED',
  VettedVerified = 'VETTED_VERIFIED',
}

export enum TenDlcCampaignStatus {
  Active = 'ACTIVE',
  Expired = 'EXPIRED',
  Draft = 'DRAFT',
}

export enum TenDlcCampaignPhoneStatus {
  Connected = 'CONNECTED',
  Pending = 'PENDING',
  NotConnected = 'NOT_CONNECTED',
  Error = 'ERROR',
  PermanentError = 'PERMANENT_ERROR',
}
