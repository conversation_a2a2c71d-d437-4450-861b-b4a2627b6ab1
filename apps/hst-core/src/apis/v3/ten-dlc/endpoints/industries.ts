import { handleAPI } from '@sinch-smb/dev-utils';
import fetchInstance from 'apps/hst-core/src/apis/utils/helpers';
import Endpoint from '../../../../helpers/endpoint';
import type { TenDlcBrandIndustry } from '../types';

export type TenDlcBrandIndustriesResult = {
  resources: TenDlcBrandIndustry[];
};

export const loadBrandIndustries = handleAPI(
  async (): Promise<TenDlcBrandIndustriesResult> =>
    fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/ten-dlc/industries`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    )
);
