import { handleAPI } from '@sinch-smb/dev-utils';
import fetchInstance from 'apps/hst-core/src/apis/utils/helpers';
import Endpoint from '../../../../helpers/endpoint';
import type { TenDlcPhoneNumber } from '../types';

type TenDlcPhoneNumbersParams = {
  accountId: string;
  vendorId: string;
};

export type TenDlcPhoneNumbersResult = {
  resources: TenDlcPhoneNumber[];
};

export const loadPhoneNumbers = handleAPI(
  async ({
    vendorId,
    accountId,
  }: TenDlcPhoneNumbersParams): Promise<TenDlcPhoneNumbersResult> =>
    fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/ten-dlc/phones`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    )
);

type UpdatePhoneNumbersParams = {
  accountId: string;
  vendorId: string;
  campaignId: string;
  phones: string[];
};

export type TenDlcUpdatePhoneNumbersResult = {
  resources: TenDlcPhoneNumber[];
};

export const updatePhoneNumbers = handleAPI(
  async ({
    vendorId,
    accountId,
    campaignId,
    phones,
  }: UpdatePhoneNumbersParams): Promise<TenDlcUpdatePhoneNumbersResult> =>
    fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/ten-dlc/campaigns/${campaignId}/phones`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ phones }),
      }
    )
);
