import { handleAPI } from '@sinch-smb/dev-utils';
import fetchInstance from 'apps/hst-core/src/apis/utils/helpers';
import Endpoint from '../../../../helpers/endpoint';

export type TenDlcAccountKeywordsResult = {
  resources: string[];
};

export const loadAccountKeywords = handleAPI(
  async ({
    vendorId,
    accountId,
  }: {
    vendorId: string;
    accountId: string;
  }): Promise<TenDlcAccountKeywordsResult> =>
    fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/ten-dlc/campaigns/keywords`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    )
);
