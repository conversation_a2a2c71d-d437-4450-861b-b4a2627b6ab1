import { handleAPI } from '@sinch-smb/dev-utils';
import fetchInstance from 'apps/hst-core/src/apis/utils/helpers';
import Endpoint from '../../../../helpers/endpoint';

type TaxpayerIdentification = {
  legalCompanyName: string;
  organizationName: string;
  ein: string;
  businessEntityType: string;
  industryVertical: string;
  tickerSymbol: string | null;
  stockExchange: string | null;
};

type BusinessDetails = {
  registrationCountry: 'US' | 'CA';
  street1: string;
  street2: string;
  city: string;
  state: string;
  zip: string;
  firstName: string;
  lastName: string;
  website: string;
  email: string;
  phoneNumber: string;
  businessContactEmail: string;
};

export type ApplicationData = {
  status: 'DRAFT' | 'SUBMITTED';
  taxpayerIdentification: TaxpayerIdentification;
  businessDetails: BusinessDetails;
};

type GetApplicationParams = {
  vendorId: string;
  accountId: string;
};

export const getApplication = handleAPI(
  async ({
    vendorId,
    accountId,
  }: GetApplicationParams): Promise<ApplicationData> =>
    fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/ten-dlc/application`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    )
);

type SaveApplicationParams = {
  vendorId: string;
  accountId: string;
  body: ApplicationData;
};

export const saveApplication = handleAPI(
  async ({
    vendorId,
    accountId,
    body,
  }: SaveApplicationParams): Promise<ApplicationData> =>
    fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/ten-dlc/application`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(body),
      }
    )
);
