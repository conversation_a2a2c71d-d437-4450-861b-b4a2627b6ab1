import { handleAPI } from '@sinch-smb/dev-utils';
import fetchInstance from 'apps/hst-core/src/apis/utils/helpers';
import Endpoint from '../../../../helpers/endpoint';
import type { TenDlcUseCase } from '../types';

type TenDlcUseCasesParams = {
  accountId: string;
  vendorId: string;
};

export type TenDlcUseCasesResult = {
  resources: TenDlcUseCase[];
};

export const loadUseCases = handleAPI(
  async ({
    vendorId,
    accountId,
  }: TenDlcUseCasesParams): Promise<TenDlcUseCasesResult> =>
    fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/ten-dlc/brand/usecases`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    )
);
