import { handleAPI } from '@sinch-smb/dev-utils';
import { TenDlcBrand } from '../types';
import fetchInstance from '../../../utils/helpers';
import Endpoint from '../../../../helpers/endpoint';

type GetBrandParams = {
  accountId: string;
  vendorId: string;
};

export const getBrand = handleAPI(
  async ({ vendorId, accountId }: GetBrandParams): Promise<TenDlcBrand> =>
    fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/ten-dlc/brand`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    )
);

type SyncBrandParams = {
  accountId: string;
  vendorId: string;
};

export const syncBrand = handleAPI(
  async ({ vendorId, accountId }: SyncBrandParams): Promise<void> =>
    fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/ten-dlc/sync`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    )
);

type UpdateBrandParams = {
  accountId: string;
  vendorId: string;
  brandId: string;
};

export const updateBrand = handleAPI(
  async ({ vendorId, accountId, brandId }: UpdateBrandParams): Promise<void> =>
    fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/ten-dlc/brand`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ brandId }),
      }
    )
);

type DeleteBrandParams = {
  accountId: string;
  vendorId: string;
};

export const deleteBrand = handleAPI(
  async ({ vendorId, accountId }: DeleteBrandParams): Promise<void> =>
    fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/ten-dlc/brand`,
      {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    )
);

type ReassignBrandParams = {
  accountId: string;
  vendorId: string;
  targetAccountId: string;
};

export const reassignBrand = handleAPI(
  async ({
    vendorId,
    accountId,
    targetAccountId,
  }: ReassignBrandParams): Promise<void> =>
    fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/ten-dlc/brand/reassign`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ accountId: targetAccountId }),
      }
    )
);

type LinkBrandParams = {
  accountId: string;
  vendorId: string;
  values: {
    parentAccountId: string;
    brandId: string;
    campaignIds: string[];
  };
};

export const linkBrand = handleAPI(
  async ({ vendorId, accountId, values }: LinkBrandParams): Promise<void> =>
    fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/ten-dlc/account/inherit`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ ...values }),
      }
    )
);

type UnlinkBrandParams = {
  accountId: string;
  vendorId: string;
};

export const unlinkBrand = handleAPI(
  async ({ vendorId, accountId }: UnlinkBrandParams): Promise<void> =>
    fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/ten-dlc/account/disconnect`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    )
);

type FetchBrandLinkAccountsParams = {
  accountId: string;
  vendorId: string;
};

export type FetchBrandLinkAccountsResult = {
  resources: [
    {
      id: string;
      brandId?: string;
      campaignIds: string[];
    }
  ];
};

export const fetchBrandLinkAccounts = handleAPI(
  async ({
    vendorId,
    accountId,
  }: FetchBrandLinkAccountsParams): Promise<FetchBrandLinkAccountsResult> =>
    fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/ten-dlc/account/parents`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    )
);
