import { handleAPI } from '@sinch-smb/dev-utils';
import { TenDlcCampaignPhoneStatus } from '../constants';
import Endpoint from '../../../../helpers/endpoint';
import paramsToQueryString from '../../../helpers/params-to-querystring';
import fetchInstance from '../../../utils/helpers';
import type { TenDlcCampaign } from '../types';

export type TenDlcCampaignsParams = {
  page?: number;
  size?: number;
};

export type TenDlcCampaignsBody = {
  category: string | null;
  value?: string;
  exactMatch?: boolean;
  phoneStatuses?: string[];
  projectIds?: string[];
};

type LoadTenDlcCampaignsRequest = {
  params: TenDlcCampaignsParams;
  body: TenDlcCampaignsBody;
};

type LoadTenDlcCampaignsResult = {
  resources: TenDlcCampaign[];
  pagination: {
    page: number;
    size: number;
    totalPages: number;
  };
};

export const loadTenDlcCampaigns = handleAPI(
  async (
    request: LoadTenDlcCampaignsRequest
  ): Promise<LoadTenDlcCampaignsResult> => {
    const queryString = paramsToQueryString({
      params: request.params,
      prefix: undefined,
    });
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/ten-dlc/search?${queryString}`,
      {
        body: JSON.stringify(request.body),
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }
);

export type CategorySearchInfo = {
  categoryCode: string;
  categoryName: string;
};

export type LoadTenDlcSearchInfoResult = {
  categories: CategorySearchInfo[];
  projectIds: string[];
  phoneStatuses: string[];
};

export const loadTenDlcFetchSearchInfo = handleAPI(
  async (): Promise<LoadTenDlcSearchInfoResult> =>
    fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/ten-dlc/search/info`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    )
);

type TenDlcCampaignsByAccountParams = {
  accountId: string;
  vendorId: string;
  params?: {
    status?: TenDlcCampaignPhoneStatus;
  };
};

export type TenDlcCampaignsByAccountResult = {
  resources: TenDlcCampaign[];
};

export const loadTenDlcCampaignsByAccount = handleAPI(
  async ({
    accountId,
    vendorId,
    params = {},
  }: TenDlcCampaignsByAccountParams): Promise<TenDlcCampaignsByAccountResult> => {
    const queryString = paramsToQueryString({ params, prefix: undefined });

    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/ten-dlc/campaigns?${queryString}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }
);

export const deleteCampaign = handleAPI(
  async ({
    vendorId,
    accountId,
    campaignId,
  }: {
    accountId: string;
    vendorId: string;
    campaignId: string;
  }): Promise<object> =>
    fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/ten-dlc/campaigns/${campaignId}`,
      {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    )
);

export type CreateCampaignBody = {
  usecase: string;
  subUsecases: string[] | null;
  optInMethod: string;
  optInKeyword: string | null;
  otherOptInMethod: string | null;
  sampleMessage1: string;
  sampleMessage2: string;
  messageFrequency: number | null;
  directLending: boolean;
  ageGated: boolean;
  affiliateMarketing: boolean;
  numberPool: boolean;
};

export const createCampaign = handleAPI(
  async ({
    vendorId,
    accountId,
    body,
  }: {
    accountId: string;
    vendorId: string;
    body: CreateCampaignBody;
  }): Promise<TenDlcCampaign> =>
    fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/ten-dlc/campaigns`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(body),
      }
    )
);

export type UpdateCampaignBody = CreateCampaignBody & {
  description: string;
  messageFlow: string;
};

export const updateCampaign = handleAPI(
  async ({
    vendorId,
    accountId,
    campaignId,
    body,
  }: {
    vendorId: string;
    accountId: string;
    campaignId: string;
    body: UpdateCampaignBody;
  }): Promise<TenDlcCampaign> =>
    fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/ten-dlc/campaigns/${campaignId}`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(body),
      }
    )
);

export const submitToTCR = handleAPI(
  async ({
    vendorId,
    accountId,
    campaignId,
  }: {
    accountId: string;
    campaignId: string;
    vendorId: string;
  }): Promise<object> =>
    fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/ten-dlc/campaigns/${campaignId}/submit`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    )
);
