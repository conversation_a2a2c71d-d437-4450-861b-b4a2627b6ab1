import { accounts, previewData } from '../../fixtures/support/account/oneportal-account-search'
import helpers from '../utils/helpers'
import AccountSearch from './accounts-search'

jest.mock('../utils/helpers', () => jest.fn())
jest.mock('@sinch-smb/dev-utils', () => ({
  handleAPI: (func) => (args) => func(args),
  paramsToQueryString: (func) => (args) => func(args),
}))

describe('OnePortal Account Serach', () => {
  const errorResponse = { code: 'validation', fatal: false }

  describe('loadAccounts', () => {
    it('returns accounts if call is successful', async () => {
      helpers.mockImplementation(() => jest.fn().mockResolvedValueOnce(accounts))
      const response = await AccountSearch.loadAccounts({})

      expect(response).toEqual(accounts)
    })

    it('returns accounts with empty params if call is successful', async () => {
      helpers.mockImplementation(() => jest.fn().mockResolvedValueOnce(accounts))
      const response = await AccountSearch.loadAccounts()

      expect(response).toEqual(accounts)
    })

    it('throws error if getApiKeys fails', async () => {
      try {
        helpers.mockImplementation(() => jest.fn().mockRejectedValueOnce(errorResponse))
        await AccountSearch.loadAccounts({})
      } catch (error) {
        expect(error.code).toBe('validation')
        expect(error.fatal).toBe(false)
      }
    })
  })
  describe('loadAllAccounts', () => {
    it('returns all accounts if call is successful', async () => {
      helpers.mockImplementation(() => jest.fn().mockResolvedValueOnce(accounts))
      const response = await AccountSearch.loadAllAccounts({})
      expect(response).toEqual(accounts)
    })

    it('returns all accounts  with empty params if call is successful', async () => {
      helpers.mockImplementation(() => jest.fn().mockResolvedValueOnce(accounts))
      const response = await AccountSearch.loadAllAccounts()
      expect(response).toEqual(accounts)
    })

    it('throws error if getSignUpProfile fails', async () => {
      try {
        helpers.mockImplementation(() => jest.fn().mockRejectedValueOnce(errorResponse))
        await AccountSearch.loadAllAccounts({})
      } catch (error) {
        expect(error.code).toBe('validation')
        expect(error.fatal).toBe(false)
      }
    })
  })

  describe('loadAccountPreview', () => {
    it('returns preview data if call is successful', async () => {
      helpers.mockImplementation(() => jest.fn().mockResolvedValueOnce(previewData))
      const response = await AccountSearch.loadAccountPreview({})
      expect(response).toEqual(previewData)
    })

    it('returns all accounts  with params if call is successful', async () => {
      helpers.mockImplementation(() => jest.fn().mockResolvedValueOnce(accounts))
      const response = await AccountSearch.loadAccountPreview({ senderId: 'test', accountId: '123' })
      expect(response).toEqual(accounts)
    })

    it('throws error if getSignUpProfile fails', async () => {
      try {
        helpers.mockImplementation(() => jest.fn().mockRejectedValueOnce(errorResponse))
        await AccountSearch.loadAccountPreview({})
      } catch (error) {
        expect(error.code).toBe('validation')
        expect(error.fatal).toBe(false)
      }
    })
  })
})
