import { handleAPI } from '@sinch-smb/dev-utils';
import { vendorId } from '../../constants/vendors';
import Endpoint from '../../helpers/endpoint';
import fetchInstance from '../utils/helpers';


export default {
  logoutJWT: handleAPI(fetchInstance?.clearSession),
  loginAs: handleAPI((body) =>
    fetchInstance()(`${Endpoint.SUPPORT_V2_API_URL}/v2.1/support/jwt/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Vendor-Id': body.vendorId || vendorId,
      },
      credentials: 'include',
      body: JSON.stringify({
        accountId: body.accountId,
        reason: body.reason,
        roles: body.roles,
      }),
    })
  )
};
