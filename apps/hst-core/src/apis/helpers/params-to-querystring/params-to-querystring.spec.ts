import paramsToQueryString, { paramsToQueryStringV2 } from './params-to-querystring'

describe('paramsToQueryString', () => {
  it('returns properly querystring from params', async () => {
    const params = {
      foo: '1',
      bar: '2',
      statuses: [
        'a',
        'b',
      ],
      badKey: undefined,
      filter: null,
    }

    expect(paramsToQueryString({ params })).toBe('foo=1&bar=2&statuses[0]=a&statuses[1]=b')
    expect(paramsToQueryString({ params: {} })).toBe('')
  })
})

describe('paramsToQueryStringV2', () => {
  it('returns properly querystring from params', async () => {
    const params = {
      foo: '1',
      bar: '2',
      statuses: [
        'a',
        'b',
      ],
      badKey: undefined,
      filter: null,
    }

    expect(paramsToQueryStringV2({ params })).toBe('foo=1&bar=2&statuses[]=a&statuses[]=b')
    expect(paramsToQueryStringV2({ params: {} })).toBe('')
  })
})
