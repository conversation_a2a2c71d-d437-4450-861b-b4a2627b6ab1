import _isNil from 'lodash/isNil'

const paramsToQueryString = ({ params, prefix }) => {
  const queryString = []
  Object.keys(params).forEach((objProperty) => {
    const queryStringKey = prefix ? `${prefix}[${objProperty}]` : objProperty
    const queryStringValue = params[objProperty]
    if (Array.isArray(queryStringValue)) {
      queryString.push(
        paramsToQueryString({ params: queryStringValue, prefix: queryStringKey }),
      )
    } else if (!_isNil(queryStringValue)) {
      queryString.push(
        `${queryStringKey}=${encodeURIComponent(queryStringValue)}`,
      )
    }
  })
  return queryString.join('&')
}

export const paramsToQueryStringV2 = ({ params, prefix }) => {
  const queryString = []
  Object.keys(params).forEach((objProperty) => {
    const queryStringKey = prefix ? `${prefix}[]` : objProperty
    const queryStringValue = params[objProperty]
    if (Array.isArray(queryStringValue)) {
      queryString.push(
        paramsToQueryStringV2({ params: queryStringValue, prefix: queryStringKey }),
      )
    } else if (!_isNil(queryStringValue)) {
      queryString.push(
        `${queryStringKey}=${encodeURIComponent(queryStringValue)}`,
      )
    }
  })
  return queryString.join('&')
}

export default paramsToQueryString
