import { getError<PERSON>essage, MMError } from 'helpers'
import
handleAPI,
{ parseKey, generateErrorResponse }
  from './handle-api'

import Endpoint from '../../../helpers/endpoint'

jest.mock('react-ga', () => ({
  exception: jest.fn(),
}))
jest.mock('../../../../../../libs/helpers/src/mm-error/mw-error-converter', () => jest.fn())

describe('handleAPI', () => {
  it('parseKey should return the desired key', async () => {
    expect(parseKey('')).toBe('')
    expect(parseKey(null)).toBe(null)
    expect(parseKey(undefined)).toBe(undefined)
    expect(parseKey('recipients.numbers[5]')).toBe('numbers')
    expect(parseKey('recipients[3].numbers[5]')).toBe('numbers')
    expect(parseKey('recipients.numbers')).toBe('numbers')
    expect(parseKey('numbers')).toBe('numbers')
    expect(parseKey('recipients.phone.numbers')).toBe('numbers')
    expect(parseKey('recipients[3].phone.numbers[10]')).toBe('numbers')
  })

  it('handles fetch responses with formContext', async () => {
    fetch.mockResponseOnce(JSON.stringify({
      content: 'Cool',
    }))
    const result = await handleAPI(async () => fetch('test'),
    )({ formContext: 'test' })
    expect(result).toEqual({ content: 'Cool' })
  })

  it('handles blank fetch responses', async () => {
    try {
      fetch.mockResponse('')
      const result = await handleAPI(async () => fetch('test'))()
      expect(result).toEqual({})
    } catch (error) {
      throw error
    }
  })

  it('handles fetch validation error with manual sync error', async () => {
    try {
      const response = {
        ok: false,
        text: jest.fn(() => Promise.resolve(JSON.stringify([{
          key: 'test',
          code: 'validation',
          fatal: false,
          error: 'Fail sync contact',
        }]))),
        status: 400,
        url: 'hello',
      }
      await handleAPI(async () => response)()
    } catch (error) {
      expect(error.message).toBe('Fail sync contact')
    }
  })

  it('handles fetch validation (submission) error', async () => {
    try {
      const response = {
        ok: false,
        text: jest.fn(() => Promise.resolve(JSON.stringify([{
          key: 'test',
          code: 'validation',
          fatal: false,
        }]))),
        status: 400,
        url: 'hello',
      }
      await handleAPI(async () => response)()
    } catch (error) {
      expect(error.message).toBe('Submit Validation Failed')
    }
  })

  it('handles fetch exists (submission) error', async () => {
    try {
      const response = {
        ok: false,
        text: jest.fn(() => Promise.resolve(JSON.stringify([{
          key: 'test',
          code: 'exists',
          fatal: false,
        }]))),
        status: 400,
        url: 'hello',
      }
      await handleAPI(async () => response)()
    } catch (error) {
      expect(error.message).toBe('Submit Validation Failed')
    }
  })

  it('handles fetch fatal error', async () => {
    try {
      const response = {
        ok: false,
        url: 'hello',
        text: jest.fn(() => Promise.resolve(JSON.stringify([{
          key: 'test',
          code: 'test',
          fatal: true,
        }]))),
        status: 400,
      }
      await handleAPI(async () => response)()
    } catch (error) {
      expect(error.message).toBe(getErrorMessage({ code: 'test' }))
    }
  })

  it('handles fetch error', async () => {
    try {
      const response = {
        ok: false,
        url: 'hello',
        text: jest.fn(() => Promise.resolve(JSON.stringify({
          key: 'test',
          code: 'test',
          fatal: true,
          message: 'message',
        }))),
        status: 400,
      }
      await handleAPI(async () => response)()
    } catch (error) {
      expect(error.originalMessage).toBe('message')
    }
  })

  it('handles any other fetch error', async () => {
    try {
      const response = {
        ok: false,
        text: jest.fn(() => Promise.resolve(JSON.stringify([{
          key: 'test',
          code: 'test',
          fatal: false,
        }]))),
        status: 400,
        url: 'hello',
      }
      await handleAPI(async () => response)()
    } catch (error) {
      expect(error.message).toBe(getErrorMessage({ code: 'test' }))
    }
  })

  it('handles dedicated number error', async () => {
    try {
      const response = {
        ok: false,
        text: jest.fn(() => Promise.resolve()),
        status: 409,
        url: `${Endpoint.NEXTGEN_API_URL}/v1/messaging/numbers/dedicated`,
      }
      await handleAPI(async () => response)()
    } catch (error) {
      expect(error.code).toBe('dedicated-number-conflict')
    }
  })

  it('throws error back straight away if it is an MMError', async () => {
    try {
      await handleAPI(() => {
        throw new MMError({ message: 'test' })
      })()
    } catch (error) {
      expect(error.message).toBe('test')
    }
  })

  it('converts normal error to MMError with proper message and throws back', async () => {
    try {
      await handleAPI(() => {
        throw new Error('test')
      })()
    } catch (error) {
      expect(error.message).toBe(getErrorMessage({ code: 'test' }))
    }
  })

  it('returns raw response', async () => {
    const response = {
      ok: true,
      blob: jest.fn(() => Promise.resolve('parsed_ok')),
    }
    fetch.mockImplementation(() => new Promise((resolve) => resolve(response)))
    const result = await handleAPI(() => response, { parseResponse: false })()
    expect(result).toEqual(response)
  })

  it('handles dedicated number error with raw response', async () => {
    try {
      const response = {
        ok: false,
        text: jest.fn(() => Promise.resolve()),
        status: 409,
        url: `${Endpoint.NEXTGEN_API_URL}/v1/messaging/numbers/dedicated`,
      }
      await handleAPI(async () => response, { parseResponse: false })()
    } catch (error) {
      expect(error.code).toBe('dedicated-number-conflict')
    }
  })

  describe('responseType is blob', () => {
    it('returns both raw and blob response if call is success', async () => {
      const response = {
        ok: true,
        blob: jest.fn(() => Promise.resolve('parsed_ok')),
      }
      fetch.mockImplementation(() => new Promise((resolve) => resolve(response)))
      const result = await handleAPI(() => response)({ responseType: 'blob' })
      expect(result).toEqual({
        parsed: 'parsed_ok',
        raw: response,
      })
    })

    it('returns parsed response as text if call is not success', async () => {
      const response = {
        ok: false,
        blob: jest.fn(() => Promise.resolve('parsed_ok')),
        text: jest.fn(() => Promise.resolve('error')),
      }
      fetch.mockImplementation(() => new Promise((resolve) => resolve(response)))
      try {
        await handleAPI(() => response)({ responseType: 'blob' })
      } catch (error) {
        expect(error.name).toEqual('MMError')
      }
    })
  })

  describe('generateErrorResponse', () => {
    it('should return default repsonse', () => {
      expect(generateErrorResponse({ url: 'hello' }, {}, 'defaultResponse')).toBe('defaultResponse')
    })

    it('should return default repsonse with context option', () => {
      const defaultResponse = {
        type: 'default',
      }
      expect(generateErrorResponse({ url: 'hello' }, {}, defaultResponse, 'context')).toEqual([defaultResponse])
    })

    it('should return custom reponse', () => {
      expect(generateErrorResponse({ url: 'hello', status: 200 }, {
        hello: (response) => response.status,
      }, 'defaultResponse')).toBe(200)
    })
  })
})
