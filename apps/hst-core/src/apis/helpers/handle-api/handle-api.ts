import _get from 'lodash/get'
import _omit from 'lodash/omit'
import _isPlainObject from 'lodash/isPlainObject'
import { getErrorMessage, SubmissionError, MMError, errorConverter } from 'helpers'

import dedicatedNumberErrorConverter from '../../../../../../libs/helpers/src/mm-error/dedicated-number-error-converter'
import Endpoint from '../../../helpers/endpoint'

export const generateErrorResponse = (response, endPointMap, defaultResponse, context) => {
  const matchedUrl = !context && Object.keys(endPointMap).find((url) => response.url.match(url))
  const fallbackResponse = context && _isPlainObject(defaultResponse) ? [defaultResponse] : defaultResponse

  return (matchedUrl && endPointMap[matchedUrl](response)) || fallbackResponse
}

/**
 * pars<PERSON><PERSON> retrieves the form field name from the key. the field name is always the last string after the last .
 * before the last [
 */
export const parseKey = (key) => {
  if (!key) return key
  const pieces = key.split(/[.]+/)
  const lastElement = pieces[pieces.length - 1]
  const ret = lastElement.lastIndexOf('[') !== -1 ? lastElement.substring(0, lastElement.lastIndexOf('[')) : lastElement
  return ret
}
export default (func, options = {}) => async (args) => {
  const { parseResponse = true, context = '' } = options
  let response
  let status
  const formContext = context || _get(args, 'formContext', 'default')
  try {
    const params = formContext !== 'default' && !context ? _omit(args, 'formContext') : args
    response = await func(params)
    status = response.status ? response.status : undefined
    let parsedResponse = response
    if (_get(args, 'responseType') === 'blob') {
      if (response.ok) {
        parsedResponse = await response.blob()
        return {
          parsed: parsedResponse,
          raw: response,
        }
      }
    }
    if (parseResponse || (!parseResponse && response.status >= 400)) {
      parsedResponse = await response.text().then((text) => {
        try {
          return JSON.parse(text)
        } catch (e) {
          return {}
        }
      })
    }

    if (!response.ok) {
      const errorResponse = generateErrorResponse(
        response,
        {
          [`${Endpoint.NEXTGEN_API_URL}/v1/messaging/numbers/dedicated`]: dedicatedNumberErrorConverter,
          [Endpoint.NEXTGEN_API_URL]: errorConverter,
        },
        parsedResponse,
        context,
      )

      if (Array.isArray(errorResponse)) {
        const fatalError = errorResponse.find((error) => error.fatal)
        if (fatalError) {
          throw new MMError({
            message: getErrorMessage({ code: fatalError.code, formContext }),
            code: fatalError.code,
            fatal: true,
            status,
          })
        } else if (errorResponse.some((error) => /validation|exists/g.test(error.code))) {
          const manualSyncErr = errorResponse.find((item) => item.error)
          if (manualSyncErr) {
            throw new MMError({
              message: manualSyncErr.error,
              code: manualSyncErr.code,
              fatal: false,
            })
          } else {
            throw new SubmissionError(Object.assign({}, ...errorResponse.map((error) => ({
              [parseKey(error.key)]: getErrorMessage({ code: error.code, key: parseKey(error.key), formContext }),
            }))))
          }
        } else if (errorResponse.some((error) => /dedicated-number/g.test(error.code))) {
          throw new MMError({
            message: getErrorMessage({ code: errorResponse[0].code, formContext }),
            code: errorResponse[0].code,
            fatal: false,
          })
        }
      }

      const errorObj = _get(errorResponse, '[0]', {})
      throw new MMError({
        originalMessage: errorResponse.message,
        message: getErrorMessage({ code: errorObj.code, formContext }),
        code: errorObj.code,
        fatal: false,
        mfaToken: _get(parsedResponse, '[0].mfa_token'),
        status,
      })
    }
    return await parsedResponse
  } catch (error) {
    if (error instanceof SubmissionError || error instanceof MMError) {
      throw error
    }

    throw new MMError({
      message: getErrorMessage({ code: error.message, formContext }),
      status,
    })
  }
}
