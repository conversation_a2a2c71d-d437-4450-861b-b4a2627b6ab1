import mime from 'mime-types'
import 'regenerator-runtime/runtime'
import storageAPI from './storage'
import helpers from '../utils/helpers'

jest.mock('../utils/helpers', () => jest.fn())

describe('storage-api', () => {
  describe('downloadFileAsBlob', () => {
    beforeEach(() => {
      const response = {
        ok: true,
        blob: jest.fn(() => Promise.resolve('ok')),
        headers: new Map()
          .set('Content-Type', 'application/json; utf-8')
          .set('Content-Disposition', 'attachment; filename="random_name.csv"'),
      }
      helpers.mockImplementation(() => jest.fn().mockResolvedValueOnce(new Promise((resolve) => resolve(response))))
    })

    it('should return blob with input type and fileName parsed from content disposition header', async () => {
      const blob = await storageAPI.downloadFileAsBlob({
        url: 'url',
        type: 'forced_type',
      })
      expect(blob).toEqual({
        blob: new Blob(['ok'], { type: 'forced_type' }),
        contentType: 'forced_type',
        fileName: 'random_name.csv',
      })
    })

    it('can work without URL prefix and credentials', async () => {
      const blob = await storageAPI.downloadFileAsBlob({
        url: 'url',
        type: 'forced_type',
        withPrefix: false,
        withCredentials: false,
      })
      expect(blob).toEqual({
        blob: new Blob(['ok'], { type: 'forced_type' }),
        contentType: 'forced_type',
        fileName: 'random_name.csv',
      })
    })

    it('should return blob with type from content-type header when input type is missing', async () => {
      const blob = await storageAPI.downloadFileAsBlob({
        responseType: 'blob',
        url: 'url',
        type: undefined,
      })
      expect(blob).toEqual({
        blob: new Blob(['ok'], { type: 'application/json' }),
        contentType: 'application/json',
        fileName: 'random_name.csv',
      })
    })

    it('should return filename with long Content-Disposition', async () => {
      mime.extension = jest.fn(() => 'exe')
      const response = {
        ok: true,
        blob: jest.fn(() => Promise.resolve('ok')),
        headers: new Map()
          .set('Content-Type', 'application/json; utf-8')
          .set('Content-Disposition', 'attachment; another="hello.world"; wrong header; filename="random_name.csv"'),
      }
      helpers.mockImplementation(() => jest.fn().mockResolvedValueOnce(new Promise((resolve) => resolve(response))))
      const blob = await storageAPI.downloadFileAsBlob({
        responseType: 'blob',
        url: 'url',
        type: 'application/json',
      })
      expect(blob).toEqual({
        blob: new Blob(['ok'], { type: 'application/json' }),
        contentType: 'application/json',
        fileName: 'random_name.csv',
      })
    })

    it('should return filename with Content-Disposition no double quote', async () => {
      mime.extension = jest.fn(() => 'exe')
      const response = {
        ok: true,
        blob: jest.fn(() => Promise.resolve('ok')),
        headers: new Map()
          .set('Content-Type', 'application/json; utf-8')
          .set('Content-Disposition', 'attachment; another="hello.world"; wrong header; filename=random_name.csv'),
      }
      helpers.mockImplementation(() => jest.fn().mockResolvedValueOnce(new Promise((resolve) => resolve(response))))
      const blob = await storageAPI.downloadFileAsBlob({
        responseType: 'blob',
        url: 'url',
        type: 'application/json',
      })
      expect(blob).toEqual({
        blob: new Blob(['ok'], { type: 'application/json' }),
        contentType: 'application/json',
        fileName: 'random_name.csv',
      })
    })

    it('should return default fileName if content-disposition header is not formatted properly', async () => {
      mime.extension = jest.fn(() => 'exe')
      const response = {
        ok: true,
        blob: jest.fn(() => Promise.resolve('ok')),
        headers: new Map()
          .set('Content-Type', 'application/json; utf-8')
          .set('Content-Disposition', undefined),
      }
      helpers.mockImplementation(() => jest.fn().mockResolvedValueOnce(new Promise((resolve) => resolve(response))))
      const blob = await storageAPI.downloadFileAsBlob({
        responseType: 'blob',
        url: 'url',
        type: 'type',
      })
      expect(blob.fileName).toBe('file.exe')

      response.headers.set('Content-Disposition', 'single header value')
      helpers.mockImplementation(() => jest.fn().mockResolvedValueOnce(new Promise((resolve) => resolve(response))))
      const blob2 = await storageAPI.downloadFileAsBlob({
        responseType: 'blob',
        url: 'url',
        type: 'type',
      })
      expect(blob2.fileName).toBe('file.exe')
    })

    it('should throw error if request fails', async () => {
      const response = {
        ok: false,
      }
      helpers.mockImplementation(() => jest.fn().mockResolvedValueOnce(new Promise((resolve) => resolve(response))))
      try {
        await storageAPI.downloadFileAsBlob({
          url: 'url',
          responseType: 'blob',
        })
      } catch (e) {
        expect(e.name).toEqual('MMError')
      }
    })
  })
})
