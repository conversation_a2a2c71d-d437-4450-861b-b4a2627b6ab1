import mime from 'mime-types'
import _replace from 'lodash/replace'
import _trim from 'lodash/trim'
import { handleAPI } from '@sinch-smb/dev-utils'
import fetchInstance from '../utils/helpers'
import Endpoint from '../../helpers/endpoint'

export default {
  downloadFileAsBlob: async (
    {
      url = '',
      responseType = 'blob',
      type = 'application/json',
      withPrefix = true,
      withCredentials = true,
    },
  ) => {
    const func = handleAPI(async () => (
      fetchInstance()(`${withPrefix ? `${Endpoint.API_URL}/` : ''}${url}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: withCredentials ? 'include' : 'omit',
      })
    ))
    // eslint-disable-next-line no-useless-catch
    try {
      const { parsed: blob, raw } = await func({ url, responseType, type })
      const contentType = type || raw.headers.get('Content-Type').split(';')[0]
      const contentDisposition = raw.headers.get('Content-Disposition')
      const extension = mime.extension(contentType)
      let fileName
      // parse fileName from disposition header
      // eg: attachment; filename=Hub Demonstration - group 1 - 2019-03-19 17-01-39.csv
      // or: inline; filename=INV00522512_100001070_02112019.pdf
      try {
        contentDisposition.split(';').find((property) => {
          const [variable, value] = property.split('=')

          if (_trim(variable) === 'filename') {
            fileName = _replace(value, /^"(.*)"$/, '$1')

            return true
          }

          return false
        })
      } catch (e) {
        fileName = undefined
      }
      return {
        blob: new Blob([blob], { type: contentType }),
        fileName: fileName || `file.${extension}`,
        contentType,
      }
    } catch (error) {
      throw error
    }
  },
}
