import devUtils from '@sinch-smb/dev-utils'
import { fetch } from 'helpers'
import fetchInstance from './helpers'

jest.mock('@sinch-smb/dev-utils', () => ({
  jwt: { getInstance: jest.fn() },
  handleAPI: jest.fn()
}))

describe('Utils: fetchInstance', () => {
  it('should return fetch instance', () => {
    devUtils.jwt.getInstance = () => 'instance'
    const spy = jest.spyOn(fetch, 'getInstance')
    fetchInstance()
    expect(spy).toHaveBeenCalled()
  })
})
