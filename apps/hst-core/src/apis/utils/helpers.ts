import { getInstance, fetch } from 'helpers'

import Endpoint from '../../helpers/endpoint'

const fetchInstance = () => {
  const configuration = {
    authUrl: `${Endpoint.API_URL}/iam/v1/jwt/auth`,
    revokeUrl: `${Endpoint.API_URL}/iam/v1/jwt/auth/revoke`,
    azureToMwAccessTokenUrl: `${Endpoint.SUPPORT_V2_API_URL}/v2.1/support/jwt/access-token`,
  }
  const jwtInstance = getInstance(configuration)
  return fetch?.getInstance(jwtInstance)
}

export default fetchInstance
