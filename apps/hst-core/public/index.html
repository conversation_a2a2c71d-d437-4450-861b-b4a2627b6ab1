<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="robots" content="noindex">
    <!-- <base
      href='<%= htmlWebpackPlugin.options.base %>'
      target="_blank"
    > -->

    <base
      href='/'
      target="_blank"
    >
    <!-- <base href='<%= htmlWebpackPlugin.options.base %>' target="_blank"> -->
    <link href="https://web-dev-syd-assets.s3-ap-southeast-2.amazonaws.com/packages/latest/vendor/antd.4.5.3.css" rel="stylesheet">
    <style>
      @font-face {
        font-family: Sinch Icons Zero To D;
        src: url(https://d2vu40klajma73.cloudfront.net/sinch-icon-zeroToD.woff2)
          format('woff2');
      }
      @font-face {
        font-family: Sinch Icons E To O;
        src: url(https://d2vu40klajma73.cloudfront.net/sinch-icon-eToO.woff2)
          format('woff2');
      }
      @font-face {
        font-family: Sinch Icons P To Z;
        src: url(https://d2vu40klajma73.cloudfront.net/sinch-icon-pToZ.woff2)
          format('woff2');
      }
    </style>
    <script>
      const vendors = [
        {
          name: '2degrees',
          domains: [
            'localhost:3015/2degrees',
            'syd.support.saas.sinch.com/2degrees',
            'syd.stg.support.saas.sinch.com/2degrees',
            'syd.qa.support.saas.sinch.com/2degrees',
          ],
          themeName: '2degrees.css',
          title: '2Degrees',
        }, {
          name: 'directsms',
          domains: [
            'localhost:3015/directsms',
            'syd.support.saas.sinch.com/directsms',
            'syd.stg.support.saas.sinch.com/directsms',
            'syd.qa.support.saas.sinch.com/directsms',
          ],
          themeName: 'directSMS.css',
          title: 'directSMS',
          label: 'directSMS',
        }, {
          name: 'messagemedia',
          themeName: 'mm.css',
          domains: [
            'localhost:3015/messagemedia',
            'syd.support.saas.sinch.com/messagemedia',
            'syd.stg.support.saas.sinch.com/messagemedia',
            'syd.qa.support.saas.sinch.com/messagemedia',
          ],
          title: 'MessageMedia Hub',
          label: 'mm',
        }, {
          name: 'vodanz',
          themeName: 'vodanz.css',
          domains: [
            'localhost:3015/onenz',
            'syd.support.saas.sinch.com/onenz',
            'syd.stg.support.saas.sinch.com/onenz',
            'syd.qa.support.saas.sinch.com/onenz',
          ],
          title: 'One MultiTXT',
        }, {
          name: 'messagenet',
          domains: [
            'localhost:3015/messagenet',
            'syd.support.saas.sinch.com/messagenet',
            'syd.stg.support.saas.sinch.com/messagenet',
            'syd.qa.support.saas.sinch.com/messagenet',
          ],
          themeName: 'messagenet.css',
          title: 'MessageNet',
        }, {
          name: 'smsbroadcast',
          domains: [
            'localhost:3015/smsbroadcast',
            'syd.support.saas.sinch.com/smsbroadcast',
            'syd.stg.support.saas.sinch.com/smsbroadcast',
            'syd.qa.support.saas.sinch.com/smsbroadcast',
          ],
          themeName: 'smsb.css',
          title: 'SMS Broadcast',
          label: 'smsb',
        }, {
          name: 'streetdata',
          domains: [
            'localhost:3015/streetdata',
            'syd.support.saas.sinch.com/streetdata',
            'syd.stg.support.saas.sinch.com/streetdata',
            'syd.qa.support.saas.sinch.com/streetdata',
          ],
          themeName: 'streetdata.css',
          title: 'Streetdata',
        }, {
          name: 'wholesalesms',
          domains: [
            'localhost:3015/wholesalesms',
            'syd.support.saas.sinch.com/wholesalesms',
            'syd.stg.support.saas.sinch.com/wholesalesms',
            'syd.qa.support.saas.sinch.com/wholesalesms',
          ],
          themeName: 'wholesale.css',
          title: 'WholesaleSMS',
          label: 'wholesale',
        }, {
          name: 'mobipost',
          domains: [
            'localhost:3015/mobipost',
            'syd.support.saas.sinch.com/mobipost',
            'syd.stg.support.saas.sinch.com/mobipost',
            'syd.qa.support.saas.sinch.com/mobipost',
          ],
          themeName: 'mobipost.css',
          title: 'Mobipost',
        }, {
          name: 'smscentral',
          domains: [
            'localhost:3015/smscentral',
            'syd.support.saas.sinch.com/smscentral',
            'syd.stg.support.saas.sinch.com/smscentral',
            'syd.qa.support.saas.sinch.com/smscentral',
          ],
          themeName: 'smscentral.css',
          title: 'SMSCentral',
        }, {
          name: 'bulletin',
          domains: [
            'localhost:3015/bulletin',
            'syd.support.saas.sinch.com/bulletin',
            'syd.stg.support.saas.sinch.com/bulletin',
            'syd.qa.support.saas.sinch.com/bulletin',
          ],
          themeName: 'bulletin.css',
          title: 'Bulletin',
        }, {
          name: 'etxt',
          domains: [
            'localhost:3015/etxt',
            'syd.support.saas.sinch.com/etxt',
            'syd.stg.support.saas.sinch.com/etxt',
            'syd.qa.support.saas.sinch.com/etxt',
          ],
          themeName: 'etxt.css',
          title: 'eTXT',
        },
        {
          name: 'tpgtelecom',
          domains: [
            'localhost:3015/tpgtelecom',
            'syd.support.saas.sinch.com/tpgtelecom',
            'syd.stg.support.saas.sinch.com/tpgtelecom',
            'syd.qa.support.saas.sinch.com/tpgtelecom',
          ],
          themeName: 'tpg.css',
          title: 'TPG Telecom Messaging Hub',
        },
        // pdx airport
        {
          name: 'simpletexting',
          domains: [
            'localhost:3015/simpletexting',
            'dub.support.saas.sinch.com/simpletexting',
            'dub.stg.support.saas.sinch.com/simpletexting',
            'dub.qa.support.saas.sinch.com/simpletexting',
          ],
          themeName: 'simpletexting.css',
          title: 'Simple Texting Messaging Hub',
        },
        // dub airport
        {
          name: 'sincheu',
          domains: [
            'localhost:3015/sincheu',
            'dub.support.saas.sinch.com/sincheu',
            'dub.stg.support.saas.sinch.com/sincheu',
            'dub.qa.support.saas.sinch.com/sincheu',
          ],
          themeName: 'mm.css',
          title: 'Sinch EU Hub',
        },
        // common sincheu
        {
          name: 'sincheu',
          themeName: 'mm.css',
          domains: [
            'dub.support.saas.sinch.com',
            'dub.stg.support.saas.sinch.com',
            'dub.qa.support.saas.sinch.com',
          ],
          title: 'Support Portal',
          label: 'sincheu',
        },
        // default
        {
          name: 'common',
          themeName: 'mm.css',
          domains: [
            'localhost:3015',
            'syd.support.saas.sinch.com',
            'syd.stg.support.saas.sinch.com',
            'syd.qa.support.saas.sinch.com',
          ],
          title: 'Support Portal',
          label: 'common',
        }
      ]

      const href = window && window.location && window.location.href
      const curVendor = vendors.find(vendor => vendor.domains.some(domain => href.includes(domain)))
      const VENDOR_LABEL = curVendor?.label || curVendor?.name|| 'common'
      const THEME_NAME = curVendor?.themeName || 'mm.css'
      const IS_EU_VENDOR =  window.location.host.startsWith('dub.')
      document.title = curVendor?.title || 'MessageMedia Hub'
    </script>
  </head>
  <body>
    <div id="root"></div>
  </body>
</html>
