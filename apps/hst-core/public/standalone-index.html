<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <base href='<%= htmlWebpackPlugin.options.base %>' target="_blank">
    <script>
      const vendors = [
        // syds airport
        {
          name: '2degrees',
          hostName: '2degrees',
          domains: ['2degrees.localhost','2degrees.qa.messagemedia.com', '2degrees.stg.messagemedia.com', 'app.grouptext.2degrees.nz'],
          themeName: '2degrees.css',
          title: '2Degrees',
          config: '2degrees.json',
        }, {
          name: 'directsms',
          hostName: 'direct',
          domains: ['directsms.localhost','directsms.qa.messagemedia.com', 'directsms.stg.messagemedia.com', 'my.directsms.com.au'],
          themeName: 'directSMS.css',
          title: 'directSMS',
          label: 'directSMS',
          config: 'directsms.json',
        }, {
          name: 'messagemedia',
          themeName: 'mm.css',
          hostName: 'hub',
          domains: ['hub.localhost','hub.qa.messagemedia.com', 'hub.stg.messagemedia.com', 'hub.messagemedia.com'],
          title: 'MessageMedia Hub',
          label: 'mm',
          config: 'mm.json',
        }, {
          name: 'vodanz',
          themeName: 'vodanz.css',
          hostName: 'multitxt',
          domains: ['vodanz.localhost','vodanz.syd.mmgp.frontend.qa.mmd.zone', 'vodanz.syd.mmgp.frontend.stg.mmd.zone', 'multitxt.vodafone.co.nz', 'app.multitxt.one.nz'],
          title: 'One MultiTXT',
          config: 'vodanz.json',
        }, {
          name: 'messagenet',
          hostName: 'messagenet',
          domains: ['messagenet.localhost','messagenet.qa.messagemedia.com', 'messagenet.stg.messagemedia.com', 'app.messagenet.com.au', 'netsms.com.au'],
          themeName: 'messagenet.css',
          title: 'MessageNet',
          config: 'messagenet.json',
        }, {
          name: 'smsbroadcast',
          hostName: 'smsbroadcast',
          domains: ['smsbroadcast.localhost','smsbroadcast.qa.messagemedia.com', 'smsbroadcast.stg.messagemedia.com', 'app.smsbroadcast.com.au'],
          themeName: 'smsb.css',
          title: 'SMS Broadcast',
          label: 'smsb',
          config: 'smsb.json',
        }, {
          name: 'streetdata',
          hostName: 'street',
          domains: ['streetdata.localhost','streetdata.qa.messagemedia.com', 'streetdata.stg.messagemedia.com', 'app.streetdata.com.au'],
          themeName: 'streetdata.css',
          title: 'Streetdata',
          config: 'streetdata.json',
        }, {
          name: 'wholesalesms',
          hostName: 'wholesalesms',
          domains: ['wholesalesms.localhost','wholesalesms.qa.messagemedia.com', 'wholesalesms.stg.messagemedia.com', 'app.wholesalesms.com.au'],
          themeName: 'wholesale.css',
          title: 'WholesaleSMS',
          label: 'wholesale',
          config: 'wholesale.json',
        }, {
          name: 'mobipost',
          hostName: 'mobipost',
          domains: ['mobipost.localhost','mobipost.qa.messagemedia.com', 'mobipost.stg.messagemedia.com', 'app.mobipost.com.au'],
          themeName: 'mobipost.css',
          title: 'Mobipost',
          config: 'mobipost.json',
        }, {
          name: 'smscentral',
          hostName: 'smscentral',
          domains: ['smscentral.localhost','smscentral.qa.messagemedia.com', 'smscentral.stg.messagemedia.com', 'my.smscentral.com.au'],
          themeName: 'smscentral.css',
          title: 'SMSCentral',
          config: 'smscentral.json',
        }, {
          name: 'bulletin',
          hostName: 'bulletin',
          domains: ['bulletin.localhost','bulletin.qa.messagemedia.com', 'bulletin.stg.messagemedia.com', 'bulletin.messagemedia.com'],
          themeName: 'bulletin.css',
          title: 'Bulletin',
          config: 'bulletin.json',
        }, {
          name: 'etxt',
          hostName: 'etxt',
          domains: ['etxt.localhost','etxt.qa.messagemedia.com', 'etxt.stg.messagemedia.com', 'etxtservice.co.nz'],
          themeName: 'etxt.css',
          title: 'eTXT',
          config: 'etxt.json',
        },
        {
          name: 'tpgtelecom',
          hostName: 'tpgtelecom',
          domains: ['tpgtelecom.localhost','tpgtelecom.qa.messagemedia.com', 'tpgtelecom.stg.messagemedia.com', 'messaging.tpgtelecom.com.au'],
          themeName: 'tpg.css',
          title: 'TPG Telecom Messaging Hub',
          config: 'tpg.json',
        },
        // pdx airport
        {
          name: 'simpletexting',
          hostName: 'simpletexting',
          domains: ['simpletexting.localhost','simpletexting.qa.messagemedia.com', 'simpletexting.stg.messagemedia.com', 'app4.simpletexting.com'],
          themeName: 'simpletexting.css',
          title: 'Simple Texting Messaging Hub',
        },
        // default
        {
          name: 'index',
          themeName: 'mm.css',
          hostName: 'hub',
          domains: ['hub.localhost','hub.qa.messagemedia.com', 'hub.stg.messagemedia.com', 'hub.messagemedia.com'],
          title: 'MessageMedia Hub',
          label: 'mm',
          config: 'mm.json',
        }
      ]

      const hostname = window && window.location && window.location.hostname
      const curVendor = vendors.find(vendor => vendor.domains.includes(hostname))
      const VENDOR_LABEL = curVendor?.label || curVendor?.name|| 'mm'
      const THEME_NAME = curVendor?.themeName || 'mm.css'
      document.title = curVendor?.title || 'MessageMedia Hub'
    </script>
  </head>
  <body>
    <div id="root"></div>
  </body>
</html>
