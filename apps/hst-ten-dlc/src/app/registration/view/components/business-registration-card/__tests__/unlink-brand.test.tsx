/* eslint-disable @nx/enforce-module-boundaries */
import userEvent from '@testing-library/user-event';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import { controlledPromise } from 'apps/hst-ten-dlc/src/testUtils/controlled-promise';
import { getMockedApiHook } from 'apps/hst-ten-dlc/src/api/__tests__/mocks/mocked-hook';
import { useTenDlcFetch } from 'apps/hst-ten-dlc/src/api/use-ten-dlc-fetch';
import { act, render } from '@testing-library/react';
import { BusinessRegistrationCard } from 'apps/hst-ten-dlc/src/app/registration/view/components/business-registration-card/business-registration-card';
import { useTenDlcBrandIndustries } from 'apps/hst-ten-dlc/src/api/use-ten-dlc-brand-industries';
import { TenDlcBrandIndustriesResult } from 'apps/hst-core/src/apis/v3/ten-dlc/endpoints/industries';
import { ApplicationData } from 'apps/hst-core/src/apis/v3/ten-dlc/endpoints/application';

import {
  useTenDlcApplication,
  useTenDlcSaveApplication,
} from 'apps/hst-ten-dlc/src/api/use-ten-dlc-application';
import { TenDlcBrand } from 'apps/hst-core/src/apis/v3/ten-dlc/types';
import { useTenDlcBrand } from 'apps/hst-ten-dlc/src/api/use-ten-dlc-brand';
import { FetchBrandLinkAccountsResult } from 'apps/hst-core/src/apis/v3/ten-dlc/endpoints/brand';
import { testUnlinkBrandPageObject } from './components/test-unlink-brand';
import { useTenDlcBrandUpdate } from '../../../../../../api/use-ten-dlc-brand-update';
import { createTenDlcBrandFixture } from '../../../../../../api/__tests__/fixtures/brand';
import {
  useTenDlcBrandUnlink,
  useTenDlcBrandLink,
  useTenDlcFetchBrandLinkAccounts,
} from '../../../../../../api/use-ten-dlc-brand-link';

jest.mock('../../../../../../api/use-ten-dlc-brand', () => ({
  useTenDlcBrand: jest.fn(),
}));
jest.mock('../../../../../../api/use-ten-dlc-brand-update.ts', () => ({
  useTenDlcBrandUpdate: jest.fn(),
}));
jest.mock('../../../../../../api/use-ten-dlc-brand-industries.ts', () => ({
  useTenDlcBrandIndustries: jest.fn(),
}));
jest.mock('../../../../../../api/use-ten-dlc-application.ts', () => ({
  useTenDlcApplication: jest.fn(),
  useTenDlcSaveApplication: jest.fn(),
}));
jest.mock('../../../../../../api/use-ten-dlc-brand-link.ts', () => ({
  useTenDlcBrandLink: jest.fn(),
  useTenDlcFetchBrandLinkAccounts: jest.fn(),
  useTenDlcBrandUnlink: jest.fn(),
}));

jest.mock('../../../../../../components/reactNectary/toast');
jest.mock('../../../../../../components/reactNectary/toast-manager');

async function setup() {
  const brandUpdateApiPromise = controlledPromise<void>();
  const brandUpdateApiFn = jest.fn(() => brandUpdateApiPromise);
  getMockedApiHook(useTenDlcBrandUpdate).mockImplementation(() =>
    useTenDlcFetch(brandUpdateApiFn)
  );

  const loadBrandIndustriesPromise =
    controlledPromise<TenDlcBrandIndustriesResult>();
  const loadBrandIndustriesApiFn = jest.fn(() => loadBrandIndustriesPromise);
  getMockedApiHook(useTenDlcBrandIndustries).mockImplementation(() =>
    useTenDlcFetch(loadBrandIndustriesApiFn)
  );

  const fetchApplicationPromise = controlledPromise<ApplicationData>();
  const fetchApplicationApiFn = jest.fn(() => fetchApplicationPromise);
  getMockedApiHook(useTenDlcApplication).mockImplementation(() =>
    useTenDlcFetch(fetchApplicationApiFn)
  );

  const saveApplicationPromise = controlledPromise<ApplicationData>();
  const saveApplicationApiFn = jest.fn(() => saveApplicationPromise);
  getMockedApiHook(useTenDlcSaveApplication).mockImplementation(() =>
    useTenDlcFetch(saveApplicationApiFn)
  );

  const unlinkBrandPromise = controlledPromise<null>();
  const unlinkBrandApiFn = jest.fn(() => unlinkBrandPromise);
  getMockedApiHook(useTenDlcBrandUnlink).mockImplementation(() =>
    useTenDlcFetch(unlinkBrandApiFn)
  );

  const linkBrandPromise = controlledPromise<null>();
  const linkBrandApiFn = jest.fn(() => linkBrandPromise);
  getMockedApiHook(useTenDlcBrandLink).mockImplementation(() =>
    useTenDlcFetch(linkBrandApiFn)
  );

  const fetchLinkBrandAccountsPromise =
    controlledPromise<FetchBrandLinkAccountsResult>();
  const fetchLinkBrandAccountsApiFn = jest.fn(
    () => fetchLinkBrandAccountsPromise
  );
  getMockedApiHook(useTenDlcFetchBrandLinkAccounts).mockImplementation(() =>
    useTenDlcFetch(fetchLinkBrandAccountsApiFn)
  );

  const getBrandPromise = controlledPromise<TenDlcBrand | null>();
  const getBrandApiFn = jest.fn(() => getBrandPromise);
  getMockedApiHook(useTenDlcBrand).mockImplementation(() =>
    useTenDlcFetch(getBrandApiFn)
  );

  const { rerender } = render(
    <BrowserRouter>
      <BusinessRegistrationCard
        permissions={{
          'account.senderid.verification': {
            isEditable: true,
            isVisible: true,
          },
        }}
      />
    </BrowserRouter>
  );

  const pageObject = testUnlinkBrandPageObject();

  return {
    pageObject,
    rerender,
    getBrandPromise,
    getBrandApiFn,
    unlinkBrandPromise,
    unlinkBrandApiFn,
  };
}

describe('Unlink brand', () => {
  it('Unlink successful', async () => {
    const {
      pageObject,
      getBrandPromise,
      unlinkBrandPromise,
      unlinkBrandApiFn,
    } = await setup();

    await getBrandPromise.resolve({
      ...createTenDlcBrandFixture(),
      inherited: true,
      sourceCode: 'XXX1',
    });

    await userEvent.click(pageObject.buttons.openDialog.get());
    expect(pageObject.dialog.get()).toHaveAttribute('open', 'true');

    await userEvent.click(pageObject.buttons.confirm.get());
    expect(pageObject.buttons.confirm.sinchButton.isLoading()).toBeTruthy();

    await unlinkBrandPromise.resolve(null);
    expect(unlinkBrandApiFn).toHaveBeenCalledTimes(1);
  });

  it('Handle common error request', async () => {
    const {
      pageObject,
      unlinkBrandPromise,
      unlinkBrandApiFn,
      getBrandPromise,
    } = await setup();

    await getBrandPromise.resolve({
      ...createTenDlcBrandFixture(),
      inherited: true,
      sourceCode: 'XXX1',
    });

    await userEvent.click(pageObject.buttons.confirm.get());
    expect(pageObject.buttons.confirm.sinchButton.isLoading()).toBeTruthy();

    await unlinkBrandPromise.reject(new Error('test'));

    expect(unlinkBrandApiFn).toHaveBeenCalledTimes(1);
    expect(pageObject.alert.commonError.get()).toBeInTheDocument();
  });

  it("Disable buttons if a user does not have 'write' permissions", async () => {
    const { pageObject, rerender, getBrandPromise } = await setup();
    await getBrandPromise.resolve({
      ...createTenDlcBrandFixture(),
      inherited: true,
      sourceCode: 'XXX1',
    });

    act(() => {
      rerender(
        <BrowserRouter>
          <BusinessRegistrationCard
            permissions={{
              'account.senderid.verification': {
                isEditable: false,
                isVisible: true,
              },
            }}
          />
        </BrowserRouter>
      );
    });

    expect(pageObject.buttons.openDialog.get()).toHaveAttribute(
      'disabled',
      'true'
    );
  });
});
