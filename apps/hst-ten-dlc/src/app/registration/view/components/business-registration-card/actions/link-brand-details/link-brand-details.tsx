import { useState } from 'react';
import { Link } from 'react-router-dom';

import {
  Button,
  Dialog,
  InlineAlert,
} from '../../../../../../../components/reactNectary';
import { useTenDlcBrandUnlink } from '../../../../../../../api/use-ten-dlc-brand-link';
import './link-brand-details.module.less';

type Props = {
  vendorId: string;
  accountId: string;
  parentAccountId: string;
  isEditable: boolean;
  onUnlinkBrand: () => void;
};

export const LinkBrandDetails = ({
  vendorId,
  accountId,
  parentAccountId,
  isEditable,
  onUnlinkBrand,
}: Props) => {
  const [openDialog, setOpenDialog] = useState(false);

  const [brandUnlinkResult, runBrandUnlink] = useTenDlcBrandUnlink();

  const handleUnlinkBrand = () => {
    runBrandUnlink({
      vendorId,
      accountId,
    }).then((result) => {
      if (result.isSuccess) {
        setOpenDialog(false);
        onUnlinkBrand();
      }
    });
  };

  const handleOpenDialog = () => {
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  return (
    <>
      <Link
        to={`/accounts/${vendorId.toLowerCase()}/${parentAccountId}?tab=10dlc-registration`}
      >
        <Button type="secondary">View brand details</Button>
      </Link>
      <Button
        type="destructive"
        onClick={handleOpenDialog}
        disabled={!isEditable}
      >
        Unlink brand
      </Button>

      <Dialog
        open={openDialog}
        caption="Unlink this brand"
        aria-label="Unlinking brand dialog"
        close-aria-label="Close unlinking brand dialog"
        onClose={handleCloseDialog}
      >
        <div slot="content">
          {brandUnlinkResult.isError && (
            <p data-testid="common-error">
              <InlineAlert
                type="error"
                caption="We couldn't unlink this brand."
              />
            </p>
          )}
          <p>Are you sure you want to unlink this brand and its campaigns?</p>
        </div>

        <Button slot="buttons" type="secondary" onClick={handleCloseDialog}>
          Cancel
        </Button>
        <Button
          slot="buttons"
          type="destructive"
          isLoading={brandUnlinkResult.isLoading}
          disabled={brandUnlinkResult.isLoading}
          onClick={handleUnlinkBrand}
        >
          Yes, unlink this brand
        </Button>
      </Dialog>
    </>
  );
};
