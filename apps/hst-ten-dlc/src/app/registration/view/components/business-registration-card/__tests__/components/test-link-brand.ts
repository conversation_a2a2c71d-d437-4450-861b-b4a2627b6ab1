import { testSelectInputPageObject } from 'apps/hst-ten-dlc/src/components/formik/__tests__/components/test-select-field';
import { testButtonPageObject } from 'apps/hst-ten-dlc/src/components/reactNectary/__tests__/components/test-button';
import { testDialogPageObject } from 'apps/hst-ten-dlc/src/components/reactNectary/__tests__/components/test-dialog';
import { testToastPageObject } from 'apps/hst-ten-dlc/src/components/reactNectary/__tests__/components/test-toast';

export const testLinkBrandPageObject = () => ({
  dialog: testDialogPageObject('Linking brand dialog'),
  fields: {
    parentAccountId: testSelectInputPageObject('Account'),
    campaignIds: testSelectInputPageObject('Campaign IDs'),
  },
  buttons: {
    save: testButtonPageObject('Save'),
    linkBrand: testButtonPageObject('Link a brand'),
  },
  alert: {
    success: testToastPageObject('The account has been linked.'),
    commonError: testToastPageObject('Something went wrong. Try again.'),
    fetchAccountsError: testToastPageObject(
      "We couldn't load accounts. Try again."
    ),
    fetchAccountsWarning: testToastPageObject(
      'You cannot link this account because there are no parent accounts.'
    ),
  },
});
