// eslint-disable-next-line @nx/enforce-module-boundaries
import { FetchBrandLinkAccountsResult } from 'apps/hst-core/src/apis/v3/ten-dlc/endpoints/brand';

type ParentAccountMap = Record<
  string,
  {
    id: string;
    brandId?: string;
    campaignIds: string[];
  }
>;

/**
 * Return map of the parent accounts.
 */
export const parentAccountsMap = (
  accounts?: FetchBrandLinkAccountsResult
): ParentAccountMap => {
  if (!accounts?.resources?.length) {
    return {};
  }

  return accounts.resources.reduce<ParentAccountMap>((acc, obj) => {
    acc[obj.id] = {
      id: obj.id,
      brandId: obj.brandId,
      campaignIds: obj.campaignIds,
    };
    return acc;
  }, {});
};

/**
 * Retutn map of the accounts for select field.
 */
export const parentAccountOptionsMap = (
  parentAccounts: ParentAccountMap
): Record<string, string> =>
  Object.keys(parentAccounts).reduce<Record<string, string>>((acc, id) => {
    acc[id] = id;
    return acc;
  }, {});

/**
 * Retutn map of the account campaigns for select field.
 */
export const parentCampaignOptionsMap = (
  campaignIds?: string[]
): Record<string, string> =>
  campaignIds?.reduce<Record<string, string>>((acc, id) => {
    acc[id] = id;
    return acc;
  }, {}) || {};
