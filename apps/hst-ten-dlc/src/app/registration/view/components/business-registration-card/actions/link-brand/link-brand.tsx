import { useEffect, useMemo, useState } from 'react';
import { Formik, Form, FormikConfig, FormikProps } from 'formik';
import * as Yup from 'yup';
import {
  Button,
  Toast,
  ToastManager,
  Dialog,
  InlineAlert,
  Text,
} from '../../../../../../../components/reactNectary';
import {
  ButtonSubmit,
  InputField,
  SelectField,
} from '../../../../../../../components/formik';

import styles from './link-brand.module.less';
import { linkBrandLabels } from './helpers/form-field-labels';
import {
  useTenDlcBrandLink,
  useTenDlcFetchBrandLinkAccounts,
} from '../../../../../../../api/use-ten-dlc-brand-link';
import {
  parentAccountOptionsMap,
  parentAccountsMap,
  parentCampaignOptionsMap,
} from './helpers/format-data';

type Values = {
  parentAccountId: string;
  brandId?: string;
  campaignIds: string[];
};
type ResetForm = FormikProps<Values>['resetForm'];

type Props = {
  descriptionClass: string;
  vendorId: string;
  accountId: string;
  isEditable: boolean;
  onDone: () => void;
};

export function LinkBrand({
  vendorId,
  accountId,
  isEditable,
  onDone,
  descriptionClass,
}: Props) {
  const [openDialog, setOpenDialog] = useState(false);
  const initialValues: Values = {
    parentAccountId: '',
    brandId: '',
    campaignIds: [],
  };

  const validationSchema = useMemo(
    () =>
      Yup.object().shape({
        parentAccountId: Yup.string()
          .required()
          .label(linkBrandLabels.parentAccountId),
        brandId: Yup.string().label(linkBrandLabels.brandId),
        campaignIds: Yup.array().label(linkBrandLabels.campaignId),
      }),
    []
  );

  const [brandLinkResult, runBrandLink] = useTenDlcBrandLink();
  const [fetchAccountsResult, runFetchAccounts] =
    useTenDlcFetchBrandLinkAccounts();

  const handleChange = () => {
    runFetchAccounts({ vendorId, accountId }).then((response) => {
      if (
        !response.isError &&
        response.data.resources &&
        response.data.resources.length > 0
      ) {
        setOpenDialog(true);
      }
    });
  };

  const handleCancel = (resetForm: ResetForm) => () => {
    setOpenDialog(false);
    resetForm();
  };

  const handleSubmit: FormikConfig<Values>['onSubmit'] = (values, helpers) =>
    runBrandLink({
      vendorId,
      accountId,
      values,
    }).then((result) => {
      if (result.isSuccess) {
        setOpenDialog(false);
        helpers.resetForm();
        onDone();
      }
    });

  const isFetchAccountsError = fetchAccountsResult.isError;
  const isFetchAccountsEmpty =
    fetchAccountsResult.isSuccess &&
    (!fetchAccountsResult.data?.resources ||
      fetchAccountsResult.data?.resources.length === 0);

  const parentAccounts = parentAccountsMap(fetchAccountsResult.data);

  return (
    <>
      <ToastManager>
        <Toast
          type="success"
          text="The account has been linked."
          show={brandLinkResult.isSuccess}
        />
        <Toast
          type="error"
          text="Something went wrong. Try again."
          show={brandLinkResult.isError}
        />
        <Toast
          type="error"
          text="We couldn't load accounts. Try again."
          show={isFetchAccountsError}
        />
        <Toast
          type="warn"
          text="You cannot link this account because there are no parent accounts."
          show={isFetchAccountsEmpty}
        />
      </ToastManager>

      <Formik
        onSubmit={handleSubmit}
        initialValues={initialValues}
        validationSchema={validationSchema}
      >
        {({ values, resetForm, setFieldValue }) => {
          const selectedAccount = values.parentAccountId;
          const selectedData = parentAccounts[selectedAccount] || {};

          // eslint-disable-next-line react-hooks/rules-of-hooks
          useEffect(() => {
            if (selectedData.brandId) {
              setFieldValue('brandId', selectedData.brandId);
            } else {
              setFieldValue('brandId', '');
            }
          }, [selectedData.brandId, setFieldValue]);

          const campaignOptions = parentCampaignOptionsMap(
            selectedData.campaignIds
          );
          let campaignsPlaceholder = 'Select campaigns';
          if (!values.parentAccountId) {
            campaignsPlaceholder = 'Select account above';
          } else if (!selectedData.campaignIds?.length) {
            campaignsPlaceholder = 'No campaigns';
          }

          return (
            <div className={styles.container}>
              <Text class={descriptionClass} type="m">
                Option 02: Link a brand from a related Account.
              </Text>

              <Button
                className={styles.linkBrandButton}
                type="secondary"
                onClick={handleChange}
                disabled={!isEditable}
                isLoading={fetchAccountsResult.isLoading}
              >
                Link a brand
              </Button>

              <Form>
                <Dialog
                  open={openDialog}
                  caption="Link a brand"
                  aria-label="Linking brand dialog"
                  close-aria-label="Close linking brand dialog"
                  onClose={handleCancel(resetForm)}
                >
                  <div className={styles.content} slot="content">
                    <InlineAlert
                      type="warn"
                      caption="Warning"
                      text="You can only link a brand from a related account."
                      isClosable
                    />
                    {values.parentAccountId && !values.brandId ? (
                      <InlineAlert
                        type="error"
                        caption="This account doesn't contain a brand."
                        text="You can link an account only if it contains a brand."
                      />
                    ) : null}

                    <SelectField<Values>
                      name="parentAccountId"
                      label={linkBrandLabels.parentAccountId}
                      disabled={brandLinkResult.isLoading}
                      options={parentAccountOptionsMap(parentAccounts)}
                      className={styles.selectInput}
                      placeholder="Select account"
                    />

                    {values.brandId ? (
                      <InputField<Values>
                        aria-label={linkBrandLabels.brandId}
                        name="brandId"
                        disabled
                        placeholder="Select account above"
                        label={linkBrandLabels.brandId}
                      />
                    ) : null}

                    <SelectField<Values>
                      name="campaignIds"
                      label={linkBrandLabels.campaignId}
                      disabled={
                        brandLinkResult.isLoading ||
                        !values.parentAccountId ||
                        !selectedData.campaignIds.length
                      }
                      options={campaignOptions}
                      className={styles.selectInput}
                      placeholder={campaignsPlaceholder}
                      isMultiple
                    />
                  </div>

                  <Button
                    slot="buttons"
                    type="secondary"
                    onClick={handleCancel(resetForm)}
                  >
                    Cancel
                  </Button>
                  <ButtonSubmit
                    slot="buttons"
                    type="primary"
                    disabled={
                      brandLinkResult.isLoading || !!values.brandId === false
                    }
                    isLoading={brandLinkResult.isLoading}
                  >
                    Save
                  </ButtonSubmit>
                </Dialog>
              </Form>
            </div>
          );
        }}
      </Formik>
    </>
  );
}
