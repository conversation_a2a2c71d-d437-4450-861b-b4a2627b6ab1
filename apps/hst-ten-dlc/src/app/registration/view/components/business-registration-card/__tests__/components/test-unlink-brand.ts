import { screen } from '@testing-library/react';
import { testButtonPageObject } from 'apps/hst-ten-dlc/src/components/reactNectary/__tests__/components/test-button';
import { testDialogPageObject } from 'apps/hst-ten-dlc/src/components/reactNectary/__tests__/components/test-dialog';

export const testUnlinkBrandPageObject = () => ({
  dialog: testDialogPageObject('Unlinking brand dialog'),
  buttons: {
    openDialog: testButtonPageObject('Unlink brand'),
    confirm: testButtonPageObject('Yes, unlink this brand'),
    cancel: testButtonPageObject('Cancel'),
  },
  alert: {
    commonError: {
      get: () => screen.queryByTestId('common-error'),
    },
  },
});
