/* eslint-disable @nx/enforce-module-boundaries */
import userEvent from '@testing-library/user-event';
import { controlledPromise } from 'apps/hst-ten-dlc/src/testUtils/controlled-promise';
import { getMockedApiHook } from 'apps/hst-ten-dlc/src/api/__tests__/mocks/mocked-hook';
import { useTenDlcFetch } from 'apps/hst-ten-dlc/src/api/use-ten-dlc-fetch';
import { act, render } from '@testing-library/react';
import { BusinessRegistrationCard } from 'apps/hst-ten-dlc/src/app/registration/view/components/business-registration-card/business-registration-card';
import { useTenDlcBrandIndustries } from 'apps/hst-ten-dlc/src/api/use-ten-dlc-brand-industries';
import { TenDlcBrandIndustriesResult } from 'apps/hst-core/src/apis/v3/ten-dlc/endpoints/industries';
import { ApplicationData } from 'apps/hst-core/src/apis/v3/ten-dlc/endpoints/application';

import {
  useTenDlcApplication,
  useTenDlcSaveApplication,
} from 'apps/hst-ten-dlc/src/api/use-ten-dlc-application';
import { TenDlcBrand } from 'apps/hst-core/src/apis/v3/ten-dlc/types';
import { useTenDlcBrand } from 'apps/hst-ten-dlc/src/api/use-ten-dlc-brand';
import { FetchBrandLinkAccountsResult } from 'apps/hst-core/src/apis/v3/ten-dlc/endpoints/brand';
import { testLinkBrandPageObject } from './components/test-link-brand';
import { useTenDlcBrandUpdate } from '../../../../../../api/use-ten-dlc-brand-update';
import {
  useTenDlcBrandLink,
  useTenDlcFetchBrandLinkAccounts,
} from '../../../../../../api/use-ten-dlc-brand-link';
import {
  createBrandLinkAccountsFixture,
  createTenDlcBrandFixture,
} from '../../../../../../api/__tests__/fixtures/brand';

jest.mock('../../../../../../api/use-ten-dlc-brand', () => ({
  useTenDlcBrand: jest.fn(),
}));
jest.mock('../../../../../../api/use-ten-dlc-brand-update.ts', () => ({
  useTenDlcBrandUpdate: jest.fn(),
}));
jest.mock('../../../../../../api/use-ten-dlc-brand-industries.ts', () => ({
  useTenDlcBrandIndustries: jest.fn(),
}));
jest.mock('../../../../../../api/use-ten-dlc-application.ts', () => ({
  useTenDlcApplication: jest.fn(),
  useTenDlcSaveApplication: jest.fn(),
}));
jest.mock('../../../../../../api/use-ten-dlc-brand-link.ts', () => ({
  useTenDlcBrandLink: jest.fn(),
  useTenDlcFetchBrandLinkAccounts: jest.fn(),
}));

jest.mock('../../../../../../components/reactNectary/toast');
jest.mock('../../../../../../components/reactNectary/toast-manager');

async function setup() {
  const brandUpdateApiPromise = controlledPromise<void>();
  const brandUpdateApiFn = jest.fn(() => brandUpdateApiPromise);
  getMockedApiHook(useTenDlcBrandUpdate).mockImplementation(() =>
    useTenDlcFetch(brandUpdateApiFn)
  );

  const loadBrandIndustriesPromise =
    controlledPromise<TenDlcBrandIndustriesResult>();
  const loadBrandIndustriesApiFn = jest.fn(() => loadBrandIndustriesPromise);
  getMockedApiHook(useTenDlcBrandIndustries).mockImplementation(() =>
    useTenDlcFetch(loadBrandIndustriesApiFn)
  );

  const fetchApplicationPromise = controlledPromise<ApplicationData>();
  const fetchApplicationApiFn = jest.fn(() => fetchApplicationPromise);
  getMockedApiHook(useTenDlcApplication).mockImplementation(() =>
    useTenDlcFetch(fetchApplicationApiFn)
  );

  const saveApplicationPromise = controlledPromise<ApplicationData>();
  const saveApplicationApiFn = jest.fn(() => saveApplicationPromise);
  getMockedApiHook(useTenDlcSaveApplication).mockImplementation(() =>
    useTenDlcFetch(saveApplicationApiFn)
  );

  const linkBrandPromise = controlledPromise<null>();
  const linkBrandApiFn = jest.fn(() => linkBrandPromise);
  getMockedApiHook(useTenDlcBrandLink).mockImplementation(() =>
    useTenDlcFetch(linkBrandApiFn)
  );

  const fetchLinkBrandAccountsPromise =
    controlledPromise<FetchBrandLinkAccountsResult>();
  const fetchLinkBrandAccountsApiFn = jest.fn(
    () => fetchLinkBrandAccountsPromise
  );
  getMockedApiHook(useTenDlcFetchBrandLinkAccounts).mockImplementation(() =>
    useTenDlcFetch(fetchLinkBrandAccountsApiFn)
  );

  const getBrandPromise = controlledPromise<TenDlcBrand | null>();
  const getBrandApiFn = jest.fn(() => getBrandPromise);
  getMockedApiHook(useTenDlcBrand).mockImplementation(() =>
    useTenDlcFetch(getBrandApiFn)
  );

  const { rerender } = render(
    <BusinessRegistrationCard
      permissions={{
        'account.senderid.verification': {
          isEditable: true,
          isVisible: true,
        },
      }}
    />
  );

  const pageObject = testLinkBrandPageObject();

  return {
    pageObject,
    rerender,
    linkBrandPromise,
    linkBrandApiFn,
    getBrandPromise,
    getBrandApiFn,
    fetchLinkBrandAccountsPromise,
    fetchLinkBrandAccountsApiFn,
  };
}

describe('Link brand', () => {
  it('Fetch accounts error', async () => {
    const {
      pageObject,
      fetchLinkBrandAccountsPromise,
      fetchLinkBrandAccountsApiFn,
    } = await setup();
    await userEvent.click(pageObject.buttons.linkBrand.get());

    await fetchLinkBrandAccountsPromise.reject(new Error('test'));
    expect(fetchLinkBrandAccountsApiFn).toHaveBeenCalledTimes(1);
    expect(pageObject.alert.fetchAccountsError.get()).toBeInTheDocument();
  });

  it('Fetch accounts warning', async () => {
    const {
      pageObject,
      fetchLinkBrandAccountsPromise,
      fetchLinkBrandAccountsApiFn,
    } = await setup();
    await userEvent.click(pageObject.buttons.linkBrand.get());

    await fetchLinkBrandAccountsPromise.resolve({
      resources: [] as unknown as FetchBrandLinkAccountsResult['resources'],
    });
    expect(fetchLinkBrandAccountsApiFn).toHaveBeenCalledTimes(1);
    expect(pageObject.alert.fetchAccountsWarning.get()).toBeInTheDocument();
  });

  it('Link successful', async () => {
    const {
      pageObject,
      getBrandPromise,
      linkBrandPromise,
      linkBrandApiFn,
      fetchLinkBrandAccountsPromise,
    } = await setup();

    await getBrandPromise.resolve(null);
    await fetchLinkBrandAccountsPromise.resolve(
      createBrandLinkAccountsFixture()
    );

    await userEvent.click(pageObject.buttons.linkBrand.get());
    expect(pageObject.dialog.get()).toHaveAttribute('open', 'true');

    act(() => {
      pageObject.fields.parentAccountId.sinchField.selectOption('account_1');
    });

    await userEvent.click(pageObject.buttons.save.get());
    expect(pageObject.buttons.save.sinchButton.isLoading()).toBeTruthy();

    await linkBrandPromise.resolve(null);
    expect(linkBrandApiFn).toHaveBeenCalledTimes(1);
    expect(pageObject.alert.success.get()).toBeInTheDocument();
  });

  it('Handle common error request', async () => {
    const {
      pageObject,
      linkBrandPromise,
      linkBrandApiFn,
      getBrandPromise,
      fetchLinkBrandAccountsPromise,
    } = await setup();

    await getBrandPromise.resolve(null);
    await fetchLinkBrandAccountsPromise.resolve(
      createBrandLinkAccountsFixture()
    );
    await userEvent.click(pageObject.buttons.linkBrand.get());

    act(() => {
      pageObject.fields.parentAccountId.sinchField.selectOption('account_1');
    });

    await userEvent.click(pageObject.buttons.save.get());
    expect(pageObject.buttons.save.sinchButton.isLoading()).toBeTruthy();

    await linkBrandPromise.reject(new Error('test'));

    expect(linkBrandApiFn).toHaveBeenCalledTimes(1);
    expect(pageObject.alert.commonError.get()).toBeInTheDocument();
  });

  it("Disable buttons if a user does not have 'write' permissions", async () => {
    const { pageObject, rerender, getBrandPromise } = await setup();
    await getBrandPromise.resolve(createTenDlcBrandFixture());

    act(() => {
      rerender(
        <BusinessRegistrationCard
          permissions={{
            'account.senderid.verification': {
              isEditable: false,
              isVisible: true,
            },
          }}
        />
      );
    });

    expect(pageObject.buttons.linkBrand.get()).toHaveAttribute(
      'disabled',
      'true'
    );
  });
});
