import {
  configure,
  mount,
  render,
  shallow,
} from 'enzyme'
import EnzymeAdapter from 'enzyme-adapter-react-16'
import jestFetch from 'jest-fetch-mock'
import 'whatwg-fetch'
// import localStorage from 'mock-local-storage'
import moment from 'moment'
import momentTZ from 'moment-timezone'
import configureMockStore from 'redux-mock-store'
import thunk from 'redux-thunk'
import { MESSAGEMEDIA } from '../src/constants/app-config'

moment.defaultFormat = 'YYYY-MM-DDTHH:mm:ss.SSS'
momentTZ.tz.setDefault('Asia/Bangkok')

configure({ adapter: new EnzymeAdapter() })
global.fetch = jestFetch
global.shallow = shallow
global.render = render
global.mount = mount
delete window.localStorage
// window.localStorage = localStorage
global.mockStore = configureMockStore([thunk])
global.window.open = jest.fn()

global.API_URL = 'API'
global.WFP_API_URL = 'WFP_API_URL'
global.NEXTGEN_API_URL = 'NEXTGEN_API'
global.SUPPORT_API_URL = 'SUPPORT_API_URL'
global.SUPPORT_V2_API_URL = 'SUPPORT_V2_API_URL'
global.SHOPIFY_SUPPORT_URL = 'SHOPIFY_SUPPORT_URL'
global.KIBANA_DASHBOARD_URL = 'KIBANA_DASHBOARD_URL'
global.AUTOMATED_BROADCAST_MANAGEMENT_URL = 'AUTOMATED_BROADCAST_MANAGEMENT_URL'
global.EMAIL2SMS_API_SUPPORT_URL = 'EMAIL2SMS_API_SUPPORT_URL'
global.MFE_CONTACTS = 'MFE_CONTACTS'
global.EMAIL2SMS_API_KEY = 'EMAIL2SMS_API_KEY'
global.ZUORA_URL = 'ZUORA_URL'
global.FEATURE_FLAGS = false
global.APP_VERSION = 'x'
global.BUILD_ENV = 'dev'
global.VENDOR_LABEL = MESSAGEMEDIA
global.DEBUG = false
global.TFN_VERIFICATION_PAPER_FORM_ID = 'messagemedia-tfn-verification'
global.SEGMENT_WRITE_KEY = 'SEGMENT_WRITE_KEY'
global.AZURE_CLIENT_ID = '97e3afee-ef0f-421d-b470-43584c4040ac'
global.AZURE_AUTHORITY = 'https://login.microsoftonline.com/clxgroup.onmicrosoft.com'
global.AZURE_SCOPES = 'api://sinch-support-main-app-dev/Support.All'
global.ALLOW_ENABLE_BETA_FEATURES = true
global.FEATURE_FLAGS = true
global.PREFIX = '/support'
global.IS_EU_VENDOR = true
global.AMS_DASHBOARD = 'https://ams.restams.syd.stg.messagemedia.com/'
global.UG_DASHBOARD = 'https://gateway-syd.stg.messagemedia.com/'
global.SALES_FORCE = 'https://mmgp.lightning.force.com/lightning/'
global.ZENDESK = 'https://messagemedia3531.zendesk.com/agent/home/<USER>'
global.ZUORA = 'https://www.zuora.com/apps/newlogin.do'
global.OPEN_SEARCH = 'https://logging-stg-ap-southeast-2.syd.logging.stg.mmd.zone/_dashboards'
global.CONFIG_CAT_KEY = 'CONFIG_CAT_KEY'
global.RUDDERSTACK_WRITE_KEY = 'CONFIG_CAT_KEY'
global.RUDDERSTACK_DATA_PLANE_URL = 'CONFIG_CAT_KEY'

// global.crypto = require('crypto')

if (!process.env.LISTENING_TO_UNHANDLED_REJECTION) {
  process.on('unhandledRejection', (reason) => {
    throw reason
  })
  // Avoid memory leak by adding too many listeners
  process.env.LISTENING_TO_UNHANDLED_REJECTION = true
}

// Fail tests on any warning
// console.error = (message) => { // eslint-disable-line
//   throw new Error(message)
// }
jest.mock('js-cookie', () => ({
  get: jest.fn(),
}))

jest.mock('@sinch-smb/checkbox', () => ({
  __esModule: true,
  default: 'Checkbox',
  Group: 'CheckboxGroup',
}))

jest.mock('@sinch-smb/modal', () => ({
  __esModule: true,
  default: 'Modal',
}))
jest.mock('@sinch-smb/button', () => 'Button')
jest.mock('@sinch-smb/antd-select', () => 'Select')
jest.mock('@sinch-smb/antd-table', () => 'AntTable')
jest.mock('@sinch-smb/table', () => 'Table')
jest.mock('@sinch-smb/tooltip', () => 'Tooltip')
jest.mock('@sinch-smb/alignment', () => {
  const React = require('react') // eslint-disable-line
  const PropTypes = require('prop-types') // eslint-disable-line global-require
  const Alignment = (props) => React.createElement('Alignment', props, props.children)
  Alignment.propTypes = {
    children: PropTypes.node.isRequired,
  }
  Alignment.Right = 'Alignment.Right'
  Alignment.Left = 'Alignment.Left'
  Alignment.Center = 'Alignment.Center'
  return Alignment
})
jest.mock('@sinch-smb/responsive', () => 'Responsive')
jest.mock('@sinch-smb/spin', () => 'Spin')
jest.mock('@sinch-smb/heading', () => 'Heading')
jest.mock('@sinch-smb/empty-report', () => 'EmptyReport')
jest.mock('@sinch-smb/spinner', () => 'Spinner')
jest.mock('@sinch-smb/tabs', () => ({
  __esModule: true,
  default: 'Tabs',
  Tabs: 'Tabs',
  TabPane: 'TabPane',
}))
jest.mock('@sinch-smb/textarea', () => 'TextArea')
jest.mock('@sinch-smb/panel', () => {
  const React = require('react') // eslint-disable-line
  const PropTypes = require('prop-types') // eslint-disable-line global-require
  const Panel = (props) => React.createElement('Panel', props, props.children)
  Panel.propTypes = {
    children: PropTypes.node.isRequired,
  }
  Panel.FullWidth = 'Panel.FullWidth'
  return Panel
})

class LocalStorageMock {
  constructor() {
    this.store = {}
  }

  clear() {
    this.store = {}
  }

  getItem(key) {
    return this.store[key] || null
  }

  setItem(key, value) {
    this.store[key] = String(value)
  }

  removeItem(key) {
    delete this.store[key]
  }
}

global.localStorage = new LocalStorageMock()
