export type User = {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
};

export type AccountItem = {
  index: string;
  id: string;
  score: number;
  sortValues: (string | number)[];
  content: {
    id: string;
    accountId: string;
    vendorId: string;
    createdAt: string;
    status: string;
    type: string;
    parentAccountId: string;
    billing: {
      billingType: string;
    };
    country: string;
    users: User[];
    dedicatedNumbers: [];
    senderAddresses: [];
    label?: string;
    verificationStatus?: string;
    restrictedAccess?: boolean;
  };
  highlightFields: {
    [key:string]: string[]
  };
  innerHits: any;
  matchedQueries: any[];
};

export type AccountSearchParams = {
  size?: number;
  label?: string;
  billingType?: string;
  vendorId?: string;
  status?: string;
  verificationStatus?: string;
  country?: string[];
  createdAt?: string[];
  exact?: boolean
}

export type AccountPreviewParams = {
  vendorId: string;
  accountId: string;
}

export type SyncAccountParams = {
  vendorId: string;
  accountId: string;
}
