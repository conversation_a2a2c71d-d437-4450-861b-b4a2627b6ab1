/* eslint-disable @nx/enforce-module-boundaries */
import { FAILED, LOADING, NOT_LOADED, SUCCEEDED } from '../../constants/redux-constants';
import * as types from './account-search-types';

const initialState = {
  loadingStatus: NOT_LOADED,
  loadingPreview: NOT_LOADED,
  loadingSyncAccount: NOT_LOADED,
  accountList: [],
  accountPreviewData: {}
}

export function accountSearchReducer(state = initialState, action) {
  switch (action.type) {
    case types.SUPPORT_ACCOUNTS_GET.REQUEST:
      return {
        ...state,
        loadingStatus: LOADING,
      };
    case types.SUPPORT_ACCOUNTS_GET.SUCCESS:
      return {
        ...state,
        loadingStatus: SUCCEEDED,
        accountList: action.data,
      };
    case types.SUPPORT_ACCOUNTS_GET.FAILURE:
      return {
        ...state,
        loadingStatus: FAILED,
      };
    // SUPPORT_ACCOUNT_PREVIEW_GET
    case types.SUPPORT_ACCOUNT_PREVIEW_GET.REQUEST:
      return {
        ...state,
        loadingPreview: LOADING,
      };
    case types.SUPPORT_ACCOUNT_PREVIEW_GET.SUCCESS:
      return {
        ...state,
        loadingPreview: SUCCEEDED,
        accountPreviewData: action.data,
      };
    case types.SUPPORT_ACCOUNT_PREVIEW_GET.FAILURE:
      return {
        ...state,
        loadingPreview: FAILED,
        accountPreviewData: {}
      };
    // SYNC_ACCOUNT_GET
    case types.SYNC_ACCOUNT_GET.REQUEST:
      return {
        ...state,
        loadingSyncAccount: LOADING,
      };
    case types.SYNC_ACCOUNT_GET.SUCCESS:
      return {
        ...state,
        loadingSyncAccount: SUCCEEDED,
      };
    case types.SYNC_ACCOUNT_GET.FAILURE:
      return {
        ...state,
        loadingSyncAccount: FAILED,
      };
    // SUPPORT_ACCOUNT_PREVIEW_CLEAR
    case types.SUPPORT_ACCOUNT_PREVIEW_CLEAR:
      return {
        ...state,
        loadingPreview: SUCCEEDED,
        accountPreviewData: {}
      };
    default:
      return state;
  }
}

export default accountSearchReducer;
