// eslint-disable-next-line @nx/enforce-module-boundaries
import { FAILED, LOADING, NOT_LOADED, SUCCEEDED } from "../../../../hst-core/src/constants/redux-constants"
// eslint-disable-next-line import/no-named-as-default
import accountSearchReducer from "./account-search-reducers"
import * as types from "./account-search-types"

const initialState = {
  loadingStatus: NOT_LOADED,
  accountList: [],
  accountPreviewData: {},
  loadingPreview: NOT_LOADED,
}

const payload = {
  pagination: {},
  resources: [{
    accountId: "MMGP-44305"
  }]
}
describe('Reducer Account Search', () => {
  it('returns initial state', () => {
    expect(accountSearchReducer(initialState, {})).toEqual(initialState)
  })

  describe('SUPPORT_ACCOUNTS_GET', () => {
    it('Getting accounts', () => {
      expect(accountSearchReducer(initialState, {
        type: types.SUPPORT_ACCOUNTS_GET.REQUEST,
      })).toEqual({
        ...initialState,
        loadingStatus: LOADING
      })
    })

    it('Loaded accounts', () => {
      expect(accountSearchReducer(initialState, {
        type: types.SUPPORT_ACCOUNTS_GET.SUCCESS,
        data: payload,
      })).toEqual({
        ...initialState,
        loadingStatus: SUCCEEDED,
        accountList: payload
      })
    })

    it('Load accounts fail', () => {
      expect(accountSearchReducer(initialState, {
        type: types.SUPPORT_ACCOUNTS_GET.FAILURE,
      })).toEqual({
        ...initialState,
        loadingStatus: FAILED
      })
    })
  })

  describe('SUPPORT_ACCOUNT_PREVIEW_GET', () => {
    it('Getting account preview data', () => {
      expect(accountSearchReducer(initialState, {
        type: types.SUPPORT_ACCOUNT_PREVIEW_GET.REQUEST,
      })).toEqual({
        ...initialState,
        loadingPreview: LOADING
      })
    })

    it('Loaded account preview data', () => {
      expect(accountSearchReducer(initialState, {
        type: types.SUPPORT_ACCOUNT_PREVIEW_GET.SUCCESS,
        accountPreviewData: {},
      })).toEqual({
        ...initialState,
        loadingPreview: SUCCEEDED,
        accountPreviewData: undefined
      })
    })

    it('Load accounts preview data fail', () => {
      expect(accountSearchReducer(initialState, {
        type: types.SUPPORT_ACCOUNT_PREVIEW_GET.FAILURE,
      })).toEqual({
        ...initialState,
        loadingPreview: FAILED
      })
    })

    it('Clear preview data', () => {
      expect(accountSearchReducer(initialState, {
        type: types.SUPPORT_ACCOUNT_PREVIEW_CLEAR,
      })).toEqual({
        ...initialState,
        loadingPreview: SUCCEEDED,
        accountPreviewData: {}
      })
    })
  })
})
