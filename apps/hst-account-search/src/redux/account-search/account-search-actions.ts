import { handleAction } from '@sinch-smb/dev-utils'
import AccountsApi from '../../apis/accounts-search'
import { AccountSearchParams } from '../../types'
import * as types from './account-search-types'

export const loadAccounts = (params: AccountSearchParams) => handleAction(
  async (dispatch) => {
    dispatch({
      type: types.SUPPORT_ACCOUNTS_GET.REQUEST,
    })
    try {
      const data = await AccountsApi.loadAccounts(params)
      dispatch({
        type: types.SUPPORT_ACCOUNTS_GET.SUCCESS,
        data,
      })
    } catch (error) {
      dispatch({
        type: types.SUPPORT_ACCOUNTS_GET.FAILURE,
      })
      throw error
    }
  })

export const loadAllAccounts = (params: AccountSearchParams) => handleAction(
  async (dispatch) => {
    dispatch({
      type: types.SUPPORT_ACCOUNTS_GET.REQUEST,
    })
    try {
      const data = await AccountsApi.loadAllAccounts(params)
      dispatch({
        type: types.SUPPORT_ACCOUNTS_GET.SUCCESS,
        data,
      })
    } catch (error) {
      dispatch({
        type: types.SUPPORT_ACCOUNTS_GET.FAILURE,
      })
      throw error
    }
  })

export const loadAccountPreview = (vendorId: string, accountId: string) => handleAction(
  async (dispatch) => {
    dispatch({
      type: types.SUPPORT_ACCOUNT_PREVIEW_GET.REQUEST,
    })
    try {
      const data = await AccountsApi.loadAccountPreview({ vendorId, accountId })
      dispatch({
        type: types.SUPPORT_ACCOUNT_PREVIEW_GET.SUCCESS,
        data,
      })
    } catch (error) {
      dispatch({
        type: types.SUPPORT_ACCOUNT_PREVIEW_GET.FAILURE,
      })
      throw error
    }
  })

export const clearAccountPreview = () => handleAction(
  async (dispatch) => {
    dispatch({
      type: types.SUPPORT_ACCOUNT_PREVIEW_CLEAR,
    })
  })


export const syncAccount = (vendorId: string, accountId: string) => handleAction(
  async (dispatch) => {
    dispatch({
      type: types.SYNC_ACCOUNT_GET.REQUEST,
    })
    try {
      const data = await AccountsApi.syncAccount({ vendorId, accountId })
      dispatch({
        type: types.SYNC_ACCOUNT_GET.SUCCESS,
        data,
      })
    } catch (error) {
      dispatch({
        type: types.SYNC_ACCOUNT_GET.FAILURE,
      })
      throw error
    }
  })


export default loadAllAccounts
