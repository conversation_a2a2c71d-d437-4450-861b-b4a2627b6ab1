import { NOT_LOADED } from "../../constants/redux-constants"
import { getSupportAccountPreview, getSupportAccountPreviewLoading, getSupportAccounts, getSupportAccountsLoading, getSupportSearchState } from "./account-search-selectors"

const state = {
  accountSearch: {
    loadingStatus: NOT_LOADED,
    loadingPreview: NOT_LOADED,
    accountList: [],
    accountPreviewData: {}
  }
}

describe('Selector: OnePortal account search', () => {
  describe('getAccount', () => {
    it('returns account object', () => {
      expect(getSupportSearchState(state)).toEqual({
        loadingStatus: NOT_LOADED,
        loadingPreview: NOT_LOADED,
        accountList: [],
        accountPreviewData: {}
      })
      expect(getSupportSearchState({})).toEqual({})
    })
  })
  describe('getSupportAccounts', () => {
    it('returns support accounts array', () => {
      expect(getSupportAccounts(state)).toEqual(
        [],
      )
      expect(getSupportAccounts({})).toEqual(undefined)
    })
  })
  describe('getSupportAccountsLoading', () => {
    it('returns loading', () => {
      expect(getSupportAccountsLoading(state)).toEqual(
        NOT_LOADED,
      )
      expect(getSupportAccountsLoading({})).toEqual(undefined)
    })
  })
  describe('getSupportAccountPreview', () => {
    it('returns accountPreviewData', () => {
      expect(getSupportAccountPreview(state)).toEqual({}
      )
    })
  })
  describe('getSupportAccountPreviewLoading', () => {
    it('returns loading', () => {
      expect(getSupportAccountsLoading(state)).toEqual(
        NOT_LOADED,
      )
      expect(getSupportAccountPreviewLoading({})).toEqual(undefined)
    })
  })
})
