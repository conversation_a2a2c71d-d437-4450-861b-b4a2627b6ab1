import { createSelector } from 'reselect'

export const getSupportSearchState = (state) => state?.accountSearch || {}

export const getSupportAccounts = createSelector(
  getSupportSearchState,
  ({ accountList }) => accountList,
)

export const getSupportAccountsLoading = createSelector(
  getSupportSearchState,
  ({ loadingStatus }) => loadingStatus,
)

export const getSupportAccountPreview = createSelector(
  getSupportSearchState,
  ({ accountPreviewData }) => accountPreviewData,
)

export const getSupportAccountPreviewLoading = createSelector(
  getSupportSearchState,
  ({ loadingPreview }) => loadingPreview,
)

export const getSyncAccountLoading = createSelector(
  getSupportSearchState,
  ({ loadingSyncAccount }) => loadingSyncAccount,
)
