import 'regenerator-runtime/runtime'
import AccountAPI from '../../apis/accounts-search'
import { NOT_LOADED } from "../../constants/redux-constants"
import loadAllAccounts, { clearAccountPreview, loadAccountPreview, loadAccounts } from "./account-search-actions"
import * as types from "./account-search-types"

describe('Action: OnePortal Account Search', () => {
  let store
  beforeEach(() => {
    store = mockStore({
      accountList: [],
      loadingStatus: NOT_LOADED
    })
  })
  describe('Load accounts', () => {
    it('call loadAccounts', async () => {
      AccountAPI.loadAccounts = jest.fn(() => Promise.resolve({
        pagination: {},
        resources: [{
          accountId: "MMGP-44305"
        }]
      }))

      await store.dispatch(loadAccounts({}))
      expect(store.getActions()).toEqual([
        {
          type: types.SUPPORT_ACCOUNTS_GET.REQUEST,

        },
        {
          type: types.SUPPORT_ACCOUNTS_GET.SUCCESS,
          data:
          {
            pagination: {},
            resources: [{
              accountId: "MMGP-44305"
            }]
          }
        },
      ])
    })
    it('call loadAccounts fail', async () => {
      AccountAPI.loadAccounts = jest.fn(() => Promise.reject(Error('loadAccounts-fail')))
      try {
        await store.dispatch(loadAccounts({}))
      } catch (error) {
        expect(store.getActions()).toEqual([
          {
            type: types.SUPPORT_ACCOUNTS_GET.REQUEST,

          },
          {
            type: types.SUPPORT_ACCOUNTS_GET.FAILURE,
          },
        ])
      }
    })
    it('call loadAllAccounts', async () => {
      AccountAPI.loadAllAccounts = jest.fn(() => Promise.resolve({
        pagination: {},
        resources: [{
          accountId: "MMGP-44305"
        }]
      }))

      await store.dispatch(loadAllAccounts({}))
      expect(store.getActions()).toEqual([
        {
          type: types.SUPPORT_ACCOUNTS_GET.REQUEST,

        },
        {
          type: types.SUPPORT_ACCOUNTS_GET.SUCCESS,
          data:
          {
            pagination: {},
            resources: [{
              accountId: "MMGP-44305"
            }]
          }
        },
      ])
    })
    it('call loadAllAccounts fail', async () => {
      AccountAPI.loadAllAccounts = jest.fn(() => Promise.reject(Error('loadAllAccounts-fail')))
      try {
        await store.dispatch(loadAllAccounts({}))
      } catch (error) {
        expect(store.getActions()).toEqual([
          {
            type: types.SUPPORT_ACCOUNTS_GET.REQUEST,

          },
          {
            type: types.SUPPORT_ACCOUNTS_GET.FAILURE,
          },
        ])
      }
    })
    it('call loadAccountPreview', async () => {
      AccountAPI.loadAccountPreview = jest.fn(() => Promise.resolve({
        pagination: {},
        resources: [{
          accountId: "MMGP-44305"
        }]
      }))

      await store.dispatch(loadAccountPreview({}))
      expect(store.getActions()).toEqual([
        {
          type: types.SUPPORT_ACCOUNT_PREVIEW_GET.REQUEST,

        },
        {
          type: types.SUPPORT_ACCOUNT_PREVIEW_GET.SUCCESS,
          data:
          {
            pagination: {},
            resources: [{
              accountId: "MMGP-44305"
            }]
          }
        },
      ])
    })
    it('call loadAccountPreview fail', async () => {
      AccountAPI.loadAccountPreview = jest.fn(() => Promise.reject(Error('loadAccountPreview-fail')))
      try {
        await store.dispatch(loadAccountPreview({}))
      } catch (error) {
        expect(store.getActions()).toEqual([
          {
            type: types.SUPPORT_ACCOUNT_PREVIEW_GET.REQUEST,

          },
          {
            type: types.SUPPORT_ACCOUNT_PREVIEW_GET.FAILURE,
          },
        ])
      }
    })
    it('call clearAccountPreview', async () => {
      await store.dispatch(clearAccountPreview({}))
      expect(store.getActions()).toEqual([
        {
          type: types.SUPPORT_ACCOUNT_PREVIEW_CLEAR,

        },
      ])
    })
  })
})
