import { handleAPI } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import { fetchInstance, paramsToQueryString } from 'helpers';
import { AccountPreviewParams, SyncAccountParams } from '../types';

export default {
  loadAccounts: handleAPI(async (params = {}) => {
    const queryString = paramsToQueryString({ params })
    const formattedQueryString = queryString.replace(/\[.*?\]/g, '')

    return fetchInstance()(`${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/search?${formattedQueryString}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    })
  }),
  loadAllAccounts: handleAPI(async (params = {}) => {
    const queryString = paramsToQueryString({ params })
    const formattedQueryString = queryString.replace(/\[.*?\]/g, '')

    return fetchInstance()(`${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/search/all?${formattedQueryString}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    })
  }),
  loadAccountPreview: handleAPI(async ({ vendorId, accountId }: AccountPreviewParams) => (

    fetchInstance()(`${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/preview`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    })
  )),
  syncAccount: handleAPI(async ({ vendorId, accountId }: SyncAccountParams)  => (
    fetchInstance()(`${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/sync`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    })
  ))
}
