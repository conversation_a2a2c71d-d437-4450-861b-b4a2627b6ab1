@import '~@sinch-smb/styles/variables.less';

.container {
  padding: .rem(28) [];
}

.tableContainer {
  padding-top: .rem(16) [];
}

.fieldContainer {
  max-width: 100%;
  width: .rem(260) [];
}

.searchBtnCol {
  padding-top: .rem(26) [];
}

.subMenuIcon {
  svg {
    color: @sub-menu-icon-color;
    height: .rem(24px) [];
    margin: 0 .rem(8px) [] 0 0;
    width: .rem(24px) [];
  }
}

.searchForm {
  margin-top: 16px;
  align-items: flex-start;
  display: flex;
  flex-wrap: wrap;
  min-height: 80px;

  .formItem {
    margin-bottom: 16px;
    margin-right: 16px;
  }
}

.container {
  text-align: center;
}

.row {
  display: flex;
  flex-direction: row;
}

.left {
  flex-basis: .rem(150px) [];
  padding: .rem(16px) [];

  .link {
    align-items: center;
    display: flex;
    flex-direction: row;
  }

  .iconWrap {
    display: inline-block;
    margin-right: .rem(4px) [];
    --sinch-global-color-icon: @blueLight;
  }
}

.right {
  border-left: 1px solid @greyLight;
  flex-grow: 1;
}

.tagWrap {
  margin-left: .rem(4px) [];
}

.guideText {
  text-align: left;
}
