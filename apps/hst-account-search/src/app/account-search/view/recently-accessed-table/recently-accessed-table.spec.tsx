import React from 'react'

import { AccountsTableView, tableColumns } from './recently-accessed-table'

const RECENT_SEARCH_LIST = []

jest.mock('@sinch-smb/dev-utils', () => ({
  handleAPI: (func) => (args) => func(args),
  storage: {
    getData: () => RECENT_SEARCH_LIST,
    saveData: () => jest.fn()
  }
}))

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => jest.fn(),
}));
jest.mock('../partials/account-summary-modal/modal', () => 'Modal')
const setState = jest.fn()
const useState = jest.spyOn(React, 'useState')
const mockUseState = (state) => useState.mockImplementation(() => [state, setState])
beforeEach(() => {
  mockUseState(false)
})

describe('Recently Accessed Table', () => {
  it('Renders', () => {
    const wrapper = shallow(<AccountsTableView />)
    expect(wrapper).toMatchSnapshot()
  })
  it('should trigger close modal', () => {
    const wrapper = shallow(<AccountsTableView />)
    wrapper.find('Modal').props().onClose()
  })
  it('should trigger open modal', () => {
    const wrapper = shallow(<AccountsTableView />)
    wrapper.find('Modal').props().onViewAccount()
  })
})

describe('TableColumns', () => {
  it('should render correctly', () => {
    const mockNavigate = jest.fn()
    const mockOpenSumary = jest.fn()
    const mockHandleViewAccount = jest.fn()
    const testColumns = tableColumns(mockNavigate, mockOpenSumary, mockHandleViewAccount)
    expect(testColumns[0].render('')).toMatchSnapshot()
    expect(testColumns[1].render({ content: { parentAccountId: "" } })).toMatchSnapshot()
    expect(testColumns[2].render({ highlightFields: {} })).toMatchSnapshot()
    expect(testColumns[2].render()).toMatchSnapshot()
    expect(testColumns[2].render({
      highlightFields: {
        keyword: ["<em>ABCD_PWW_0001</em>"],
        label: ["<em>ABCD</em>"]
      }
    })).toMatchSnapshot()
    expect(testColumns[3].render({ content: { vendorId: "MM" } })).toMatchSnapshot()
    expect(testColumns[4].render({ content: { country: "AT" } })).toMatchSnapshot()
    expect(testColumns[5].render({ content: { status: "ACTIVE" } })).toMatchSnapshot()
    expect(testColumns[6].render({ content: { verificationStatus: "VERIFIED" } })).toMatchSnapshot()
    expect(testColumns[7].render({ content: { billing: { billingType: "123" } } })).toMatchSnapshot()
    expect(testColumns[8].render({ restrictedAccess: "RESTRICTED" })).toMatchSnapshot()
    expect(testColumns[8].render({})).toMatchSnapshot()
    expect(testColumns[10].render("")).toMatchSnapshot()
  })
})
