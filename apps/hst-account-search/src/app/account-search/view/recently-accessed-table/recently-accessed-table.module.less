@import '~@sinch-smb/styles/variables.less';

.accountLink {
  color: @primary-color;
  cursor: pointer;
  font-size: @table-row-font-size;
}

.accountDetails {
  color: @detail-row-label-color;
  font-size: @text-font-size-small;
  font-style: italic;
  margin-top: .rem(2px) [];
}

.hidden {
  display: none;
}

.columnPicker {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 5px;
}

.loginContainer,
.viewAccountContainer {
  text-align: center;
}

.loginContainer:nth-child(2) {
  padding: 10px 0 0 0;
}

.statusSuspended,
.statusCancelled,
.statusUnverified {
  color: @red;
}

.statusActive,
.statusVerified {
  color: @green;
}

.restricted {
  .restrictedIcon {
    color: @red;
  }

  .restrictedText {
    color: @red;
    margin-left: .rem(6px) [];
  }
}

.unrestricted {
  .unrestrictedIcon {
    color: @green;
  }

  .unrestrictedText {
    color: @green;
    margin-left: .rem(6px) [];
  }
}

.verified {
  align-items: flex-start;
  display: flex;
  justify-content: flex-start;
  sinch-text {
    color: var(--sinch-ref-color-complementary-olive-400);
  }
  --sinch-global-color-icon: var(--sinch-ref-color-complementary-olive-400);
}

.unverified {
  align-items: flex-start;
  display: flex;
  justify-content: flex-start;
  sinch-text {
    color: var(--sinch-ref-color-complementary-jasper-400);
  }
  --sinch-global-color-icon: var(--sinch-ref-color-complementary-jasper-400);
}

.dormant {
  align-items: flex-start;
  display: flex;
  justify-content: flex-start;
  sinch-text {
    color: var(--sinch-ref-color-complementary-orange-400);
  }
  --sinch-global-color-icon: var(--sinch-ref-color-complementary-orange-400);
}

.matchTag {
  margin-bottom: .rem(2px) [];
}

.unrestricted {
  align-items: flex-start;
  display: flex;
  justify-content: flex-start;
  sinch-text {
    color: var(--sinch-ref-color-complementary-olive-400);
  }
  --sinch-global-color-icon: var(--sinch-ref-color-complementary-olive-400);
}

.restricted {
  align-items: flex-start;
  display: flex;
  justify-content: flex-start;
  sinch-text {
    color: var(--sinch-ref-color-complementary-jasper-400);
  }
  --sinch-global-color-icon: var(--sinch-ref-color-complementary-jasper-400);
}

.countryFlag {
  --sinch-global-size-icon: 32px;
}
