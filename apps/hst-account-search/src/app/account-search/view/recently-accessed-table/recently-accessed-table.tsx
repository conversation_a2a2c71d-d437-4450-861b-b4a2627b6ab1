/* eslint-disable @nx/enforce-module-boundaries */
import { countries as NECTARY_COUNTRIES } from '@nectary/components/utils/countries';
import { storage } from '@sinch-smb/dev-utils';
import { DORMANT } from 'apps/hst-account-details/src/app/constants';
import { AccountItem } from 'apps/hst-account-search/src/types';
import {
  ACTIVE,
  CANCELLED,
  RESTRICTED,
  SUSPENDED,
  UNRESTRICTED,
  UNVERIFIED,
  VERIFIED,
} from 'apps/hst-core/src/constants/account-verification';
import {
  customDateTimeFormatReadable,
  isNonEmptyString,
} from 'helpers';
import _get from 'lodash/get';
import _uniqBy from 'lodash/uniqBy';
import { Button, Flag, Link, Table, Tag, Text, Tooltip } from 'nectary';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ONENZ, VODANZ } from '../constants';
import Modal from '../partials/account-summary-modal/modal';
import { saveToRecentlyAccessedAccounts, TAG_MAPPING } from '../search-result-table/search-result-table';
import styles from './recently-accessed-table.module.less';

export const RECENT_SEARCH_LIST = 'RECENT_SEARCH_LIST';

export const tableColumns = (
  handleOpenSummary: (item: AccountItem) => void,
  onViewAccount: (accountId: string, item: AccountItem) => void,
) => [
  {
    title: 'Account',
    index: 'accountId',
    sort: false,
    align: 'left',
    render: (value: AccountItem) => (
      <>
        <Link
          text={value?.content?.accountId}
          href="/#"
          preventDefault
          onClick={() => handleOpenSummary(value)}
          aria-label="Link"
        />
        <span>{value?.content?.label}</span>
      </>
    ),
  },
  {
    title: 'Parent Account',
    index: 'parentAccountId',
    sort: false,
    align: 'left',
    render: (value: AccountItem) => {
      const data = { ...value, content: { ...value.content, accountId: value.content.parentAccountId } }
      return (
        <Link
          text={value?.content?.parentAccountId}
          href="/#"
          preventDefault
          onClick={() => handleOpenSummary(data)}
          aria-label="Link"
        />
      )
    },
  },
  {
    title: 'Type',
    sort: false,
    index: 'type',
    align: 'left',
    render: (value: AccountItem) => (
      <Text type="s">{value?.content?.type}</Text>
    ),
  },
  {
    title: 'Match',
    tooltipText: 'Show the categories where the keyword matches.',
    index: 'parentAccountId',
    sort: false,
    align: 'left',
    render: (value: AccountItem) => {
      const fields = Object.keys(value?.highlightFields || {}) || [];;
      let combinedFields = fields.map(field => ({
        field,
        translatedField: TAG_MAPPING[field]
      }))
      combinedFields = _uniqBy(combinedFields, 'translatedField')

      return (
        <>
          {combinedFields?.map(({ field, translatedField }) => (
            <div className={styles.matchTag}>
              <Tooltip
                orientation="top"
                type="fast"
                text={_get(value?.highlightFields[field], '[0]')
                  ?.replaceAll('<em>', '')
                  .replaceAll('</em>', '')}
              >
                <Tag
                  key={translatedField}
                  color="light-green"
                  text={translatedField}
                />
              </Tooltip>
            </div>
          ))}
        </>
      );
    },
  },
  {
    title: 'Brand',
    sort: false,
    index: 'vendorId',
    align: 'left',
    render: (value: AccountItem) => (
      <Text type="s">{value?.content?.vendorId}</Text>
    ),
  },
  {
    title: 'Country',
    sort: false,
    index: 'country',
    align: 'left',
    render: (value: AccountItem) => {
      const country = _get(value, 'content.country', null);
      if (!country) return '-';
      const countryObj = _get(NECTARY_COUNTRIES, country.toLowerCase(), null);

      if (!countryObj) return '-';

      return (
        <Tooltip type="fast" orientation="top" text={countryObj.name}>
          <div className={styles.countryFlag}>
            <Flag code={country.toLowerCase()} />
          </div>
        </Tooltip>
      );
    },
  },
  {
    title: 'Status',
    sort: false,
    index: 'status',
    align: 'left',
    render: (value: AccountItem) =>
    ({
      [ACTIVE]: (
        <div className={styles.verified}>
          <Text type="s">{value?.content?.status}</Text>
        </div>
      ),
      [UNVERIFIED]: (
        <div className={styles.unverified}>
          <Text type="s">{value?.content?.status}</Text>
        </div>
      ),
      [CANCELLED]: (
        <div className={styles.unverified}>
          <Text type="s">{value?.content?.status}</Text>
        </div>
      ),
      [SUSPENDED]: (
        <div className={styles.unverified}>
          <Text type="s">{value?.content?.status}</Text>
        </div>
      ),
      [DORMANT]: (
        <div className={styles.dormant}>
          <Text type="s">{value?.content?.status}</Text>
        </div>
      ),
    }[value?.content?.status]),
  },
  {
    title: 'Verification',
    sort: false,
    index: 'verificationStatus',
    align: 'left',
    render: (value: AccountItem) =>
    ({
      [VERIFIED]: (
        <div className={styles.verified}>
          <Text type="s">
            {value?.content?.verificationStatus}
          </Text>
        </div>
      ),
      [UNVERIFIED]: (
        <div className={styles.unverified}>
          <Text type="s">
            {value?.content?.verificationStatus}
          </Text>
        </div>
      ),
    }[value?.content?.verificationStatus]),
  },
  {
    title: 'Billing Type',
    sort: false,
    index: 'billing',
    align: 'left',
    render: (value: AccountItem) => (
      <Text type="s">{value?.content?.billing?.billingType}</Text>
    ),
  },
  {
    title: 'Date Created',
    sort: false,
    index: 'createdAt',
    align: 'left',
    render: (value: AccountItem) => (
      <Text type="s">
        {isNonEmptyString(value?.content?.createdAt)
          ? customDateTimeFormatReadable({
            datetime: value?.content?.createdAt,
            showTime: true,
          })
          : '-'}</Text>
    ),
  },
  {
    title: 'Access',
    sort: false,
    index: '',
    align: 'left',
    render: (value: AccountItem) =>
      value?.content?.restrictedAccess ? (
        <div className={styles.restricted}>
          <sinch-icon-not-interested
            title="sinch-icon-not-interested"
            style={{ marginRight: 10 }}
          />
          <Text type="s">{RESTRICTED}</Text>
        </div>
      ) : (
        <div className={styles.unrestricted}>
          <sinch-icon-check-circle-outline
            title="sinch-icon-check-circle-outline"
            style={{ marginRight: 10 }}
          />

          <Text type="s">{UNRESTRICTED}</Text>
        </div>
      ),
  },
  {
    title: 'Action',
    index: 'id',
    sort: false,
    align: 'left',
    render: (value: AccountItem) => (
      <div>
        <Button
          label="View Account"
          onClick={() => {
            onViewAccount(value?.content?.accountId, value)
          }}
        />
      </div>
    )
  },
];

export const AccountsTableView = () => {
  const navigate = useNavigate();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [accountSummary, setAccountSummary] = useState<AccountItem | null>(null);

  const handleOpenSummary = (account: AccountItem) => {
    setAccountSummary(account);
    setIsDialogOpen((prev) => ({ ...prev, isDialogOpen: !isDialogOpen }));
  };

  const handleCloseSummary = () => {
    setIsDialogOpen(false);
  };

  const handleViewAccount = (accountId: string, account: AccountItem) => {
    let baseName = _get(account?.content, 'vendorId', '').toLowerCase();
    if (baseName === VODANZ) baseName = ONENZ
    saveToRecentlyAccessedAccounts(accountId, account);
    navigate(`/accounts/${baseName}/${accountId}`, { state: { from: 'support'} })
  };

  const recentlySearchList = storage.getData(RECENT_SEARCH_LIST) || [];

  return (
    <>
      <Table
        hasCheckbox={false}
        tableColumns={tableColumns(handleOpenSummary, handleViewAccount)}
        tableData={recentlySearchList}
        actionLabel="View Account"
        scrollX
      />
      <Modal
        isDialogOpen={isDialogOpen}
        onClose={handleCloseSummary}
        account={accountSummary}
        onViewAccount={handleViewAccount}
      />
    </>
  );
};

AccountsTableView.propTypes = {};

AccountsTableView.defaultProps = {};

export default AccountsTableView;
