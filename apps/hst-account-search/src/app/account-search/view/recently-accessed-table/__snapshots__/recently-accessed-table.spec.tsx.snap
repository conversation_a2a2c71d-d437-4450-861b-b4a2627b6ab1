// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`Recently Accessed Table Renders 1`] = `
<Fragment>
  <Table
    actionLabel="View Account"
    hasCheckbox={false}
    scrollX={true}
    tableColumns={
      [
        {
          "align": "left",
          "index": "accountId",
          "render": [Function],
          "sort": false,
          "title": "Account",
        },
        {
          "align": "left",
          "index": "parentAccountId",
          "render": [Function],
          "sort": false,
          "title": "Parent Account",
        },
        {
          "align": "left",
          "index": "type",
          "render": [Function],
          "sort": false,
          "title": "Type",
        },
        {
          "align": "left",
          "index": "parentAccountId",
          "render": [Function],
          "sort": false,
          "title": "Match",
          "tooltipText": "Show the categories where the keyword matches.",
        },
        {
          "align": "left",
          "index": "vendorId",
          "render": [Function],
          "sort": false,
          "title": "Brand",
        },
        {
          "align": "left",
          "index": "country",
          "render": [Function],
          "sort": false,
          "title": "Country",
        },
        {
          "align": "left",
          "index": "status",
          "render": [Function],
          "sort": false,
          "title": "Status",
        },
        {
          "align": "left",
          "index": "verificationStatus",
          "render": [Function],
          "sort": false,
          "title": "Verification",
        },
        {
          "align": "left",
          "index": "billing",
          "render": [Function],
          "sort": false,
          "title": "Billing Type",
        },
        {
          "align": "left",
          "index": "createdAt",
          "render": [Function],
          "sort": false,
          "title": "Date Created",
        },
        {
          "align": "left",
          "index": "",
          "render": [Function],
          "sort": false,
          "title": "Access",
        },
        {
          "align": "left",
          "index": "id",
          "render": [Function],
          "sort": false,
          "title": "Action",
        },
      ]
    }
    tableData={[]}
  />
  <Modal
    account={false}
    isDialogOpen={false}
    onClose={[Function]}
    onViewAccount={[Function]}
  />
</Fragment>
`;

exports[`TableColumns should render correctly 1`] = `
<React.Fragment>
  <Link
    aria-label="Link"
    href="/#"
    onClick={[Function]}
    preventDefault={true}
  />
  <span />
</React.Fragment>
`;

exports[`TableColumns should render correctly 2`] = `
<Link
  aria-label="Link"
  href="/#"
  onClick={[Function]}
  preventDefault={true}
  text=""
/>
`;

exports[`TableColumns should render correctly 3`] = `
<Text
  type="s"
/>
`;

exports[`TableColumns should render correctly 4`] = `
<Text
  type="s"
/>
`;

exports[`TableColumns should render correctly 5`] = `
<Text
  type="s"
/>
`;

exports[`TableColumns should render correctly 6`] = `<React.Fragment />`;

exports[`TableColumns should render correctly 7`] = `
<Text
  type="s"
/>
`;

exports[`TableColumns should render correctly 8`] = `"-"`;

exports[`TableColumns should render correctly 9`] = `undefined`;

exports[`TableColumns should render correctly 10`] = `undefined`;

exports[`TableColumns should render correctly 11`] = `
<Text
  type="s"
/>
`;

exports[`TableColumns should render correctly 12`] = `
<Text
  type="s"
/>
`;

exports[`TableColumns should render correctly 13`] = `
<div
  className="unrestricted"
>
  <sinch-icon-check-circle-outline
    style={
      {
        "marginRight": 10,
      }
    }
    title="sinch-icon-check-circle-outline"
  />
  <Text
    type="s"
  >
    UNRESTRICTED
  </Text>
</div>
`;
