// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`View OnePortal Account Search renders 1`] = `
<div>
  <AccountSearchContent />
</div>
`;

exports[`View OnePortal Account Search renders Account Search content 1`] = `
<ContextProvider
  value={
    [
      [MockFunction] {
        "calls": [
          [
            {
              "length": false,
            },
          ],
        ],
        "results": [
          {
            "type": "return",
            "value": undefined,
          },
        ],
      },
      [MockFunction] {
        "calls": [
          [
            {
              "length": false,
            },
          ],
        ],
        "results": [
          {
            "type": "return",
            "value": undefined,
          },
        ],
      },
      [MockFunction] {
        "calls": [
          [
            {
              "length": false,
            },
          ],
        ],
        "results": [
          {
            "type": "return",
            "value": undefined,
          },
        ],
      },
      [MockFunction] {
        "calls": [
          [
            {
              "length": false,
            },
          ],
        ],
        "results": [
          {
            "type": "return",
            "value": undefined,
          },
        ],
      },
      [MockFunction] {
        "calls": [
          [
            {
              "length": false,
            },
          ],
        ],
        "results": [
          {
            "type": "return",
            "value": undefined,
          },
        ],
      },
      [MockFunction] {
        "calls": [
          [
            {
              "length": false,
            },
          ],
        ],
        "results": [
          {
            "type": "return",
            "value": undefined,
          },
        ],
      },
      [MockFunction] {
        "calls": [
          [
            {
              "length": false,
            },
          ],
        ],
        "results": [
          {
            "type": "return",
            "value": undefined,
          },
        ],
      },
    ]
  }
>
  <div
    data-test="account-search"
  >
    <ErrorBoundary
      componentName="AccountSearch"
      fallback={<ErrorFallbackDefault />}
    >
      <div
        className="container"
      >
        <div
          className="guideText"
        >
          <Text
            type="s"
          >
            <b>
              Enter a keyword
            </b>
             and click 'Search.' It will take you to the research results tab with 
            <b>
              matching accounts.
            </b>
             To narrow down the search, use the 
            <b>
              exact match
            </b>
             or the 
            <b>
              filters
            </b>
             options. For more details, click 
            <Link
              to="https://messagemedia.atlassian.net/wiki/spaces/RD/pages/**********/Account+Search"
            >
              here
            </Link>
            !
          </Text>
        </div>
        <form
          className="searchForm"
          onKeyDown={[Function]}
        >
          <div
            className="formItem"
          >
            <SearchInput
              defaultValue=""
              errorText=""
              onChange={[Function]}
              width={
                {
                  "minWidth": 400,
                }
              }
            />
          </div>
          <div
            className="formItem"
          >
            <Select
              customStyles={
                {
                  "width": 270,
                }
              }
              defaultValue="All categories"
              onSelect={[Function]}
              options={
                [
                  {
                    "value": "All categories",
                  },
                  {
                    "value": "Account Name",
                  },
                  {
                    "value": "Account ID (Gateway ID)",
                  },
                  {
                    "value": "Billing Account ID",
                  },
                  {
                    "value": "User Email",
                  },
                  {
                    "value": "User Phone",
                  },
                  {
                    "value": "Integration Platform ID (Ecosystem)",
                  },
                  {
                    "value": "E2S Email",
                  },
                  {
                    "value": "Carrier Billing Number",
                  },
                  {
                    "value": "API Key",
                  },
                  {
                    "value": "API Key name",
                  },
                  {
                    "value": "Dedicated Number",
                  },
                  {
                    "value": "Sender Address",
                  },
                ]
              }
              placeholder="All categories"
              testId="category-select"
              value="All categories"
            />
          </div>
          <div
            className="formItem"
          >
            <Checkbox
              label="Exact match only"
              onChange={
                [MockFunction] {
                  "calls": [
                    [
                      {
                        "length": false,
                      },
                    ],
                  ],
                  "results": [
                    {
                      "type": "return",
                      "value": undefined,
                    },
                  ],
                }
              }
              tooltipText="Ticking this box means we won't match partially against text, you'll get whole word matched only"
              value=""
            />
          </div>
          <div
            className="formItem"
          >
            <Button
              disabled={true}
              label="Search"
              onClick={[Function]}
            />
          </div>
        </form>
        <Tab
          currentTab=""
          onChangeTab={[Function]}
          tabContent={
            [
              {
                "content": <AccountsTableView />,
                "value": 1,
              },
              {
                "content": <AccountsTableView
                  billingTypeParams="All"
                  brand=""
                  brands={
                    [
                      {
                        "label": "All",
                        "value": "All",
                      },
                      {
                        "label": "MessageMedia",
                        "value": "MessageMedia",
                      },
                      {
                        "label": "DirectSMS",
                        "value": "DirectSMS",
                      },
                      {
                        "label": "SMSBroadcast",
                        "value": "SMSBroadcast",
                      },
                      {
                        "label": "SMSCentral",
                        "value": "SMSCentral",
                      },
                      {
                        "label": "Streetdata",
                        "value": "Streetdata",
                      },
                      {
                        "label": "MessageNet",
                        "value": "MessageNet",
                      },
                      {
                        "label": "Mobipost",
                        "value": "Mobipost",
                      },
                      {
                        "label": "WholesaleSMS",
                        "value": "WholesaleSMS",
                      },
                      {
                        "label": "VodaNZ",
                        "value": "VodaNZ",
                      },
                      {
                        "label": "2Degrees",
                        "value": "2Degrees",
                      },
                      {
                        "label": "eTXT",
                        "value": "eTXT",
                      },
                      {
                        "label": "TPGTelecom",
                        "value": "TPGTelecom",
                      },
                    ]
                  }
                  category=""
                  countries=""
                  currentPageSize={10}
                  dateRangeParams={[]}
                  disabled={true}
                  handleClearAll={[Function]}
                  handleSearch={[Function]}
                  handleSetURLParams={[Function]}
                  integrationTypeParams={[]}
                  isClearedAll=""
                  searchParams={
                    {
                      "": "",
                      "billingType": "",
                      "status": "",
                      "vendorId": "",
                      "verificationStatus": "",
                    }
                  }
                  searchValue=""
                  setTokenQueue={
                    [MockFunction] {
                      "calls": [
                        [
                          {
                            "length": false,
                          },
                        ],
                      ],
                      "results": [
                        {
                          "type": "return",
                          "value": undefined,
                        },
                      ],
                    }
                  }
                  status=""
                  tokenQueue=""
                  verificationStatus=""
                />,
                "value": 2,
              },
            ]
          }
          tabs={
            [
              {
                "text": "Recently Accessed Accounts",
                "value": 1,
              },
              {
                "text": "Search Results",
                "value": 2,
              },
            ]
          }
        />
      </div>
    </ErrorBoundary>
  </div>
   
</ContextProvider>
`;

exports[`View OnePortal Account Search renders Account Search content different vendor 1`] = `
<ContextProvider
  value={
    [
      [MockFunction] {
        "calls": [
          [
            {
              "length": false,
            },
          ],
        ],
        "results": [
          {
            "type": "return",
            "value": undefined,
          },
        ],
      },
      [MockFunction] {
        "calls": [
          [
            {
              "length": false,
            },
          ],
        ],
        "results": [
          {
            "type": "return",
            "value": undefined,
          },
        ],
      },
      [MockFunction] {
        "calls": [
          [
            {
              "length": false,
            },
          ],
        ],
        "results": [
          {
            "type": "return",
            "value": undefined,
          },
        ],
      },
      [MockFunction] {
        "calls": [
          [
            {
              "length": false,
            },
          ],
        ],
        "results": [
          {
            "type": "return",
            "value": undefined,
          },
        ],
      },
      [MockFunction] {
        "calls": [
          [
            {
              "length": false,
            },
          ],
        ],
        "results": [
          {
            "type": "return",
            "value": undefined,
          },
        ],
      },
      [MockFunction] {
        "calls": [
          [
            {
              "length": false,
            },
          ],
        ],
        "results": [
          {
            "type": "return",
            "value": undefined,
          },
        ],
      },
      [MockFunction] {
        "calls": [
          [
            {
              "length": false,
            },
          ],
        ],
        "results": [
          {
            "type": "return",
            "value": undefined,
          },
        ],
      },
    ]
  }
>
  <div
    data-test="account-search"
  >
    <ErrorBoundary
      componentName="AccountSearch"
      fallback={<ErrorFallbackDefault />}
    >
      <div
        className="container"
      >
        <div
          className="guideText"
        >
          <Text
            type="s"
          >
            <b>
              Enter a keyword
            </b>
             and click 'Search.' It will take you to the research results tab with 
            <b>
              matching accounts.
            </b>
             To narrow down the search, use the 
            <b>
              exact match
            </b>
             or the 
            <b>
              filters
            </b>
             options. For more details, click 
            <Link
              to="https://messagemedia.atlassian.net/wiki/spaces/RD/pages/**********/Account+Search"
            >
              here
            </Link>
            !
          </Text>
        </div>
        <form
          className="searchForm"
          onKeyDown={[Function]}
        >
          <div
            className="formItem"
          >
            <SearchInput
              defaultValue=""
              errorText=""
              onChange={[Function]}
              width={
                {
                  "minWidth": 400,
                }
              }
            />
          </div>
          <div
            className="formItem"
          >
            <Select
              customStyles={
                {
                  "width": 270,
                }
              }
              defaultValue="All categories"
              onSelect={[Function]}
              options={
                [
                  {
                    "value": "All categories",
                  },
                  {
                    "value": "Account Name",
                  },
                  {
                    "value": "Account ID (Gateway ID)",
                  },
                  {
                    "value": "Billing Account ID",
                  },
                  {
                    "value": "User Email",
                  },
                  {
                    "value": "User Phone",
                  },
                  {
                    "value": "Integration Platform ID (Ecosystem)",
                  },
                  {
                    "value": "E2S Email",
                  },
                  {
                    "value": "Carrier Billing Number",
                  },
                  {
                    "value": "API Key",
                  },
                  {
                    "value": "API Key name",
                  },
                  {
                    "value": "Dedicated Number",
                  },
                  {
                    "value": "Sender Address",
                  },
                ]
              }
              placeholder="All categories"
              testId="category-select"
              value="All categories"
            />
          </div>
          <div
            className="formItem"
          >
            <Checkbox
              label="Exact match only"
              onChange={
                [MockFunction] {
                  "calls": [
                    [
                      {
                        "length": false,
                      },
                    ],
                  ],
                  "results": [
                    {
                      "type": "return",
                      "value": undefined,
                    },
                  ],
                }
              }
              tooltipText="Ticking this box means we won't match partially against text, you'll get whole word matched only"
              value=""
            />
          </div>
          <div
            className="formItem"
          >
            <Button
              disabled={true}
              label="Search"
              onClick={[Function]}
            />
          </div>
        </form>
        <Tab
          currentTab=""
          onChangeTab={[Function]}
          tabContent={
            [
              {
                "content": <AccountsTableView />,
                "value": 1,
              },
              {
                "content": <AccountsTableView
                  billingTypeParams="All"
                  brand=""
                  brands={
                    [
                      {
                        "label": "All",
                        "value": "All",
                      },
                      {
                        "label": "SinchEU",
                        "value": "SinchEU",
                      },
                      {
                        "label": "SimpleTexting",
                        "value": "SimpleTexting",
                      },
                    ]
                  }
                  category=""
                  countries=""
                  currentPageSize={10}
                  dateRangeParams={[]}
                  disabled={true}
                  handleClearAll={[Function]}
                  handleSearch={[Function]}
                  handleSetURLParams={[Function]}
                  integrationTypeParams={[]}
                  isClearedAll=""
                  searchParams={
                    {
                      "": "",
                      "billingType": "",
                      "status": "",
                      "vendorId": "",
                      "verificationStatus": "",
                    }
                  }
                  searchValue=""
                  setTokenQueue={
                    [MockFunction] {
                      "calls": [
                        [
                          {
                            "length": false,
                          },
                        ],
                      ],
                      "results": [
                        {
                          "type": "return",
                          "value": undefined,
                        },
                      ],
                    }
                  }
                  status=""
                  tokenQueue=""
                  verificationStatus=""
                />,
                "value": 2,
              },
            ]
          }
          tabs={
            [
              {
                "text": "Recently Accessed Accounts",
                "value": 1,
              },
              {
                "text": "Search Results",
                "value": 2,
              },
            ]
          }
        />
      </div>
    </ErrorBoundary>
  </div>
   
</ContextProvider>
`;
