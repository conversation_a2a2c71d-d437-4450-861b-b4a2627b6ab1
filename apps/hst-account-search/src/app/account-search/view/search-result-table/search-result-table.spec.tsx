import * as ReactRedux from 'react-redux'
// import * as ReactRouterDom from 'react-router-dom'
import React from 'react'

import * as AccountSearchContext from '../../../../context/app-context'
import { AccountsTableView, tableColumns } from './search-result-table'

const RECENT_SEARCH_LIST = ['']

jest.mock('@sinch-smb/dev-utils', () => ({
  handleAPI: (func) => (args) => func(args),
  storage: {
    getData: () => RECENT_SEARCH_LIST
  }
}))
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: jest.fn(),
  useSelector: jest.fn(),
}));
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: jest.fn(),
}));

let useEffect
let useDispatch
const setState = jest.fn()
const useState = jest.spyOn(React, 'useState')
const mockUseEffect = () => { useEffect.mockImplementation((f) => f()) }
const mockUseDispatch = () => useDispatch.mockImplementation(() => jest.fn())
const mockUseState = (state) => useState.mockImplementation(() => [state, setState])
jest.spyOn(AccountSearchContext, 'useAppContext').mockImplementation(() => []);

beforeEach(() => {
  useDispatch = jest.spyOn(ReactRedux, 'useDispatch')
  useEffect = jest.spyOn(React, 'useEffect')
  mockUseEffect()
  mockUseDispatch()
  mockUseState(false)
})

describe('Search Result Table', () => {
  const wrapper = shallow(<AccountsTableView />)
  it('renders', () => {
    expect(wrapper).toBeTruthy()
  })

})

describe('TableColumns', () => {
  const mockNavigate = jest.fn()
  const mockOpenSumary = jest.fn()
  const mockViewAccount = jest.fn()

  it('should render correctly', () => {
    const testColumns = tableColumns(mockNavigate, mockOpenSumary)
    expect(testColumns[0].render('accountId')).toMatchSnapshot()
    expect(testColumns[1].render({ content: { parentAccountId: "" } })).toMatchSnapshot()
    expect(testColumns[2].render({ highlightFields: {} })).toMatchSnapshot()
    expect(testColumns[2].render()).toMatchSnapshot()
    expect(testColumns[2].render({
      highlightFields: {
        keyword: ["<em>ABCD_PWW_0001</em>"],
        label: ["<em>ABCD</em>"]
      }
    })).toMatchSnapshot()
    expect(testColumns[3].render({ content: { vendorId: "MM" } })).toMatchSnapshot()
    expect(testColumns[4].render({ content: { country: "AT" } })).toMatchSnapshot()
    expect(testColumns[5].render({ content: { status: "ACTIVE" } })).toMatchSnapshot()
    expect(testColumns[6].render({ content: { verificationStatus: "VERIFIED" } })).toMatchSnapshot()
    expect(testColumns[7].render({ content: { billing: { billingType: "123" } } })).toMatchSnapshot()
    expect(testColumns[8].render({ restrictedAccess: "RESTRICTED" })).toMatchSnapshot()
    expect(testColumns[8].render({})).toMatchSnapshot()
    expect(testColumns[9].render("")).toMatchSnapshot()
  })
  it('should click on columns', () => {
    const testColumns = tableColumns(mockNavigate, mockOpenSumary, mockViewAccount)

    const accountIdColum = shallow(testColumns[0].render({ content: { accountId: 'test', label: 'test' } }).props.children[0])
    accountIdColum.find("#account-id").simulate('click')

    const parentColum = shallow(testColumns[1].render({ content: { parentAccountId: 'test' } }))
    parentColum.find("#parent-account").simulate('click')

    const actionColum = shallow(testColumns[12].render({ content: { accountId: 'test' } }))
    actionColum.find("#view-account").simulate('click')
  })
})
