// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TableColumns should render correctly 1`] = `
<React.Fragment>
  <Link
    aria-label="Link"
    href="#"
    id="account-id"
    onClick={[Function]}
    preventDefault={true}
  />
  <span />
</React.Fragment>
`;

exports[`TableColumns should render correctly 2`] = `
<Link
  aria-label="Link"
  href=""
  id="parent-account"
  onClick={[Function]}
  preventDefault={true}
  text=""
/>
`;

exports[`TableColumns should render correctly 3`] = `
<Text
  type="s"
/>
`;

exports[`TableColumns should render correctly 4`] = `
<Text
  type="s"
/>
`;

exports[`TableColumns should render correctly 5`] = `
<Text
  type="s"
/>
`;

exports[`TableColumns should render correctly 6`] = `<React.Fragment />`;

exports[`TableColumns should render correctly 7`] = `
<Text
  type="s"
/>
`;

exports[`TableColumns should render correctly 8`] = `"-"`;

exports[`TableColumns should render correctly 9`] = `undefined`;

exports[`TableColumns should render correctly 10`] = `undefined`;

exports[`TableColumns should render correctly 11`] = `
<Text
  type="s"
/>
`;

exports[`TableColumns should render correctly 12`] = `
<Text
  type="s"
/>
`;

exports[`TableColumns should render correctly 13`] = `
<Text
  type="s"
>
  -
</Text>
`;
