import '@nectary/assets/icons/check-circle-outline';
import '@nectary/assets/icons/not-interested';
import { countries as NECTARY_COUNTRIES } from '@nectary/components/utils/countries';
import { storage } from '@sinch-smb/dev-utils';
import { DORMANT } from 'apps/hst-account-details/src/app/constants';
import { AccountItem } from 'apps/hst-account-search/src/types';
import {
  ACTIVE,
  CANCELLED,
  RESTRICTED,
  SUSPENDED,
  UNRESTRICTED,
  UNVERIFIED,
  VERIFIED,
} from 'apps/hst-core/src/constants/account-verification';
import { customDateTimeFormatReadable, isNonEmptyString } from 'helpers';
import _compact from 'lodash/compact';
import _get from 'lodash/get';
import _take from 'lodash/take';
import _uniqBy from 'lodash/uniqBy';
import moment from 'moment';
import {
  Button,
  DateRange,
  Flag,
  MultiSelect,
  Link as NectaryLink,
  Select,
  Table,
  Tag,
  Text,
  Tooltip,
} from 'nectary';
import PropTypes from 'prop-types';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { SUCCEEDED } from '../../../../constants/redux-constants';
import { useAppContext } from '../../../../context/app-context';
import {
  loadAccounts,
  loadAllAccounts,
  syncAccount,
} from '../../../../redux/account-search/account-search-actions';
import {
  getSupportAccounts,
  getSupportAccountsLoading,
  getSyncAccountLoading,
} from '../../../../redux/account-search/account-search-selectors';
import {
  ALL_CATEGORIES,
  BILLING_TYPES,
  COUNTRIES,
  COUNTRIES_EU,
  INTEGRATION_TYPES,
  ONENZ,
  STATUSES,
  VERIFICATION_STATUSES,
  VODANZ,
} from '../constants';
import Modal from '../partials/account-summary-modal/modal';
import { RECENT_SEARCH_LIST } from '../recently-accessed-table/recently-accessed-table';
import styles from './search-result-table.module.less';

export const TAG_MAPPING = {
  accountId: 'Account ID',
  'accountId.keyword': 'Account ID',
  vendorId: 'Vendor ID',
  'vendorId.keyword': 'Vendor ID',
  label: 'Account Name',
  'label.keyword': 'Account Name',
  status: 'Status',
  'status.keyword': 'Status',
  verificationStatus: 'Verification Status',
  'verificationStatus.keyword': 'Verification Status',
  type: 'Account Type',
  'type.keyword': 'Account Type',
  verificationReason: 'Verification Reason',
  'verificationReason.keyword': 'Verification Reason',
  parentAccountId: 'Parent Account ID',
  'parentAccountId.keyword': 'Parent Account ID',
  'billing.billingAccountId': 'Billing Account ID',
  'billing.billingAccountId.keyword': 'Billing Account ID',
  'billing.billingType': 'Billing Type',
  'billing.billingType.keyword': 'Billing Type',
  'users.username': 'Username',
  'users.username.keyword': 'Username',
  'users.email': 'User Email',
  'users.email.keyword': 'User Email',
  'users.email.custom': 'User Email',
  'users.email.custom.keyword': 'User Email',
  'users.phone': 'User Phone Number',
  'users.phone.keyword': 'User Phone Number',
  'users.firstName': 'User First Name',
  'users.firstName.keyword': 'User First Name',
  'users.lastName': 'User Last Name',
  'users.lastName.keyword': 'User Last Name',
  'integrations.id': 'Integration ID',
  'integrations.id.keyword': 'Integration ID',
  'integrations.type': 'Integration Type',
  'integrations.type.keyword': 'Integration Type',
  'integrations.status': 'Integration Status',
  'integrations.status.keyword': 'Integration Status',
  'integrations.externalPlatformId': 'Integration Platform ID',
  'integrations.externalPlatformId.keyword': 'Integration Platform ID',
  'integrations.externalPlatformId.url': 'Integration Platform ID',
  'integrations.externalPlatformId.url.keyword': 'Integration Platform ID',
  carrierBillingNumber: 'Carrier billing',
  'carrierBillingNumber.keyword': 'Carrier billing',
  'e2s.email': 'E2S Email',
  'e2s.email.custom': 'E2S Email',
  'e2s.email.keyword': 'E2S Email',
  'e2s.domain': 'E2S Domain',
  'e2s.domain.custom': 'E2S Domain',
  'apiKeys.key': 'API Key',
  'apiKeys.key.keyword': 'API Key',
  'apiKeys.label': 'API Key name',
  'apiKeys.label.keyword': 'API Key name',
  'dedicatedNumber.label': 'Dedicated Number',
  'dedicatedNumber.label.keyword': 'Dedicated Number',
  'dedicatedNumbers.number': 'Dedicated Number',
  'dedicatedNumbers.number.keyword': 'Dedicated Number',
  senderAddresses: 'Sender Address',
  'senderAddresses.keyword': 'Sender Address',
  legacyCredentialUsers: 'Legacy Credential Users',
  'legacyCredentialUsers.keyword': 'Legacy Credential Users',
};

export const tableColumns = (
  searchValue: string,
  handleOpenSummary: (item: AccountItem) => void,
  onViewAccount: (accountId: string, item: AccountItem) => void,
  onSyncAccount: (item: AccountItem) => void
) => [
  {
    title: 'Account',
    index: 'accountId',
    sort: false,
    align: 'left',
    render: (value: AccountItem) => (
      <>
        <NectaryLink
          id="account-id"
          text={value?.content?.accountId}
          href="#"
          aria-label="Link"
          preventDefault
          onClick={() => handleOpenSummary(value)}
        />
        <span>{value?.content?.label}</span>
      </>
    ),
  },
  {
    title: 'Parent Account',
    index: 'parentAccountId',
    sort: false,
    align: 'left',
    render: (value: AccountItem) => {
      const data = {
        ...value,
        content: { ...value.content, accountId: value.content.parentAccountId },
      };
      return (
        <NectaryLink
          id="parent-account"
          text={value?.content?.parentAccountId}
          href=""
          preventDefault
          aria-label="Link"
          onClick={() => handleOpenSummary(data)}
        />
      );
    },
  },
  {
    title: 'Type',
    sort: false,
    index: 'type',
    align: 'left',
    render: (value: AccountItem) => (
      <Text type="s">{value?.content?.type}</Text>
    ),
  },
  {
    title: 'Match',
    tooltipText: 'Show the categories where the keyword matches.',
    index: 'match',
    sort: false,
    align: 'left',
    render: (value: AccountItem) => {
      const fields = Object.keys(value?.highlightFields || {}) || [];
      let combinedFields = fields.map((field) => ({
        field,
        translatedField: TAG_MAPPING[field],
      }));
      combinedFields = _uniqBy(combinedFields, 'translatedField');
      return (
        <>
          {combinedFields?.map(({ field, translatedField }) => {
            const displayValue = _get(value?.highlightFields[field], '[0]')
              ?.replaceAll('<em>', '')
              .replaceAll('</em>', '');
            return (
              <div className={styles.matchTag}>
                <Tooltip orientation="top" type="fast" text={displayValue}>
                  <div>
                    {displayValue === searchValue ? (
                      <Tag
                        key={translatedField}
                        color="light-green"
                        text={translatedField}
                      />
                    ) : (
                      <Tag
                        key={translatedField}
                        color="light-gray"
                        text={translatedField}
                      />
                    )}
                  </div>
                </Tooltip>
              </div>
            );
          })}
        </>
      );
    },
  },
  {
    title: 'Brand',
    sort: false,
    index: 'vendorId',
    align: 'left',
    render: (value: AccountItem) => (
      <Text type="s">{value?.content?.vendorId}</Text>
    ),
  },
  {
    title: 'Country',
    sort: false,
    index: 'country',
    align: 'left',
    render: (value: AccountItem) => {
      const country = _get(value, 'content.country', null);
      if (!country) return '-';
      const countryObj = _get(NECTARY_COUNTRIES, country.toLowerCase(), null);

      if (!countryObj) return '-';

      return (
        <Tooltip type="fast" orientation="top" text={countryObj.name}>
          <div className={styles.countryFlag}>
            <Flag code={country.toLowerCase()} />
          </div>
        </Tooltip>
      );
    },
  },
  {
    title: 'Status',
    sort: false,
    index: 'status',
    align: 'left',
    render: (value: AccountItem) =>
      ({
        [ACTIVE]: (
          <div className={styles.verified}>
            <Text type="s">{value?.content?.status}</Text>
          </div>
        ),
        [UNVERIFIED]: (
          <div className={styles.unverified}>
            <Text type="s">{value?.content?.status}</Text>
          </div>
        ),
        [CANCELLED]: (
          <div className={styles.unverified}>
            <Text type="s">{value?.content?.status}</Text>
          </div>
        ),
        [SUSPENDED]: (
          <div className={styles.unverified}>
            <Text type="s">{value?.content?.status}</Text>
          </div>
        ),
        [DORMANT]: (
          <div className={styles.dormant}>
            <Text type="s">{value?.content?.status}</Text>
          </div>
        ),
      }[value?.content?.status]),
  },
  {
    title: 'Verification',
    sort: false,
    index: 'verificationStatus',
    align: 'left',
    render: (value: AccountItem) =>
      ({
        [VERIFIED]: (
          <div className={styles.verified}>
            <Text type="s">{value?.content?.verificationStatus}</Text>
          </div>
        ),
        [UNVERIFIED]: (
          <div className={styles.unverified}>
            <Text type="s">{value?.content?.verificationStatus}</Text>
          </div>
        ),
      }[value?.content?.verificationStatus]),
  },
  {
    title: 'Billing Type',
    sort: false,
    index: 'billing',
    align: 'left',
    render: (value: AccountItem) => (
      <Text type="s">{value?.content?.billing?.billingType}</Text>
    ),
  },
  {
    title: 'Date Created',
    sort: false,
    index: 'createdAt',
    align: 'left',
    render: (value: AccountItem) => (
      <Text type="s">
        {isNonEmptyString(value?.content?.createdAt)
          ? customDateTimeFormatReadable({
              datetime: value?.content?.createdAt,
              showTime: true,
            })
          : '-'}
      </Text>
    ),
  },
  {
    title: 'Access',
    sort: false,
    index: '',
    align: 'left',
    render: (value: AccountItem) =>
      value?.content?.restrictedAccess ? (
        <div className={styles.restricted}>
          <sinch-icon-not-interested
            title="sinch-icon-not-interested"
            style={{ marginRight: 10 }}
          />
          <Text type="s">{RESTRICTED}</Text>
        </div>
      ) : (
        <div className={styles.unrestricted}>
          <sinch-icon-check-circle-outline
            title="sinch-icon-check-circle-outline"
            style={{ marginRight: 10 }}
          />
          <Text type="s">{UNRESTRICTED}</Text>
        </div>
      ),
  },
  {
    title: 'Sync',
    index: 'id',
    sort: false,
    align: 'left',
    render: (item: AccountItem) => (
      <div>
        <Button
          id="sync-account"
          label="Sync"
          onClick={() => onSyncAccount(item)}
        />
      </div>
    ),
  },
  {
    title: 'Action',
    index: 'id',
    sort: false,
    align: 'left',
    render: (item: AccountItem) => (
      <div>
        <Button
          id="view-account"
          label="View Account"
          onClick={() => onViewAccount(item?.content?.accountId, item)}
        />
      </div>
    ),
  },
];

export const saveToRecentlyAccessedAccounts = (
  accountId: string,
  accountData: AccountItem
) => {
  let recentlySearchList = storage.getData(RECENT_SEARCH_LIST) || [];
  const currentItem = recentlySearchList.find(
    (account: AccountItem) => account?.content?.accountId === accountId
  );
  if (currentItem) {
    recentlySearchList = recentlySearchList.filter(
      (recentItem: AccountItem) => recentItem.content.accountId !== accountId
    );
    recentlySearchList.unshift(accountData);
  } else {
    recentlySearchList.unshift(accountData);
  }
  recentlySearchList = _compact(recentlySearchList);
  recentlySearchList = _take(recentlySearchList, 10);
  storage.saveData(RECENT_SEARCH_LIST, recentlySearchList);
};

export const AccountsTableView = ({
  currentPageSize,
  searchParams,
  category,
  searchValue,
  handleSetURLParams,
  billingTypeParams,
  integrationTypeParams,
  brand,
  brands,
  status,
  verificationStatus,
  countries,
  isClearedAll,
  handleClearAll,
  handleSearch,
  disabled,
  tokenQueue,
  setTokenQueue,
  dateRangeParams,
}) => {
  const navigate = useNavigate();
  const [
    setBillingType,
    setBrand,
    setStatus,
    setVerificationStatus,
    setDateRange,
    setCountries,
    setIntegrationType,
  ] = useAppContext();
  const dispatch = useDispatch();

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [accountSummary, setAccountSummary] = useState<AccountItem | null>(
    null
  );
  const accountList = useSelector(getSupportAccounts);
  const loadingStatus = useSelector(getSupportAccountsLoading);
  const loadingSyncAccount = useSelector(getSyncAccountLoading);
  const nextToken = _get(accountList?.pagination, 'next', '');
  const countryList = IS_EU_VENDOR ? COUNTRIES_EU : COUNTRIES;

  useEffect(() => {
    if (loadingSyncAccount === SUCCEEDED) {
      handleSearch('2');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loadingSyncAccount]);

  const handlePreviousPage = () => {
    tokenQueue.pop();
    setTokenQueue(tokenQueue);
    const tokensLen = tokenQueue.length;
    if (category === ALL_CATEGORIES) {
      dispatch(
        loadAllAccounts({
          size: currentPageSize,
          next: tokensLen ? tokenQueue[tokensLen - 1] : null,
          ...(searchValue && { term: searchValue }),
          ...searchParams,
        })
      );
    } else {
      dispatch(
        loadAccounts({
          size: currentPageSize,
          next: tokensLen ? tokenQueue[tokensLen - 1] : null,
          ...searchParams,
        })
      );
    }
  };

  const handleNextPage = () => {
    tokenQueue.push(nextToken);
    setTokenQueue(tokenQueue);
    if (category === ALL_CATEGORIES) {
      dispatch(
        loadAllAccounts({
          size: currentPageSize,
          next: nextToken,
          ...(searchValue && { term: searchValue }),
          ...searchParams,
        })
      );
    } else {
      dispatch(
        loadAccounts({
          size: currentPageSize,
          next: nextToken,
          ...searchParams,
        })
      );
    }
  };

  const handleSelectBillingType = (value: string) => {
    handleSetURLParams('billingType', value);
    setBillingType(value);
  };

  const handleSelectBrand = (value: string) => {
    handleSetURLParams('brand', value);
    setBrand(value);
  };

  const handleSelectStatus = (value: string) => {
    handleSetURLParams('status', value);
    setStatus(value);
  };

  const handleSelectVerificationStatus = (value: string) => {
    handleSetURLParams('verificationStatus', value);
    setVerificationStatus(value);
  };

  const handleSelectCountries = (value: string[]) => {
    handleSetURLParams('countries', value);
    value[0] ? setCountries(value) : setCountries([]);
  };

  const handleSelectIntegrationType = (value: string[]) => {
    handleSetURLParams('integrationType', value);
    value[0] ? setIntegrationType(value) : setIntegrationType([]);
  };

  const handleChangeDateRange = (dates: string[]) => {
    handleSetURLParams('dateRange', dates);
    setDateRange(dates);
  };

  const handleOpenSummary = (account: AccountItem) => {
    setAccountSummary(account);
    setIsDialogOpen((prev) => ({ ...prev, isDialogOpen: !isDialogOpen }));
  };

  const handleCloseSummary = () => {
    setIsDialogOpen(false);
  };

  const handleViewAccount = (accountId: string, account: AccountItem) => {
    let baseName = _get(account?.content, 'vendorId', '').toLowerCase();
    if (baseName === VODANZ) baseName = ONENZ;
    saveToRecentlyAccessedAccounts(accountId, account);
    navigate(`/accounts/${baseName}/${accountId}`, {
      state: { from: 'support' },
    });
  };

  const handleSyncAccount = (item: AccountItem) => {
    const { content } = item || {};
    const { vendorId, accountId } = content || {};
    if (!vendorId || !accountId) return;

    dispatch(syncAccount(vendorId, accountId));
  };

  return (
    <>
      <div className={styles.filterWrapper}>
        <>
          {' '}
          <div className={styles.formItem}>
            <Select
              label="Brand (Vendor)"
              placeholder="All"
              options={brands}
              value={brand}
              onSelect={handleSelectBrand}
              isClearedAll={isClearedAll}
              testId="brand-select"
              customStyles={{ width: 170 }}
            />
          </div>
          <div className={styles.formItem}>
            <MultiSelect
              label="Country"
              value={countries}
              placeholder="All"
              options={countryList}
              onSelect={handleSelectCountries}
              isClearedAll={isClearedAll}
              customStyles={{ width: 170 }}
            />
          </div>
          <div className={styles.formItem}>
            <Select
              label="Status"
              value={status}
              placeholder="All"
              options={STATUSES}
              onSelect={handleSelectStatus}
              isClearedAll={isClearedAll}
              testId="status-select"
              customStyles={{ width: 170 }}
            />
          </div>
          <div className={styles.formItem}>
            <Select
              label="Verification Status"
              value={verificationStatus}
              placeholder="All"
              options={VERIFICATION_STATUSES}
              onSelect={handleSelectVerificationStatus}
              isClearedAll={isClearedAll}
              testId="verification-status-select"
              customStyles={{ width: 170 }}
            />
          </div>
          <div className={styles.billingType}>
            <Select
              label="Billing Type"
              value={billingTypeParams}
              placeholder="All"
              options={BILLING_TYPES}
              onSelect={handleSelectBillingType}
              isClearedAll={isClearedAll}
              testId="billing-type-select"
              customStyles={{ width: 170 }}
            />
          </div>
          <div className={styles.billingType}>
            <MultiSelect
              label="Integration Type"
              value={integrationTypeParams}
              placeholder="All"
              options={INTEGRATION_TYPES}
              onSelect={handleSelectIntegrationType}
              isClearedAll={isClearedAll}
              customStyles={{ width: 170 }}
            />
          </div>
          <div className={styles.formItem}>
            <DateRange
              label="Created Date"
              value={dateRangeParams}
              onChange={handleChangeDateRange}
              min={moment().subtract('1', 'years').format('YYYY-MM-DD')}
              max={moment().endOf('day').format('YYYY-MM-DD')}
              testId="date-range"
              disabled
            />
          </div>
          <div className={styles.applyFilter}>
            <Button
              disabled={disabled}
              label="Apply Filters"
              onClick={() => handleSearch('2')}
            />
          </div>
          <div className={styles.clearAll}>
            <Button
              type="secondary"
              label="Clear All"
              onClick={() => handleClearAll()}
            />
          </div>
          {/* Temprary hide */}
          {/* <Button label="Clear all" type="secondary" onClick="" /> */}
        </>
      </div>
      <Table
        hasCheckbox={false}
        tableColumns={tableColumns(
          searchValue,
          handleOpenSummary,
          handleViewAccount,
          handleSyncAccount
        )}
        tableData={accountList?.resources}
        currentPageSize={currentPageSize}
        loading={loadingStatus}
        max={0}
        next={nextToken}
        previous={tokenQueue?.length}
        handlePreviousPage={handlePreviousPage}
        handleNextPage={handleNextPage}
        scrollX
        pageSize={Number(currentPageSize)}
        handleChangePageSize={(size: number) => handleSearch('2', size)}
      />
      <Modal
        searchValue={searchValue}
        isDialogOpen={isDialogOpen}
        onClose={handleCloseSummary}
        account={accountSummary}
        // onViewAccountLegacy={handleViewAccountLegacy}
        onViewAccount={handleViewAccount}
      />
    </>
  );
};

AccountsTableView.propTypes = {
  currentPageSize: PropTypes.string.isRequired,
  searchParams: PropTypes.shape({
    category: PropTypes.string,
    billingType: PropTypes.string,
    brand: PropTypes.string,
    status: PropTypes.string,
    country: PropTypes.string,
  }).isRequired,
  category: PropTypes.string.isRequired,
  searchValue: PropTypes.string.isRequired,
  handleSetURLParams: PropTypes.string.isRequired,
  billingTypeParams: PropTypes.string.isRequired,
  brand: PropTypes.string.isRequired,
  status: PropTypes.string.isRequired,
  countries: PropTypes.string.isRequired,
};

AccountsTableView.defaultProps = {};

export default AccountsTableView;
