@import '~@sinch-smb/styles/variables.less';

.verified {
  align-items: flex-start;
  display: flex;
  justify-content: flex-start;
  sinch-text {
    color: var(--sinch-ref-color-complementary-olive-400);
  }
  --sinch-global-color-icon: var(--sinch-ref-color-complementary-olive-400);
}

.unverified {
  align-items: flex-start;
  display: flex;
  justify-content: flex-start;
  sinch-text {
    color: var(--sinch-ref-color-complementary-jasper-400);
  }
  --sinch-global-color-icon: var(--sinch-ref-color-complementary-jasper-400);
}

.dormant {
  align-items: flex-start;
  display: flex;
  justify-content: flex-start;
  sinch-text {
    color: var(--sinch-ref-color-complementary-orange-400);
  }
  --sinch-global-color-icon: var(--sinch-ref-color-complementary-orange-400);
}

.filterWrapper {
  display: flex;
  margin: 20px 0;
  text-align: left;
  flex-wrap: wrap;
  > div {
    margin-bottom: 16px;
  }
  sinch-button {
    margin: 4px 0 0 10px;
  }
}

.matchTag {
  margin-bottom: .rem(2px) [];
}

.unrestricted {
  align-items: flex-start;
  display: flex;
  justify-content: flex-start;
  sinch-text {
    color: var(--sinch-ref-color-complementary-olive-400);
  }
  --sinch-global-color-icon: var(--sinch-ref-color-complementary-olive-400);
}

.restricted {
  align-items: flex-start;
  display: flex;
  justify-content: flex-start;
  sinch-text {
    color: var(--sinch-ref-color-complementary-jasper-400);
  }
  --sinch-global-color-icon: var(--sinch-ref-color-complementary-jasper-400);
}

.clearAll,
.applyFilter {
  margin-top: 23px;
}

.formItem {
  margin: 0 17px 0 0;
}

.billingType {
  margin: 0 10px 0 0;
}

.countryFlag {
  --sinch-global-size-icon: 32px;
}
