import '@nectary/assets/icons/content-copy';
import '@nectary/assets/icons/person';
import '@nectary/components/button';
import '@nectary/components/checkbox';
import '@nectary/components/dialog';
import '@nectary/components/icon';
import '@nectary/components/spinner';
import { customDateTimeFormatReadable } from '@sinch-smb/dev-utils';
import { POSTPAID } from 'apps/hst-core/src/constants/billing';
import { LOADING } from 'apps/hst-core/src/constants/loading-status';
import labelFunc from 'apps/hst-core/src/constants/vendor-labels';
import { SupportSelectAccountModal } from 'components';
import { haveImpersonationRole, isNonEmptyString, msalInstance } from 'helpers';
import { withPermissions } from 'hocs';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _uniqBy from 'lodash/uniqBy';
import { Flag, Tag, Text, Tooltip } from 'nectary';
import PropTypes from 'prop-types';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getAuthAzure } from '../../../../../../../hst-core/src/redux/auth-azure/auth-selectors';
import { loadAccountPreview } from '../../../../../redux/account-search/account-search-actions';
import {
  getSupportAccountPreview,
  getSupportAccountPreviewLoading,
} from '../../../../../redux/account-search/account-search-selectors';
import { TAG_MAPPING } from '../../search-result-table/search-result-table';
import styles from './modal.module.less';

// temporary use for Account Search purpose, will split to a different modal in future
export const Modal = ({
  isDialogOpen,
  onClose,
  account,
  onViewAccount,
  roles,
  searchValue,
}) => {
  const { content, highlightFields } = account || {};
  const { accountId, vendorId, country, countryName } = content || {};
  const dispatch = useDispatch();
  const accountPreviewData = useSelector(getSupportAccountPreview);
  const accountPreviewDataLoading = useSelector(
    getSupportAccountPreviewLoading
  );
  const {
    mtdVolume,
    totalSubAccounts,
    totalUsers,
    createdAt,
    billingAccountId,
    balance,
    integrations,
    billingType,
    label,
  } = accountPreviewData;
  const [loginAs, setLoginAs] = useState({
    account: null,
    isAccountSelectionModalOpen: false,
  });

  const rolesPermissionsEnabled = labelFunc('roles-permissions');
  const havingImpersonationRole = haveImpersonationRole(
    roles,
    rolesPermissionsEnabled
  );
  const fields = Object.keys(highlightFields || {}) || [];
  let combinedFields = fields.map((field) => ({
    field,
    translatedField: TAG_MAPPING[field],
  }));
  combinedFields = _uniqBy(combinedFields, 'translatedField');
  const authAzure = useSelector(getAuthAzure);
  const activeAccount = msalInstance.getActiveAccount();
  const username = authAzure ? `${_get(activeAccount, 'username')}` : '';

  const handleLoginAs = () => {
    onClose();
    setLoginAs({
      account: { accountName: label, accountId, vendorId },
      isAccountSelectionModalOpen: true,
    });
  };

  const closeLoginAsModal = () => {
    setLoginAs({ ...loginAs, isAccountSelectionModalOpen: false });
  };

  const openLoginAsModal = () => {
    setLoginAs({ ...loginAs, isAccountSelectionModalOpen: true });
  };

  useEffect(() => {
    if (isDialogOpen) {
      dispatch(loadAccountPreview(vendorId, accountId));
    }
  }, [isDialogOpen, dispatch, vendorId, accountId]);

  const showInfo = (field: any) => {
    if (accountPreviewDataLoading === LOADING) {
      return <sinch-spinner size="m" />;
    }
    return field || '-';
  };

  const showNumberInfo = (field?: string) => {
    if (accountPreviewDataLoading === LOADING)
      return <sinch-spinner size="m" />;

    if (field === null || field === undefined) {
      return '-';
    }
    return field;
  };

  const renderCreatedAt = () => {
    if (accountPreviewDataLoading === LOADING) {
      return <sinch-spinner size="m" />;
    }
    return isNonEmptyString(createdAt)
      ? customDateTimeFormatReadable({
          datetime: createdAt,
          showTime: false,
        })
      : '-';
  };

  const renderBillingType = () => {
    if (accountPreviewDataLoading === LOADING) {
      return <sinch-spinner size="m" />;
    }
    if (billingType && billingType.includes(POSTPAID)) {
      return 'Not Applicable';
    }
    return balance || '-';
  };

  const renderIntegrations = () => {
    if (accountPreviewDataLoading === LOADING) {
      return <sinch-spinner size="m" />;
    }

    if (_isEmpty(integrations)) return '-';

    return integrations.map((item: string, index: number) => (
      <div key={item}>
        {item}
        {index === integrations.length - 1 ? '' : ','}
      </div>
    ));
  };

  return (
    <div className={styles.dialogWrapper}>
      <SupportSelectAccountModal
        isVisible={loginAs.isAccountSelectionModalOpen}
        openModal={openLoginAsModal}
        closeModal={closeLoginAsModal}
        account={loginAs.account}
        userEmail={username}
      />
      <sinch-dialog
        open={isDialogOpen}
        caption="Account Preview"
        aria-label="Dialog"
        close-aria-label="Close dialog"
        on-close={onClose}
      >
        <div slot="content">
          <div style={{ display: 'flex' }}>
            <div className={styles.leftColumn}>
              <Text type="m">Account Name:</Text>
              <Text type="m">Account ID:</Text>
              <Text type="m">Country</Text>
              <Text type="m">Billing Account ID:</Text>
              <Text type="m">Date Created:</Text>
              <Text type="m">Sub-Accounts:</Text>
              <Text type="m">Associated Users:</Text>
              <Text type="m">MTD Volume:</Text>
              <Text type="m">Prepaid Balance:</Text>
              <Text type="m">Integrations:</Text>
              <Text type="m">Search Categories Matched to:</Text>
            </div>
            <div className={styles.rightColumn}>
              <Text type="m">{showInfo(label)}</Text>
              <Text type="m">
                {showInfo(accountId)}
                <sinch-button
                  style={{ marginTop: -8 }}
                  size="s"
                  on-click={() => {
                    navigator.clipboard.writeText(accountId);
                  }}
                >
                  <sinch-icon-content-copy slot="icon" />
                </sinch-button>
              </Text>
              <Text type="m">
                {accountPreviewDataLoading === LOADING ? (
                  <sinch-spinner size="m" />
                ) : (
                  <div className={styles.countryFlag}>
                    <Tooltip type="fast" orientation="top" text={countryName}>
                      <div className={styles.countryFlag}>
                        <Flag code={country?.toLowerCase()} />
                      </div>
                    </Tooltip>
                  </div>
                )}
              </Text>
              <Text type="m">
                {showInfo(billingAccountId)}
                <sinch-button
                  style={{ marginTop: -8 }}
                  size="s"
                  on-click={() => {
                    navigator.clipboard.writeText(billingAccountId);
                  }}
                >
                  <sinch-icon-content-copy slot="icon" />
                </sinch-button>
              </Text>
              <Text type="m">{renderCreatedAt()}</Text>
              <Text type="m">{showNumberInfo(totalSubAccounts)}</Text>
              <Text type="m">
                <sinch-icon-person />
                {showNumberInfo(totalUsers)}
              </Text>
              <Text type="m">{showNumberInfo(mtdVolume)}</Text>
              <Text type="m">{renderBillingType()}</Text>
              <Text type="m">{renderIntegrations()}</Text>
              <div className={styles.fieldWrapper}>
                <>
                  {combinedFields?.map(({ field, translatedField }) => {
                    const displayValue = _get(highlightFields[field], '[0]')
                      ?.replaceAll('<em>', '')
                      .replaceAll('</em>', '');
                    return (
                      <div className={styles.matchTag}>
                        <Tooltip
                          orientation="top"
                          type="fast"
                          text={displayValue}
                        >
                          {displayValue === searchValue ? (
                            <Tag
                              key={translatedField}
                              color="light-green"
                              text={translatedField}
                            />
                          ) : (
                            <Tag
                              key={translatedField}
                              color="light-gray"
                              text={translatedField}
                            />
                          )}
                        </Tooltip>
                      </div>
                    );
                  })}
                </>
              </div>
            </div>
          </div>
        </div>

        <sinch-button
          slot="buttons"
          text="Login to the Hub"
          aria-label="Login to the Hub"
          type="secondary"
          on-click={handleLoginAs}
          disabled={!havingImpersonationRole}
        />
        <sinch-button
          slot="buttons"
          text="View Account"
          aria-label="View Account"
          on-click={() => onViewAccount(accountId, account)}
        >
          <span>View Account</span>
        </sinch-button>
      </sinch-dialog>
    </div>
  );
};

Modal.propTypes = {
  isDialogOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  account: PropTypes.shape({
    content: PropTypes.shape({
      label: PropTypes.string,
      accountId: PropTypes.string,
    }),
  }).isRequired,
  // onViewAccountLegacy: PropTypes.func.isRequired,
  onViewAccount: PropTypes.func.isRequired,
};

export default withPermissions(Modal);
