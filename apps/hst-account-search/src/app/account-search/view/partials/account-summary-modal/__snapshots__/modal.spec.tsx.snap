// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`View Modal Renders 1`] = `
<div
  className="dialogWrapper"
>
  <Connect(SupportSelectAccountModalComponent)
    account={null}
    closeModal={[Function]}
    isVisible={false}
    openModal={[Function]}
    userEmail="undefined"
  />
  <sinch-dialog
    aria-label="Dialog"
    caption="Account Preview"
    close-aria-label="Close dialog"
    on-close={[Function]}
    open={true}
  >
    <div
      slot="content"
    >
      <div
        style={
          {
            "display": "flex",
          }
        }
      >
        <div
          className="leftColumn"
        >
          <Text
            type="m"
          >
            Account Name:
          </Text>
          <Text
            type="m"
          >
            Account ID:
          </Text>
          <Text
            type="m"
          >
            Country
          </Text>
          <Text
            type="m"
          >
            Billing Account ID:
          </Text>
          <Text
            type="m"
          >
            Date Created:
          </Text>
          <Text
            type="m"
          >
            Sub-Accounts:
          </Text>
          <Text
            type="m"
          >
            Associated Users:
          </Text>
          <Text
            type="m"
          >
            MTD Volume:
          </Text>
          <Text
            type="m"
          >
            Prepaid Balance:
          </Text>
          <Text
            type="m"
          >
            Integrations:
          </Text>
          <Text
            type="m"
          >
            Search Categories Matched to:
          </Text>
        </div>
        <div
          className="rightColumn"
        >
          <Text
            type="m"
          >
            -
          </Text>
          <Text
            type="m"
          >
            123
            <sinch-button
              on-click={[Function]}
              size="s"
              style={
                {
                  "marginTop": -8,
                }
              }
            >
              <sinch-icon-content-copy
                slot="icon"
              />
            </sinch-button>
          </Text>
          <Text
            type="m"
          >
            <div
              className="countryFlag"
            >
              <Tooltip
                orientation="top"
                type="fast"
              >
                <div
                  className="countryFlag"
                >
                  <Flag />
                </div>
              </Tooltip>
            </div>
          </Text>
          <Text
            type="m"
          >
            -
            <sinch-button
              on-click={[Function]}
              size="s"
              style={
                {
                  "marginTop": -8,
                }
              }
            >
              <sinch-icon-content-copy
                slot="icon"
              />
            </sinch-button>
          </Text>
          <Text
            type="m"
          >
            -
          </Text>
          <Text
            type="m"
          >
            -
          </Text>
          <Text
            type="m"
          >
            <sinch-icon-person />
            -
          </Text>
          <Text
            type="m"
          >
            -
          </Text>
          <Text
            type="m"
          >
            -
          </Text>
          <Text
            type="m"
          >
            -
          </Text>
          <div
            className="fieldWrapper"
          >
            <div
              className="matchTag"
            >
              <Tooltip
                orientation="top"
                text="ABCD_PWW_0001"
                type="fast"
              >
                <Tag
                  color="light-gray"
                />
              </Tooltip>
            </div>
            <div
              className="matchTag"
            >
              <Tooltip
                orientation="top"
                text="ABCD"
                type="fast"
              >
                <Tag
                  color="light-gray"
                  key="Account Name"
                  text="Account Name"
                />
              </Tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
    <sinch-button
      aria-label="Login to the Hub"
      disabled={true}
      on-click={[Function]}
      slot="buttons"
      text="Login to the Hub"
      type="secondary"
    />
    <sinch-button
      aria-label="View Account"
      on-click={[Function]}
      slot="buttons"
      text="View Account"
    >
      <span>
        View Account
      </span>
    </sinch-button>
  </sinch-dialog>
</div>
`;

exports[`View Modal Renders empty data 1`] = `
<div
  className="dialogWrapper"
>
  <Connect(SupportSelectAccountModalComponent)
    account={null}
    closeModal={[Function]}
    isVisible={false}
    openModal={[Function]}
    userEmail="undefined"
  />
  <sinch-dialog
    aria-label="Dialog"
    caption="Account Preview"
    close-aria-label="Close dialog"
    on-close={[Function]}
    open={false}
  >
    <div
      slot="content"
    >
      <div
        style={
          {
            "display": "flex",
          }
        }
      >
        <div
          className="leftColumn"
        >
          <Text
            type="m"
          >
            Account Name:
          </Text>
          <Text
            type="m"
          >
            Account ID:
          </Text>
          <Text
            type="m"
          >
            Country
          </Text>
          <Text
            type="m"
          >
            Billing Account ID:
          </Text>
          <Text
            type="m"
          >
            Date Created:
          </Text>
          <Text
            type="m"
          >
            Sub-Accounts:
          </Text>
          <Text
            type="m"
          >
            Associated Users:
          </Text>
          <Text
            type="m"
          >
            MTD Volume:
          </Text>
          <Text
            type="m"
          >
            Prepaid Balance:
          </Text>
          <Text
            type="m"
          >
            Integrations:
          </Text>
          <Text
            type="m"
          >
            Search Categories Matched to:
          </Text>
        </div>
        <div
          className="rightColumn"
        >
          <Text
            type="m"
          >
            -
          </Text>
          <Text
            type="m"
          >
            -
            <sinch-button
              on-click={[Function]}
              size="s"
              style={
                {
                  "marginTop": -8,
                }
              }
            >
              <sinch-icon-content-copy
                slot="icon"
              />
            </sinch-button>
          </Text>
          <Text
            type="m"
          >
            <div
              className="countryFlag"
            >
              <Tooltip
                orientation="top"
                type="fast"
              >
                <div
                  className="countryFlag"
                >
                  <Flag />
                </div>
              </Tooltip>
            </div>
          </Text>
          <Text
            type="m"
          >
            -
            <sinch-button
              on-click={[Function]}
              size="s"
              style={
                {
                  "marginTop": -8,
                }
              }
            >
              <sinch-icon-content-copy
                slot="icon"
              />
            </sinch-button>
          </Text>
          <Text
            type="m"
          >
            -
          </Text>
          <Text
            type="m"
          >
            -
          </Text>
          <Text
            type="m"
          >
            <sinch-icon-person />
            -
          </Text>
          <Text
            type="m"
          >
            -
          </Text>
          <Text
            type="m"
          >
            -
          </Text>
          <Text
            type="m"
          >
            -
          </Text>
          <div
            className="fieldWrapper"
          />
        </div>
      </div>
    </div>
    <sinch-button
      aria-label="Login to the Hub"
      disabled={true}
      on-click={[Function]}
      slot="buttons"
      text="Login to the Hub"
      type="secondary"
    />
    <sinch-button
      aria-label="View Account"
      on-click={[Function]}
      slot="buttons"
      text="View Account"
    >
      <span>
        View Account
      </span>
    </sinch-button>
  </sinch-dialog>
</div>
`;

exports[`View Modal Renders prepaid 1`] = `
<div
  className="dialogWrapper"
>
  <Connect(SupportSelectAccountModalComponent)
    account={null}
    closeModal={[Function]}
    isVisible={false}
    openModal={[Function]}
    userEmail="undefined"
  />
  <sinch-dialog
    aria-label="Dialog"
    caption="Account Preview"
    close-aria-label="Close dialog"
    on-close={[Function]}
    open={true}
  >
    <div
      slot="content"
    >
      <div
        style={
          {
            "display": "flex",
          }
        }
      >
        <div
          className="leftColumn"
        >
          <Text
            type="m"
          >
            Account Name:
          </Text>
          <Text
            type="m"
          >
            Account ID:
          </Text>
          <Text
            type="m"
          >
            Country
          </Text>
          <Text
            type="m"
          >
            Billing Account ID:
          </Text>
          <Text
            type="m"
          >
            Date Created:
          </Text>
          <Text
            type="m"
          >
            Sub-Accounts:
          </Text>
          <Text
            type="m"
          >
            Associated Users:
          </Text>
          <Text
            type="m"
          >
            MTD Volume:
          </Text>
          <Text
            type="m"
          >
            Prepaid Balance:
          </Text>
          <Text
            type="m"
          >
            Integrations:
          </Text>
          <Text
            type="m"
          >
            Search Categories Matched to:
          </Text>
        </div>
        <div
          className="rightColumn"
        >
          <Text
            type="m"
          >
            -
          </Text>
          <Text
            type="m"
          >
            123
            <sinch-button
              on-click={[Function]}
              size="s"
              style={
                {
                  "marginTop": -8,
                }
              }
            >
              <sinch-icon-content-copy
                slot="icon"
              />
            </sinch-button>
          </Text>
          <Text
            type="m"
          >
            <div
              className="countryFlag"
            >
              <Tooltip
                orientation="top"
                type="fast"
              >
                <div
                  className="countryFlag"
                >
                  <Flag />
                </div>
              </Tooltip>
            </div>
          </Text>
          <Text
            type="m"
          >
            -
            <sinch-button
              on-click={[Function]}
              size="s"
              style={
                {
                  "marginTop": -8,
                }
              }
            >
              <sinch-icon-content-copy
                slot="icon"
              />
            </sinch-button>
          </Text>
          <Text
            type="m"
          >
            -
          </Text>
          <Text
            type="m"
          >
            -
          </Text>
          <Text
            type="m"
          >
            <sinch-icon-person />
            -
          </Text>
          <Text
            type="m"
          >
            -
          </Text>
          <Text
            type="m"
          >
            -
          </Text>
          <Text
            type="m"
          >
            -
          </Text>
          <div
            className="fieldWrapper"
          >
            <div
              className="matchTag"
            >
              <Tooltip
                orientation="top"
                text="ABCD_PWW_0001"
                type="fast"
              >
                <Tag
                  color="light-gray"
                />
              </Tooltip>
            </div>
            <div
              className="matchTag"
            >
              <Tooltip
                orientation="top"
                text="ABCD"
                type="fast"
              >
                <Tag
                  color="light-gray"
                  key="Account Name"
                  text="Account Name"
                />
              </Tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
    <sinch-button
      aria-label="Login to the Hub"
      disabled={true}
      on-click={[Function]}
      slot="buttons"
      text="Login to the Hub"
      type="secondary"
    />
    <sinch-button
      aria-label="View Account"
      on-click={[Function]}
      slot="buttons"
      text="View Account"
    >
      <span>
        View Account
      </span>
    </sinch-button>
  </sinch-dialog>
</div>
`;
