import * as ReactRedux from 'react-redux'
import React from 'react'
import Modal from './index'
import { PREPAID, POSTPAID } from '../../constants'

const RECENT_SEARCH_LIST = []

jest.mock('@sinch-smb/dev-utils', () => ({
  customDateTimeFormatReadable: jest.fn((args) => jest.fn(args)),
  handleAPI: (func) => (args) => func(args),
  // storage: (func) => (args) => func(args),
  handleAction: jest.fn(),
  storage: {
    getData: () => RECENT_SEARCH_LIST
  }
}))


jest.mock('hocs', () => ({
  withPermissions: (func) => (args) => func(args),
}));


let useEffect
let useDispatch
let useSelector

const previewDataEmpty = {
  loadingPreview: 'loading',
  accountPreviewData: {
    mtdVolume: '', totalSubAccounts: '', totalUsers: undefined, createdAt: '',
    billingAccountId: undefined, balance: '', integrations: undefined,
    billingType: '',
    label: ""
  }
}
const previewData = {
  loadingPreview: 'loaded',
  accountPreviewData: {
    mtdVolume: '', totalSubAccounts: '', totalUsers: '', createdAt: '2024',
    billingAccountId: '', balance: '', integrations: ['channel1', 'channel2'],
    billingType: POSTPAID,
    label: ""
  }
}
const previewDataPrepaid = {
  loadingPreview: 'loading',
  accountPreviewData: {
    mtdVolume: '', totalSubAccounts: '', totalUsers: '', createdAt: '2024',
    billingAccountId: '', balance: '', integrations: ['channel1', 'channel2'],
    billingType: PREPAID,
    label: ""
  }
}

const mockUseEffect = () => { useEffect.mockImplementation((f) => f()) }
const mockUseDispatch = () => useDispatch.mockImplementation(() => jest.fn())

beforeEach(() => {
  useDispatch = jest.spyOn(ReactRedux, 'useDispatch')
  useEffect = jest.spyOn(React, 'useEffect')
  useSelector = jest.spyOn(ReactRedux, 'useSelector')

  mockUseEffect()
  mockUseDispatch()
})

describe('View Modal', () => {
  it('Renders empty data', () => {
    const props = {
      isDialogOpen: false, onClose: () => { },
      onViewAccount: () => { }
    }
    const mockUseSelector = () => useSelector.mockImplementation(() => previewDataEmpty)
    mockUseSelector()
    const wrapper = shallow(<Modal {...props} />)
    expect(wrapper).toMatchSnapshot()
  })
  it('Renders', () => {
    const props = {
      isDialogOpen: true,
      onClose: () => { },
      account: {
        content: {
          accountId: '123'
        },
        highlightFields: {
          keyword: ["<em>ABCD_PWW_0001</em>"],
          label: ["<em>ABCD</em>"]
        }
      },
      onViewAccount: () => { },
      roles: []
    }
    const mockUseSelector = () => useSelector.mockImplementation(() => previewData)
    mockUseSelector()
    const wrapper = shallow(<Modal {...props} />)
    expect(wrapper).toMatchSnapshot()
  })
  it('Renders prepaid', () => {
    const props = {
      isDialogOpen: true, onClose: () => { }, account: {
        content: {
          accountId: '123'
        },
        highlightFields: {
          keyword: ["<em>ABCD_PWW_0001</em>"],
          label: ["<em>ABCD</em>"]
        }
      }, onViewAccount: () => { }
    }
    const mockUseSelector = () => useSelector.mockImplementation(() => previewDataPrepaid)
    mockUseSelector()
    const wrapper = shallow(<Modal {...props} />)

    wrapper.find('sinch-button[text="Login to the Hub"]').simulate('click')
    wrapper.find('sinch-button[text="View Account"]').at(0).simulate('click')

    expect(wrapper).toMatchSnapshot()
  })
})
