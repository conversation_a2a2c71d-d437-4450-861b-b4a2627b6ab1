import { withFeatureFlag } from '@sinch-smb/feature-flag';
import { SEGMENT_TRACK_EVENTS_V2 } from 'apps/hst-core/src/constants/segment';
import { segmentTracker } from 'apps/hst-core/src/redux/segment/segment-actions';
import { ErrorBoundary } from 'components';
import moment from 'moment';
import { Button, Checkbox, SearchInput, Select, Tab, Text } from 'nectary';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import { getAzureInfoDecoded } from '../../../../../hst-core/src/redux/auth-azure/auth-selectors';
import AccountSearchContext from '../../../context/app-context';
import {
  loadAccounts,
  loadAllAccounts,
} from '../../../redux/account-search/account-search-actions';
import styles from './account-search.module.less';
import {
  ACCOUNT_ID,
  ACCOUNT_NAME,
  ALL_CATEGORIES,
  API_KEY,
  API_NAME,
  BILLING_ACCOUNT_ID,
  BILLING_TYPES,
  BRANDS,
  BRANDS_EU,
  CARRIER_BILLING_NUMBER,
  DEDICATED_NUMBER,
  E2S_EMAIL,
  INTEGRATION_PLATFORM_ID,
  KEY_ACCOUNT_ID,
  KEY_ACCOUNT_NAME,
  KEY_API_KEY,
  KEY_API_NAME,
  KEY_BILLING_ACCOUNT_ID,
  KEY_CARRIER_BILLING,
  KEY_DEDICATED_NUMBER,
  KEY_E2S_EMAIL,
  KEY_INTEGRATION_PLATFORM_ID,
  KEY_SENDER_ADDRESS,
  KEY_USER_EMAIL,
  KEY_USER_PHONE,
  MAX_INPUT_LENGTH,
  SENDER_ADDRESS,
  STATUSES,
  USER_EMAIL,
  USER_PHONE,
  VERIFICATION_STATUSES,
} from './constants';
import RecentlyAccessedTable from './recently-accessed-table/recently-accessed-table';
import SearchResultTable from './search-result-table/search-result-table';

const tabs = () => [
  {
    value: 1,
    text: 'Recently Accessed Accounts',
  },
  {
    value: 2,
    text: 'Search Results',
  },
];

const tableContent = (
  currentPageSize,
  searchParams,
  category,
  searchValue,
  handleSetURLParams,
  billingTypeParams,
  integrationTypeParams,
  brand,
  brands,
  status,
  verificationStatus,
  countries,
  isClearedAll,
  handleClearAll,
  handleSearch,
  disabled,
  tokenQueue,
  setTokenQueue,
  dateRangeParams
) => [
  {
    value: 1,
    content: <RecentlyAccessedTable />,
  },
  {
    value: 2,
    content: (
      <SearchResultTable
        currentPageSize={currentPageSize}
        searchParams={searchParams}
        category={category}
        searchValue={searchValue}
        handleSetURLParams={handleSetURLParams}
        billingTypeParams={billingTypeParams}
        integrationTypeParams={integrationTypeParams}
        brand={brand}
        brands={brands}
        status={status}
        verificationStatus={verificationStatus}
        countries={countries}
        isClearedAll={isClearedAll}
        handleClearAll={handleClearAll}
        handleSearch={handleSearch}
        disabled={disabled}
        tokenQueue={tokenQueue}
        setTokenQueue={setTokenQueue}
        dateRangeParams={dateRangeParams}
      />
    ),
  },
];

const categoryOptions = () => [
  {
    value: ALL_CATEGORIES,
  },
  {
    value: ACCOUNT_NAME,
  },
  {
    value: ACCOUNT_ID,
  },
  {
    value: BILLING_ACCOUNT_ID,
  },
  {
    value: USER_EMAIL,
  },
  {
    value: USER_PHONE,
  },
  {
    value: INTEGRATION_PLATFORM_ID,
  },
  {
    value: E2S_EMAIL,
  },
  {
    value: CARRIER_BILLING_NUMBER,
  },
  {
    value: API_KEY,
  },
  {
    value: API_NAME,
  },
  {
    value: DEDICATED_NUMBER,
  },
  {
    value: SENDER_ADDRESS,
  },
  // {
  //   value: SENDER_ID,
  // },
  // {
  //   value: HUBSPOT_ID,
  // },
  // {
  //   value: SSO_DOMAIN,
  // },
  // {
  //   value: CARRIER_BILLING_NUMBER,
  // },
];

const convertCategoryOption = (value) => {
  switch (value) {
    case ACCOUNT_NAME:
      return KEY_ACCOUNT_NAME;
    case ACCOUNT_ID:
      return KEY_ACCOUNT_ID;
    case BILLING_ACCOUNT_ID:
      return KEY_BILLING_ACCOUNT_ID;
    case USER_EMAIL:
      return KEY_USER_EMAIL;
    case USER_PHONE:
      return KEY_USER_PHONE;
    case INTEGRATION_PLATFORM_ID:
      return KEY_INTEGRATION_PLATFORM_ID;
    case E2S_EMAIL:
      return KEY_E2S_EMAIL;
    case CARRIER_BILLING_NUMBER:
      return KEY_CARRIER_BILLING;
    case API_KEY:
      return KEY_API_KEY;
    case API_NAME:
      return KEY_API_NAME;
    case DEDICATED_NUMBER:
      return KEY_DEDICATED_NUMBER;
    case SENDER_ADDRESS:
      return KEY_SENDER_ADDRESS;
    default:
      return ALL_CATEGORIES;
  }
};

export const AccountSearchContent = () => {
  const dispatch = useDispatch();
  const brands = IS_EU_VENDOR ? BRANDS_EU : BRANDS;
  const currentPageSize = 10;
  const queryParams = new URLSearchParams(window.location.search);
  const searchURLParam = queryParams.get('search') || '';
  const billingTypeParams =
    queryParams.get('billingType') || BILLING_TYPES[0].value;
  const integrationTypeParams = queryParams.getAll('integrationType') || [];
  const tabURLParam = queryParams.get('tab') || '1';
  const categoryURLParam = queryParams.get('category') || ALL_CATEGORIES;
  const brandParams = queryParams.get('brand') || brands[0].value;
  const statusParams = queryParams.get('status') || STATUSES[0].value;
  const verificationStatusParams =
    queryParams.get('verificationStatus') || VERIFICATION_STATUSES[0].value;
  const countriesParams = queryParams.getAll('countries')[0]?.split(',') || [];
  const dateRangeParams = queryParams.getAll('dateRange')[0]?.split(',') || [];

  const [isClearedAll, setIsClearedAll] = useState(false);
  const [tokenQueue, setTokenQueue] = useState([]);
  const [isExact, setIsExact] = useState(false);
  const [category, setCategory] = useState(
    convertCategoryOption(categoryURLParam)
  );
  const [brand, setBrand] = useState(brandParams);
  const [status, setStatus] = useState(statusParams);
  const [verificationStatus, setVerificationStatus] = useState(
    verificationStatusParams
  );
  const [countries, setCountries] = useState(countriesParams);
  const [dateRange, setDateRange] = useState(dateRangeParams);
  const [searchValue, setSearchValue] = useState(searchURLParam);
  const [billingType, setBillingType] = useState(billingTypeParams);
  const [integrationType, setIntegrationType] = useState(integrationTypeParams);
  const [currentTab, setCurrentTab] = useState(tabURLParam);
  const [validation, setValidation] = useState({ length: false });
  // const user = useSelector(getUser) || {}
  const userTimezone = moment.tz.guess();
  const azureInfoDecoded = useSelector(getAzureInfoDecoded);
  const { email } = azureInfoDecoded || {};

  const onChangeTab = (tab: string) => {
    handleSetURLParams('tab', tab);
    setCurrentTab(tab);
  };

  const getStartDateStr = (momentVal?: string) => {
    if (!momentVal) return undefined;
    return moment(momentVal, 'DD-MM-YYYY')
      ?.startOf('day')
      .tz(userTimezone, true)
      .format();
  };

  const getEndDateStr = (momentVal?: string) => {
    if (!momentVal) return undefined;
    return moment(momentVal, 'DD-MM-YYYY')
      ?.endOf('day')
      .tz(userTimezone, true)
      .format();
  };

  const handleSearch = (searchTab: string, size?: number) => {
    if (searchTab === '1') {
      setCurrentTab('2');
      handleSetURLParams('tab', '2');
    }
    const from = getStartDateStr(dateRange?.[0]);
    const to = getEndDateStr(dateRange?.[1]);

    if (category === ALL_CATEGORIES) {
      dispatch(
        loadAllAccounts({
          size: size || currentPageSize,
          ...(searchValue && { term: searchValue }),
          ...(!billingType.includes(BILLING_TYPES[0].value) && { billingType }),
          ...(integrationType[0] && { integrationType }),
          ...(!brand.includes(brands[0].value) && { vendorId: brand }),
          ...(!status.includes(STATUSES[0].value) && { status }),
          ...(!verificationStatus.includes(VERIFICATION_STATUSES[0].value) && {
            verificationStatus,
          }),
          ...(countries[0] && { country: countries }),
          ...(from && to
            ? { createdAt: [`after:${from}`, `before:${to}`] }
            : undefined),
          exact: isExact,
        })
      );
      dispatch(
        segmentTracker(SEGMENT_TRACK_EVENTS_V2.Click_account_search, '', {
          category: 'All',
          userEmail: email,
        })
      );
    } else {
      dispatch(
        loadAccounts({
          size: size || currentPageSize,
          [category]: searchValue,
          ...(!billingType.includes(BILLING_TYPES[0].value) && { billingType }),
          ...(integrationType[0] && { integrationType }),
          ...(!brand.includes(brands[0].value) && { vendorId: brand }),
          ...(!status.includes(STATUSES[0].value) && { status }),
          ...(!verificationStatus.includes(VERIFICATION_STATUSES[0].value) && {
            verificationStatus,
          }),
          ...(countries[0] && { country: countries }),
          ...(from && to
            ? { createdAt: [`after:${from}`, `before:${to}`] }
            : undefined),
          exact: isExact,
        })
      );
      dispatch(
        segmentTracker(SEGMENT_TRACK_EVENTS_V2.Click_account_search, '', {
          category,
          userEmail: email,
        })
      );
    }
    setTokenQueue([]);
  };
  const handleSetURLParams = (param: string, value: string) => {
    queryParams.delete(param);
    queryParams.set(param, value);
    handleUpdateURL(queryParams.toString());
  };

  const handleUpdateURL = (queryString: string) => {
    const newurl = `${window.location.protocol}//${window.location.host}${window.location.pathname}?${queryString}`;
    window.history.pushState({ path: newurl }, '', newurl);
  };

  const handleInputSearch = (value: string) => {
    handleSetURLParams('search', value);
    setSearchValue(value);
  };

  const handleSelectOption = (value: string) => {
    const selectedCategory = convertCategoryOption(value);
    handleSetURLParams('category', value);
    setCategory(selectedCategory);
  };

  const handleOnKeyDown = (e: React.KeyboardEvent<HTMLFormElement>) => {
    if (e.key === 'Enter' && searchValue.length <= MAX_INPUT_LENGTH) {
      handleSearch(currentTab);
    }
  };

  const handleClearAll = () => {
    const newurl = `${window.location.protocol}//${window.location.host}${window.location.pathname}`;
    window.history.pushState({ path: newurl }, '', newurl);
    setBrand(brands[0].value);
    setCountries([]);
    setStatus(STATUSES[0].value);
    setVerificationStatus(VERIFICATION_STATUSES[0].value);
    setBillingType(BILLING_TYPES[0].value);
    setIntegrationType([]);
    setIsClearedAll(true);
    const from = getStartDateStr(dateRange?.[0]);
    const to = getEndDateStr(dateRange?.[1]);
    if (category === ALL_CATEGORIES) {
      dispatch(
        loadAllAccounts({
          size: currentPageSize,
          ...(searchValue && { term: searchValue }),
          exact: isExact,
          ...(from && to
            ? { createdAt: [`after:${from}`, `before:${to}`] }
            : undefined),
        })
      );
    } else {
      dispatch(
        loadAccounts({
          size: currentPageSize,
          [category]: searchValue,
          exact: isExact,
          ...(from && to
            ? { createdAt: [`after:${from}`, `before:${to}`] }
            : undefined),
        })
      );
    }
    setTokenQueue([]);
  };

  useEffect(() => {
    if (isClearedAll) {
      setIsClearedAll(false);
    }
  }, [
    brand,
    countries,
    status,
    billingTypeParams,
    integrationTypeParams,
    isClearedAll,
  ]);

  useEffect(() => {
    const from = getStartDateStr(dateRange?.[0]);
    const to = getEndDateStr(dateRange?.[1]);

    if (tabURLParam && tabURLParam === '2') {
      if (category === ALL_CATEGORIES) {
        dispatch(
          loadAllAccounts({
            size: currentPageSize,
            ...(searchValue && { term: searchValue }),
            ...(!billingType.includes(BILLING_TYPES[0].value) && {
              billingType,
            }),
            ...(integrationType[0] && { integrationType }),
            ...(!brand.includes(brands[0].value) && { vendorId: brand }),
            ...(!status.includes(STATUSES[0].value) && { status }),
            ...(!verificationStatus.includes(
              VERIFICATION_STATUSES[0].value
            ) && { verificationStatus }),
            ...(countries[0] && { country: countries }),
            ...(from && to
              ? { createdAt: [`after:${from}`, `before:${to}`] }
              : undefined),
            exact: isExact,
          })
        );
      } else {
        dispatch(
          loadAccounts({
            size: currentPageSize,
            [category]: searchValue,
            ...(!billingType.includes(BILLING_TYPES[0].value) && {
              billingType,
            }),
            ...(integrationType[0] && { integrationType }),
            ...(!brand.includes(brands[0].value) && { vendorId: brand }),
            ...(!status.includes(STATUSES[0].value) && { status }),
            ...(!verificationStatus.includes(
              VERIFICATION_STATUSES[0].value
            ) && { verificationStatus }),
            ...(countries[0] && { country: countries }),
            ...(from && to
              ? { createdAt: [`after:${from}`, `before:${to}`] }
              : undefined),
            exact: isExact,
          })
        );
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (searchValue.length > 3) {
      setValidation({ ...validation, length: true });
    } else {
      setValidation({ ...validation, length: false });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchValue]);

  return (
    <AccountSearchContext.Provider
      value={[
        setBillingType,
        setBrand,
        setStatus,
        setVerificationStatus,
        setDateRange,
        setCountries,
        setIntegrationType,
      ]}
    >
      <div data-test="account-search">
        <ErrorBoundary componentName="AccountSearch">
          <div className={styles.container}>
            <div className={styles.guideText}>
              <Text type="s">
                <b>Enter a keyword</b>
                {` and click 'Search.' It will take you to the research results tab with `}
                <b>matching accounts.</b>
                {` To narrow down the search, use the `}
                <b>exact match</b>
                {` or the `}
                <b>filters</b>
                {` options. For more details, click `}
                <Link to="https://messagemedia.atlassian.net/wiki/spaces/RD/pages/**********/Account+Search">
                  here
                </Link>
                !
              </Text>
            </div>
            {/* eslint-disable-next-line jsx-a11y/no-noninteractive-element-interactions */}
            <form className={styles.searchForm} onKeyDown={handleOnKeyDown}>
              <div className={styles.formItem}>
                <SearchInput
                  defaultValue={searchValue}
                  onChange={handleInputSearch}
                  width={{ minWidth: 400 }}
                  errorText={
                    searchValue.length > MAX_INPUT_LENGTH
                      ? 'Max 100 characters'
                      : ''
                  }
                />
              </div>
              <div className={styles.formItem}>
                <Select
                  defaultValue={categoryURLParam}
                  value={categoryURLParam}
                  placeholder="All categories"
                  options={categoryOptions()}
                  onSelect={handleSelectOption}
                  customStyles={{ width: 270 }}
                  testId="category-select"
                />
              </div>
              {/* <Checkbox label="Exact matches only" /> */}
              <div className={styles.formItem}>
                <Checkbox
                  value={isExact}
                  onChange={setIsExact}
                  label="Exact match only"
                  tooltipText="Ticking this box means we won't match partially against text, you'll get whole word matched only"
                />
              </div>
              <div className={styles.formItem}>
                <Button
                  disabled={
                    !validation.length || searchValue.length > MAX_INPUT_LENGTH
                  }
                  label="Search"
                  onClick={() => handleSearch(currentTab)}
                />
              </div>
            </form>
            <Tab
              currentTab={currentTab}
              tabs={tabs()}
              tabContent={tableContent(
                currentPageSize,
                {
                  [category]: searchValue,
                  ...(!billingType.includes(BILLING_TYPES[0].value) && {
                    billingType,
                  }),
                  ...(integrationType[0] && { integrationType }),
                  ...(!brand.includes(brands[0].value) && { vendorId: brand }),
                  ...(!status.includes(STATUSES[0].value) && { status }),
                  ...(!verificationStatus.includes(
                    VERIFICATION_STATUSES[0].value
                  ) && { verificationStatus }),
                  ...(countries[0] && { country: countries }),
                },
                category,
                searchValue,
                handleSetURLParams,
                billingTypeParams,
                integrationTypeParams,
                brand,
                brands,
                status,
                verificationStatus,
                countries,
                isClearedAll,
                handleClearAll,
                handleSearch,
                !dateRange.length > 0 && !validation.length,
                tokenQueue,
                setTokenQueue,
                dateRangeParams
              )}
              onChangeTab={onChangeTab}
            />
          </div>
        </ErrorBoundary>
      </div>{' '}
    </AccountSearchContext.Provider>
  );
};

export const AccountSearch = () => (
  <div>
    <AccountSearchContent />
  </div>
);

AccountSearch.propTypes = {};

AccountSearch.defaultProps = {};

const AccountSearchWithFeature = withFeatureFlag(AccountSearch);

export default AccountSearchWithFeature;
