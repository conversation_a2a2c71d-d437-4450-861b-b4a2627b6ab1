import React from 'react'
import * as ReactRedux from 'react-redux'

import { AccountSearch, AccountSearchContent } from './account-search'
import { ACCOUNT_ID, ACCOUNT_NAME, ALL_CATEGORIES, BILLING_ACCOUNT_ID, USER_EMAIL, USER_PHONE } from './constants'

let useEffect
let useDispatch
let useSelector
jest.mock('react-redux', () => {
  const originalReactRedux = jest.requireActual('react-redux')
  return ({
    ...originalReactRedux,
    useSelector: jest.fn(),
    useDispatch: jest.fn().mockImplementation((fn) => jest.fn()),
  })
})
const setState = jest.fn()
const useState = jest.spyOn(React, 'useState')
const mockUseState = (state) => useState.mockImplementation(() => [state, setState])
const mockUseEffect = () => { useEffect.mockImplementation((f) => f()) }
const mockUseDispatch = () => useDispatch.mockImplementation(() => jest.fn())
const mockUseSelector = () => useSelector.mockImplementation(() => jest.fn())
const setVal = (val) => jest.spyOn(URLSearchParams.prototype, 'get').mockReturnValue(val)

beforeEach(() => {
  useDispatch = jest.spyOn(ReactRedux, 'useDispatch')
  useSelector = jest.spyOn(ReactRedux, 'useSelector')
  useEffect = jest.spyOn(React, 'useEffect')
  mockUseEffect()
  mockUseSelector()
  mockUseDispatch()
  mockUseState('')
  jest.clearAllMocks()
})

describe('View OnePortal Account Search', () => {
  it('renders', () => {
    const wrapper = shallow(<AccountSearch />)
    expect(wrapper).toMatchSnapshot()
  })
  it('renders Account Search content different vendor', () => {
    IS_EU_VENDOR = true
    const wrapper = shallow(<AccountSearchContent />)
    expect(wrapper).toMatchSnapshot()
  })
  it('renders Account Search content', () => {
    IS_EU_VENDOR = false
    const wrapper = shallow(<AccountSearchContent />)
    expect(wrapper).toMatchSnapshot()
  })
  it('should trigger change tab', () => {
    const wrapper = shallow(<AccountSearchContent />)
    wrapper.find('Tab').props().onChangeTab()
  })
  it('should trigger handleSearch tab1', () => {
    setVal('1')
    const wrapper = shallow(<AccountSearchContent />)
    wrapper.find('Button').simulate('click')
  })
  it('should trigger handleSearch tab2', () => {
    setVal('2')
    const wrapper = shallow(<AccountSearchContent />)
    wrapper.find('Button').simulate('click')
  })
  it('should trigger handle Input Search', () => {
    const wrapper = shallow(<AccountSearchContent />)
    wrapper.find('SearchInput').props().onChange()
  })
  it('should trigger enter', () => {
    const wrapper = shallow(<AccountSearchContent />)
    wrapper.find('form').simulate('keypress', { key: 'Enter' })
  })
  it('should trigger select with value', () => {
    const wrapper = shallow(<AccountSearchContent />)
    wrapper.find('Select').props().onSelect(ALL_CATEGORIES)
  })
  it('should trigger select with ACCOUNT_ID', () => {
    const wrapper = shallow(<AccountSearchContent />)
    wrapper.find('Select').props().onSelect(ACCOUNT_ID)
  })
  it('should trigger select with BILLING_ACCOUNT_ID', () => {
    const wrapper = shallow(<AccountSearchContent />)
    wrapper.find('Select').props().onSelect(BILLING_ACCOUNT_ID)
  })
  it('should trigger select with USER_EMAIL', () => {
    const wrapper = shallow(<AccountSearchContent />)
    wrapper.find('Select').props().onSelect(USER_EMAIL)
  })
  it('should trigger select with USER_PHONE', () => {
    const wrapper = shallow(<AccountSearchContent />)
    wrapper.find('Select').props().onSelect(USER_PHONE)
  })
  it('should trigger select with ACCOUNT_NAME', () => {
    const wrapper = shallow(<AccountSearchContent />)
    wrapper.find('Select').props().onSelect(ACCOUNT_NAME)
  })
  it('should trigger select with default ', () => {
    const wrapper = shallow(<AccountSearchContent />)
    wrapper.find('Select').props().onSelect('abc')
  })
  it('should trigger select', () => {
    const wrapper = shallow(<AccountSearchContent />)
    wrapper.find('Select').props().onSelect()
  })
})
