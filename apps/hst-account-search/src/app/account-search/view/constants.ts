export const ALL_CATEGORIES = 'All categories';
export const ACCOUNT_NAME = 'Account Name';
export const ACCOUNT_ID = 'Account ID (Gateway ID)';
export const BILLING_ACCOUNT_ID = 'Billing Account ID';
export const USER_EMAIL = 'User Email';
export const USER_PHONE = 'User Phone';
export const SENDER_ID = 'Sender ID';
export const HUBSPOT_ID = 'Hubspot Integration ID';
export const API_KEY = 'API Key';
export const API_NAME = 'API Key name';
export const SSO_DOMAIN = 'SSO Domain';
export const CARRIER_BILLING_NUMBER = 'Carrier Billing Number';
export const PREPAID = 'Prepaid';
export const POSTPAID = 'Postpaid';
export const PARENT_ALLOCATED = 'Parent Allocated';
export const PREPAID_MONEY = 'Prepaid Money';
export const INTEGRATION_PLATFORM_ID = 'Integration Platform ID (Ecosystem)';
export const E2S_EMAIL = 'E2S Email';
export const DEDICATED_NUMBER = 'Dedicated Number';
export const SENDER_ADDRESS = 'Sender Address';
export const ALL = 'All';
// api key
export const KEY_ALL_CATEGORIES = 'allCategories';
export const KEY_ACCOUNT_NAME = 'label';
export const KEY_ACCOUNT_ID = 'accountId';
export const KEY_BILLING_ACCOUNT_ID = 'billingAccountId';
export const KEY_USER_EMAIL = 'usersEmail';
export const KEY_USER_PHONE = 'usersPhone';
export const KEY_PREPAID = 'PREPAID';
export const KEY_POSTPAID = 'POSTPAID';
export const KEY_PARENT_ALLOCATED = 'PARENT_ALLOCATED';
export const KEY_PREPAID_MONEY = 'PREPAID_MONEY';
export const KEY_INTEGRATION_PLATFORM_ID = 'integrationsExternalPlatformId';
export const KEY_E2S_EMAIL = 'e2sEmail';
export const KEY_API_KEY = 'apiKey';
export const KEY_API_NAME = 'apiKeysLabel';
export const KEY_CARRIER_BILLING = 'carrierBillingNumber';
export const KEY_DEDICATED_NUMBER = 'dedicatedNumber';
export const KEY_SENDER_ADDRESS = 'senderAddress';

export const VODANZ = 'vodanz';
export const ONENZ = 'onenz';

export const BRANDS = [
  {
    value: 'All',
    label: 'All',
  },
  {
    value: 'MessageMedia',
    label: 'MessageMedia',
  },
  {
    value: 'DirectSMS',
    label: 'DirectSMS',
  },
  {
    value: 'SMSBroadcast',
    label: 'SMSBroadcast',
  },
  {
    value: 'SMSCentral',
    label: 'SMSCentral',
  },
  {
    value: 'Streetdata',
    label: 'Streetdata',
  },
  {
    value: 'MessageNet',
    label: 'MessageNet',
  },
  {
    value: 'Mobipost',
    label: 'Mobipost',
  },
  {
    value: 'WholesaleSMS',
    label: 'WholesaleSMS',
  },
  // {
  //   value: 'ClickSend',
  //   label: 'ClickSend',
  // },
  // carrier brands
  {
    value: 'VodaNZ',
    label: 'VodaNZ',
  },
  {
    value: '2Degrees',
    label: '2Degrees',
  },
  {
    value: 'eTXT',
    label: 'eTXT',
  },
  {
    value: 'TPGTelecom',
    label: 'TPGTelecom',
  },
];

export const BILLING_TYPES = [
  {
    value: 'All',
    label: 'All',
  },
  {
    value: 'POSTPAID',
    label: 'Postpaid',
  },
  {
    value: 'PREPAID',
    label: 'Prepaid',
  },
  {
    value: 'PREPAID_MONEY',
    label: 'Prepaid Money',
  },
  {
    value: 'PARENT_ALLOCATED',
    label: 'Parent Allocated',
  },
];

export const BRANDS_EU = [
  {
    value: 'All',
    label: 'All',
  },
  {
    value: 'SinchEU',
    label: 'SinchEU',
  },
  {
    value: 'SimpleTexting',
    label: 'SimpleTexting',
  },
];

export const STATUSES = [
  {
    value: 'All',
    label: 'All',
  },
  {
    value: 'ACTIVE',
    label: 'Active',
  },
  {
    value: 'CANCELLED',
    label: 'Cancelled',
  },
  {
    value: 'SUSPENDED',
    label: 'Suspended',
  },
  {
    value: 'DORMANT',
    label: 'Dormant',
  },
];

export const VERIFICATION_STATUSES = [
  {
    value: 'All',
    label: 'All',
  },
  {
    value: 'VERIFIED',
    label: 'Verified',
  },
  {
    value: 'UNVERIFIED',
    label: 'Unverified',
  },
];

export const COUNTRIES_EU = [
  {
    value: 'GB',
    label: 'United Kingdom',
  },
  {
    value: 'DE',
    label: 'Germany',
  },
  {
    value: 'FR',
    label: 'France',
  },
  {
    value: 'US',
    label: 'United States',
  },
  {
    value: 'ES',
    label: 'Spain',
  },
];

export const COUNTRIES = [
  {
    value: 'AU',
    label: 'Australia',
  },
  {
    value: 'CA',
    label: 'Canada',
  },
  {
    value: 'NZ',
    label: 'New Zealand',
  },
  {
    value: 'GB',
    label: 'United Kingdom',
  },
  {
    value: 'US',
    label: 'United States',
  },
];

export const INTEGRATION_TYPES = [
  {
    label: 'MAILCHIMP',
    value: 'mailchimp',
  },
  {
    label: 'SHOPIFY',
    value: 'shopify',
  },
  {
    label: 'MAILJET',
    value: 'mailjet',
  },
  {
    label: 'HUBSPOT',
    value: 'hubspot',
  },
  {
    label: 'BIGCOMMERCE',
    value: 'bigcommerce',
  },
  {
    label: 'KLAVIYO',
    value: 'klaviyo',
  },
  {
    label: 'ACTIVECAMPAIGN',
    value: 'activecampaign',
  },
  {
    label: 'NETSUITE',
    value: 'netsuite',
  },
  {
    label: 'SALESFORCE',
    value: 'salesforce',
  },
  {
    label: 'ZOHO',
    value: 'zoho',
  },
];

export const MAX_INPUT_LENGTH = 100;
