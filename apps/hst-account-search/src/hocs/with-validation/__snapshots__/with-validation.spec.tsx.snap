// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`HOC: with-validation Renders 1`] = `
<Fragment>
  <div
    data-test="input-field-test"
  >
    <div
      className="validationWrapper"
    >
      <DumbComponent
        hasError={false}
        inputClassName=""
        meta={
          {
            "dirtySinceLastSubmit": false,
            "error": "",
            "modified": false,
            "submitError": undefined,
            "touched": false,
          }
        }
        name="test"
        onChange={[MockFunction]}
      />
    </div>
  </div>
</Fragment>
`;

exports[`HOC: with-validation Renders Component error 1`] = `
<div
  className="baseClassName test"
  data-test="validator-message-test"
>
  sample error
</div>
`;

exports[`HOC: with-validation Renders Component submission error 1`] = `
<div
  className="baseClassName test"
  data-test="validator-message-test"
>
  sample error
</div>
`;
