import { withValidation } from './with-validation'

jest.mock('rc-animate/lib/CSSMotion', () => 'MockCSSMotion')

describe('HOC: with-validation', () => {
  let props
  beforeEach(() => {
    props = {
      hasFeedback: false,
      meta: {
        touched: false,
        modified: false,
        error: '',
        submitError: undefined,
        dirtySinceLastSubmit: false,
      },
      input: {
        name: 'test',
        onChange: jest.fn(),
      },
      children: null,
    }
  })
  const DumbComponent = () => <div />
  const ComposedComponent = withValidation(DumbComponent)

  it('Renders', () => {
    const wrapper = shallow(<ComposedComponent {...props} />)
    expect(wrapper).toMatchSnapshot()
  })

  it('Renders Component error', () => {
    const newProps = {
      ...props,
      hasFeedback: true,
      meta: {
        ...props.meta,
        touched: true,
        invalid: true,
        error: 'sample error',
        modified: true,
      },
    }
    const wrapper = shallow(<ComposedComponent {...newProps} />)
    const CSSMotion = wrapper.find('MockCSSMotion')
    expect(CSSMotion.length).toEqual(1)
    const errorMessage = CSSMotion.props().children({ className: 'test' })
    expect(errorMessage).toMatchSnapshot()
  })

  it('Renders Component error without message', () => {
    const newProps = {
      ...props,
      hasFeedback: true,
      meta: {
        ...props.meta,
        touched: true,
        invalid: true,
        error: 'sample error',
        modified: true,
      },
      displayDefaultError: false,
    }
    const wrapper = shallow(<ComposedComponent {...newProps} />)
    const CSSMotion = wrapper.find('MockCSSMotion')
    expect(CSSMotion.length).toEqual(0)
  })

  it('Renders Component submission error', () => {
    const newProps = {
      ...props,
      hasFeedback: true,
      meta: {
        ...props.meta,
        submitError: 'sample error',
        dirtySinceLastSubmit: false,
      },
    }
    const wrapper = shallow(<ComposedComponent {...newProps} />)
    const CSSMotion = wrapper.find('MockCSSMotion')
    expect(CSSMotion.length).toEqual(1)
    const errorMessage = CSSMotion.props().children({ className: 'test' })
    expect(errorMessage).toMatchSnapshot()
  })
})
