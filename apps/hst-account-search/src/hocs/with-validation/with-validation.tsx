import React from 'react'
import <PERSON><PERSON>otion from 'rc-animate/lib/CSSMotion'
import PropTypes from 'prop-types'
import classNames from 'classnames'

import styles from './with-validation.less'

const propTypes = {
  hasFeedback: PropTypes.bool,
  meta: PropTypes.shape({
    visited: PropTypes.bool,
    touched: PropTypes.bool,
    error: PropTypes.string,
    dirtySinceLastSubmit: PropTypes.bool,
    submitError: PropTypes.string,
    modified: PropTypes.bool,
    invalid: PropTypes.bool,
  }),
  input: PropTypes.shape({
    name: PropTypes.string,
    onChange: PropTypes.func,
  }).isRequired,
  children: PropTypes.node,
  forwardRef: PropTypes.shape({
    current: PropTypes.instanceOf(React.Component),
  }),
  displayDefaultError: PropTypes.bool,
}

const defaultProps = {
  hasFeedback: false,
  meta: {},
  children: null,
  forwardRef: undefined,
  displayDefaultError: true,
}

export const withValidation = (Component) => {
  const InnerComponent = ({
    input, forwardRef, meta, hasFeedback, children, displayDefaultError, ...rest
  }) => {
    const hasSubmitError = !meta.dirtySinceLastSubmit && meta.submitError
    const hasError = (meta.visited || meta.touched || meta.modified) && meta.invalid
    const showError = meta.submitError ? hasSubmitError : hasError
    return (
      <>
        <div data-test={`input-field-${input.name}`}>
          <div className={styles.validationWrapper}>
            <Component
              {...input}
              ref={forwardRef}
              meta={meta}
              {...rest}
              inputClassName={classNames(rest.inputClassName, { [styles.error]: showError })}
              hasError={showError}
            >
              {children}
            </Component>
          </div>
          {showError && displayDefaultError
            && (
            <CSSMotion
              visible={showError}
              motionName="show-help"
              motionAppear
              removeOnLeave
            >
              {({ className: motionClassName }) => (
                <div className={classNames(styles.baseClassName, motionClassName)} key="help" data-test={`validator-message-${input.name}`}>
                  {meta.error || meta.submitError}
                </div>
              )}
            </CSSMotion>
            )}
        </div>
      </>
    )
  }
  InnerComponent.propTypes = propTypes
  InnerComponent.defaultProps = defaultProps
  return InnerComponent
}

export default withValidation
