import React from 'react'
import withAsyncReducers from './with-async-reducers'

React.useCallback = jest.fn((fn) => fn())

describe('withAsyncReducer', () => {
  let useEffect
  let useState
  const setState = jest.fn()

  const mockUseEffect = () => useEffect.mockImplementation((f) => f())
  const mockUseState = (state) => useState.mockImplementation(() => [state, setState])

  beforeEach(() => {
    useState = jest.spyOn(React, 'useState')
    useEffect = jest.spyOn(React, 'useEffect')
    mockUseEffect()
    mockUseState()
  })

  it('should not call store.injectAsyncReducer if store doesnt exist', () => {
    const WrappedComponent = () => <div />

    const reducer = () => 'hello'

    const Comp = withAsyncReducers(WrappedComponent, { reducers: [reducer], reducerNames: ['supportDetails'] })

    const store = { injectAsyncReducer: jest.fn() }

    shallow(<Comp />)

    expect(store.injectAsyncReducer).not.toBeCalled()
  })

  it('should not call store.injectAsyncReducer if reducers dont exist', () => {
    const WrappedComponent = () => <div />

    const Comp = withAsyncReducers(WrappedComponent, { reducers: [], reducerNames: ['supportDetails'] })

    const store = { injectAsyncReducer: jest.fn() }

    shallow(<Comp store={store} />)

    expect(store.injectAsyncReducer).not.toBeCalled()
  })

  it('should call store.injectAsyncReducer if store exists', () => {
    mockUseState(true)

    const WrappedComponent = () => <div />

    const reducer = () => 'hello'

    const Comp = withAsyncReducers(WrappedComponent, { reducers: [reducer], reducerNames: ['supportDetails'] })

    const store = { injectAsyncReducer: jest.fn() }

    shallow(<Comp store={store} />)

    expect(store.injectAsyncReducer).toBeCalledWith('supportDetails', reducer)
  })
})
