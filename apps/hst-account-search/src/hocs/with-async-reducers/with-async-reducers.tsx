/* eslint-disable */
import _isEmpty from 'lodash/isEmpty';
import React from 'react';

const withAsyncReducers =
  (Component, reducerSettings) =>
  ({ store, ...rest }) => {
    const [done, setDone] = React.useState(false);

    React.useEffect(() => {
      const { reducers, reducerNames } = reducerSettings;

      if (store && !_isEmpty(reducers)) {
        reducers.forEach((reducer, index) =>
          store.injectAsyncReducer(reducerNames[index], reducer)
        );
      }

      setDone(true);
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return done ? <Component {...rest} /> : null;
  };

export default withAsyncReducers;
