import { useLocation, useNavigate, useParams } from 'react-router-dom'

const withRouter = (Component) => {
  const ComponentWithRouterProp = (props) => {
    const location = useLocation()
    const params = useParams()
    const navigate = useNavigate()
    return (
      <Component
        {
        ...props
        }
        navigate={navigate}
        location={location}
        match={{
          params,
        }}
      />
    )
  }

  return ComponentWithRouterProp
}

export default withRouter
