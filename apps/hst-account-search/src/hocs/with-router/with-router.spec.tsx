import withRouter from './with-router'

jest.mock('react-router-dom', () => ({
  useLocation: jest.fn().mockReturnValue({
    search: '',
    state: {},
  }),
  useParams: jest.fn().mockReturnValue({
    accountId: '1',
  }),
  useNavigate: jest.fn(),
}))

describe('hoc-with-router', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  const DumbComponent = () => <div />
  const props = { dump: 'prop' }

  const ComposedComponent = withRouter(DumbComponent)
  const wrapper = shallow(<ComposedComponent {...props} />)

  it('renders correct tree', () => {
    expect(wrapper).toMatchSnapshot()
  })

  it('does not render a DumbComponent', () => {
    expect(wrapper.find('DumbComponent')).toHaveLength(1)
  })
})
