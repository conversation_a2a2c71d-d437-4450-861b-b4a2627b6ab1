{"name": "hst-account-search", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/hst-account-search/src", "projectType": "application", "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"compiler": "babel", "outputPath": "dist/apps/hst-account-search", "index": "apps/hst-account-search/src/index.html", "baseHref": "/", "main": "apps/hst-account-search/src/main.ts", "tsConfig": "apps/hst-account-search/tsconfig.app.json", "assets": ["apps/hst-account-search/src/favicon.ico", "apps/hst-account-search/src/assets"], "styles": ["apps/hst-account-search/src/styles.less"], "scripts": [], "isolatedConfig": true, "webpackConfig": "apps/hst-account-search/webpack.config.ts"}, "configurations": {"development": {"extractLicenses": false, "optimization": false, "sourceMap": true, "vendorChunk": true}, "production": {"fileReplacements": [{"replace": "apps/hst-account-search/src/environments/environment.js", "with": "apps/hst-account-search/src/environments/environment.prod.js"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false}}}, "serve": {"executor": "@nx/webpack:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "hst-account-search:build", "hmr": true}, "configurations": {"development": {"buildTarget": "hst-account-search:build:development"}, "production": {"buildTarget": "hst-account-search:build:production", "hmr": false}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/hst-account-search/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/hst-account-search/jest.config.js"}}}, "tags": []}