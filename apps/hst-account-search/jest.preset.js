const nxPreset = require('@nx/jest/preset').default;

module.exports = {
  ...nxPreset,
  setupFiles: [
    './tools/jest-shim.js',
    './tools/jest-setup.js',
    '../../mocks/mock-nectary.js',
    'jest-localstorage-mock',
  ],
  moduleDirectories: ['node_modules', 'src/'],
  moduleNameMapper: {
    '\\.(jpg|jpeg|png|gif|eot|otf|webp|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga|xlsx)$':
      '<rootDir>/tools/mocks/file-mock.ts',
    '\\.svg$': '<rootDir>/tools/mocks/svg-mock.ts',
    '\\.(css|less)$': 'identity-obj-proxy',
  },
  snapshotSerializers: [
    '<rootDir>/../../node_modules/enzyme-to-json/serializer',
  ],
  testEnvironment: 'jest-environment-jsdom',
  globals: {
    NODE_ENV: 'test',
  },
  roots: ['./src'],
  collectCoverage: true,
  collectCoverageFrom: [
    'src/**/*.ts',
    'src/**/*.tsx',
    'src/**/*.js',
    'src/hoc/**/*.js',
    '!src/*.js',
    '!src/**/*/*-module.js',
    '!src/**/*/*.steps.js',
    '!src/*/**/index.js',
    '!src/*.tsx',
    '!**/app.js',
    '!src/coverage/**/*.js',
    '!**/coverage/',
  ],
  coverageThreshold: {
    global: {
      branches: 40,
      functions: 50,
      lines: 50,
      statements: 50,
    },
  },
};
