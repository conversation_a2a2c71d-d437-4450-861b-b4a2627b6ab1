import '@nectary/assets/icons/help';
import { But<PERSON>, Dialog, Select, Text, Tooltip } from 'nectary';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { LOADING_STATUS } from '../constants';
import { reassignNumber } from '../redux/numbers/numbers-slice';
import { RootState } from '../types/store';
import { NumberItemType } from '../types/numbers';
import styles from './assign-modal.module.less';

// eslint-disable-next-line @nx/enforce-module-boundaries
import { fetchSubAccountsDetails } from '../../../../hst-account-details/src/app/redux/summary/sub-accounts-slice';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { fetchAccountDetails } from '../../../../hst-account-details/src/app/redux/summary/account-details-slice';
// eslint-disable-next-line @nx/enforce-module-boundaries, @nx/enforce-module-boundaries
import {
  SubAccount,
  SubAccountsResponse,
} from '../../../../hst-account-details/src/app/types/sub-accounts';
// eslint-disable-next-line @nx/enforce-module-boundaries
import subAccountsApi from '../../../../hst-account-details/src/app/redux/summary/sub-accounts-api';

const SUSPENDED = 'SUSPENDED';
const CANCELLED = 'CANCELLED';

type ReassignNumberModalProps = {
  isVisible: boolean;
  setIsVisible: (isVisible: boolean) => void;
  formValues: NumberItemType | null;
};

const ReassignNumberModal = ({
  isVisible,
  setIsVisible,
  formValues,
}: ReassignNumberModalProps) => {
  const dispatch = useDispatch();
  const [selectedAccountId, setSelectedAccountId] = useState('');
  const [isValidated, setIsValidated] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [parentSubAccounts, setParentSubAccounts] = useState<SubAccount[]>([]);
  const [parentSubAccountsLoading, setParentSubAccountsLoading] =
    useState(false);

  const { id, phoneNumber, assignedTo } = formValues || {};
  const currentVendorId = assignedTo?.vendorId || '';
  const currentAccountId = assignedTo?.accountId || '';

  const { numbers, subAccounts, accountDetails } = useSelector(
    (state: RootState) => state
  );
  const { reassignNumberLoading } = numbers || {};
  const { data: subAccountsData } = subAccounts || {};
  const { resources: subAccountsList } = subAccountsData || {};
  const { details } = accountDetails || {};
  const { resources: accountDetailsData } = details || {};

  const filterValidSubAccounts = (
    subAccountList: SubAccount[],
    excludeAccountIds: string[]
  ): SubAccount[] =>
    subAccountList.filter(
      (item: SubAccount) =>
        item?.status !== SUSPENDED &&
        item?.status !== CANCELLED &&
        item?.id &&
        item?.accountId &&
        !excludeAccountIds.includes(item.accountId)
    );

  const accountOptions = useMemo(() => {
    let mappedAccounts: { value: string; label: string }[] = [];

    const currentAccountSubAccounts = subAccountsList?.length
      ? filterValidSubAccounts(subAccountsList, [currentAccountId])
      : [];

    const parentAccountSubAccounts = filterValidSubAccounts(parentSubAccounts, [
      currentAccountId,
      currentVendorId,
    ]);

    const allSubAccounts = [
      ...currentAccountSubAccounts,
      ...parentAccountSubAccounts,
    ];
    const uniqueSubAccounts = allSubAccounts.filter(
      (item, index, self) =>
        index === self.findIndex((t) => t.accountId === item.accountId)
    );

    mappedAccounts = uniqueSubAccounts.map((item: SubAccount) => ({
      value: item.accountId,
      label: item.accountId,
    }));

    if (
      accountDetailsData?.parentAccountId &&
      accountDetailsData.parentAccountId !== currentAccountId &&
      accountDetailsData.parentAccountId !== currentVendorId
    ) {
      mappedAccounts.unshift({
        value: accountDetailsData.parentAccountId,
        label: accountDetailsData.parentAccountId,
      });
    }

    return mappedAccounts;
  }, [
    subAccountsList,
    parentSubAccounts,
    accountDetailsData,
    currentAccountId,
    currentVendorId,
  ]);

  const fetchParentSubAccounts = async (parentAccountId: string) => {
    if (parentAccountId === currentVendorId) {
      return;
    }

    try {
      setParentSubAccountsLoading(true);
      const response: SubAccountsResponse =
        await subAccountsApi.loadSubAccountsDetails({
          vendorId: currentVendorId,
          accountId: parentAccountId,
          size: 1000,
        });
      setParentSubAccounts(response?.resources || []);
    } catch (error) {
      setParentSubAccounts([]);
    } finally {
      setParentSubAccountsLoading(false);
    }
  };

  useEffect(() => {
    if (isVisible && currentVendorId && currentAccountId) {
      dispatch(
        fetchSubAccountsDetails({
          vendorId: currentVendorId,
          accountId: currentAccountId,
          size: 1000,
        })
      );
      dispatch(
        fetchAccountDetails({
          vendorId: currentVendorId,
          accountId: currentAccountId,
        })
      );
    }
  }, [isVisible, currentVendorId, currentAccountId, dispatch]);

  useEffect(() => {
    if (
      isVisible &&
      accountDetailsData?.parentAccountId &&
      accountDetailsData.parentAccountId !== currentVendorId &&
      accountDetailsData.parentAccountId !== currentAccountId
    ) {
      fetchParentSubAccounts(accountDetailsData.parentAccountId);
    } else {
      setParentSubAccounts([]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    isVisible,
    accountDetailsData?.parentAccountId,
    currentVendorId,
    currentAccountId,
  ]);

  useEffect(() => {
    if (!isVisible) {
      setSelectedAccountId('');
      setIsValidated(false);
      setErrorMessage('');
      setParentSubAccounts([]);
      setParentSubAccountsLoading(false);
    }
  }, [isVisible]);

  useEffect(() => {
    if (reassignNumberLoading === LOADING_STATUS.SUCCEEDED) {
      setIsVisible(false);
    } else if (reassignNumberLoading === LOADING_STATUS.FAILED) {
      setErrorMessage(
        `Failed to reassign number ${phoneNumber}. Please try again.`
      );
    }
  }, [reassignNumberLoading, phoneNumber, setIsVisible]);

  const handleSubmit = () => {
    setIsValidated(true);
    if (!selectedAccountId || !id) {
      return;
    }

    setErrorMessage('');
    dispatch(
      reassignNumber({
        numberId: id,
        payload: {
          vendorId: currentVendorId,
          accountId: selectedAccountId,
          label: assignedTo?.label || '',
          metadata: assignedTo?.metadata || {},
        },
      })
    );
  };

  const handleClose = () => {
    if (reassignNumberLoading !== LOADING_STATUS.LOADING) {
      setIsVisible(false);
    }
  };

  const isLoading =
    reassignNumberLoading === LOADING_STATUS.LOADING ||
    parentSubAccountsLoading;

  return (
    <div className={styles.dialogWrap}>
      <Dialog
        isOpen={isVisible}
        caption="Reassign Number"
        onClose={handleClose}
      >
        <div className={styles.formItemWrap}>
          <Text>
            Reassigning number {phoneNumber} from account {currentAccountId}
          </Text>
        </div>

        {errorMessage && (
          <div className={styles.formItemWrap}>
            <Text style={{ color: '#e74c3c', fontSize: '14px' }}>
              {errorMessage}
            </Text>
          </div>
        )}

        <div className={styles.formItemWrap}>
          <div style={{ marginBottom: '8px' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Text style={{ fontWeight: 500, fontSize: '14px' }}>Account</Text>
              <Tooltip
                type="fast"
                orientation="top"
                text="Only accounts in the same account hierarchy are available to select"
              >
                <sinch-icon-help
                  style={{ cursor: 'help', color: '#666', fontSize: '16px' }}
                />
              </Tooltip>
            </div>
          </div>
          <Select
            value={selectedAccountId}
            onSelect={setSelectedAccountId}
            options={accountOptions}
            testId="account-select"
            placeholder={
              accountOptions.length === 0
                ? 'No accounts available'
                : 'Select Account'
            }
            rows={10}
            errorText={
              isValidated && !selectedAccountId ? 'Account is required' : ''
            }
            disabled={isLoading || accountOptions.length === 0}
            customStyles={{
              width: '350px',
            }}
          />
          {accountOptions.length === 0 && (
            <div style={{ marginTop: '8px' }}>
              <Text style={{ color: '#666', fontSize: '14px' }}>
                No available accounts found in the same hierarchy to reassign
                this number to.
              </Text>
            </div>
          )}
        </div>

        <div className={styles.btnGroup}>
          <Button
            label="Cancel"
            type="secondary"
            onClick={handleClose}
            disabled={isLoading}
          />
          <Button
            label="Reassign Number"
            onClick={handleSubmit}
            disabled={isLoading || accountOptions.length === 0}
          />
        </div>
      </Dialog>
    </div>
  );
};

export default ReassignNumberModal;
