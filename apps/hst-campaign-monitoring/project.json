{"name": "hst-campaign-monitoring", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/hst-campaign-monitoring/src", "projectType": "application", "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"compiler": "babel", "outputPath": "dist/apps/hst-campaign-monitoring", "index": "apps/hst-campaign-monitoring/src/index.html", "baseHref": "/", "main": "apps/hst-campaign-monitoring/src/main.tsx", "tsConfig": "apps/hst-campaign-monitoring/tsconfig.app.json", "assets": ["apps/hst-campaign-monitoring/src/favicon.ico", "apps/hst-campaign-monitoring/src/assets"], "styles": ["apps/hst-campaign-monitoring/src/styles.less"], "scripts": [], "isolatedConfig": true, "webpackConfig": "apps/hst-campaign-monitoring/webpack.config.js"}, "configurations": {"development": {"extractLicenses": false, "optimization": false, "sourceMap": true, "vendorChunk": true}, "production": {"fileReplacements": [{"replace": "apps/hst-campaign-monitoring/src/environments/environment.ts", "with": "apps/hst-campaign-monitoring/src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false}}}, "serve": {"executor": "@nx/webpack:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "hst-campaign-monitoring:build", "hmr": true}, "configurations": {"development": {"buildTarget": "hst-campaign-monitoring:build:development"}, "production": {"buildTarget": "hst-campaign-monitoring:build:production", "hmr": false}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/hst-campaign-monitoring/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/hst-campaign-monitoring/jest.config.ts"}}}, "tags": []}