// eslint-disable-next-line max-classes-per-file
import { setAssetsRegistry } from '@nectary/assets/utils';
import { setNectaryRegistry } from '@nectary/components/utils/element';
// import { setNectaryRegistry } from '@nectary/components/utils';
import '@testing-library/jest-dom';
import { cleanup } from '@testing-library/react';
import configureMockStore from 'redux-mock-store';
import thunk from 'redux-thunk';
import 'regenerator-runtime/runtime';
import 'whatwg-fetch';

global.mockStore = configureMockStore([thunk])

setNectaryRegistry(window.customElements);
setAssetsRegistry(window.customElements);

Object.defineProperty(window, 'global', {
  writable: true,
  value: window,
});

Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // Deprecated
    removeListener: jest.fn(), // Deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

Object.defineProperty(window, 'IntersectionObserver', {
  writable: true,
  value: class IntersectionObserver {
    observe = jest.fn();

    disconnect = jest.fn();

    unobserve = jest.fn();
  },
});

Object.defineProperty(HTMLDialogElement.prototype, 'showModal', {
  writable: true,
  value: jest.fn(),
});

Object.defineProperties(window, {
  API_URL: { value: 'API' },
  WFP_API_URL: { value: 'WFP_API_URL' },
  NEXTGEN_API_URL: { value: 'NEXTGEN_API' },
  SUPPORT_API_URL: { value: 'SUPPORT_API_URL' },
  SUPPORT_V2_API_URL: { value: 'SUPPORT_V2_API_URL' },
  SHOPIFY_SUPPORT_URL: { value: 'SHOPIFY_SUPPORT_URL' },
  KIBANA_DASHBOARD_URL: { value: 'KIBANA_DASHBOARD_URL' },
  AUTOMATED_BROADCAST_MANAGEMENT_URL: {
    value: 'AUTOMATED_BROADCAST_MANAGEMENT_URL',
  },
  EMAIL2SMS_API_SUPPORT_URL: { value: 'EMAIL2SMS_API_SUPPORT_URL' },
  MFE_CONTACTS: { value: 'MFE_CONTACTS' },
  EMAIL2SMS_API_KEY: { value: 'EMAIL2SMS_API_KEY' },
  ZUORA_URL: { value: 'ZUORA_URL' },
  APP_VERSION: { value: 'x' },
  BUILD_ENV: { value: 'dev' },
  VENDOR_LABEL: { value: 'messagemedia' },
  DEBUG: { value: false },
  TFN_VERIFICATION_PAPER_FORM_ID: { value: 'messagemedia-tfn-verification' },
  SEGMENT_WRITE_KEY: { value: 'SEGMENT_WRITE_KEY' },
  AZURE_CLIENT_ID: { value: '97e3afee-ef0f-421d-b470-43584c4040ac' },
  AZURE_AUTHORITY: {
    value: 'https://login.microsoftonline.com/clxgroup.onmicrosoft.com',
  },
  AZURE_SCOPES: { value: 'api://sinch-support-main-app-dev/Support.All' },
  ALLOW_ENABLE_BETA_FEATURES: { value: true },
  FEATURE_FLAGS: { value: true },
  PREFIX: { value: '/support' },
  IS_EU_VENDOR: {
    value: true, writable: true
  },
  AMS_DASHBOARD: {
    value: 'AMS_DASHBOARD'
  },
  UG_DASHBOARD: {
    value: 'UG_DASHBOARD'
  },
  SALES_FORCE: {
    value: 'SALES_FORCE'
  },
  ZENDESK: {
    value: 'ZENDESK'
  },
  ZUORA: {
    value: 'ZUORA'
  },
  OPEN_SEARCH: {
    value: 'OPEN_SEARCH'
  },
  CONFIG_CAT_KEY: {
    value: 'CONFIG_CAT_KEY'
  },
  RUDDERSTACK_WRITE_KEY: {
    value: 'RUDDERSTACK_WRITE_KEY'
  },
  RUDDERSTACK_DATA_PLANE_URL: {
    value: 'RUDDERSTACK_DATA_PLANE_URL'
  },
});

afterEach(() => {
  cleanup();
});
