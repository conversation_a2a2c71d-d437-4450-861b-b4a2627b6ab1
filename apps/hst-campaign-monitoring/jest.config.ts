/* eslint-disable */
export default {
  displayName: 'hst-campaign-monitoring',
  preset: './jest.preset.js',
  transform: {
    '^(?!.*\\.(js|jsx|ts|tsx|css|json)$)': '@nx/react/plugins/jest',
    '^.+\\.[tj]sx?$': [
      'babel-jest',
      {
        presets: ['@nx/react/babel'],
        plugins: [['@babel/plugin-transform-private-methods', { loose: true }]],
      }
  ],
  },
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx'],
  coverageDirectory: '../../coverage/apps/hst-campaign-monitoring',
};
