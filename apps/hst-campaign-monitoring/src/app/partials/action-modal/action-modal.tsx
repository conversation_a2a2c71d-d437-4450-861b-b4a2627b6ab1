/* eslint-disable no-nested-ternary */
/* eslint-disable @nx/enforce-module-boundaries */
import '@nectary/assets/icons/person';
import '@nectary/assets/icons/content-copy';
import '@nectary/components/button';
import '@nectary/components/dialog';
import '@nectary/components/text';
import '@nectary/components/icon';
import '@nectary/components/spinner';

import styles from './action-modal.less';

type ActionModalProp = {
  title: string;
  isDialogOpen: boolean;
  actionModalContent: string;
  disabled: boolean;
  testId?: string;
  onConfirm: () => void;
  onClose: () => void;
};

const ActionModal = (props: ActionModalProp) => {
  const {
    title,
    isDialogOpen,
    onClose,
    actionModalContent,
    onConfirm,
    testId,
    disabled,
  } = props;

  const handleCloseModal = () => {
    onClose();
  };
  return (
    <div className={styles.dialogWrapper}>
      <sinch-dialog
        data-testid={testId || 'action-dialog'}
        open={isDialogOpen}
        caption={title}
        aria-label="Dialog"
        close-aria-label="Close dialog"
        on-close={onClose}
      >
        <div slot="content">
          <div className={styles.section}>
            <sinch-text type="m">
              {actionModalContent}
            </sinch-text>
          </div>
        </div>
        <sinch-button
          slot="buttons"
          text="Cancel"
          aria-label="Cancel"
          type="secondary"
          on-click={handleCloseModal}
        />
        <sinch-button
          slot="buttons"
          text="Confirm"
          aria-label="Confirm"
          type="primary"
          disabled={disabled}
          on-click={onConfirm}
        />
      </sinch-dialog>
    </div>
  );
};

export default ActionModal;
