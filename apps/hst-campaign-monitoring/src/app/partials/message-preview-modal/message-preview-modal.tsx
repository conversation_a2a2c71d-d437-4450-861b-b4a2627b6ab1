import '@nectary/components/dialog';
import '@nectary/components/text';
import '@nectary/components/list';
import '@nectary/components/list-item';
import '@nectary/components/icon';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import { Spinner } from 'nectary';

import { MessageItem } from '../../types';

import styles from './message-preview-modal.less';

type MessagePreviewModalProp = {
  isDialogOpen: boolean;
  onClose: () => void;
  messages: MessageItem[];
  loading: boolean;
};

const MessagePreviewModal = (props: MessagePreviewModalProp) => {
  const { isDialogOpen, onClose, messages, loading } = props;

  const renderContent = () => {
    if (_isEmpty(messages)) {
      return (
        <div slot="content" className={styles.contentWrap}>
          <div className={styles.noDataIcon}>
            <sinch-icon name="scan_delete" />
          </div>
          <div>
            <sinch-text type="m">No Data</sinch-text>
          </div>
        </div>
      );
    }
    return (
      <div slot="content" className={styles.section}>
        <sinch-list>
          {messages.map((item: MessageItem) => (
            <sinch-list-item>
              <div slot="content" className={styles.textItem}>
                <sinch-text type="m">
                  Content: {_get(item, 'content')}
                </sinch-text>
              </div>
              <div slot="content" className={styles.textItem}>
                <sinch-text slot="content" type="m">
                  To: {_get(item, 'phoneNumber')}
                </sinch-text>
              </div>
              {_get(item, 'subject') && (
                <div slot="content" className={styles.textItem}>
                  <sinch-text slot="content" type="m">
                    Subject: {_get(item, 'subject')}
                  </sinch-text>
                </div>
              )}
              {!_isEmpty(item.urls) && (
                <div slot="content" className={styles.textItem}>
                  <sinch-text slot="content" type="m">
                    URL:{' '}
                    {_get(item, 'urls', []).map((url) => (
                      <div>
                        <a href={url} target="_blank">
                          {url}
                        </a>
                      </div>
                    ))}
                  </sinch-text>
                </div>
              )}
            </sinch-list-item>
          ))}
        </sinch-list>
      </div>
    );
  };

  return (
    <div className={styles.dialogWrapper}>
      <sinch-dialog
        data-testid="message-dialog"
        open={isDialogOpen}
        caption="Message"
        aria-label="Dialog"
        close-aria-label="Close dialog"
        on-close={onClose}
      >
        {loading ? (
          <div slot="content" className={styles.contentWrap}>
            <Spinner size="m" />;
          </div>
        ) : (
          renderContent()
        )}
      </sinch-dialog>
    </div>
  );
};

export default MessagePreviewModal;
