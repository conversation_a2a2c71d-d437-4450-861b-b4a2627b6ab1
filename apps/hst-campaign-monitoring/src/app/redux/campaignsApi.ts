import { fetchInstance, paramsToQueryString } from 'helpers';
import { handleAPI } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import {
  MessagePreviewParams,
  UpdatedCampaignParams,
  UpdateUsageTrackingThresholdParams,
} from '../types';

export default {
  loadCampaigns: handleAPI(async (params: object) => {
    const queryString = paramsToQueryString({ params });
    const formattedQueryString = queryString.replace(/\[.*?\]/g, '');

    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/broadcast-hold-filters?${formattedQueryString}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
  updateCampaign: handleAPI(async (params: UpdatedCampaignParams) => {
    const { vendorId, accountId, campaignId, holdStatus } = params;
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/broadcast-hold-filters/${campaignId}`,
      {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ holdStatus }),
      }
    );
  }),
  updateUsageTrackingThreshold: handleAPI(
    async (params: UpdateUsageTrackingThresholdParams) => {
      const {
        vendorId,
        accountId,
        threshold,
        period,
        label,
        timezoneType,
        action,
        timezone,
        alerts,
      } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/usage-trackings`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            threshold,
            period,
            label,
            timezoneType,
            action,
            timezone,
            alerts,
          }),
        }
      );
    }
  ),
  fetchMessagePreview: handleAPI(async (params: MessagePreviewParams) => {
    const { accountId, vendorId, campaignId } = params;
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/broadcast-hold-filters/${campaignId}/preview`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
};
