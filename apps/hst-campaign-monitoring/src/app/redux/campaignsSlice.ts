import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import campaignsApi from './campaignsApi';

import { LOADING_STATUS } from '../constants/index';
import {
  CampaignParams,
  MessageItem,
  MessagePreviewParams,
  UpdatedCampaignParams,
  UpdateUsageTrackingThresholdParams,
} from '../types';

export interface CampaignsState {
  pagination: {
    next?: string;
  };
  resources: [];
  loading: string;
  updateLoading: string;
  updateUsageTrackingThresholdLoading: string;
  fetchMessagePreviewLoading: string;
  messagePreviewList: MessageItem[];
}

const initialState: CampaignsState = {
  pagination: {},
  resources: [],
  loading: LOADING_STATUS.NOT_LOADED,
  updateLoading: LOADING_STATUS.NOT_LOADED,
  updateUsageTrackingThresholdLoading: LOADING_STATUS.NOT_LOADED,
  fetchMessagePreviewLoading: LOADING_STATUS.NOT_LOADED,
  messagePreviewList: []
};

export const fetchCampaigns = createAsyncThunk(
  'campaigns/fetchCampaigns',
  async (params: CampaignParams, thunkAPI) => {
    const response = await campaignsApi.loadCampaigns(params);
    return response;
  }
);

export const updateCampaign = createAsyncThunk(
  'campaigns/updateCampaign',
  async (params: UpdatedCampaignParams, thunkAPI) => {
    const response = await campaignsApi.updateCampaign(params);
    return response;
  }
);

export const updateUsageTrackingThreshold = createAsyncThunk(
  'campaigns/updateUsageTrackingThreshold',
  async (params: UpdateUsageTrackingThresholdParams, thunkAPI) => {
    const response = await campaignsApi.updateUsageTrackingThreshold(params);
    return response;
  }
);

export const fetchMessagePreview = createAsyncThunk(
  'campaigns/fetchMessagePreview',
  async (params: MessagePreviewParams, thunkAPI) => {
    const response = await campaignsApi.fetchMessagePreview(params);
    return response;
  }
);

export const campaignsSlice = createSlice({
  name: 'request',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    // fetch campaign list
    builder.addCase(
      fetchCampaigns.fulfilled,
      (state: CampaignsState, action) => ({
        ...state,
        pagination: action.payload.pagination,
        resources: action.payload.resources,
        loading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchCampaigns.pending,
      (state: CampaignsState, action) => ({
        ...state,
        loading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchCampaigns.rejected,
      (state: CampaignsState, action) => ({
        ...state,
        loading: LOADING_STATUS.FAILED,
      })
    );
    // update campaign
    builder.addCase(
      updateCampaign.pending,
      (state: CampaignsState, action) => ({
        ...state,
        updateLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      updateCampaign.fulfilled,
      (state: CampaignsState, action) => ({
        ...state,
        updateLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      updateCampaign.rejected,
      (state: CampaignsState, action) => ({
        ...state,
        updateLoading: LOADING_STATUS.FAILED,
      })
    );
    // updateUsageTrackingThreshold
    builder.addCase(
      updateUsageTrackingThreshold.pending,
      (state: CampaignsState, action) => ({
        ...state,
        updateUsageTrackingThresholdLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      updateUsageTrackingThreshold.fulfilled,
      (state: CampaignsState, action) => ({
        ...state,
        updateUsageTrackingThresholdLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      updateUsageTrackingThreshold.rejected,
      (state: CampaignsState, action) => ({
        ...state,
        updateUsageTrackingThresholdLoading: LOADING_STATUS.FAILED,
      })
    );
    // fetchMessagePreview
    builder.addCase(
      fetchMessagePreview.pending,
      (state: CampaignsState, action) => ({
        ...state,
        messagePreviewList: [],
        fetchMessagePreviewLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchMessagePreview.fulfilled,
      (state: CampaignsState, action) => ({
        ...state,
        messagePreviewList: action.payload.resources,
        fetchMessagePreviewLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchMessagePreview.rejected,
      (state: CampaignsState, action) => ({
        ...state,
        fetchMessagePreviewLoading: LOADING_STATUS.FAILED,
      })
    );
  },
});

export default campaignsSlice.reducer;
