/* eslint-disable @nx/enforce-module-boundaries */
import { ErrorBoundary } from 'components';
import { withPermissions } from 'hocs';

import CampaignMonitoringView from './campaign-monitoring/campaign-monitoring';
import styles from './app.module.less';

export const CampaignMonitoring = () => (
  <ErrorBoundary componentName="CampaignMonitoring">
    <div className={styles.container}>
      <CampaignMonitoringView />
    </div>
  </ErrorBoundary>
);

export default withPermissions(CampaignMonitoring);
