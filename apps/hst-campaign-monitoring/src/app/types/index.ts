export type AccountFeatureFlag = {
  label: string;
  name: string;
  scope: string;
};

export type CampaignItemType = {
  id: string;
  accountName: string;
  accountId: string;
  vendorId: string;
  accountTimezone: string;
  accountCreatedAt: string;
  userName: string;
  accountFeatureFlags?: AccountFeatureFlag[];
  heldAt: string;
  actionedAt: string;
  actionedBy: string;
  holdStatus: string;
  reason: string;
  broadcast: {
    status: string;
    id: string;
    name: string;
    from: string;
    enableDeliveryReceipts: boolean;
    recipientCount: number;
    estimatedRecipientCount: number;
    recipientErrorCount: number;
    processedCount: number;
    processedErrorCount: number;
    estimatedBillingUnits: number;
    messagesQueued: number;
    queueFailures: number;
    format: string;
    createdAt: string;
    updatedAt: string;
    staggerWindow: string;
    scheduledDisplayTimezone: string;
    scheduled: string;
    message: string;
    source?: SourceItem;
    mms?: {
      message?: string;
    };
  };
};

export type SourceItem = {
  number?: string;
  pool?: string;
  type?: string;
  label?: any;
};

export type CampaignParams = {
  accountId?: string;
  vendorId?: string;
  statuses: string[];
  startingAt?: string;
  endingAt?: string;
  sortBy?: string;
  sortDirection?: string;
  size?: number;
  next?: string | null;
};

export type UpdatedCampaignParams = {
  vendorId: string;
  accountId: string;
  campaignId: string;
  holdStatus: string;
};

export type SourceMappingKey = {
  ALPHANUMERIC: string;
  INTERNATIONAL: string;
};

export type ToastItem = {
  id?: string;
  text: string;
  type: string;
};

export type ActionItem = {
  accountId: string;
  vendorId: string;
  campaignId: string;
  updatedStatus: string;
};

export type UpdateUsageTrackingThresholdParams = {
  threshold: number;
  period: string;
  accountId: string;
  vendorId: string;
  label: string;
  timezoneType: string;
  action: string;
  timezone: string;
  alerts: number[];
}

export type MessageItem = {
  content: string;
  phoneNumber: string;
  subject: string;
  urls: string[];
};

export type MessagePreviewParams = {
  accountId: string;
  vendorId: string;
  campaignId: string;
};
