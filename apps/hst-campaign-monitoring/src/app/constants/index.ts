export const STATUS = {
  HELD: 'HELD',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  AUTO_APPROVED: 'AUTO_APPROVED',
};

export const DEFAULT_OPTION = {
  label: STATUS.HELD,
  value: STATUS.HELD,
};

export const APPROVED_OPTION = {
  label: STATUS.APPROVED,
  value: STATUS.APPROVED,
};

export const REJECTED_OPTION = {
  label: STATUS.REJECTED,
  value: STATUS.REJECTED,
};

export const STATUSES = [DEFAULT_OPTION, APPROVED_OPTION, REJECTED_OPTION];

export const PROD_BRANDS = [
  // {
  //   value: 'All',
  //   label: 'All',
  // },
  {
    value: 'MessageMedia',
    label: 'MessageMedia',
  },
  // {
  //   value: 'DirectSMS',
  //   label: 'DirectSMS',
  // },
  {
    value: 'SMSBroadcast',
    label: 'SMSBroadcast',
  },
  // {
  //   value: 'SMSCentral',
  //   label: 'SMSCentral',
  // },
  // {
  //   value: 'Streetdata',
  //   label: 'Streetdata',
  // },
  // {
  //   value: 'MessageNet',
  //   label: 'MessageNet',
  // },
  // {
  //   value: 'Mobipost',
  //   label: 'Mobipost',
  // },
  // {
  //   value: 'WholesaleSMS',
  //   label: 'WholesaleSMS',
  // },
  // // carrier brands
  // {
  //   value: 'VodaNZ',
  //   label: 'VodaNZ',
  // },
  // {
  //   value: '2Degrees',
  //   label: '2Degrees',
  // },
  // {
  //   value: 'eTXT',
  //   label: 'eTXT',
  // },
  // {
  //   value: 'TPGTelecom',
  //   label: 'TPGTelecom',
  // },
];


export const BRANDS = [
  {
    value: 'MessageMedia',
    label: 'MessageMedia',
  },
  {
    value: 'DirectSMS',
    label: 'DirectSMS',
  },
  {
    value: 'SMSBroadcast',
    label: 'SMSBroadcast',
  },
];


export const BRANDS_EU = [
  // {
  //   value: 'All',
  //   label: 'All',
  // },
  {
    value: 'SinchEU',
    label: 'SinchEU',
  },
  {
    value: 'SimpleTexting',
    label: 'SimpleTexting',
  },
];

export const LOADING_STATUS = {
  NOT_LOADED: 'not_loaded',
  LOADING: 'loading',
  SUCCEEDED: 'succeeded',
  FAILED: 'failed',
};


export const VODANZ = 'vodanz';
export const ONENZ = 'onenz';

export const ASCENDING = 'ASCENDING';
export const DESCENDING = 'DESCENDING';

export const DEFAULT_SENDER_TYPE = {
  ALPHA_TAG: 'ALPHA_TAG',
  DEDICATED_NUMBER: 'DEDICATED_NUMBER',
  USER_OWN_NUMBER: 'USER_OWN_NUMBER',
  SHARED_NUMBER: 'SHARED_NUMBER',
}

export const INTERNATIONAL = 'Dedicated number'

export const ALPHA_TAG = 'Alpha Tag'

export const sourceMapping = {
  ALPHANUMERIC: ALPHA_TAG,
  INTERNATIONAL,
}

export const MY_NUMBER = 'My number'

export const SENDER = {
  SHARED_NUMBER: 'Shared number',
  INTERNATIONAL: 'INTERNATIONAL',
  BUSINESS_NAME: 'ALPHANUMERIC',
}

export const URL_SHORTENER_NAME = 'URL_SHORTENER'
