import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
/* eslint-disable @nx/enforce-module-boundaries */
import '@nectary/assets/icons/check-circle-outline';
import '@nectary/assets/icons/highlight-off';
import '@nectary/assets/icons/not-interested';
import '@nectary/components/link';
import '@nectary/components/tag';
import '@nectary/components/text';
import classNames from 'classnames';
import { withPermissions } from 'hocs';
import _capitalize from 'lodash/capitalize';
import _get from 'lodash/get';
import _some from 'lodash/some';
import moment from 'moment';
import 'moment-timezone';

import {
  customDateTimeFormatReadable,
  getPermissionWithKey,
  isNonEmptyString,
  ROLES_KEY,
} from 'helpers';
import {
  Button,
  DateRange,
  Input,
  MultiSelect,
  Select,
  Table,
  Toast,
  Tooltip,
} from 'nectary';

import {
  BRANDS,
  BRANDS_EU,
  DEFAULT_SENDER_TYPE,
  DESCENDING,
  LOADING_STATUS,
  MY_NUMBER,
  ONENZ,
  PROD_BRANDS,
  SENDER,
  sourceMapping,
  STATUS,
  STATUSES,
  URL_SHORTENER_NAME,
  VODANZ,
} from '../constants';
import { RootState } from '../types/store';

import ActionModal from '../partials/action-modal/action-modal';
import MessagePreviewModal from '../partials/message-preview-modal/message-preview-modal';
import {
  fetchCampaigns,
  fetchMessagePreview,
  updateCampaign,
  updateUsageTrackingThreshold,
} from '../redux/campaignsSlice';
import {
  ActionItem,
  CampaignItemType,
  SourceItem,
  SourceMappingKey,
  ToastItem,
} from '../types';
import styles from './campaign-monitoring.module.less';

const wrapperStyles: React.CSSProperties = {
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'flex-start',
  gap: 10,
};

const DEFAULT_PAGESIZE = 10;

export const getSourceName = (source?: SourceItem) => {
  const sourcePool = _get(source, 'pool');
  const sourceType = _get(source, 'type') as keyof SourceMappingKey;
  const { DEDICATED_NUMBER, USER_OWN_NUMBER } = DEFAULT_SENDER_TYPE;

  if (sourcePool === DEDICATED_NUMBER) {
    return _get(source, 'label', sourceMapping[sourceType]);
  }
  if (sourcePool === USER_OWN_NUMBER) {
    return _get(source, 'label') || MY_NUMBER;
  }
  return _get(source, 'number') || SENDER.SHARED_NUMBER;
};

export const tableColumns = ({
  updateCampaignStatus,
  showBlockSendingModal,
  handleShowPreviewBody,
  isCampaignMonitoringEditable,
}: {
  updateCampaignStatus: ({
    vendorId,
    accountId,
    campaignId,
    updatedStatus,
  }: {
    vendorId: string;
    accountId: string;
    campaignId: string;
    updatedStatus: string;
  }) => void;
  showBlockSendingModal: (item: CampaignItemType) => void;
  handleShowPreviewBody: (item: CampaignItemType) => void;
  isCampaignMonitoringEditable: boolean;
}) => [
    {
      title: 'Account Name',
      index: 'accountName',
      sort: false,
      align: 'left',
      render: (item: CampaignItemType) => item.accountName,
    },
    {
      title: 'Account ID',
      index: 'accountId',
      sort: false,
      align: 'left',
      render: (item: CampaignItemType) => (
        <div className={styles.link}>
          <sinch-link
            text={item.accountId}
            aria-label="Link"
            href=""
            preventDefault
            on-click={() => {
              let baseName = _get(item, 'vendorId', '').toLowerCase();
              if (baseName === VODANZ) baseName = ONENZ;
              window.open(
                `${window.location.origin}/accounts/${baseName}/${item.accountId}`,
                '_blank'
              );
            }}
          />
        </div>
      ),
    },
    {
      title: 'Brand',
      index: 'vendorId',
      sort: false,
      align: 'left',
      render: (item: CampaignItemType) => (
        <sinch-text type="s">{item.vendorId}</sinch-text>
      ),
    },
    {
      title: 'Account Timezone',
      index: 'accountTimezone',
      sort: false,
      align: 'left',
      render: (item: CampaignItemType) => item.accountTimezone,
    },
    {
      title: 'Account Created Date',
      index: 'accountCreatedAt',
      sort: false,
      align: 'left',
      render: (item: CampaignItemType) => (
        <div className={styles.dateColumn}>
          {isNonEmptyString(item.accountCreatedAt)
            ? customDateTimeFormatReadable({
              datetime: item.accountCreatedAt,
              showTime: true,
            })
            : '-'}
        </div>
      ),
    },
    {
      title: 'Broadcast Name',
      index: 'name',
      sort: false,
      align: 'left',
      render: (item: CampaignItemType) => item.broadcast.name,
    },
    {
      title: 'From',
      index: 'id',
      sort: false,
      align: 'left',
      render: (item: CampaignItemType) => (
        <div className={styles.smallColumn}>
          <Tooltip orientation="top" text={_get(item.broadcast, 'source.number')}>
            {getSourceName(_get(item.broadcast, 'source'))}
          </Tooltip>
        </div>
      ),
    },
    {
      title: 'Recipients',
      index: 'recipientCount',
      sort: false,
      align: 'left',
      render: (item: CampaignItemType) => item.broadcast.recipientCount,
    },
    {
      title: 'Body',
      index: 'message',
      sort: false,
      align: 'left',
      render: (item: CampaignItemType) => {
        const previewButton = (
          <Button
            label="Preview"
            size="s"
            onClick={() => handleShowPreviewBody(item)}
          />
        );
        return (
          <div>
            <div className={styles.bodyColumn}>
              {item.broadcast.format === 'MMS'
                ? item.broadcast?.mms?.message
                : item.broadcast.message}
            </div>
            <div>{previewButton}</div>
          </div>
        );
      },
    },
    {
      title: 'URL Shortener',
      index: 'accountFeatureFlags',
      tooltipText: 'Displays the URL shortener feature status for the Account.',
      sort: false,
      render: (item: CampaignItemType) => {
        const enabledShortenerUrl = _some(
          item?.accountFeatureFlags,
          (feature) => feature.name === URL_SHORTENER_NAME
        );
        return enabledShortenerUrl ? 'Yes' : 'No';
      },
    },
    {
      title: 'Time held',
      index: 'heldAt',
      sort: false,
      align: 'left',
      render: (item: CampaignItemType) => (
        <div className={styles.dateColumn}>
          {isNonEmptyString(item.heldAt)
            ? customDateTimeFormatReadable({
              datetime: item.heldAt,
              showTime: true,
            })
            : '-'}
        </div>
      ),
    },
    {
      title: 'Time actioned',
      index: 'actionedAt',
      sort: false,
      align: 'left',
      render: (item: CampaignItemType) => (
        <div className={styles.dateColumn}>
          {isNonEmptyString(item.actionedAt)
            ? customDateTimeFormatReadable({
              datetime: item.actionedAt,
              showTime: true,
            })
            : '-'}
        </div>
      ),
    },
    {
      title: 'Status',
      index: 'holdStatus',
      sort: false,
      align: 'left',
      render: (item: CampaignItemType) =>
      ({
        [STATUS.HELD]: (
          <span className={styles.statusTag}>
            <sinch-tag text={_capitalize(item.holdStatus)} color="gray" small />
          </span>
        ),
        [STATUS.REJECTED]: (
          <span className={styles.statusTag}>
            <sinch-tag text={_capitalize(item.holdStatus)} color="red" small />
          </span>
        ),
        [STATUS.APPROVED]: (
          <span className={styles.statusTag}>
            <sinch-tag
              text={_capitalize(item.holdStatus)}
              color="green"
              small
            />
          </span>
        ),
        [STATUS.AUTO_APPROVED]: (
          <span className={styles.statusTag}>
            <sinch-tag
              text={_capitalize(item.holdStatus)}
              color="green"
              small
            />
          </span>
        ),
      }[item.holdStatus]),
    },
    {
      title: 'User name',
      index: 'userName',
      sort: false,
      align: 'left',
      render: (item: CampaignItemType) => item.userName,
    },
    {
      title: 'Action',
      index: 'id',
      sort: false,
      align: 'left',
      render: (item: CampaignItemType) => {
        if (item.holdStatus === STATUS.APPROVED) return <div />;

        const approveButton = (
          <div className={styles.aprroveBtn}>
            <Button
              label="Approve"
              size="s"
              icon={<sinch-icon-check-circle-outline slot="right-icon" />}
              onClick={() =>
                updateCampaignStatus({
                  accountId: item.accountId,
                  vendorId: item.vendorId,
                  campaignId: item.id,
                  updatedStatus: STATUS.APPROVED,
                })
              }
              disabled={!isCampaignMonitoringEditable}
            />
          </div>
        );
        const rejectButton = (
          <div className={styles.rejectBtn}>
            <Button
              label="Reject"
              size="s"
              icon={<sinch-icon-highlight-off slot="right-icon" />}
              onClick={() =>
                updateCampaignStatus({
                  accountId: item.accountId,
                  vendorId: item.vendorId,
                  campaignId: item.id,
                  updatedStatus: STATUS.REJECTED,
                })
              }
              disabled={!isCampaignMonitoringEditable}
            />
          </div>
        );
        const blockSendingButton = (
          <div className={styles.blockSendingBtn}>
            <Button
              label="Block Sending"
              size="s"
              icon={<sinch-icon-not-interested slot="right-icon" />}
              onClick={() => showBlockSendingModal(item)}
              disabled={!isCampaignMonitoringEditable}
            />
          </div>
        );

        if (item.holdStatus === STATUS.REJECTED) {
          return (
            <div style={wrapperStyles}>
              {approveButton}
              {blockSendingButton}
            </div>
          );
        }

        return (
          <div style={wrapperStyles}>
            {approveButton}
            {rejectButton}
            {blockSendingButton}
          </div>
        );
      },
    },
  ];

const CampaignMonitoring = ({ roles }: { roles: string[] }) => {
  const queryParams = new URLSearchParams(window.location.search);
  const brandURLParam = queryParams.get('brand') || '';
  let brands = [];
  if (IS_EU_VENDOR) {
    brands = BRANDS_EU;
  } else {
    brands = BUILD_ENV === 'production' ? PROD_BRANDS : BRANDS;
  }
  const [searchStatuses, setSearchStatuses] = useState([STATUSES[0].value]);
  const [accountId, setAccountId] = useState('');
  const [dateRange, setDateRange] = useState<string[]>([]);
  const [vendorId, setVendorId] = useState(brandURLParam || brands[0].value);
  const [tokenQueue, setTokenQueue] = useState<string[]>([]);
  const [pageSize, setPageSize] = useState<number>(DEFAULT_PAGESIZE);
  const [toasts, setToasts] = useState<ToastItem[]>([]);
  const rootState = useSelector((state: RootState) => state || {});
  const [actionItem, setActionItem] = useState<ActionItem | null>(null);
  const [blockSendingItem, setBlockSendingItem] =
    useState<CampaignItemType | null>(null);
  const [isVisibleBlockSendingModal, setVisibleBlockSendingModal] =
    useState<boolean>(false);
  const [isVisibleMessagePreviewModal, setVisibleMessagePreviewModal] =
    useState<boolean>(false);
  const {
    resources,
    pagination,
    loading,
    updateLoading,
    updateUsageTrackingThresholdLoading,
    messagePreviewList,
    fetchMessagePreviewLoading,
  } = rootState.campaigns || {};
  const userTimezone = moment.tz.guess()
  const dispatch = useDispatch();

  const getStartDateStr = useCallback(
    (momentVal: string) => {
      if (!momentVal) return undefined;
      return moment(momentVal, 'DD-MM-YYYY')
        ?.startOf('day')
        .tz(userTimezone, true)
        .format();
    },
    [userTimezone]
  );
  const getEndDateStr = useCallback(
    (momentVal: string) => {
      if (!momentVal) return undefined;
      return moment(momentVal, 'DD-MM-YYYY')
        ?.endOf('day')
        .tz(userTimezone, true)
        .format();
    },
    [userTimezone]
  );

  useEffect(() => {
    const from = getStartDateStr(dateRange?.[0]);
    const to = getEndDateStr(dateRange?.[1]);

    dispatch(
      fetchCampaigns({
        ...(accountId ? { accountId } : undefined),
        vendorId,
        statuses: searchStatuses,
        ...(from ? { startingAt: from } : undefined),
        ...(to ? { endingAt: to } : undefined),
        sortBy: 'heldAt',
        sortDirection: DESCENDING,
        size: pageSize,
      })
    );
  }, []);

  useEffect(() => {
    if (!actionItem) return;
    let newToasts = toasts;
    if (
      updateLoading === LOADING_STATUS.SUCCEEDED ||
      updateLoading === LOADING_STATUS.FAILED
    ) {
      const text =
        updateLoading === LOADING_STATUS.SUCCEEDED
          ? `Successfully updated campaign status to ${actionItem.updatedStatus}`
          : `Failed to update status for ${actionItem?.campaignId}`;
      newToasts = toasts.concat({
        id: actionItem?.campaignId,
        text,
        type: updateLoading === LOADING_STATUS.SUCCEEDED ? 'success' : 'error',
      });
      setToasts(newToasts);
      setActionItem(null);
      setTimeout(() => {
        handleSearch();
      }, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [updateLoading]);

  useEffect(() => {
    if (!blockSendingItem) return;
    let newToasts = toasts;
    if (
      updateUsageTrackingThresholdLoading === LOADING_STATUS.SUCCEEDED ||
      updateUsageTrackingThresholdLoading === LOADING_STATUS.FAILED
    ) {
      const text =
        updateUsageTrackingThresholdLoading === LOADING_STATUS.SUCCEEDED
          ? `Successfully blocked sending of the suspicious account`
          : `Failed to block sending of the suspicious account`;
      newToasts = toasts.concat({
        id: blockSendingItem.id,
        text,
        type:
          updateUsageTrackingThresholdLoading === LOADING_STATUS.SUCCEEDED
            ? 'success'
            : 'error',
      });
      setToasts(newToasts);
      setBlockSendingItem(null);
      setTimeout(() => {
        handleSearch();
      }, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [updateUsageTrackingThresholdLoading]);

  const handleChangeStatuses = (statuses: string[]) => {
    setSearchStatuses(statuses);
  };

  const handleChangeAccountId = (value: string) => setAccountId(value);

  const handleChangeDateRange = (dates: string[]) => {
    setDateRange(dates);
  };

  const handleChangeBrand = (value: string) => {
    setVendorId(value);
    queryParams.delete('brand');
    queryParams.set('brand', value);
    handleUpdateURL(queryParams.toString());
  };

  const handleUpdateURL = (queryString: string) => {
    const newurl = `${window.location.protocol}//${window.location.host}${window.location.pathname}?${queryString}`;
    window.history.pushState({ path: newurl }, '', newurl);
  };

  const handleOnKeyDown = (e: React.KeyboardEvent<HTMLFormElement>) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const handleSearch = () => {
    const from = getStartDateStr(dateRange?.[0]);
    const to = getEndDateStr(dateRange?.[1]);

    dispatch(
      fetchCampaigns({
        ...(accountId ? { accountId } : undefined),
        vendorId,
        statuses: searchStatuses,
        ...(from ? { startingAt: from } : undefined),
        ...(to ? { endingAt: to } : undefined),
        sortBy: 'heldAt',
        sortDirection: DESCENDING,
        size: pageSize,
      })
    );
    setTokenQueue([]);
  };

  const handlePrevious = () => {
    tokenQueue.pop();
    setTokenQueue(tokenQueue);
    const tokensLen = tokenQueue.length;
    const from = getStartDateStr(dateRange?.[0]);
    const to = getEndDateStr(dateRange?.[1]);

    setTimeout(() => {
      dispatch(
        fetchCampaigns({
          ...(accountId ? { accountId } : undefined),
          vendorId,
          statuses: searchStatuses,
          ...(from ? { startingAt: from } : undefined),
          ...(to ? { endingAt: to } : undefined),
          sortBy: 'heldAt',
          sortDirection: DESCENDING,
          size: pageSize,
          next: tokensLen ? tokenQueue[tokensLen - 1] : '',
        })
      );
    });
  };

  const handleNext = (token: string) => {
    tokenQueue.push(token);
    setTokenQueue(tokenQueue);
    const from = getStartDateStr(dateRange?.[0]);
    const to = getEndDateStr(dateRange?.[1]);

    setTimeout(() => {
      dispatch(
        fetchCampaigns({
          ...(accountId ? { accountId } : undefined),
          vendorId,
          statuses: searchStatuses,
          ...(from ? { startingAt: from } : undefined),
          ...(to ? { endingAt: to } : undefined),
          sortBy: 'heldAt',
          sortDirection: DESCENDING,
          size: pageSize,
          next: token,
        })
      );
    });
  };

  const handleChangePageSize = (newSize: number) => {
    setPageSize(newSize);
    const from = getStartDateStr(dateRange?.[0]);
    const to = getEndDateStr(dateRange?.[1]);

    dispatch(
      fetchCampaigns({
        ...(accountId ? { accountId } : undefined),
        vendorId,
        statuses: searchStatuses,
        ...(from ? { startingAt: from } : undefined),
        ...(to ? { endingAt: to } : undefined),
        sortBy: 'heldAt',
        sortDirection: DESCENDING,
        size: newSize,
      })
    );
  };

  const updateCampaignStatus = ({
    vendorId: campaignVendorId,
    accountId: campaignAccountId,
    campaignId,
    updatedStatus,
  }: {
    vendorId: string;
    accountId: string;
    campaignId: string;
    updatedStatus: string;
  }) => {
    setActionItem({
      vendorId: campaignVendorId,
      accountId: campaignAccountId,
      campaignId,
      updatedStatus,
    });

    dispatch(
      updateCampaign({
        vendorId: campaignVendorId,
        accountId: campaignAccountId,
        campaignId,
        holdStatus: updatedStatus,
      })
    );
  };

  const showBlockSendingModal = (item: CampaignItemType) => {
    setBlockSendingItem(item);
    setVisibleBlockSendingModal(true);
  };

  const handleBlockSending = () => {
    if (!blockSendingItem) return;
    dispatch(
      updateUsageTrackingThreshold({
        threshold: 1,
        period: 'MONTHLY',
        accountId: blockSendingItem.accountId,
        vendorId: blockSendingItem.vendorId,
        label: 'Created by Hub',
        timezoneType: 'FIXED',
        action: 'DISCARD',
        timezone: 'Australia/Melbourne',
        alerts: [0.1, 1],
      })
    );
    setVisibleBlockSendingModal(false);
  };

  const handleShowPreviewBody = (item: CampaignItemType) => {
    dispatch(
      fetchMessagePreview({
        accountId: item.accountId,
        vendorId: item.vendorId,
        campaignId: item.id,
      })
    );
    setVisibleMessagePreviewModal(true);
  };

  const nextToken = _get(pagination, 'next', '');
  const { isEditable: isCampaignMonitoringEditable } = getPermissionWithKey(
    ROLES_KEY.ACCOUNT_CAMPAIGN_MONITORING,
    roles,
    true
  );

  return (
    <div>
      <div className={styles.section}>
        {/* eslint-disable-next-line jsx-a11y/no-noninteractive-element-interactions */}
        <form className={styles.searchForm} onKeyDown={handleOnKeyDown}>
          <div className={styles.formItem}>
            <MultiSelect
              label="Status"
              value={searchStatuses}
              placeholder="Status"
              options={STATUSES}
              onSelect={handleChangeStatuses}
              testId="status"
            />
          </div>
          <div className={styles.formItem}>
            <Input
              label="Account ID"
              defaultValue=""
              onChange={handleChangeAccountId}
              testId="account-id"
            />
          </div>
          <div className={styles.formItem}>
            <Select
              label="Brand"
              value={vendorId}
              defaultValue={brands[0].value}
              placeholder="Brand"
              options={brands}
              onSelect={handleChangeBrand}
              testId="brand"
              errorText=""
            />
          </div>
          <div className={styles.formItem}>
            <DateRange
              label="Time Requested"
              onChange={handleChangeDateRange}
              min={moment().subtract('1', 'years').format('YYYY-MM-DD')}
              max={moment().endOf('day').format('YYYY-MM-DD')}
              testId="date-range"
              disabled
            />
          </div>
          <div className={classNames(styles.formItem, styles.searchButton)}>
            <Button
              className={styles.searchButton}
              label="Search"
              onClick={handleSearch}
              testId="search-btn"
              disabled={loading === LOADING_STATUS.LOADING}
            />
          </div>
        </form>
      </div>
      <div className={styles.table}>
        <Table
          hasCheckbox={false}
          tableColumns={tableColumns({
            updateCampaignStatus,
            showBlockSendingModal,
            handleShowPreviewBody,
            isCampaignMonitoringEditable,
          })}
          keyField="id"
          tableData={resources}
          loading={loading}
          next={nextToken}
          previous={tokenQueue.length}
          handlePreviousPage={handlePrevious}
          handleNextPage={handleNext}
          pageSize={pageSize}
          handleChangePageSize={handleChangePageSize}
          scrollX
        />
      </div>
      <Toast toasts={toasts} setToasts={setToasts} />

      <ActionModal
        title="Block Sending"
        isDialogOpen={isVisibleBlockSendingModal}
        onClose={() => setVisibleBlockSendingModal(false)}
        actionModalContent={`Confirm to block sending account ${blockSendingItem?.accountId}`}
        onConfirm={handleBlockSending}
        disabled={updateLoading === LOADING_STATUS.LOADING}
      />
      <MessagePreviewModal
        isDialogOpen={isVisibleMessagePreviewModal}
        onClose={() => setVisibleMessagePreviewModal(false)}
        messages={messagePreviewList}
        loading={fetchMessagePreviewLoading === LOADING_STATUS.LOADING}
      />
    </div>
  );
};

export default withPermissions(CampaignMonitoring);
