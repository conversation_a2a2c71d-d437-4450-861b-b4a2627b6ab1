@import '~@sinch-smb/styles/variables.less';

.section {
  padding-top: .rem(28) [];
  padding-bottom: .rem(28) [];
}

.searchForm {
  display: flex;
  align-items: flex-end;
  justify-content: flex-start;
  flex-wrap: wrap;
}

.formItem {
  margin-right: .rem(15) [];
  margin-bottom: .rem(8) [];
}

.searchButton {
  margin-bottom: 13px;
}


.aprroveBtn {
  --sinch-comp-button-color-primary-default-background-initial: var(
    --sinch-ref-color-main-tropical-200
  );
  --sinch-comp-button-color-primary-default-background-hover: var(
    --sinch-ref-color-main-tropical-300
  );
  --sinch-comp-button-size-icon-s: 16px;
}

.rejectBtn {
  --sinch-comp-button-color-primary-default-background-initial: var(
    --sinch-ref-color-main-raspberry-400
  );
  --sinch-comp-button-color-primary-default-background-hover: var(
    --sinch-ref-color-main-raspberry-500
  );
  --sinch-comp-button-size-icon-s: 16px;
}

.blockSendingBtn {
  --sinch-comp-button-color-primary-default-background-initial: var(
    --sinch-ref-color-main-honey-200
  );
  --sinch-comp-button-color-primary-default-background-hover: var(
    --sinch-ref-color-main-honey-500
  );
  --sinch-comp-button-size-icon-s: 16px;
}


.statusTag {
  --sinch-comp-tag-color-gray-background: var(
    --sinch-sys-color-status-offline-background
  );
  --sinch-comp-tag-color-green-background: var(
    --sinch-sys-color-status-online-background
  );
  --sinch-comp-tag-color-red-background: var(
    --sinch-sys-color-status-busy-background
  );
  --sinch-comp-tag-color-gray-foreground: var(
    --sinch-sys-color-status-offline-contrast
  );
  --sinch-comp-tag-color-green-foreground: var(
    --sinch-sys--color-status-online-contrast
  );
  --sinch-comp-tag-color-red-foreground: var(
    --sinch-sys-color-status-busy-contrast
  );
}

.dateColumn {
  min-width: 100px
}
