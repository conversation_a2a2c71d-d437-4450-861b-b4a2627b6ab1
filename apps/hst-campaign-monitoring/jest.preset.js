const nxPreset = require('@nx/jest/preset').default;

module.exports = {
  ...nxPreset,
  setupFilesAfterEnv: [
    "./tools/jest-shim.js",
    "./tools/jest-setup.js",
    "jest-localstorage-mock"
  ],
  globals: {
    "NODE_ENV": "test"
  },
  roots: [
    "./src"
  ],
  collectCoverage: true,
  collectCoverageFrom: [
    "src/**/*.ts",
    "src/**/*.tsx",
    "!src/environments/*.ts",
  ],
  transformIgnorePatterns: [
    "jest-runner"
  ],
  coverageThreshold: {
    "global": {
      "branches": 20,
      "functions": 20,
      "lines": 20,
      "statements": 20
    }
  },
};
