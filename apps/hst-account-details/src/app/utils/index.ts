// import { TABS } from '../constants/tabs'
// import { ACCOUNT_UI_TYPE } from '../constants/account-types'
import { labelLookup } from 'helpers'
import _get from 'lodash/get'

export const isShowTab = (accountType: string, tabName: string) => {
  if (!accountType || !tabName) return false
  // todo: mapping account type to toggle showing/hiding tabs
  // for now, we just return true for all tabs
  return true
}

export const isShowPanel = (accountType: string, tabName: string, panelName: string) => {
  if (!accountType || !tabName || !panelName) return false
  // todo: mapping account type to toggle showing/hiding panels/widgets in each tab
  // for now, we just return true for all panels/widgets
  return true
}

export function detectRegion() {
    const { hostname } = window.location;
    if (hostname.includes('dub.')) {
        return 'EMEA';
    } if (hostname.includes('syd.')) {
        return 'APAC';
    }
    return null;
}

export type Environment = 'qa' | 'staging' | 'dev' | 'production';

export function detectEnvironment(): Environment {
  const { hostname } = window.location;
  if (hostname.includes('.qa.')) return 'qa';
  if (hostname.includes('.stg.')) return 'staging';
  if (hostname.includes('.dev.')) return 'dev';
  return 'production';
}


export const getPriceRule = (number: any) => {
  const rules = labelLookup('dedicated-number-prices')
  let { country } = number
  if (country === 'US') {
    if (['TOLL_FREE'].includes(number.type)) {
      country = 'US_TFN'
    } else if (number.capabilities.includes('MMS')) {
      country = 'US_MMS'
    } else {
      country = 'US_DEFAULT'
    }
  }
  if (
    number?.capabilities?.includes('CALL')
    && number?.country === 'AU'
    && number?.classification === 'BRONZE'
  ) {
    return '$49'
  }
  const defaultValue = _get(rules, `${country}.BRONZE`, 'TBD')
  return _get(rules, `${country}.${number.classification}`, defaultValue)
}