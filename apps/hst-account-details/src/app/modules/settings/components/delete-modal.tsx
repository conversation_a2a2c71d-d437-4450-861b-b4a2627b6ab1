import { useState } from 'react';
import { useDispatch } from 'react-redux';

import { Dialog, Text, Input, Button } from 'nectary';

import styles from './delete-modal.module.less';

const DeleteModal = ({
  isVisible,
  setIsVisible,
  id,
  vendorId,
  accountId,
  type,
  label,
  deleteAction
}: {
  isVisible: boolean,
  setIsVisible: (isVisible: boolean) => void,
  id: string | undefined,
  vendorId: string,
  accountId: string,
  type: string,
  label: string | undefined,
  deleteAction: ({ vendorId, accountId, id }: { vendorId: string, accountId: string, id: string }) => void
}) => {
  const dispatch = useDispatch();
  const [value, setValue] = useState('');
  const onSubmit = () => {
    if (!id) {
      return;
    }
    dispatch(
      deleteAction({
        vendorId,
        accountId,
        id,
      })
    );
    setIsVisible(false);
    setValue('');
  };

  return (
    <Dialog
      isOpen={isVisible}
      caption="Delete"
      onClose={() => {
        setIsVisible(false);
        setValue('');
      }}
    >
      <div className={styles.formItem}>
        <Text>{`Are you sure you want to delete ${type} ${label}`}</Text>
      </div>
      <div className={styles.formItem}>
        <Input
          label="Please type delete"
          value={value}
          onChange={(val: string) => setValue(val)}
        />
      </div>
      <div className={styles.formItem}>
        <Button
          label="Confirm"
          disabled={value.toLowerCase() !== 'delete'}
          onClick={onSubmit}
        />
      </div>
    </Dialog>
  );
};

export default DeleteModal;
