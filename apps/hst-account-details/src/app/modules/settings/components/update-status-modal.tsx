import _capitalize from 'lodash/capitalize';
import { useState } from 'react';
import { useDispatch } from 'react-redux';

import { Button, Checkbox, Dialog, Input, Tag, Text } from 'nectary';

import {
  ACTIVE,
  CANCELLED,
  REINSTATE,
  SUSPENDED,
  VERIFIED,
} from '../../../constants';
import { updateAccountStatus } from '../../../redux/settings/account-status/account-status-slice';

import styles from './update-status-modal.module.less';

export const STATUS_MAPPING = {
  ACTIVE: 'reinstate',
  SUSPENDED: 'suspend',
  CANCELLED: 'cancel',
  VERIFIED: 'verify',
  REINSTATE: 'reinstate',
};

// const REASONS = [
//   {
//     label: 'Spammer',
//     value: 'SPAMMER'
//   },
//   {
//     label: 'Fraud',
//     value: 'FRAUD'
//   },
//   {
//     label: 'Finance',
//     value: 'CHARGEBACK'
//   },
// ]

const UpdateStatusModal = ({
  isVisible,
  setIsVisible,
  newStatus,
  vendorId,
  accountId,
  currentStatus,
}: {
  isVisible: boolean;
  setIsVisible: (isVisible: boolean) => void;
  newStatus: keyof typeof STATUS_MAPPING;
  vendorId: string;
  accountId: string;
  currentStatus: string;
}) => {
  const dispatch = useDispatch();
  const [value, setValue] = useState('');
  const [applyToSubaccounts, setApplyToSubaccounts] = useState(true);

  const onSubmit = () => {
    dispatch(
      updateAccountStatus({
        vendorId,
        accountId,
        status: newStatus,
        applyToSubaccounts,
      })
    );
    setIsVisible(false);
    setValue('');
  };

  // const handleSelectReason = (val: string) => setValue(val)

  const renderStatus = () => {
    const text = _capitalize(newStatus);
    if (
      newStatus === ACTIVE ||
      newStatus === VERIFIED ||
      newStatus === REINSTATE
    ) {
      return (
        <span className={styles.active}>
          <Tag key="light-green" color="light-green" text={text} />
        </span>
      );
    }
    if (newStatus === SUSPENDED) {
      return (
        <span className={styles.suspended}>
          <Tag key="orange" color="orange" text={text} />
        </span>
      );
    }
    return (
      <span className={styles.cancelled}>
        <Tag key="light-red" color="light-red" text={text} />
      </span>
    );
  };

  const disabled =
    (newStatus === REINSTATE && value.toUpperCase() !== REINSTATE) ||
    (newStatus === VERIFIED && value.toUpperCase() !== 'VERIFY') ||
    (newStatus === CANCELLED && value.toUpperCase() !== 'CANCEL') ||
    (newStatus === SUSPENDED && value.toUpperCase() !== 'SUSPEND');

  return (
    <div className={styles.dialogWrap}>
      <Dialog
        isOpen={isVisible}
        caption="Account Status"
        onClose={() => {
          setValue('');
          setIsVisible(false);
        }}
      >
        <div className={styles.formItem}>
          <span className={styles.inlineText}>
            <Text>{`Status change to `}</Text>
          </span>
          {renderStatus()}
        </div>

        <div className={styles.formItem}>
          <Text>
            {`Are you sure you want `}
            <b>{STATUS_MAPPING[newStatus]}</b>
            {` the Account `}
            <b>{accountId}</b>?
          </Text>
        </div>

        <div className={styles.formItem}>
          <Checkbox
            key="sub-accounts"
            label={`${_capitalize(STATUS_MAPPING[newStatus])} all ${_capitalize(
              currentStatus
            )} sub-accounts together with the parent`}
            value={applyToSubaccounts}
            onChange={setApplyToSubaccounts}
          />
        </div>

        {(newStatus === REINSTATE ||
          newStatus === VERIFIED ||
          newStatus === CANCELLED ||
          newStatus === SUSPENDED) && (
          <div className={styles.formItem}>
            <Input
              label={`Please type "${STATUS_MAPPING[newStatus]}"`}
              value={value}
              onChange={(val: string) => setValue(val)}
            />
          </div>
        )}
        <div className={styles.formItem}>
          <Button label="Confirm" disabled={disabled} onClick={onSubmit} />
        </div>
      </Dialog>
    </div>
  );
};

export default UpdateStatusModal;
