import moment from 'moment';
import 'moment-timezone';
import { useState, useEffect } from 'react';

import { AutoComplete } from 'nectary';

const timezoneOptions = moment.tz.names().map((timezone: string) => ({
  value: timezone,
  label: timezone,
}));

const TimezoneAutoComplete = ({
  value,
  onChange,
  errorText,
}: {
  value: string;
  onChange: (option: { value: string, label: string }) => void;
  errorText: string;
}) => {
  const [searchValue, setValue] = useState(value || '');
  const [options, setOptions] = useState<{value: string, label: string}[]>([]);

  useEffect(() => {
    setValue(value || '');
  }, [value]);

  const handleSearch = (searchText: string) => {
    setOptions(
      !searchText
        ? []
        : timezoneOptions.filter((timezone) =>
            timezone.label.toLowerCase().includes(searchText.toLowerCase())
          )
    );
  };

  const handleSelect = (data: { value: string, label: string }) => {
    onChange && onChange(data);
  };

  const handleChange = (data: string) => {
    setValue(data);
  };

  return (
    <AutoComplete
      label="Timezone"
      placeholder="Search for Timezones"
      searchValue={searchValue}
      options={options}
      handleSearch={handleSearch}
      handleSelect={handleSelect}
      handleChange={handleChange}
      errorText={errorText}
    />
  );
};

export default TimezoneAutoComplete;
