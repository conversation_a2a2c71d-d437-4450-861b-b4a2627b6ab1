.dialogWrap {
  sinch-dialog {
    --sinch-comp-dialog-max-width: 750px;
  }
}
.formItem {
  margin-bottom: 16px;
  position: relative;
}

.inlineText {
  display: inline-flex;
  margin-right: 8px;
}

.active,
.verified {
  align-items: flex-start;
  display: inline-flex;
  justify-content: flex-start;
  sinch-text {
    color: var(--sinch-ref-color-complementary-olive-400);
  }
  --sinch-global-color-icon: var(--sinch-ref-color-complementary-olive-400);
}

.suspended {
  align-items: flex-start;
  display: inline-flex;
  justify-content: flex-start;
  sinch-text {
    color: var(--sinch-ref-color-complementary-pumpkin-400);
  }
  --sinch-global-color-icon: var(--sinch-ref-color-pumpkin-400);
}

.unverified,
.cancelled {
  align-items: flex-start;
  display: inline-flex;
  justify-content: flex-start;
  sinch-text {
    color: var(--sinch-ref-color-complementary-raspberry-400);
  }
  --sinch-global-color-icon: var(--sinch-ref-color-raspberry-400);
}

.spinnerWrap {
  width: 100%;
  height: 100%;
  position: absolute;
  opacity: 0.3;
  display: flex;
  align-items: center;
}
