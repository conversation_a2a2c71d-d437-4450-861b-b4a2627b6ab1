import { ROLES_KEY, getPermission<PERSON><PERSON><PERSON>ey } from 'helpers';
import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';

import { Tab } from 'nectary';
import AccessControl from './panels/access-control/access-control';
import AccountDetails from './panels/account-details/account-details';
import AccountStatus from './panels/account-status/account-status';
import DeliveryProperties from './panels/delivery-properties/delivery-properties';
import HoldFilterSettings from './panels/hold-filter-settings/hold-filter-settings';
import InboundKeywords from './panels/inbound-keywords/inbound-keywords';
import ProviderWeights from './panels/provider-weights/provider-weights';
import UsageTracking from './panels/usage-tracking/usage-tracking';

import {
  fetchCarriers,
  fetchCountries,
} from '../../redux/settings/common-settings/common-settings-slice';

import { RelatedAccounts } from './panels/related-accounts/related-accounts';
import styles from './settings.module.less';

type SettingsProp = {
  vendorId: string;
  accountId: string;
  roles: Array<string>;
};

const getTabs = ({
  isAccountSettingVisible,
}: {
  isAccountSettingVisible: boolean;
}) => {
  if (!isAccountSettingVisible) {
    return [];
  }
  return [
    { text: 'Basic', value: 1 },
    { text: 'Provider Weights', value: 2 },
    { text: 'Access Control', value: 3 },
    { text: 'Related Accounts', value: 4 },
    { text: 'Inbound Keywords', value: 5 },
    { text: 'Delivery Properties', value: 6 },
  ];
};

const getSubTabNames = ({
  isAccountSettingVisible,
}: {
  isAccountSettingVisible: boolean;
}) => {
  if (!isAccountSettingVisible) {
    return [];
  }
  return [
    'basic',
    'provider-weights',
    'access-control',
    'related-accounts',
    'inbound-keywords',
    'delivery-properties',
  ];
};

export const Settings = (props: SettingsProp) => {
  const { vendorId, accountId, roles } = props;
  const dispatch = useDispatch();
  const { pathname } = window.location;
  const pathItems = pathname.split('/');
  const queryParams = new URLSearchParams(window.location.search);

  const {
    isVisible: isAccountSettingVisible,
    isEditable: isAccountSettingEditable,
  } = getPermissionWithKey(ROLES_KEY.ACCOUNT_SETTINGS, roles);

  const {
    isEditable: isAccountSettingBasicEditable,
  } = getPermissionWithKey(ROLES_KEY.ACCOUNT_SETTINGS_BASIC, roles);

  const {
    isVisible: isAccountStatusVisible,
    isEditable: isAccountStatusEditable,
  } = getPermissionWithKey(ROLES_KEY.ACCOUNT_STATUS, roles);

  const {
    isEditable: isAccountSettingAdvanceEditable,
  } = getPermissionWithKey(ROLES_KEY.ACCOUNT_SETTINGS_ADVANCE, roles);

  const {
    isEditable: isAccountSettingFinanceEditable,
  } = getPermissionWithKey(ROLES_KEY.ACCOUNT_SETTINGS_FINANCE, roles);

  const {
    isEditable: isVerificationEditable,
    isVisible: isVerificationVisible,
  } = getPermissionWithKey(ROLES_KEY.ACCOUNT_VERIFICATION, roles);

  const getSubTabIndexByName = (subTabName: string | null): number => {
    const subTabs = getSubTabNames({ isAccountSettingVisible });
    return subTabs.indexOf(subTabName || '') + 1 || 1;
  };

  const subTabURLParam =
    queryParams.get('subTab') || pathItems[pathItems.length - 1];
  const [currentSubTab, setCurrentSubTab] = useState(
    getSubTabIndexByName(subTabURLParam)
  );

  const handleUpdateURL = (queryString: string) => {
    const cleanPath = pathItems.join('/');
    const newurl = `${window.location.protocol}//${window.location.host}${cleanPath}?${queryString}`;
    window.history.replaceState({ path: newurl }, '', newurl);
  };

  const handleSetURLParams = (param: string, value: string) => {
    queryParams.set(param, value);
    handleUpdateURL(queryParams.toString());
  };

  const handleSubTabChange = (subTab: number) => {
    const subTabNames = getSubTabNames({ isAccountSettingVisible });
    const subTabName = subTabNames[subTab - 1];
    handleSetURLParams('subTab', subTabName);
    setCurrentSubTab(subTab);
  };

  useEffect(() => {
    dispatch(
      fetchCarriers({
        vendorId,
        accountId,
      })
    );
    dispatch(
      fetchCountries({
        vendorId,
        accountId,
      })
    );
  }, []);

  const tabs = getTabs({ isAccountSettingVisible });

  const basicContent = (
    <div className={styles.content}>
      {isAccountStatusVisible && (
        <div className={styles.card}>
          <AccountStatus
            {...props}
            isVerificationEditable={isVerificationEditable}
            isVerificationVisible={isVerificationVisible}
            isStatusEditable={
              isAccountStatusEditable
            }
          />
        </div>
      )}

      <div className={styles.card}>
        <AccountDetails
          {...props}
          isEditable={isAccountSettingEditable}
          isBasicEditable={
            isAccountSettingBasicEditable
          }
          isAdvancedEditable={
            isAccountSettingAdvanceEditable
          }
          isFinanceEditable={
            isAccountSettingFinanceEditable
          }
        />
      </div>
      {isAccountSettingVisible && (
        <div className={styles.card}>
          <UsageTracking
            {...props}
            isEditable={
              isAccountSettingFinanceEditable
            }
          />
        </div>
      )}
      {isAccountSettingVisible && (
        <div className={styles.card}>
          <HoldFilterSettings
            {...props}
            isEditable={
              isAccountSettingAdvanceEditable
            }
          />
        </div>
      )}
    </div>
  );

  const providerWeightsContent = (
    <div className={styles.content}>
      <div className={styles.card}>
        <ProviderWeights
          {...props}
          isEditable={
            isAccountSettingAdvanceEditable
          }
        />
      </div>
    </div>
  );

  const inboundKeywordsContent = (
    <div className={styles.content}>
      <div className={styles.card}>
        <InboundKeywords
          {...props}
          isEditable={
            isAccountSettingAdvanceEditable
          }
        />
      </div>
    </div>
  );

  const accessControlContent = (
    <div className={styles.content}>
      <div className={styles.card}>
        <AccessControl
          vendorId={vendorId}
          accountId={accountId}
          isEditable={
            isAccountSettingBasicEditable
          }
        />
      </div>
    </div>
  );

  const relatedAccountsContent = (
    <div className={styles.content}>
      <div className={styles.card}>
        <RelatedAccounts
          {...props}
          isEditable={
            isAccountSettingBasicEditable
          }
        />
      </div>
    </div>
  );

  const deliveryPropertyContent = (
    <div className={styles.content}>
      <div className={styles.card}>
        <DeliveryProperties
          {...props}
          isEditable={
            isAccountSettingAdvanceEditable
          }
        />
      </div>
    </div>
  );

  const tabContent = [{ content: basicContent }];

  if (isAccountSettingVisible) {
    tabContent.push(
      { content: providerWeightsContent },
      { content: accessControlContent },
      { content: relatedAccountsContent },
      { content: inboundKeywordsContent },
      { content: deliveryPropertyContent }
    );
  }

  return (
    <div className={styles.wrapper}>
      <Tab
        currentTab={currentSubTab}
        tabs={tabs}
        tabContent={tabContent}
        onChangeTab={handleSubTabChange}
      />
    </div>
  );
};

export default Settings;
