import _find from 'lodash/find';
import _get from 'lodash/get';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Button,
  CountryAutoComplete,
  Dialog,
  Input,
  Select,
  Spinner,
} from 'nectary';

import { useAppDispatch } from 'hooks';
import { LOADING_STATUS } from '../../../../constants/index';
import {
  createProviderWeight,
  fetchAddressTypes,
  fetchProviders,
  fetchProviderWeightTypes,
  updateProviderWeight,
} from '../../../../redux/settings/provider-weights/provider-weights-slice';
import { CarrierItem, CountryItem } from '../../../../types/settings';
import {
  AddressTypeItem,
  ProviderItem,
  ProviderWeightsItem,
  ProviderWeightTypeItem,
} from '../../../../types/settings/provider-weights';
import { RootState } from '../../../../types/store';
import {
  ENDPOINT_TYPE_OPTIONS,
  PRESENCE_OPTIONS,
  SERVICE_TYPE_OPTIONS,
  SERVICE_TYPE_VALUE_MAPPING,
} from './constants';
import styles from './provider-weights-modal.module.less';

const FIELD_NAMES = {
  type: 'type',
  presence: 'presence',
  weight: 'weight',
  value: 'value',
  serviceType: 'serviceType',
  provider: 'provider',
  addressType: 'addressType',
  carrier: 'carrier',
  endpointType: 'endpointType',
  country: 'country',
};

const isFieldValid = (fieldName: string, value: any) => {
  switch (fieldName) {
    case FIELD_NAMES.type:
      return !!value;
    case FIELD_NAMES.presence:
      return !!value;
    case FIELD_NAMES.value: {
      const { typeLabel, value: newValue } = value;
      if (
        typeLabel === 'MESSAGE_FLAG' ||
        typeLabel === 'PROVIDER_FLAG' ||
        typeLabel === 'SOURCE_ADDRESS' ||
        typeLabel === 'SERVICE_TYPE'
      ) {
        return !!newValue;
      }
      return true;
    }
    case FIELD_NAMES.provider: {
      const { typeLabel, provider } = value;
      if (typeLabel === 'PROVIDER') {
        return !!provider;
      }
      return true;
    }
    case FIELD_NAMES.weight: {
      const { presence, weight } = value;
      if (presence === 'WEIGHTED') {
        return !!weight && !isNaN(weight);
      }
      return !isNaN(weight);
    }
    case FIELD_NAMES.serviceType: {
      const { typeLabel, serviceType } = value;
      if (typeLabel === 'SERVICE_TYPE') {
        return !!serviceType;
      }
      return true;
    }
    case FIELD_NAMES.carrier:
    case FIELD_NAMES.endpointType:
    case FIELD_NAMES.country: {
      const { carrier, endpointType, country, countryOptions } = value || {};
      let truthyCount = 0;
      if (carrier) truthyCount += 1;
      if (endpointType) truthyCount += 1;
      if (country) {
        // Validate that the country is from the valid list
        if (countryOptions) {
          const isValidCountry = countryOptions.some(
            (option: { value: string; label: string }) => option.value === country
          );
          if (!isValidCountry) {
            return false;
          }
        }
        truthyCount += 1;
      }
      return truthyCount <= 1;
    }
    default:
      return true;
  }
};

const isFormValid = (payload: ProviderWeightsItem, countryOptions: { value: string; label: string }[]) => {
  let isValid = true;
  for (const [key, value] of Object.entries(payload)) {
    if (
      key === FIELD_NAMES.carrier ||
      key === FIELD_NAMES.endpointType ||
      key === FIELD_NAMES.country ||
      key === FIELD_NAMES.weight ||
      key === FIELD_NAMES.provider ||
      key === FIELD_NAMES.serviceType ||
      key === FIELD_NAMES.value
    ) {
      if (
        !isFieldValid(key, {
          ...payload,
          countryOptions,
        })
      ) {
        isValid = false;
      }
    } else if (!isFieldValid(key, value)) {
      isValid = false;
    }
  }
  return isValid;
};

const ProviderWeightModal = ({
  isVisible,
  setIsVisible,
  formValues = {} as ProviderWeightsItem,
  accountId,
  vendorId,
  setFormvalues,
  onSuccess,
  onError
}: {
  isVisible: boolean;
  setIsVisible: (isVisible: boolean) => void;
  formValues: ProviderWeightsItem;
  accountId: string;
  vendorId: string;
  setFormvalues: (item: any) => void;
  onSuccess?: (formValues: ProviderWeightsItem | object) => void;
  onError?: (formValues: ProviderWeightsItem | object) => void;
}) => {
  const rootState = useSelector((state: RootState) => state || {});
  const { accountCommonSettings, providerWeights } = rootState;
  const { carriers, carriersLoading, countries, countriesLoading } =
    accountCommonSettings || {};
  const {
    providers,
    providersLoading,
    addressTypes,
    addressTypesLoading,
    providerWeightTypes,
    providerWeightTypesLoading,
  } = providerWeights || {};

  const { resources: carriersResources } = carriers || {};
  const { resources: countriesResources } = countries || {};
  const { resources: providersResources } = providers || {};
  const { resources: addressTypesResources } = addressTypes || {};
  const { resources: providerWeightTypesResources } = providerWeightTypes || {};

  const COUNTRY_OPTIONS =
    (countriesResources || [])?.map(
      ({ countryName, id, _links }: CountryItem) => ({
        label: countryName,
        value: _get(_links, 'self.href') || id,
      })
    ) || [];
  const initialCountryOption = _find(
    COUNTRY_OPTIONS,
    ({ label }: { label: string }) => label === formValues?.country
  );
  const [countryOption, setCountryOption] = useState<{ value: string; label: string }>(
    initialCountryOption || { value: '', label: '' }
  );
  const [countryInputText, setCountryInputText] = useState<string>(
    initialCountryOption?.label || ''
  );

  const CARRIER_OPTIONS =
    (carriersResources || [])?.map(({ name, id, _links }: CarrierItem) => ({
      value: _get(_links, 'self.href') || id,
      label: name,
    })) || [];
  CARRIER_OPTIONS.unshift({ label: '', value: '' });

  const PROVIDER_OPTIONS =
    (providersResources || [])?.map(({ name, id, _links }: ProviderItem) => ({
      value: _get(_links, 'self.href') || id,
      label: name,
    })) || [];
  PROVIDER_OPTIONS.unshift({ label: '', value: '' });

  const ADDRESS_TYPE_OPTIONS =
    (addressTypesResources || [])?.map(
      ({ name, id, _links }: AddressTypeItem) => ({
        value: _get(_links, 'self.href') || id,
        label: name,
      })
    ) || [];
  ADDRESS_TYPE_OPTIONS.unshift({ label: '', value: '' });

  const TYPE_OPTIONS =
    (providerWeightTypesResources || [])?.map(
      ({ name, id, _links }: ProviderWeightTypeItem) => ({
        value: _get(_links, 'self.href') || id,
        label: name,
      })
    ) || [];

  const [type, setType] = useState(formValues?.type || '');
  const [presence, setPresence] = useState(formValues?.presence || '');
  const [weight, setWeight] = useState(formValues?.weight || '');
  const [value, setValue] = useState(formValues?.value || '');
  const [serviceType, setServiceType] = useState(formValues?.serviceType || '');
  const [provider, setProvider] = useState(formValues?.provider || '');
  const [addressType, setAddressType] = useState(formValues?.addressType || '');
  const [carrier, setCarrier] = useState(formValues?.carrier || '');
  const [endpointType, setEndpointType] = useState(
    formValues?.endpointType || ''
  );

  const [isValidated, setIsValidated] = useState(false);

  const dispatch = useAppDispatch();

  useEffect(() => {
    const nextCarrier = _find(
      CARRIER_OPTIONS,
      ({ label }: { label: string }) => label === formValues?.carrier
    );
    setCarrier(nextCarrier?.value || '');

    const nextCountryOption = _find(
      COUNTRY_OPTIONS,
      ({ label }: { label: string }) => label === formValues?.country
    );
    setCountryOption(nextCountryOption || { value: '', label: '' });

    const nextProvider = _find(
      PROVIDER_OPTIONS,
      ({ label }: { label: string }) => label === formValues?.provider
    );
    setProvider(nextProvider?.value || '');

    const nextAddressType = _find(
      ADDRESS_TYPE_OPTIONS,
      ({ label }: { label: string }) => label === formValues?.addressType
    );
    setAddressType(nextAddressType?.value || '');

    const nextProviderWeightType = _find(
      TYPE_OPTIONS,
      ({ label }: { label: string }) => label === formValues?.type
    );
    setType(nextProviderWeightType?.value || '');

    setPresence(formValues?.presence || '');
    setWeight(formValues?.weight || '');
    setValue(formValues?.value || '');
    if (formValues?.type === 'SERVICE_TYPE') {
      setServiceType(formValues?.value || '');
    } else {
      setServiceType(formValues?.serviceType || '');
    }
    setEndpointType(formValues?.endpointType || '');
  }, [formValues]);

  useEffect(() => {
    dispatch(
      fetchProviders({
        vendorId,
        accountId,
      })
    );

    dispatch(
      fetchProviderWeightTypes({
        vendorId,
        accountId,
      })
    );

    dispatch(
      fetchAddressTypes({
        vendorId,
        accountId,
      })
    );
  }, []);

  useEffect(() => {
    if (isVisible) setIsValidated(false);
  }, [isVisible]);

  const typeLabel = _find(
    TYPE_OPTIONS,
    ({ value: typeVal }: { value: string }) => typeVal === type
  )?.label;

  const handleSubmit = async () => {
    const payload = {
      id: formValues?.id,
      type,
      presence,
      weight,
      value,
      serviceType: _get(SERVICE_TYPE_VALUE_MAPPING, serviceType, undefined),
      provider,
      addressType,
      carrier,
      endpointType,
      country: countryOption?.value || '',
      typeLabel, // remove after validation
    };
    setIsValidated(true);

    // Check if country input text is valid
    if (!isCountryValid()) {
      return;
    }

    const isPayloadValid = isFormValid(payload, COUNTRY_OPTIONS);
    if (!isPayloadValid) return;

    Object.keys(payload).forEach((key) => {
      if (!payload?.[key as keyof typeof payload] || key === 'typeLabel') {
        delete payload[key as keyof typeof payload];
      }
    });

    if (formValues?.id) {
      dispatch(
        updateProviderWeight({
          accountId,
          vendorId,
          payload,
        })
      ).unwrap().then(() => {
        onSuccess && onSuccess(formValues);
      }).catch(() => {
        onError && onError(formValues);
      });
    } else {
      dispatch(
        createProviderWeight({
          accountId,
          vendorId,
          payload,
        })
      ).unwrap().then(() => {
        onSuccess && onSuccess(formValues);
      }).catch(() => {
        onError && onError(formValues);
      })
    }
  };

  const handleClose = () => {
    setFormvalues({});
    setIsVisible(false);
    setIsValidated(false);
  };

  const handleCountryChange = (option: { value: string; label: string }) => {
    setCountryOption(option);
    setCountryInputText(option.label || '');
  };

  const isCountryValid = () => {
    if (countryInputText?.trim()) {
      const matchingCountry = COUNTRY_OPTIONS.find(
        option => option.label.toLowerCase() === countryInputText.toLowerCase().trim()
      );
      return !!matchingCountry;
    }
    return true;
  };

  const getCountryErrorText = () => {
    if (!isValidated) return '';

    if (!isCountryValid()) {
      return 'Please select a valid country from the list';
    }

    if (!isFieldValid(FIELD_NAMES.country, {
      carrier,
      endpointType,
      country: countryOption?.value || '',
      countryOptions: COUNTRY_OPTIONS,
    })) {
      return 'Carrier, EndpointType and Country cannot be set at the same time';
    }

    return '';
  };

  return (
    <div className={styles.dialogWrap}>
      <Dialog
        isOpen={isVisible}
        caption="Provider Weight"
        onClose={handleClose}
        footer={
          <>
            <Button slot="buttons" label="Submit" size="m" onClick={handleSubmit} />
            <Button slot="buttons" label="Cancel" size="m" type="secondary" onClick={handleClose} /></>
        }
      >
        <form className={styles.filterWrapper}>
          <div className={styles.formItem}>
            {providerWeightTypesLoading === LOADING_STATUS.LOADING && (
              <div className={styles.spinner}>
                <Spinner size="m" />
              </div>
            )}
            <Select
              label="Type (Required)"
              value={type}
              options={TYPE_OPTIONS}
              onSelect={(newType: string) => {
                setType(newType);
                setWeight('');
                setValue('');
                setServiceType('');
                setProvider('');
              }}
              testId="type-select"
              customStyles={{ width: '100%' }}
              errorText={
                isValidated && !isFieldValid(FIELD_NAMES.type, type)
                  ? 'Type is required'
                  : ''
              }
            />
          </div>
          <div className={styles.formItem}>
            <Select
              label="Presence (Required)"
              value={presence}
              options={PRESENCE_OPTIONS}
              onSelect={(newPresence: string) => {
                setPresence(newPresence);
                setServiceType('');
                setProvider('');
                if (newPresence === 'WEIGHTED') {
                  setWeight('0');
                } else {
                  setWeight('');
                }
              }}
              testId="presence-select"
              customStyles={{ width: '100%' }}
              errorText={
                isValidated && !isFieldValid(FIELD_NAMES.presence, presence)
                  ? 'Presence is required'
                  : ''
              }
            />
          </div>
          <div className={styles.formItem}>
            <Input
              label={`Weight ${presence === 'WEIGHTED' ? '(Required)' : ''}`}
              value={weight}
              onChange={setWeight}
              testId="weight-input"
              fieldStyles={{ width: '70%' }}
              disabled={presence !== 'WEIGHTED'}
              errorText={
                isValidated &&
                  !isFieldValid(FIELD_NAMES.weight, { presence, weight })
                  ? 'Weight must be a number'
                  : ''
              }
            />
          </div>
          <div className={styles.formItem}>
            <Input
              label="Value"
              value={value}
              onChange={setValue}
              testId="value-input"
              fieldStyles={{ width: '70%' }}
              disabled={
                typeLabel !== 'MESSAGE_FLAG' &&
                typeLabel !== 'PROVIDER_FLAG' &&
                typeLabel !== 'SOURCE_ADDRESS'
              }
              errorText={
                isValidated &&
                  !isFieldValid(FIELD_NAMES.value, { typeLabel, value })
                  ? 'Value is required'
                  : ''
              }
            />
          </div>
          <div className={styles.formItem}>
            <Select
              label={`Service Type ${typeLabel === 'SERVICE_TYPE' ? '(Required)' : ''
                }`}
              value={serviceType}
              options={SERVICE_TYPE_OPTIONS}
              onSelect={(newServiceType: string) => {
                setServiceType(newServiceType);
                setValue(newServiceType);
              }}
              testId="service-type-select"
              customStyles={{ width: '100%' }}
              disabled={typeLabel !== 'SERVICE_TYPE'}
              errorText={
                isValidated &&
                  !isFieldValid(FIELD_NAMES.serviceType, {
                    serviceType,
                    typeLabel,
                  })
                  ? 'Service Type is required'
                  : ''
              }
            />
          </div>
          <div className={styles.formItem}>
            {providersLoading === LOADING_STATUS.LOADING && (
              <div className={styles.spinner}>
                <Spinner size="m" />
              </div>
            )}
            <Select
              label={`Provider ${typeLabel === 'PROVIDER' ? '(Required)' : ''}`}
              value={provider}
              options={PROVIDER_OPTIONS}
              onSelect={setProvider}
              testId="provider-select"
              customStyles={{ width: '100%' }}
              disabled={typeLabel !== 'PROVIDER'}
              rows={5}
              errorText={
                isValidated &&
                  !isFieldValid(FIELD_NAMES.provider, {
                    type,
                    typeLabel,
                    provider,
                  })
                  ? 'Provider is required'
                  : ''
              }
            />
          </div>
          <div className={styles.formItem}>
            {addressTypesLoading === LOADING_STATUS.LOADING && (
              <div className={styles.spinner}>
                <Spinner size="m" />
              </div>
            )}
            <Select
              label="Address Type"
              value={addressType}
              options={ADDRESS_TYPE_OPTIONS}
              onSelect={setAddressType}
              testId="address-type-select"
              customStyles={{ width: '100%' }}
              rows={5}
            />
          </div>
          <div className={styles.formItem}>
            {carriersLoading === LOADING_STATUS.LOADING && (
              <div className={styles.spinner}>
                <Spinner size="m" />
              </div>
            )}
            <Select
              label="Carrier"
              value={carrier}
              options={CARRIER_OPTIONS}
              onSelect={setCarrier}
              testId="carrier-select"
              customStyles={{ width: '100%' }}
              errorText={
                isValidated &&
                !isFieldValid(FIELD_NAMES.carrier, {
                  carrier,
                  endpointType,
                  country: countryOption?.value || '',
                  countryOptions: COUNTRY_OPTIONS,
                })
                  ? 'Carrier, EndpointType and Country cannot be set at the same time'
                  : ''
              }
              rows={5}
            />
          </div>
          <div className={styles.formItem}>
            <Select
              label="Endpoint Type"
              value={endpointType}
              options={ENDPOINT_TYPE_OPTIONS}
              onSelect={setEndpointType}
              testId="endpoint-type-select"
              customStyles={{ width: '100%' }}
              errorText={
                isValidated &&
                !isFieldValid(FIELD_NAMES.endpointType, {
                  carrier,
                  endpointType,
                  country: countryOption?.value || '',
                  countryOptions: COUNTRY_OPTIONS,
                })
                  ? 'Carrier, EndpointType and Country cannot be set at the same time'
                  : ''
              }
            />
          </div>
          <div className={styles.formItem}>
            {countriesLoading === LOADING_STATUS.LOADING && (
              <div className={styles.spinner}>
                <Spinner size="m" />
              </div>
            )}
            <CountryAutoComplete
              onChange={handleCountryChange}
              selectedOption={countryOption}
              countries={COUNTRY_OPTIONS}
              errorText={getCountryErrorText()}
              fieldStyles={{ width: '70%' }}
              onInputChange={setCountryInputText}
            />
          </div>
        </form>
      </Dialog>
    </div>
  );
};

export default ProviderWeightModal;
