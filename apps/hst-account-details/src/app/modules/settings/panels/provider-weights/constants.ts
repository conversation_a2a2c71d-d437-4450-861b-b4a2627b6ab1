
export const TYPE_OPTIONS = [
  {
    label: 'DELIVERY_RECEIPT',
    value: 'DELIVERY_RECEIPT',
  },
  {
    label: 'MESSAGE_FLAG',
    value: 'MESSAGE_FLAG',
  },
  {
    label: 'ONNET',
    value: 'ONNET',
  },
  {
    label: 'ONSHORE',
    value: 'ONSHORE',
  },
  {
    label: 'PROVIDER',
    value: 'PROVIDER',
  },
  {
    label: 'PROVIDER_FLAG',
    value: 'PROVIDER_FLAG',
  },
  {
    label: 'SERVICE_TYPE',
    value: 'SERVICE_TYPE',
  },
  {
    label: 'SOURCE_ADDRESS',
    value: 'SOURCE_ADDRESS',
  },
];

export const PRESENCE_OPTIONS = [
  {
    label: 'REQUIRED',
    value: 'REQUIRED',
  },
  {
    label: 'FORBIDDEN',
    value: 'FORBIDDEN',
  },
  {
    label: 'WEIGHTED',
    value: 'WEIGHTED',
  },
];

export const ADDRESS_TYPE_OPTIONS = [
  {
    label: '',
    value: '',
  },
  {
    label: 'ALPHANUMERIC',
    value: 'ALPHANUMERIC',
  },
  {
    label: 'INTERNATIONAL',
    value: 'INTERNATIONAL',
  },
  {
    label: 'SHORTCODE',
    value: 'SHORTCODE',
  },
  {
    label: 'UNKNOWN',
    value: 'UNKNOWN',
  },
];

export const SERVICE_TYPE_OPTIONS = [
  {
    label: 'SMS',
    value: 'SMS',
  },
  {
    label: 'MMS',
    value: 'MMS',
  },
  {
    label: 'TTS',
    value: 'TTS',
  },
  {
    label: 'RCS',
    value: 'RCS',
  },
];


export const SERVICE_TYPE_VALUE_MAPPING = {
  SMS: {
    id: '1',
    name: 'SMS',
  },
  MMS: {
    id: '2',
    name: 'MMS',
  },
  TTS: {
    id: '3',
    name: 'TTS',
  },
  RCS: {
    id: '4',
    name: 'RCS',
  },
};


export const ENDPOINT_TYPE_OPTIONS = [
  {
    label: '',
    value: '',
  },
  {
    label: 'MOBILE',
    value: 'MOBILE',
  },
  {
    label: 'LANDLINE',
    value: 'LANDLINE',
  },
];
