import {
  Button,
  Checkbox,
  Dialog,
  Input,
  MultiSelect,
  Table,
  Tag,
  Text,
  Toast,
  Toggle,
  Tooltip,
} from 'nectary';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { CANCELLED, LOADING_STATUS, SUSPENDED } from '../../../../constants';
import { loadAccounts } from '../../../../redux/account/accounts-search-slice';
import {
  fetchRelatedAccounts,
  fetchRelatedAccountsContext,
  resetUpdateLoading,
  updateRelatedAccounts,
} from '../../../../redux/settings/related-accounts/related-accounts-slice';
import { fetchSubAccountsDetails } from '../../../../redux/summary/sub-accounts-slice';
import { ToastItem } from '../../../../types';
import { RelatedAccountsContext } from '../../../../types/settings/related-accounts';
import { RootState } from '../../../../types/store';
import { SubAccount } from '../../../../types/sub-accounts';
import styles from './related-accounts.module.less';

type Option = {
  label: string;
  value: string;
};

interface RelatedAccount {
  id: string;
  name: string;
  enabled: boolean;
  accounts: string[];
}

interface TableItem extends Omit<RelatedAccount, 'accounts'> {
  contextId: string;
  accounts: Array<{ name: string; accountId: string }>;
}

interface ActiveContext {
  context: {
    id: string;
  };
  relatedAccountList: Array<{ name: string }>;
}

export function RelatedAccounts(props: {
  isEditable: boolean;
  vendorId: string;
  accountId: string;
}): JSX.Element {
  const { vendorId, accountId, isEditable } = props;
  const dispatch = useDispatch();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedContext, setSelectedContext] = useState<TableItem | null>(
    null
  );
  const [immediateParent, setImmediateParent] = useState(false);
  const [sameTree, setSameTree] = useState(false);
  const [unrelatedAccount, setUnrelatedAccount] = useState(false);
  const [selectedAccounts, setSelectedAccounts] = useState<string[]>([]);
  const [unrelatedAccountInput, setUnrelatedAccountInput] = useState('');
  const [dialogToggle, setDialogToggle] = useState(false);
  const [toasts, setToasts] = useState<ToastItem[]>([]);
  const [isToggleDialogOpen, setIsToggleDialogOpen] = useState(false);
  const [toggleItem, setToggleItem] = useState<TableItem | null>(null);
  const [includeItself, setIncludeItself] = useState(false);

  const rootState = useSelector((state: RootState) => state);
  const { relatedAccounts, accountDetails } = rootState || {};
  const { details } = accountDetails || {};
  const { resources: accountDetailsData } = details || {};

  const { relatedAccountsData, relatedAccountsContexts, updateLoading } =
    relatedAccounts || {};
  const { resources: activeRelatedAccounts } = relatedAccountsData || {};
  const { resources: allRelatedAccounts } = relatedAccountsContexts || {};

  const { data: subAccountsData } = useSelector(
    (state: RootState) => state.subAccounts
  );
  const { resources: subAccounts } = subAccountsData || {};

  const accountOptions = useMemo(() => {
    if (!subAccounts?.length || !details) return [];

    const nonSuspendedAccounts = subAccounts.filter(
      (item: SubAccount) =>
        item?.status !== SUSPENDED &&
        item?.status !== CANCELLED &&
        item?.id &&
        item?.accountId
    );

    const mappedAccounts = nonSuspendedAccounts.map((item: SubAccount) => ({
      value: item.id,
      label: item.accountId,
    }));

    if (mappedAccounts.length > 0 && details?.label) {
      return mappedAccounts;
    }

    if (accountId && details?.label) {
      return [{ value: accountId, label: details.label }];
    }

    return [];
  }, [subAccounts, details, accountId]);

  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        await Promise.all([
          dispatch(fetchRelatedAccountsContext({ vendorId, accountId })),
          dispatch(fetchRelatedAccounts({ vendorId, accountId })),
          dispatch(
            fetchSubAccountsDetails({ vendorId, accountId, size: 1000 })
          ),
        ]);
      } catch (error) {
        setToasts((prev) => [
          ...prev,
          {
            id: Date.now().toString(),
            text: 'Failed to fetch related accounts data',
            type: 'error',
          },
        ]);
      }
    };

    fetchInitialData();
  }, [dispatch, vendorId, accountId]);

  useEffect(
    () => () => {
      dispatch(resetUpdateLoading());
    },
    [dispatch]
  );

  useEffect(() => {
    const handleUpdateStatus = async () => {
      switch (updateLoading) {
        case LOADING_STATUS.SUCCEEDED:
          await Promise.all([
            dispatch(fetchRelatedAccountsContext({ vendorId, accountId })),
            dispatch(fetchRelatedAccounts({ vendorId, accountId })),
          ]);
          resetDialogState();
          setDialogToggle(false);
          setToasts((prev) => [
            ...prev,
            {
              id: Date.now().toString(),
              text: `Successfully updated Related Account settings for ${selectedContext?.name}`,
              type: 'success',
            },
          ]);
          break;

        case LOADING_STATUS.FAILED:
          setToasts((prev) => [
            ...prev,
            {
              id: Date.now().toString(),
              text: 'Failed to update Related Account settings. Please try again.',
              type: 'error',
            },
          ]);
          break;
      }
    };
    handleUpdateStatus();
  }, [updateLoading]);

  const toTitleCase = (str: string): string =>
    str
      .toLowerCase()
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');

  const validateParentAccount = async (
    parentAccountId: string
  ): Promise<string | null> => {
    try {
      const response = await dispatch(
        loadAccounts({
          searchCriteria: {
            accountId: parentAccountId.trim(),
            exact: true,
          },
        })
      );

      const accountContent = response?.payload?.resources?.[0]?.content;
      if (!accountContent?.id) {
        throw new Error('Parent account not found');
      }

      return accountContent.id;
    } catch (error) {
      setToasts((prev) => [
        ...prev,
        {
          id: Date.now().toString(),
          text: `Error validating parent account: ${
            error instanceof Error ? error.message : 'Unknown error'
          }`,
          type: 'error',
        },
      ]);
      return null;
    }
  };

  const handleStatusChange = async (item: TableItem) => {
    if (!item?.contextId) {
      setToasts((prev) => [
        ...prev,
        {
          id: Date.now().toString(),
          text: 'Invalid item data',
          type: 'error',
        },
      ]);
      return;
    }

    const parentId = await validateParentAccount(
      accountDetailsData?.parentAccountId || ''
    );
    if (!parentId) {
      setToasts((prev) => [
        ...prev,
        {
          id: Date.now().toString(),
          text: 'Invalid parent account ID',
          type: 'error',
        },
      ]);
      return;
    }

    // Determine which option should be selected based on the accounts
    if (item.accounts?.length > 0) {
      const accountIds = item.accounts.map((acc) => acc.accountId);

      // Check if current account is included
      const isCurrentAccountIncluded = accountIds.includes(accountId);
      setIncludeItself(isCurrentAccountIncluded);

      // Remove current account from the list if it's included
      const filteredAccountIds = isCurrentAccountIncluded
        ? accountIds.filter((id) => id !== accountId)
        : accountIds;

      // Check if any account matches the parent account ID
      if (
        parentId &&
        filteredAccountIds.length === 1 &&
        filteredAccountIds[0] === parentId
      ) {
        setImmediateParent(true);
        setSameTree(false);
        setUnrelatedAccount(false);
      }
      // Check if accounts match with sub-accounts
      else if (
        subAccounts?.some((subAcc: SubAccount) =>
          filteredAccountIds.includes(subAcc.id)
        )
      ) {
        setImmediateParent(false);
        setSameTree(true);
        setUnrelatedAccount(false);
        setSelectedAccounts(filteredAccountIds);
      }
      // If neither parent nor sub-accounts, it must be unrelated accounts
      else {
        setImmediateParent(false);
        setSameTree(false);
        setUnrelatedAccount(true);
        setUnrelatedAccountInput(
          item.accounts
            .filter((acc) => acc.accountId !== accountId)
            .map((acc) => acc.name)
            .join(', ')
        );
      }
    } else {
      // Reset to default state if no accounts
      setImmediateParent(false);
      setSameTree(false);
      setUnrelatedAccount(false);
      setIncludeItself(false);
    }

    setSelectedContext(item);
    setDialogToggle(item.enabled);
    setIsDialogOpen(true);
  };

  const resetDialogState = () => {
    setIsDialogOpen(false);
    setSelectedContext(null);
    setImmediateParent(false);
    setSameTree(false);
    setUnrelatedAccount(false);
    setSelectedAccounts([]);
    setUnrelatedAccountInput('');
    setDialogToggle(false);
    setIncludeItself(false);
  };

  const handleCheckboxChange = (
    type: 'immediateParent' | 'sameTree' | 'unrelatedAccount'
  ) => {
    const isUnchecking =
      (type === 'immediateParent' && immediateParent) ||
      (type === 'sameTree' && sameTree) ||
      (type === 'unrelatedAccount' && unrelatedAccount);

    setImmediateParent(false);
    setSameTree(false);
    setUnrelatedAccount(false);
    setSelectedAccounts([]);
    setUnrelatedAccountInput('');

    if (!isUnchecking) {
      switch (type) {
        case 'immediateParent':
          setImmediateParent(true);
          break;
        case 'sameTree':
          setSameTree(true);
          break;
        case 'unrelatedAccount':
          setUnrelatedAccount(true);
          break;
      }
    }
  };

  const validateUnrelatedAccounts = async (
    accountIds: string[]
  ): Promise<string[]> => {
    try {
      const validatedIds = await Promise.all(
        accountIds.map(async (inputAccountId) => {
          const response = await dispatch(
            loadAccounts({
              searchCriteria: {
                accountId: inputAccountId.trim(),
                exact: true,
              },
            })
          );
          const accountContent = response?.payload?.resources?.[0]?.content;
          if (!accountContent?.id) {
            throw new Error(`Account ${inputAccountId} not found`);
          }
          return accountContent.id;
        })
      );
      return validatedIds;
    } catch (error) {
      setToasts((prev) => [
        ...prev,
        {
          id: Date.now().toString(),
          text: `Error validating accounts: ${
            error instanceof Error ? error.message : 'Unknown error'
          }`,
          type: 'error',
        },
      ]);
      return [];
    }
  };

  const handleUpdateRelatedAccounts = async () => {
    if (!selectedContext) {
      setToasts((prev) => [
        ...prev,
        {
          id: Date.now().toString(),
          text: 'No valid data to update',
          type: 'error',
        },
      ]);
      return;
    }

    try {
      let selectedAccountsList: string[] = [];

      if (immediateParent && accountDetailsData?.parentAccountId) {
        const parentId = await validateParentAccount(
          accountDetailsData.parentAccountId
        );
        if (!parentId) return;
        selectedAccountsList = [parentId];
      } else if (sameTree && selectedAccounts.length > 0) {
        selectedAccountsList = selectedAccounts;
      } else if (unrelatedAccount && unrelatedAccountInput) {
        const inputAccountIds = unrelatedAccountInput
          .split(',')
          .map((id) => id?.trim())
          .filter(Boolean);

        if (!inputAccountIds.length) {
          setToasts((prev) => [
            ...prev,
            {
              id: Date.now().toString(),
              text: 'No valid account IDs provided',
              type: 'error',
            },
          ]);
          return;
        }

        const validatedIds = await validateUnrelatedAccounts(inputAccountIds);
        if (validatedIds.length !== inputAccountIds.length) return;
        selectedAccountsList = validatedIds;
      }

      if (includeItself && details?.id) {
        selectedAccountsList.push(details.id);
      }

      if (!processedTableData?.length) {
        setToasts((prev) => [
          ...prev,
          {
            id: Date.now().toString(),
            text: 'No table data available',
            type: 'error',
          },
        ]);
        return;
      }

      const updatedList = Object.entries(processedTableData).reduce<
        Record<string, RelatedAccountsContext>
      >((acc, [_, config]) => {
        if (!config?.contextId) return acc;

        if (config.contextId !== selectedContext.contextId) {
          return {
            ...acc,
            [config.contextId]: {
              enabled: config.enabled,
              relatedAccounts:
                config.accounts
                  ?.map((account) => account?.accountId)
                  .filter(Boolean) || [],
              relatedAccountsId: config.id || null,
            },
          };
        }

        return {
          ...acc,
          [config.contextId]: {
            enabled: dialogToggle,
            relatedAccounts: selectedAccountsList,
            relatedAccountsId: selectedContext.id || null,
          },
        };
      }, {});

      if (!Object.keys(updatedList).length) {
        setToasts((prev) => [
          ...prev,
          {
            id: Date.now().toString(),
            text: 'No valid data to update',
            type: 'error',
          },
        ]);
        return;
      }

      await dispatch(
        updateRelatedAccounts({
          vendorId,
          accountId,
          relatedAccountsContexts: updatedList,
        })
      );
    } catch (error) {
      setToasts((prev) => [
        ...prev,
        {
          id: Date.now().toString(),
          text: `Error updating related accounts: ${
            error instanceof Error ? error.message : 'Unknown error'
          }`,
          type: 'error',
        },
      ]);
    }
  };

  const handleToggleConfirm = async () => {
    if (!toggleItem) return;

    const updatedList = processedTableData.reduce<
      Record<string, RelatedAccountsContext>
    >(
      (acc, item) => ({
        ...acc,
        [item.contextId]: {
          enabled:
            item.contextId === toggleItem.contextId ? false : item.enabled,
          relatedAccounts:
            item.accounts?.map((account) => account.accountId) || [],
          relatedAccountsId: item.id || null,
        },
      }),
      {}
    );

    await dispatch(
      updateRelatedAccounts({
        vendorId,
        accountId,
        relatedAccountsContexts: updatedList,
      })
    );
    setIsToggleDialogOpen(false);
    setToggleItem(null);
  };

  const handleToggleClick = (item: TableItem): void => {
    if (!item.enabled) {
      handleStatusChange(item);
    } else {
      setToggleItem(item);
      setIsToggleDialogOpen(true);
    }
  };

  const processedTableData: TableItem[] = useMemo(
    () =>
      allRelatedAccounts
        ?.map((context: RelatedAccount) => {
          if (!context?.id || !context?.name) {
            return null;
          }
          const activeContext = activeRelatedAccounts?.find(
            (active: ActiveContext) => active?.context?.id === context.id
          );

          return {
            id: activeContext?.id,
            contextId: context.id,
            name: toTitleCase(context.name),
            enabled: !!activeContext,
            accounts:
              activeContext?.relatedAccountList?.map(
                (account: { name: string; id: string }) => ({
                  name: account?.name || 'Unknown',
                  accountId: account?.id || '',
                })
              ) || [],
          };
        })
        .filter(Boolean) || [],
    [allRelatedAccounts, activeRelatedAccounts]
  );

  const tableColumns = [
    {
      title: 'Name',
      index: 'name',
      align: 'left',
      render: (item: TableItem) => <Text>{item.name}</Text>,
    },
    {
      title: 'Status',
      index: 'enabled',
      align: 'left',
      render: (item: TableItem) => (
        <Toggle
          checked={item.enabled}
          labeled
          onChange={(): void => handleToggleClick(item)}
          disabled={!isEditable}
        />
      ),
    },
    {
      title: 'Accounts',
      index: 'accounts',
      align: 'left',
      render: (item: TableItem) => (
        <div className={styles.tableAccounts}>
          {item.accounts.map((account) => (
            <Tag key={account.accountId} text={account.name} />
          ))}
        </div>
      ),
    },
    {
      title: 'Actions',
      index: 'actions',
      align: 'left',
      render: (item: TableItem) => (
        <Button
          label="Edit"
          type="secondary"
          onClick={() => handleStatusChange(item)}
          disabled={!isEditable || !item.enabled}
        />
      ),
    },
  ];

  return (
    <div className={styles.root}>
      <Table
        tableColumns={tableColumns}
        tableData={processedTableData}
        keyField="id"
        loading="succeeded"
      />
      <div className={styles.dialogWrapper}>
        <Dialog
          caption={`Related Account Settings: ${
            selectedContext?.name || 'Access Control'
          }`}
          isOpen={isDialogOpen}
          onClose={resetDialogState}
        >
          <div className={styles.dialogContent}>
            <div className={styles.dialogSection}>
              <Text>
                Confirm the Access Control settings to apply to this account
              </Text>
            </div>
            <div className={`${styles.dialogSection} ${styles.stateRow}`}>
              <Text>State</Text>
              <Toggle
                labeled
                checked={dialogToggle}
                onChange={(checked: boolean) => {
                  setDialogToggle(checked);
                }}
              />
            </div>
            <div className={`${styles.dialogSection} ${styles.stateRow}`}>
              <Text>Include itself</Text>
              <Toggle
                labeled
                checked={includeItself}
                onChange={() => setIncludeItself(!includeItself)}
                disabled={!dialogToggle}
              />
            </div>
            <div className={styles.dialogSection}>
              <Text>Select Accounts</Text>
              <div className={styles.accountRow}>
                <Checkbox
                  label="Parent Account"
                  value={immediateParent}
                  onChange={() => handleCheckboxChange('immediateParent')}
                  disabled={
                    !accountDetailsData?.parentAccountId || !dialogToggle
                  }
                />
                {immediateParent && accountDetailsData?.parentAccountId && (
                  <Tag text={accountDetailsData.parentAccountId} />
                )}
              </div>
              <div className={styles.accountRow}>
                <Checkbox
                  label="Sub-account"
                  value={sameTree}
                  onChange={() => handleCheckboxChange('sameTree')}
                  disabled={!dialogToggle}
                />
              </div>
              {sameTree && (
                <MultiSelect
                  label="Account"
                  value={selectedAccounts}
                  options={accountOptions}
                  onSelect={setSelectedAccounts}
                  errorText={
                    selectedAccounts.length === 0 && sameTree
                      ? 'Account is required'
                      : ''
                  }
                  className={styles.multiSelect}
                  isValueDisplayed
                />
              )}
              <div className={styles.accountRow}>
                <Checkbox
                  label="Any other account"
                  value={unrelatedAccount}
                  onChange={() => handleCheckboxChange('unrelatedAccount')}
                  disabled={!dialogToggle}
                />
                {unrelatedAccount && (
                  <div className={styles.inputWrapper}>
                    <Input
                      value={unrelatedAccountInput}
                      onChange={(value: string) => {
                        setUnrelatedAccountInput(value);
                      }}
                      placeholder="Enter account ID"
                      testId="unrelated-account-input"
                    />
                    <Tooltip
                      type="fast"
                      orientation="top"
                      text="Enter multiple values separated by commas (e.g., value1, value2, value3)"
                    >
                      <div className={styles.iconInfo}>
                        <sinch-icon-info />
                      </div>
                    </Tooltip>
                  </div>
                )}
              </div>
            </div>
            <div className={styles.footer}>
              <Button
                type="default"
                label="Cancel"
                onClick={resetDialogState}
              />
              <Button
                label="Confirm"
                onClick={handleUpdateRelatedAccounts}
                disabled={sameTree && selectedAccounts.length === 0}
              />
            </div>
          </div>
        </Dialog>
        <Dialog
          caption={`Disable Related Account - ${toggleItem?.name || ''}`}
          isOpen={isToggleDialogOpen}
          onClose={() => {
            setIsToggleDialogOpen(false);
            setToggleItem(null);
          }}
        >
          <div className={styles.dialogContent}>
            <div className={styles.dialogSection}>
              <Text>
                Are you sure you want to disable related account settings for{' '}
                {toggleItem?.name}?
              </Text>
            </div>
            <div className={styles.footer}>
              <Button
                type="default"
                label="Cancel"
                onClick={() => {
                  setIsToggleDialogOpen(false);
                  setToggleItem(null);
                }}
              />
              <Button label="Confirm" onClick={handleToggleConfirm} />
            </div>
          </div>
        </Dialog>
      </div>
      <Toast toasts={toasts} setToasts={setToasts} />
    </div>
  );
}
