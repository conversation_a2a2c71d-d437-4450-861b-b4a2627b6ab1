.root {
  width: 100%;
  .dialogWrapper {
    sinch-dialog{
      --sinch-comp-dialog-max-width: 850px;
      --sinch-comp-dialog-max-height: 600px;
    }
  }
}

.tableAccounts {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.dialogContent {
  padding: 24px;
}

.dialogSection {
  margin-bottom: 16px;
}

.stateRow {
  display: flex;
  gap: 8px;
  align-items: center;
}

.accountRow {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 8px;

  :global(sinch-checkbox) {
    width: 250px;
    flex-shrink: 0;
  }
}

.multiSelect {
  min-width: 200px;
}

.inputWrapper {
  display: flex;
  align-items: center;
  gap: 8px;

  :global(sinch-input) {
    flex: 1;
  }
}

.iconInfo {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
