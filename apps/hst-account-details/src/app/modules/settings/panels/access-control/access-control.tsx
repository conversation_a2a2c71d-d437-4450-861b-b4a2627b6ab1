/* eslint-disable @nx/enforce-module-boundaries */
import { LOADING_STATUS } from 'apps/hst-account-details/src/app/constants';
import {
  fetchAccessControls,
  fetchAccessControlTypes,
  fetchValidActions,
  fetchValidProperties,
  updateAccessControls,
  resetUpdateStatus,
} from 'apps/hst-account-details/src/app/redux/settings/access-control/access-controls-slice';
import { ToastItem } from 'apps/hst-account-details/src/app/types';
import { RootState } from 'apps/hst-account-details/src/app/types/store';
import countryOptions from 'libs/nectary/src/lib/country-autocomplete/countries';
import {
  Button,
  Dialog,
  Input,
  MultiSelect,
  Select,
  Table,
  Text,
  Toast,
  Toggle,
} from 'nectary';
import { FC, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import styles from './access-control.module.less';

const formatPropertyKey = (key: string): string =>
  key
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, (str) => str.toUpperCase())
    .trim();

const LIST_TYPES = {
  BLACKLIST: 'BLACKLIST',
  WHITELIST: 'WHITELIST',
} as const;

const ALLOWED_TYPES = {
  NONE: 'NONE',
  ACCOUNT: 'ACCOUNT',
  REGEX: 'REGEX',
} as const;

type AccessControlProps = {
  vendorId: string;
  accountId: string;
  isEditable: boolean;
};

interface AccessControlType {
  id: string;
  name: string;
}

interface AccessControlAction {
  id: string;
  name: string;
}

interface AccessControlProperty {
  id: string;
  key: string;
  value: string;
  new: boolean;
}

type CountryPropertyKey = 'countries' | 'destinationCountries';

interface CountryProperty extends AccessControlProperty {
  key: CountryPropertyKey;
  value: string; // comma-separated country codes
}

interface ModalPropertyValues {
  countries?: string;
  destinationCountries?: string;
  [key: string]: string | undefined;
}

interface AccessControl {
  id: string;
  action: AccessControlAction;
  type: AccessControlType;
  accessControlProperties: AccessControlProperty[];
}

interface TableDataItem {
  id: string;
  name: string;
  isActive: boolean;
  action: AccessControlAction | undefined;
  type: {
    id: string;
    name: string;
    listType?: AccessControlProperty;
  };
  properties: AccessControlProperty[];
}

interface ValidAction {
  id: string;
  name: string;
}

interface ValidProperty {
  id: string;
  multiValue: boolean;
  new: boolean;
  propertyKey: string;
}

interface ModalState {
  isOpen: boolean;
  selectedType: AccessControlType | null;
  isEnabled: boolean;
  selectedAction: string;
  propertyValues: ModalPropertyValues;
  errors: Record<string, string>;
  originalValues: {
    isEnabled: boolean;
    selectedAction: string;
    propertyValues: ModalPropertyValues;
  } | null;
}

const isCountryProperty = (key: string): key is CountryPropertyKey =>
  key === 'countries' || key === 'destinationCountries';

export const AccessControl: FC<AccessControlProps> = ({
  vendorId,
  accountId,
  isEditable,
}) => {
  const dispatch = useDispatch();
  const {
    data,
    loading,
    types,
    typesLoading,
    validActions,
    validActionsLoading,
    validProperties,
    validPropertiesLoading,
    updateLoading,
  } = useSelector((state: RootState) => state.accessControls);

  const [toasts, setToasts] = useState<ToastItem[]>([]);
  const [modalState, setModalState] = useState<ModalState>({
    isOpen: false,
    selectedType: null,
    isEnabled: false,
    selectedAction: '',
    propertyValues: {},
    errors: {},
    originalValues: null,
  });
  const [isToggleDialogOpen, setIsToggleDialogOpen] = useState(false);
  const [toggleItem, setToggleItem] = useState<TableDataItem | null>(null);

  const addToast = (text: string, type: 'success' | 'error') => {
    const newToast = { id: Date.now().toString(), text, type };
    setToasts((prevToasts) => [...prevToasts, newToast]);
  };

  const activeControls = (data?.resources || []) as AccessControl[];
  const allTypes = (types?.resources || []) as AccessControlType[];

  const tableData = useMemo(
    () =>
      allTypes.map((type: AccessControlType) => {
        const activeControl = activeControls.find(
          (control: AccessControl) => control.type.id === type.id
        );

        return {
          id: type.id,
          name: type.name,
          isActive: !!activeControl,
          action: activeControl?.action,
          type: {
            ...activeControl?.type,
            listType: activeControl?.accessControlProperties?.find(
              (prop) => prop.key === 'listType'
            ),
          },
          properties: activeControl?.accessControlProperties || [],
        };
      }),
    [allTypes, activeControls]
  );

  useEffect(() => {
    if (vendorId && accountId) {
      dispatch(fetchAccessControls({ vendorId, accountId }));
      dispatch(fetchAccessControlTypes({ vendorId, accountId }));
    }
  }, [accountId, dispatch, vendorId]);

  const handleToggle = (record: TableDataItem) => {
    if (!isEditable) return;

    // Group properties by key and collect all values
    const propertyValues = record.properties.reduce<Record<string, string>>(
      (acc, prop) => {
        if (prop.key === 'countries' || prop.key === 'destinationCountries') {
          // For properties that can have multiple values, collect them in an array first
          const existingValues = acc[prop.key] ? acc[prop.key].split(',') : [];
          existingValues.push(prop.value);
          return {
            ...acc,
            [prop.key]: existingValues.join(','),
          };
        }
        // For single-value properties, just use the value directly
        return {
          ...acc,
          [prop.key]: prop.value,
        };
      },
      {}
    );

    setModalState({
      isOpen: true,
      selectedType: { id: record.id, name: record.name },
      isEnabled: record.isActive,
      selectedAction: record.action?.id || '',
      propertyValues,
      errors: {},
      originalValues: {
        isEnabled: record.isActive,
        selectedAction: record.action?.id || '',
        propertyValues,
      },
    });

    dispatch(
      fetchValidActions({
        vendorId,
        accountId,
        accessControlTypeId: record.id,
      })
    );
    dispatch(
      fetchValidProperties({
        vendorId,
        accountId,
        accessControlTypeId: record.id,
      })
    );
  };

  const handleCloseModal = () => {
    setModalState({
      isOpen: false,
      selectedType: null,
      isEnabled: false,
      selectedAction: '',
      propertyValues: {},
      errors: {},
      originalValues: null,
    });
  };

  const handleModalToggle = (checked: boolean) => {
    setModalState((prev) => ({
      ...prev,
      isEnabled: checked,
    }));
  };

  const handleActionChange = (value: string) => {
    setModalState((prev) => ({
      ...prev,
      selectedAction: value,
    }));
  };

  const handlePropertyChange = (key: string, value: string) => {
    setModalState((prev) => {
      const newPropertyValues = { ...prev.propertyValues };

      if (isCountryProperty(key)) {
        // Handle country-specific properties
        newPropertyValues[key] = value;
      } else {
        // Handle other properties
        newPropertyValues[key] = value;
      }

      return {
        ...prev,
        propertyValues: newPropertyValues,
      };
    });
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (modalState.isEnabled) {
      if (!modalState.selectedAction) {
        errors.action = 'Action is required';
      }

      validProperties?.resources?.forEach((property: ValidProperty) => {
        const value = modalState.propertyValues[property.propertyKey];
        const isSourceAddressRegex =
          property.propertyKey === 'sourceAddressRegex';
        const isAllowedRegex =
          modalState.propertyValues.allowed === ALLOWED_TYPES.REGEX;

        // Make sourceAddressRegex required only when allowed is REGEX
        if (isSourceAddressRegex && isAllowedRegex && !value) {
          errors[property.propertyKey] = 'Source Address Regex is required';
        } else if (
          !value &&
          !isSourceAddressRegex && // Skip validation for sourceAddressRegex when not in REGEX mode
          !['destinationCountries'].includes(property.propertyKey)
        ) {
          errors[property.propertyKey] = `${formatPropertyKey(
            property.propertyKey
          )} is required`;
        }
      });
    }

    setModalState((prev) => ({ ...prev, errors }));
    return Object.keys(errors).length === 0;
  };

  const hasChanges = (): boolean => {
    if (!modalState.originalValues) return false;

    if (modalState.isEnabled !== modalState.originalValues.isEnabled)
      return true;
    if (modalState.selectedAction !== modalState.originalValues.selectedAction)
      return true;

    const originalProps = modalState.originalValues.propertyValues;
    return Object.entries(modalState.propertyValues).some(
      ([key, value]) => value !== originalProps[key]
    );
  };

  const getPropertyInput = (property: ValidProperty) => {
    const propertyValue = modalState.propertyValues[property.propertyKey] || '';
    const error = modalState.errors[property.propertyKey];

    if (property.propertyKey === 'listType') {
      return (
        <div>
          <Select
            value={propertyValue}
            onSelect={(selectedValue: string) =>
              handlePropertyChange(property.propertyKey, selectedValue)
            }
            placeholder="Select list type"
            customStyles={{ width: '100%' }}
            options={[
              { value: LIST_TYPES.BLACKLIST, label: 'Blacklist' },
              { value: LIST_TYPES.WHITELIST, label: 'Whitelist' },
            ]}
          />
          {error && (
            <Text type="s" style={{ color: 'red' }}>
              {error}
            </Text>
          )}
        </div>
      );
    }

    if (property.propertyKey === 'allowed') {
      return (
        <div>
          <Select
            value={propertyValue}
            onSelect={(selectedValue: string) =>
              handlePropertyChange(property.propertyKey, selectedValue)
            }
            placeholder="Select allowed type"
            customStyles={{ width: '100%' }}
            options={[
              { value: ALLOWED_TYPES.NONE, label: 'None' },
              { value: ALLOWED_TYPES.ACCOUNT, label: 'Account' },
              { value: ALLOWED_TYPES.REGEX, label: 'Regex' },
            ]}
          />
          {error && (
            <Text type="s" style={{ color: 'red' }}>
              {error}
            </Text>
          )}
        </div>
      );
    }

    if (['countries', 'destinationCountries'].includes(property.propertyKey)) {
      const currentSelectedCountries = propertyValue
        ? propertyValue
            .split(',')
            .filter(Boolean)
            .map((code) => code.trim())
        : [];

      return (
        <div>
          <MultiSelect
            value={currentSelectedCountries}
            onSelect={(selectedCountries: string[]) => {
              const countriesString = selectedCountries.join(',');
              handlePropertyChange(property.propertyKey, countriesString);
            }}
            customStyles={{ width: '100%' }}
            placeholder={
              property.propertyKey === 'destinationCountries'
                ? 'Search and select destination countries'
                : 'Search and select countries'
            }
            options={countryOptions}
            searchable
          />
          {error && (
            <Text type="s" style={{ color: 'red' }}>
              {error}
            </Text>
          )}
        </div>
      );
    }

    if (['prefixes', 'sourceAddressRegex'].includes(property.propertyKey)) {
      const isAllowedRegex =
        modalState.propertyValues.allowed === ALLOWED_TYPES.REGEX;
      const isDisabled =
        property.propertyKey === 'sourceAddressRegex' && !isAllowedRegex;

      return (
        <div>
          <Input
            value={propertyValue}
            onChange={(e: string) =>
              handlePropertyChange(property.propertyKey, e)
            }
            placeholder={`Enter ${formatText(property.propertyKey)}`}
            fieldStyles={{ width: '100%' }}
            disabled={isDisabled}
          />
          {error && (
            <Text type="s" style={{ color: 'red' }}>
              {error}
            </Text>
          )}
        </div>
      );
    }

    return (
      <div>
        <Select
          value={propertyValue}
          onSelect={(selectedValue: string) =>
            handlePropertyChange(property.propertyKey, selectedValue)
          }
          placeholder={`Select ${formatText(property.propertyKey)}`}
          customStyles={{ width: '100%' }}
          options={[
            {
              value: property.propertyKey,
              label: formatText(property.propertyKey),
            },
          ]}
        />
        {error && (
          <Text type="s" style={{ color: 'red' }}>
            {error}
          </Text>
        )}
      </div>
    );
  };

  const handleSave = () => {
    if (!modalState.selectedType) return;

    if (!validateForm()) return;

    if (!hasChanges()) {
      addToast('No changes to save', 'error');
      return;
    }

    // Create a map of all current access controls, preserving existing controls
    const currentAccessControls = tableData.reduce<Record<string, any>>(
      (acc, item) => {
        if (item.isActive && item.id !== modalState.selectedType?.id) {
          const activeControl = activeControls.find(
            (control) => control.type.id === item.id
          );
          acc[item.id] = {
            ...(activeControl?.id ? { id: activeControl.id } : {}),
            enabled: true,
            action: item.action?.id || '',
            properties: item.properties.reduce<Record<string, string[]>>(
              (propAcc, prop) => {
                const result = { ...propAcc };
                if (prop.key && prop.value) {
                  // For multi-value properties like countries, we need to handle them differently
                  if (
                    prop.key === 'countries' ||
                    prop.key === 'destinationCountries'
                  ) {
                    // If the key already exists, add to the array
                    result[prop.key] = result[prop.key]
                      ? [...result[prop.key], prop.value]
                      : [prop.value];
                  } else {
                    // For single-value properties, just set the value
                    result[prop.key] = [prop.value];
                  }
                }
                return result;
              },
              {}
            ),
          };
        }
        return acc;
      },
      {}
    );

    const activeControl = activeControls.find(
      (control) => control.type.id === modalState.selectedType?.id
    );

    // Only update the specific access control being modified
    currentAccessControls[modalState.selectedType.id] = {
      ...(activeControl?.id ? { id: activeControl.id } : {}),
      enabled: modalState.isEnabled,
      action: modalState.isEnabled ? modalState.selectedAction : null,
      properties: modalState.isEnabled
        ? Object.entries(modalState.propertyValues).reduce<
            Record<string, string[]>
          >((acc, [key, value]) => {
            const result = { ...acc };
            if (!value) return result;
            if (key === 'countries' || key === 'destinationCountries') {
              // Split the comma-separated values and ensure they are trimmed
              const values = value
                .split(',')
                .map((code) => code.trim())
                .filter(Boolean);
              if (values.length > 0) {
                result[key] = values;
              }
            } else {
              result[key] = [value];
            }
            return result;
          }, {})
        : {},
    };

    dispatch(
      updateAccessControls({
        vendorId,
        accountId,
        data: {
          accessControls: currentAccessControls,
        },
      })
    );
  };

  useEffect(() => {
    let isSubscribed = true;

    if (updateLoading === LOADING_STATUS.SUCCEEDED && isSubscribed) {
      addToast('Access control updated successfully', 'success');
      handleCloseModal();
      setIsToggleDialogOpen(false);
      setToggleItem(null);
      dispatch(fetchAccessControls({ vendorId, accountId }));
      dispatch(fetchAccessControlTypes({ vendorId, accountId }));
      dispatch(resetUpdateStatus());
    } else if (updateLoading === LOADING_STATUS.FAILED && isSubscribed) {
      addToast('Failed to update access control', 'error');
      dispatch(resetUpdateStatus());
    }

    return () => {
      isSubscribed = false;
    };
  }, [accountId, dispatch, updateLoading, vendorId]);

  const formatText = (text: string): string => {
    if (!text) return '';

    const specialCases: Record<string, string> = {
      listtype: 'List Type',
      sourceaddressregex: 'Source Address Regex',
      destinationcountries: 'Destination Countries',
      prefixes: 'Prefixes',
      allowed: 'Allowed',
      countries: 'Countries',
    };

    const lowerText = text.toLowerCase();
    if (specialCases[lowerText]) {
      return specialCases[lowerText];
    }

    return text
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  const formatPropertyValue = (key: string, value: string): string => {
    if (!value) return '';

    if (key.toLowerCase() === 'prefixes') {
      return value;
    }

    if (key === 'countries') {
      return value
        .split(',')
        .map((country) => country.trim().toUpperCase())
        .join(', ');
    }

    if (key === 'destinationCountries') {
      return value
        .split(',')
        .map((country) => country.trim().toUpperCase())
        .join(', ');
    }

    if (key.toLowerCase() === 'listtype') {
      return value.toUpperCase() === 'WHITELIST' ? 'Whitelist' : 'Blacklist';
    }

    if (key.toLowerCase() === 'allowed') {
      const allowedMap: Record<string, string> = {
        NONE: 'None',
        ACCOUNT: 'Account',
        REGEX: 'Regex',
      };
      return allowedMap[value.toUpperCase()] || value;
    }

    return formatText(value);
  };

  const handleToggleClick = (record: TableDataItem) => {
    if (!record.isActive) {
      setToggleItem(record);
      setIsToggleDialogOpen(true);
    } else {
      setToggleItem(record);
      setIsToggleDialogOpen(true);
    }
  };

  const handleToggleConfirm = async () => {
    if (!toggleItem) return;

    if (!toggleItem.isActive) {
      // If enabling, open the edit modal instead
      handleToggle(toggleItem);
      setIsToggleDialogOpen(false);
      setToggleItem(null);
      return;
    }

    // Create a map of all current access controls
    const currentAccessControls = tableData.reduce<Record<string, any>>(
      (acc, item) => {
        if (item.isActive) {
          const activeControl = activeControls.find(
            (control) => control.type.id === item.id
          );
          acc[item.id] = {
            ...(activeControl?.id ? { id: activeControl.id } : {}),
            enabled: item.id === toggleItem.id ? false : item.isActive,
            action: item.action?.id || '',
            properties: item.properties.reduce<Record<string, string[]>>(
              (propAcc, prop) => {
                const newAcc = { ...propAcc };
                if (prop.key && prop.value) {
                  if (prop.key === 'countries' || prop.key === 'destinationCountries') {
                    newAcc[prop.key] = newAcc[prop.key]
                      ? [...newAcc[prop.key], prop.value]
                      : [prop.value];
                  } else {
                    newAcc[prop.key] = [prop.value];
                  }
                }
                return newAcc;
              },
              {}
            ),
          };
        }
        return acc;
      },
      {}
    );

    dispatch(
      updateAccessControls({
        vendorId,
        accountId,
        data: {
          accessControls: currentAccessControls,
        },
      })
    );
    setIsToggleDialogOpen(false);
    setToggleItem(null);
  };

  const columns = [
    {
      title: 'Name',
      index: 'name',
      render: (record: TableDataItem) => (
        <Text>{formatText(record?.name || '')}</Text>
      ),
    },
    {
      title: 'Status',
      index: 'isActive',
      render: (record: TableDataItem) => (
        <button
          onClick={() => handleToggleClick(record)}
          style={{
            cursor: isEditable ? 'pointer' : 'default',
            background: 'none',
            border: 'none',
            padding: 0,
          }}
          disabled={!isEditable}
          aria-label={`Configure ${record.name} access control`}
        >
          <Toggle
            checked={!!record?.isActive}
            onClick={(e: React.MouseEvent) => e.preventDefault()}
            disabled={!isEditable}
          />
        </button>
      ),
    },
    {
      title: 'Action',
      index: 'action',
      render: (record: TableDataItem) => (
        <Text>{formatText(record?.action?.name || '')}</Text>
      ),
    },
    {
      title: 'Type',
      index: 'type',
      render: (record: TableDataItem) => (
        <Text>{formatText(record?.type?.listType?.value || '')}</Text>
      ),
    },
    {
      title: 'Details',
      render: (record: TableDataItem) => {
        // Group properties by key
        const groupedProperties = record?.properties?.reduce(
          (acc: Record<string, string[]>, prop) => {
            if (!prop.key || !prop.value) return acc;
            if (!acc[prop.key]) {
              acc[prop.key] = [];
            }
            acc[prop.key].push(prop.value);
            return acc;
          },
          {}
        );

        // Convert grouped properties to formatted strings
        const formattedDetails = Object.entries(groupedProperties || {})
          .map(([key, values]) => {
            if (key === 'countries') {
              return `Countries: ${values.join(', ')}`;
            }
            if (key === 'destinationCountries') {
              return `Destination Countries: ${values.join(', ')}`;
            }
            return `${formatText(key)}: ${values
              .map((value) => formatPropertyValue(key, value))
              .join(', ')}`;
          })
          .join(', ');

        return <Text>{formattedDetails}</Text>;
      },
    },
    {
      title: '',
      index: 'actions',
      align: 'left',
      render: (record: TableDataItem) => (
        <Button
          label="Edit"
          type="secondary"
          onClick={() => handleToggle(record)}
          disabled={!isEditable || !record.isActive}
        />
      ),
    },
  ];

  const isModalLoading =
    validActionsLoading === LOADING_STATUS.LOADING ||
    validPropertiesLoading === LOADING_STATUS.LOADING;

  useEffect(() => {
    if (validActions?.resources?.length === 1) {
      const singleAction = validActions.resources[0];
      setModalState((prev) => ({
        ...prev,
        selectedAction: singleAction.id,
      }));
    }
  }, [validActions]);

  return (
    <div className={styles.container}>
      <Table
        hasCheckbox={false}
        tableColumns={columns}
        tableData={tableData}
        loading={
          loading === LOADING_STATUS.LOADING ||
          typesLoading === LOADING_STATUS.LOADING
        }
      />
      <Dialog
        isOpen={modalState.isOpen}
        onClose={handleCloseModal}
        caption={`Configure ${formatText(
          modalState.selectedType?.name || ''
        )} Access Control`}
      >
        <div className={styles.modalContent}>
          {isModalLoading ? (
            <Text>Loading...</Text>
          ) : (
            <>
              <div className={styles.section}>
                <div className={styles.toggleSection}>
                  <Text type="m" style={{ fontWeight: 500 }}>
                    Enable
                  </Text>
                  <Toggle
                    checked={modalState.isEnabled}
                    onChange={handleModalToggle}
                  />
                </div>
              </div>
              {modalState.isEnabled && (
                <>
                  <div className={styles.section}>
                    <Text
                      type="m"
                      style={{
                        fontWeight: 500,
                        display: 'flex',
                        alignItems: 'center',
                      }}
                    >
                      Action
                      <Text
                        type="s"
                        style={{
                          color: 'var(--sinch-sys-color-text-disabled)',
                          marginLeft: '4px',
                          fontWeight: 'normal',
                        }}
                      >
                        (Required)
                      </Text>
                    </Text>
                    <Select
                      value={modalState.selectedAction}
                      onSelect={handleActionChange}
                      placeholder="Select an action"
                      customStyles={{ width: '100%' }}
                      options={(validActions?.resources || []).map(
                        (action: ValidAction) => ({
                          value: action.id,
                          label: formatText(action.name),
                        })
                      )}
                      disabled={validActions?.resources?.length === 1}
                    />
                    {modalState.errors.action && (
                      <Text type="s" style={{ color: 'red' }}>
                        {modalState.errors.action}
                      </Text>
                    )}
                  </div>
                  {validProperties?.resources && validProperties.resources.length > 0 && (
                    <div className={styles.section}>
                      <Text type="m" style={{ fontWeight: 500 }}>
                        Properties
                      </Text>
                      {(validProperties?.resources || []).map(
                        (property: ValidProperty) => {
                          const isRequired = ![
                            'sourceAddressRegex',
                            'destinationCountries',
                          ].includes(property.propertyKey);
                          const isSourceAddressRegex =
                            property.propertyKey === 'sourceAddressRegex';
                          const isAllowedRegex =
                            modalState.propertyValues.allowed ===
                            ALLOWED_TYPES.REGEX;
                          const showRequired =
                            isRequired ||
                            (isSourceAddressRegex && isAllowedRegex);

                          return (
                            <div
                              key={property.id}
                              className={styles.propertyItem}
                            >
                              <Text
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                }}
                              >
                                {formatPropertyKey(property.propertyKey)}
                                {showRequired && (
                                  <Text
                                    type="s"
                                    style={{
                                      color:
                                        'var(--sinch-sys-color-text-disabled)',
                                      marginLeft: '4px',
                                      fontWeight: 'normal',
                                    }}
                                  >
                                    (Required)
                                  </Text>
                                )}
                              </Text>
                              {getPropertyInput(property)}
                            </div>
                          );
                        }
                      )}
                    </div>
                  )}
                </>
              )}
              <div className={styles.actions}>
                <Button
                  label="Cancel"
                  type="secondary"
                  onClick={handleCloseModal}
                />
                <Button
                  label="Save"
                  type="primary"
                  onClick={handleSave}
                  loading={updateLoading === LOADING_STATUS.LOADING}
                  disabled={!hasChanges()}
                />
              </div>
            </>
          )}
        </div>
      </Dialog>
      <Dialog
        caption={`${
          toggleItem?.isActive ? 'Disable' : 'Enable'
        } Access Control - ${toggleItem?.name || ''}`}
        isOpen={isToggleDialogOpen}
        onClose={() => {
          setIsToggleDialogOpen(false);
          setToggleItem(null);
        }}
      >
        <div className={styles.modalContent}>
          <div className={styles.section}>
            <Text>
              Are you sure you want to{' '}
              {toggleItem?.isActive ? 'disable' : 'enable'} access control
              settings for {toggleItem?.name}?
              {!toggleItem?.isActive && (
                <Text
                  type="s"
                  style={{
                    marginTop: '8px',
                    color: 'var(--sinch-sys-color-text-disabled)',
                  }}
                >
                  You will be redirected to configure the settings after
                  confirming.
                </Text>
              )}
            </Text>
          </div>
          <div className={styles.actions}>
            <Button
              type="secondary"
              label="Cancel"
              onClick={() => {
                setIsToggleDialogOpen(false);
                setToggleItem(null);
              }}
            />
            <Button
              label="Confirm"
              onClick={handleToggleConfirm}
              loading={updateLoading === LOADING_STATUS.LOADING}
            />
          </div>
        </div>
      </Dialog>
      <Toast toasts={toasts} setToasts={setToasts} />
    </div>
  );
};

export default AccessControl;
