.container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 24px;
}

.modalContent {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px;
}

.section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.toggleSection {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.propertyItem {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 16px;
}

.actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 8px;
}
