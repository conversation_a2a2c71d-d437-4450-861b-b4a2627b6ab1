/* eslint-disable @nx/enforce-module-boundaries */
/* eslint-disable @typescript-eslint/no-shadow */
import '@nectary/assets/icons/edit';
import '@nectary/assets/icons/info';
import '@nectary/components/chip';
import '@nectary/components/skeleton';
import '@nectary/components/skeleton-item';
import { ToastItem } from 'apps/hst-account-details/src/app/types';
import { AccountDetailsType } from 'apps/hst-account-details/src/app/types/settings';
import { RootState } from 'apps/hst-account-details/src/app/types/store';
import { Text, Toast, Toggle, Tooltip } from 'nectary';
import { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import EditableField, {
  FIELD_TYPE,
  FieldType,
} from '../../../../components/editable-field/editable-field';
import { LOADING_STATUS } from '../../../../constants';
import {
  fetchAccountDetails,
  updateAccountDetails,
} from '../../../../redux/settings/account-details/account-details-slice';
import { updateDeliveryReceipts } from '../../../../redux/settings/delivery-receipts/delivery-receipts-slice';
import styles from './account-details.module.less';

interface AccountDetailsProps {
  vendorId: string;
  accountId: string;
  isEditable: boolean;
  isBasicEditable: boolean;
  isAdvancedEditable: boolean;
  isFinanceEditable: boolean;
}

interface EditableFieldState {
  [key: string]: boolean;
}

const INITIAL_EDITABLE_STATE: EditableFieldState = {
  accountId: false,
  accountName: false,
  operatingCountry: false,
  status: false,
  timezone: false,
  defaultTrafficClass: false,
  delegatedCompliance: false,
  ignoreClientAccount: false,
  saveDeliveryReportsInREST: false,
  saveRepliesInREST: false,
  externalContentSupported: false,
  type: false,
  billingType: false,
};

const INITIAL_ACCOUNT_STATE: AccountDetailsType = {
  accountId: '',
  accountName: '',
  operatingCountry: '',
  status: '',
  timezone: '',
  defaultTrafficClass: '',
  delegatedCompliance: false,
  ignoreClientSendingAccount: false,
  saveDeliveryReportsInREST: false,
  saveRepliesInREST: false,
  externalContentSupported: false,
  type: '',
  billingType: '',
  deliveryReceiptEnabled: false,
};

function AccountDetails({
  vendorId,
  accountId,
  isEditable,
  isBasicEditable,
  isAdvancedEditable,
  isFinanceEditable,
}: AccountDetailsProps) {
  const dispatch = useDispatch();
  const rootState: RootState = useSelector((state) => state || {});
  const { accountSettingsDetails, deliveryReceipts } = rootState;
  const { data, loading, updateLoading } = accountSettingsDetails || {};
  const { loading: deliveryReceiptsLoading } = deliveryReceipts || {};
  const [toasts, setToasts] = useState<ToastItem[]>([]);
  const [showEdit, setShowEdit] = useState(true);
  const [isEditingValue, setIsEditingValue] = useState<AccountDetailsType>(
    INITIAL_ACCOUNT_STATE
  );
  const [isEditing, setIsEditing] = useState<EditableFieldState>(
    INITIAL_EDITABLE_STATE
  );

  const addToast = useCallback(
    (id: string, text: string, type: 'success' | 'error'): void => {
      setToasts((prev) => [...prev, { id, text, type }]);
    },
    []
  );

  const handleChangeValue = useCallback(
    (type: string, value: string | boolean): void => {
      setIsEditingValue((prev) => ({
        ...prev,
        [type]: typeof value === 'boolean' ? value : String(value),
      }));
    },
    []
  );

  const handleEdit = useCallback((type: string, isEdit: boolean): void => {
    setIsEditing((prev) => ({ ...prev, [type]: isEdit }));
    setShowEdit(!isEdit);
  }, []);

  const handleUpdateDetails = useCallback(
    (updatedData: AccountDetailsType): void => {
      dispatch(
        updateAccountDetails({
          vendorId,
          accountId,
          data: updatedData,
        })
      );
    },
    [dispatch, vendorId, accountId]
  );

  const handleToggleUpdate = useCallback(
    (
      field: keyof AccountDetailsType,
      value: boolean,
      updateAction: typeof updateAccountDetails | typeof updateDeliveryReceipts,
      requiredPermission: boolean
    ): void => {
      if (!requiredPermission) return;

      setIsEditingValue((prev) => ({
        ...prev,
        [field]: value,
      }));

      if (updateAction === updateDeliveryReceipts) {
        dispatch(
          updateAction({
            vendorId,
            accountId,
            isEnabled: value,
          })
        );
      } else if (updateAction === updateAccountDetails) {
        dispatch(
          updateAction({
            vendorId,
            accountId,
            data: { ...isEditingValue, [field]: value },
          })
        );
      }
    },
    [dispatch, isEditingValue, vendorId, accountId]
  );

  type ToggleFieldProps = {
    title: string;
    field: keyof AccountDetailsType;
    tooltipText?: string;
    updateAction: typeof updateAccountDetails | typeof updateDeliveryReceipts;
    isEditable?: boolean;
  };

  const renderToggleField = useCallback(
    ({ title, field, tooltipText, updateAction, isEditable: isToggleEditable }: ToggleFieldProps) => (
      <div className={styles.row}>
        <div className={styles.left}>
          <div className={styles.row}>
            <Text type="s" inline>
              {title}
            </Text>
            {tooltipText && (
              <Tooltip type="fast" orientation="top" text={tooltipText}>
                <div className={styles.iconInfo}>
                  <sinch-icon-info />
                </div>
              </Tooltip>
            )}
          </div>
        </div>
        <div className={styles.right}>
          <Toggle
            disabled={
              !isToggleEditable || updateLoading === LOADING_STATUS.LOADING
            }
            checked={Boolean(isEditingValue[field])}
            loading={
              updateAction === updateDeliveryReceipts
                ? deliveryReceiptsLoading === LOADING_STATUS.LOADING
                : updateLoading === LOADING_STATUS.LOADING
            }
            onChange={() =>
              handleToggleUpdate(field, !isEditingValue[field], updateAction, Boolean(isToggleEditable))
            }
          />
        </div>
      </div>
    ),
    [
      updateLoading,
      deliveryReceiptsLoading,
      isEditingValue,
      handleToggleUpdate,
    ]
  );

  const renderEditableField = useCallback(
    (type: string, title: string, value: string, canEdit = true) => (
      <div className={styles.row}>
        <div className={styles.left}>
          <Text type="s" inline>
            {title}
          </Text>
        </div>
        <div className={styles.right}>
          {isEditing[type] ? (
            <EditableField
              type={type as FieldType}
              defaultValue={value}
              errorText=""
              loading={updateLoading === LOADING_STATUS.LOADING}
              handleCancel={handleEdit}
              handleChangeValue={handleChangeValue}
              handleOk={() => handleUpdateDetails(isEditingValue)}
            />
          ) : (
            <>
              <Text type="s" inline emphasized>
                {value || '-'}
              </Text>
              {(showEdit &&
                canEdit &&
                type !== FIELD_TYPE.ACCOUNT_ID &&
                loading === LOADING_STATUS.SUCCEEDED) && (
                  <button
                    className={styles.iconEdit}
                    onClick={() => handleEdit(type, true)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        handleEdit(type, true);
                      }
                    }}
                    aria-label="Edit field"
                  >
                    <sinch-icon-edit />
                  </button>
                )}
            </>
          )}
        </div>
      </div>
    ),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [isEditing, updateLoading, isEditingValue, showEdit, loading]
  );

  useEffect(() => {
    dispatch(fetchAccountDetails({ vendorId, accountId }));
  }, [dispatch, vendorId, accountId]);

  useEffect(() => {
    if (data) {
      setIsEditingValue({
        ...INITIAL_ACCOUNT_STATE,
        ...data,
        delegatedCompliance: Boolean(data.delegatedCompliance),
        ignoreClientSendingAccount: Boolean(data.ignoreClientSendingAccount),
        saveDeliveryReportsInREST: Boolean(data.saveDeliveryReportsInREST),
        saveRepliesInREST: Boolean(data.saveRepliesInREST),
        externalContentSupported: Boolean(data.externalContentSupported),
        deliveryReceiptEnabled: Boolean(data.deliveryReceiptEnabled),
      });
    }
  }, [data]);

  useEffect(() => {
    const handleUpdateStatus = (status: string, loading: string) => {
      if (
        loading === LOADING_STATUS.SUCCEEDED ||
        loading === LOADING_STATUS.FAILED
      ) {
        const isSuccess = loading === LOADING_STATUS.SUCCEEDED;
        addToast(
          accountId,
          `${isSuccess ? 'Successfully' : 'Failed to'} updated ${status}`,
          isSuccess ? 'success' : 'error'
        );

        if (status === 'account details') {
          setIsEditing(INITIAL_EDITABLE_STATE);
          setShowEdit(true);
        }

        dispatch(fetchAccountDetails({ vendorId, accountId }));
      }
    };

    handleUpdateStatus('account details', updateLoading);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [updateLoading]);

  useEffect(() => {
    const handleUpdateStatus = (status: string, loading: string) => {
      if (
        loading === LOADING_STATUS.SUCCEEDED ||
        loading === LOADING_STATUS.FAILED
      ) {
        const isSuccess = loading === LOADING_STATUS.SUCCEEDED;
        addToast(
          accountId,
          `${isSuccess ? 'Successfully' : 'Failed to'} updated ${status}`,
          isSuccess ? 'success' : 'error'
        );

        if (status === 'account details') {
          setIsEditing(INITIAL_EDITABLE_STATE);
          setShowEdit(true);
        }

        dispatch(fetchAccountDetails({ vendorId, accountId }));
      }
    };

    handleUpdateStatus('delivery receipts', deliveryReceiptsLoading);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [deliveryReceiptsLoading]);

  if (!data || !deliveryReceipts) {
    return (
      <div className={styles.title}>
        <Text type="l" inline emphasized>
          Edit Account Details
        </Text>
      </div>
    );
  }

  return (
    <div>
      <div className={styles.title}>
        <Text type="l" inline emphasized>
          Edit Account Details
        </Text>
      </div>
      <div className={styles.settingsColumns}>
        <div className={styles.settingsColumn}>
          {renderEditableField(
            FIELD_TYPE.NAME,
            'Account Name',
            data.accountName,
            isBasicEditable
          )}
          {renderEditableField(
            FIELD_TYPE.ACCOUNT_ID,
            'Account ID',
            data.accountId,
            false
          )}
          {renderEditableField(
            FIELD_TYPE.COUNTRY,
            'Country',
            data.operatingCountry,
            isBasicEditable
          )}
          {renderEditableField(
            FIELD_TYPE.TIMEZONE,
            'Timezone',
            data.timezone,
            isBasicEditable
          )}
          {renderEditableField(
            FIELD_TYPE.DEFAULT_TRAFFIC_CLASS,
            'Default Traffic Class',
            data.defaultTrafficClass,
            isAdvancedEditable
          )}
          {renderEditableField(
            FIELD_TYPE.STATUS,
            'Status',
            data.status,
            isAdvancedEditable
          )}
          {renderEditableField(
            FIELD_TYPE.TYPE,
            'Type',
            data.type,
            isFinanceEditable
          )}
          {renderEditableField(
            FIELD_TYPE.BILLING_TYPE,
            'Billing Type',
            data.billingType,
            isFinanceEditable
          )}
        </div>
        <div className={styles.settingsColumn}>
          {renderToggleField({
            title: 'Delivery receipts',
            field: 'deliveryReceiptEnabled',
            tooltipText:
              'Activate delivery receipts for both Hub broadcasts and REST API messages',
            updateAction: updateDeliveryReceipts,
            isEditable: isBasicEditable,
          })}
          {renderToggleField({
            title: 'Delegated Compliance',
            field: 'delegatedCompliance',
            tooltipText:
              'Customer agreement and legal sign-off are required before enabling this.',
            updateAction: updateAccountDetails,
            isEditable: isAdvancedEditable,
          })}
          {renderToggleField({
            title: 'Ignore Client Account',
            field: 'ignoreClientSendingAccount',
            tooltipText:
              'When the API is processing an MO or DR, by default it will use settings for the account associated with the credentials for the request (client account) that sent the message if it exists in preference of the account associated with the message (the sending account). This is common when an API key or SMPP bind is used to send messages for several different accounts. Enabling the Ignore Client Account feature will override this default behaviour and use the sending account settings. Account settings used by the API for MOs and DRs include HTTP Notifications, SMPP bind name to return the MOs or DRs, and which types of DRs to return.',
            updateAction: updateAccountDetails,
            isEditable: isAdvancedEditable,
          })}
          {renderToggleField({
            title: 'Save DRs in REST',
            field: 'saveDeliveryReportsInREST',
            tooltipText:
              'Enable this to allow REST check reports functionality. No impact to ASIT or SMPP.',
            updateAction: updateAccountDetails,
            isEditable: isAdvancedEditable,
          })}
          {renderToggleField({
            title: 'Save Replies in REST',
            field: 'saveRepliesInREST',
            tooltipText:
              'Enable this to allow REST check replies functionality. No impact to ASIT or SMPP.',
            updateAction: updateAccountDetails,
            isEditable: isAdvancedEditable,
          })}
          {renderToggleField({
            title: 'External Content Supported',
            field: 'externalContentSupported',
            tooltipText:
              'Enable this to allow External Content Retrieval (For e.g. MMS) for the account.',
            updateAction: updateAccountDetails,
            isEditable: isAdvancedEditable,
          })}
        </div>
      </div>
      <Toast toasts={toasts} setToasts={setToasts} />
    </div>
  );
}

export default AccountDetails;
