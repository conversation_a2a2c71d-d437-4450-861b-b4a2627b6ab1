import { useState } from 'react';
import { useDispatch } from 'react-redux';

import { Dialog, Input, Select, RadioGroup, Button } from 'nectary';

import { updateUsageTracking, createUsageTracking } from '../../../../../redux/settings/usage-tracking/usage-tracking-slice'

import TimezoneAutoComplete from '../../../components/timezone-autocomplete'

import styles from './utt-form-modal.module.less';
import { UsageTrackingItem } from '../../../../../types/settings';

const FIXED = 'FIXED'
const ROLLING = 'ROLLING'

const PERIODS = [
  {
    value: 'HOURLY',
    label: 'Hourly',
  },
  {
    value: 'DAILY',
    label: 'Daily',
  },
  {
    value: 'WEEKLY',
    label: 'Weekly',
  },
  {
    value: 'MONTHLY',
    label: 'Monthly',
  },
];

const ACTIONS = [
  {
    value: 'DISCARD',
    label: 'Discard',
  },
];

const TIMEZONE_TYPES = [
  {
    value: 'FIXED',
    label: 'Fixed',
  },
  {
    value: 'ROLLING',
    label: 'Rolling',
  },
];

const UTTFormModal = ({
  accountId,
  vendorId,
  isVisible,
  setIsVisible,
  formValues,
  setFormValues
}: {
  accountId: string,
  vendorId: string,
  isVisible: boolean,
  setIsVisible: (isVisible: boolean) => void,
  formValues: UsageTrackingItem | null,
  setFormValues: (formValues: UsageTrackingItem | null) => void,
}) => {
  const dispatch = useDispatch()
  const [validating, setValidating] = useState(false)
  const { action, id, label, period, threshold, timezone, timezoneType } = formValues || {
    action: 'DISCARD',
    alerts: [],
    id: '',
    label: '',
    period: 'HOURLY',
    threshold: '',
    timezone: '',
    timezoneType: 'FIXED'
  };

  const onChangeLabel = (val: string) => {
    if (!formValues) return
    setFormValues({
      ...formValues,
      label: val,
    })
  };

  const onChangeThreshold = (val: string) => {
    if (!formValues) return
    setFormValues({
      ...formValues,
      threshold: val,
    })
  };

  const handleSelectPeriod = (val: string) => {
    if (!formValues) return
    setFormValues({
      ...formValues,
      period: val,
    })
  };

  const handleSelectAction = (val: string) => {
    if (!formValues) return
    setFormValues({
      ...formValues,
      action: val,
    })
  };

  const handleSelectTimezoneType = (val: string) => {
    if (!formValues) return
    setFormValues({
      ...formValues,
      timezoneType: val,
      timezone: val === ROLLING ? '' : timezone
    })
  };

  const handleSelectTimezone = (timezoneOption: { label: string, value: string }) => {
    if (!formValues) return
    setFormValues({
      ...formValues,
      timezone: timezoneOption.value,
    })
  };

  const handleClose = () => {
    setIsVisible(false)
  }

  const handleSubmit = () => {
    setValidating(true);
    if (!label || !threshold || (timezoneType === FIXED && !timezone) || !formValues) {
      return null
    }

    if (id) {
      return dispatch(updateUsageTracking({
        accountId,
        vendorId,
        body: formValues,
      }))
    }
    return dispatch(createUsageTracking({
      accountId,
      vendorId,
      body: formValues,
    }))
  }

  return (
    <Dialog
      isOpen={isVisible}
      caption="Usage Tracking Threshold"
      onClose={handleClose}
    >
      <form className={styles.uttForm}>
        <div className={styles.formItem}>
          <Input
            label="Label"
            defaultValue={label}
            value={label}
            onChange={onChangeLabel}
            testId="label"
            errorText={validating && !label ? 'Label is required' : ''}
          />
        </div>
        <div className={styles.formItem}>
          <Input
            label="Threshold"
            defaultValue={threshold}
            value={threshold}
            onChange={onChangeThreshold}
            testId="threshold"
            errorText={validating && !threshold ? 'Threshold is required' : ''}
          />
        </div>
        <div className={styles.formItem}>
          <Select
            label="Period"
            options={PERIODS}
            value={period}
            defaultValue={period}
            onSelect={handleSelectPeriod}
            testId="period-select"
          />
        </div>
        <div className={styles.formItem}>
          <Select
            label="Action"
            options={ACTIONS}
            value={action}
            defaultValue={action}
            onSelect={handleSelectAction}
            testId="action-select"
          />
        </div>
        <div className={styles.formItem}>
          <RadioGroup
            label="Type:"
            value={timezoneType}
            options={TIMEZONE_TYPES}
            onSelect={handleSelectTimezoneType}
            testId="timezone-type-select"
          />
        </div>
        {timezoneType === FIXED && (
          <div className={styles.formItem}>
            <TimezoneAutoComplete
              value={timezone}
              onChange={handleSelectTimezone}
              errorText={validating && timezoneType === FIXED && !timezone ? 'Timezone is required' : ''}
            />
          </div>
        )}

        <div className={styles.btnGroup}>
          <Button label="Submit" onClick={handleSubmit} />
          <Button label="Cancel" onClick={handleClose} />
        </div>
      </form>
    </Dialog>
  );
};

export default UTTFormModal;
