/* eslint-disable @nx/enforce-module-boundaries */
/* eslint-disable @typescript-eslint/no-shadow */
import { useDispatch, useSelector } from 'react-redux';
import { useEffect, useState } from 'react';
import _isEmpty from 'lodash/isEmpty';
import '@nectary/components/chip'
import '@nectary/components/skeleton';
import '@nectary/components/skeleton-item';
import '@nectary/assets/icons/add';

import { Text, Button, Table, Toast } from 'nectary';
import { RootState } from 'apps/hst-account-details/src/app/types/store';

import { deleteUsageTracking, fetchUsageTracking } from '../../../../redux/settings/usage-tracking/usage-tracking-slice';
import { LOADING_STATUS } from '../../../../constants';
import { UsageTrackingItem } from '../../../../types/settings';
import { ToastItem } from '../../../../types/index';
import UTTFormModal from './components/utt-form-modal'
import DeleteModal from '../../components/delete-modal'

import styles from "./usage-tracking.module.less"

const DEFAULT_PAGE_SIZE = 10;

type UsageTrackingProps = {
  vendorId: string,
  accountId: string,
  isEditable: boolean
}

const tableColumns = ({
  handleEdit,
  handleDelete,
  isEditable,
}: {
  handleEdit: (item: UsageTrackingItem) => void,
  handleDelete: (item: UsageTrackingItem) => void,
  isEditable: boolean
}) => [
    {
      title: 'Label',
      index: 'label',
      sort: false,
      align: 'left',
      render: (value: UsageTrackingItem) => (
        <sinch-text type="s">{value.label}</sinch-text>
      ),
    },
    {
      title: 'Threshold',
      index: 'threshold',
      sort: false,
      align: 'left',
      render: (value: UsageTrackingItem) => (
        <sinch-text type="s">{value.threshold}</sinch-text>
      ),
    },
    {
      title: 'Period',
      index: 'period',
      sort: false,
      align: 'left',
      render: (value: UsageTrackingItem) => (
        <sinch-text type="s">{value.period}</sinch-text>
      ),
    },
    {
      title: 'Action',
      index: 'action',
      sort: false,
      align: 'left',
      render: (value: UsageTrackingItem) => (
        <sinch-text type="s">{value.action}</sinch-text>
      ),
    },
    {
      title: 'Type',
      index: 'timezoneType',
      sort: false,
      align: 'left',
      render: (value: UsageTrackingItem) => (
        <sinch-text type="s">{value.timezoneType}</sinch-text>
      ),
    },
    {
      title: 'Timezone',
      index: 'timezone',
      sort: false,
      align: 'left',
      render: (value: UsageTrackingItem) => (
        <sinch-text type="s">{value.timezone}</sinch-text>
      ),
    },
    {
      title: 'Action',
      index: 'timezone',
      sort: false,
      align: 'left',
      render: (value: UsageTrackingItem) => (
        <div className={styles.btnGroup}>
          <div className={styles.editBtn}>
            <Button
              label="Edit"
              type="secondary"
              onClick={() => handleEdit(value)}
              disabled={!isEditable}
            />
          </div>
          <Button
            label="Delete"
            type="destructive"
            onClick={() => handleDelete(value)}
            disabled={!isEditable}
          />
        </div>
      )
    },
  ]

function UsageTracking({ vendorId, accountId, isEditable }: UsageTrackingProps) {
  const rootState = useSelector((state: RootState) => state || {});
  const [isUTTModalOpen, setUTTModalOpen] = useState<boolean>(false)
  const [isDeleteUTTModalOpen, setDeleteUTTModalOpen] = useState<boolean>(false)
  const [deleteItem, setDeleteItem] = useState<UsageTrackingItem | null>(null)
  const [formValues, setFormValues] = useState<UsageTrackingItem | null>(null)
  const [tokenQueue, setTokenQueue] = useState<string[]>([]);
  const [toasts, setToasts] = useState<ToastItem[]>([]);
  const { accountUsageTracking } = rootState;
  const { data, loading, updateLoading, createLoading, deleteLoading } = accountUsageTracking || {};
  const dispatch = useDispatch()

  useEffect(() => {
    dispatch(
      fetchUsageTracking({
        vendorId,
        accountId,
        size: DEFAULT_PAGE_SIZE,
      })
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (_isEmpty(formValues)) return;
    let newToasts = [];
    if (
      updateLoading === LOADING_STATUS.SUCCEEDED ||
      updateLoading === LOADING_STATUS.FAILED
    ) {
      const text =
        updateLoading === LOADING_STATUS.SUCCEEDED
          ? `Successfully updated UTT ${formValues.label}`
          : `Failed to update UTT ${formValues.label}`;
      newToasts = toasts.concat({
        id: formValues.id,
        text,
        type: updateLoading === LOADING_STATUS.SUCCEEDED ? 'success' : 'error',
      });
      setToasts(newToasts);
      setFormValues(null);
      setUTTModalOpen(false)
      setTimeout(() => {
        dispatch(
          fetchUsageTracking({
            vendorId,
            accountId,
            size: DEFAULT_PAGE_SIZE,
          })
        );
      }, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [updateLoading])

  useEffect(() => {
    if (!formValues) return;
    let newToasts = [];
    if (
      createLoading === LOADING_STATUS.SUCCEEDED ||
      createLoading === LOADING_STATUS.FAILED
    ) {
      const text =
        createLoading === LOADING_STATUS.SUCCEEDED
          ? `Successfully created UTT ${formValues.label}`
          : `Failed to create UTT ${formValues.label}`;
      newToasts = toasts.concat({
        id: formValues.label,
        text,
        type: createLoading === LOADING_STATUS.SUCCEEDED ? 'success' : 'error',
      });
      setToasts(newToasts);
      setFormValues(null);
      setUTTModalOpen(false)
      setTimeout(() => {
        dispatch(
          fetchUsageTracking({
            vendorId,
            accountId,
            size: DEFAULT_PAGE_SIZE,
          })
        );
      }, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [createLoading])

  useEffect(() => {
    if (_isEmpty(deleteItem)) return;
    let newToasts = [];
    if (
      deleteLoading === LOADING_STATUS.SUCCEEDED ||
      deleteLoading === LOADING_STATUS.FAILED
    ) {
      const text =
        deleteLoading === LOADING_STATUS.SUCCEEDED
          ? `Successfully deleted UTT ${deleteItem.label}`
          : `Failed to delete UTT ${deleteItem.label}`;
      newToasts = toasts.concat({
        id: deleteItem.label,
        text,
        type: deleteLoading === LOADING_STATUS.SUCCEEDED ? 'success' : 'error',
      });
      setToasts(newToasts);
      setFormValues(null);
      setDeleteUTTModalOpen(false)
      setTimeout(() => {
        dispatch(
          fetchUsageTracking({
            vendorId,
            accountId,
            size: DEFAULT_PAGE_SIZE,
          })
        );
      }, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [deleteLoading])

  const handleEdit = (item: UsageTrackingItem) => {
    setUTTModalOpen(true)
    setFormValues(item)
  }

  const handleAdd = (item: UsageTrackingItem) => {
    setUTTModalOpen(true)
    setFormValues({
      action: 'DISCARD',
      alerts: [],
      id: '',
      label: '',
      period: 'HOURLY',
      threshold: '',
      timezone: '',
      timezoneType: 'FIXED'
    })
  }

  const handleDelete = (item: UsageTrackingItem) => {
    setDeleteUTTModalOpen(true)
    setDeleteItem(item)
  }

  const handlePrevious = () => {
    tokenQueue.pop();
    setTokenQueue(tokenQueue);
    const tokensLen = tokenQueue.length;

    setTimeout(() => {
      dispatch(
        fetchUsageTracking({
          vendorId,
          accountId,
          size: DEFAULT_PAGE_SIZE,
          next: tokensLen ? tokenQueue[tokensLen - 1] : '',
        })
      );
    });
  };

  const handleNext = (token: string) => {
    tokenQueue.push(token);
    setTokenQueue(tokenQueue);

    setTimeout(() => {
      dispatch(
        fetchUsageTracking({
          vendorId,
          accountId,
          size: DEFAULT_PAGE_SIZE,
          next: token,
        })
      );
    });
  };

  if (!data) {
    return (
      <div>
        <div className={styles.title}>
          <Text type="l" inline emphasized>
            Usage Tracking Details
          </Text>
        </div>
        <sinch-skeleton style={{ width: 500, height: 100 }}>
          <sinch-skeleton-item style={{ width: '70%', height: '100%' }} />
          <sinch-skeleton-item style={{ width: '100%', height: '100%' }} />
        </sinch-skeleton>
      </div>
    );
  }

  return (
    <div>
      <div className={styles.title}>
        <Text type="l" inline emphasized>
          Usage Tracking Details
        </Text>
        <div className={styles.titleRight}>
          <Button
            label="Add Usage Tracking Threshold"
            type="secondary"
            onClick={handleAdd}
            disabled={!isEditable}
          >
            <sinch-icon-add slot="icon" />
          </Button>
        </div>

      </div>
      <Table
        hasCheckbox={false}
        tableColumns={tableColumns({
          handleEdit,
          handleDelete,
          isEditable
        })}
        tableData={data.resources || []}
        loading={loading}
        next={data.pagination?.next}
        previous={tokenQueue.length}
        handlePreviousPage={handlePrevious}
        handleNextPage={handleNext}
      />
      <UTTFormModal
        isVisible={isUTTModalOpen}
        setIsVisible={setUTTModalOpen}
        formValues={formValues}
        setFormValues={setFormValues}
        accountId={accountId}
        vendorId={vendorId}
      />
      <DeleteModal
        isVisible={isDeleteUTTModalOpen}
        setIsVisible={setDeleteUTTModalOpen}
        id={deleteItem?.id}
        accountId={accountId}
        vendorId={vendorId}
        type="UTT"
        label={deleteItem?.label}
        deleteAction={deleteUsageTracking}
      />
      <Toast toasts={toasts} setToasts={setToasts} />
    </div>
  )
}

export default UsageTracking
