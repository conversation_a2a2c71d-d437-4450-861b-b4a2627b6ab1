import '@nectary/assets/icons/add';
import '@nectary/components/chip';
import '@nectary/components/skeleton';
import '@nectary/components/skeleton-item';
import { Button, Dialog, Input, Table, Text, Toast } from 'nectary';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { LOADING_STATUS } from '../../../../constants/index';
import { deleteDeliveryProperty, fetchDeliveryProperties } from '../../../../redux/settings/delivery-properties/delivery-properties-slice';
import { ToastItem } from '../../../../types/index';
import { DeliveryPropertiesItem } from '../../../../types/settings/delivery-properties';
import { RootState } from '../../../../types/store';
import DeliveryPropertiesModal from './delivery-properties-modal';
import styles from "./delivery-properties.module.less";

const DEFAULT_PAGE_SIZE = 10;

type DeliveryPropertiesProps = {
  vendorId: string,
  accountId: string,
  isEditable: boolean
}

const tableColumns = ({
  handleDelete,
  handleEdit,
  isEditable,
}: {
  handleDelete: (item: DeliveryPropertiesItem) => void;
  handleEdit: (item: DeliveryPropertiesItem) => void;
  isEditable: boolean;
}) => [
  {
    title: 'Type',
    index: 'type',
    sort: false,
    align: 'left',
    render: (value: DeliveryPropertiesItem) => (
      <sinch-text type="s">{value.type}</sinch-text>
    ),
  },
  {
    title: 'Value',
    index: 'value',
    sort: false,
    align: 'left',
    render: (value: DeliveryPropertiesItem) => (
      <sinch-text type="s">{value.value}</sinch-text>
    ),
  },
  {
    title: 'Country',
    index: 'country',
    sort: false,
    align: 'left',
    render: (value: DeliveryPropertiesItem) => (
      <sinch-text type="s">{value.country}</sinch-text>
    ),
  },
  {
    title: 'Endpoint Type',
    index: 'endpoint',
    sort: false,
    align: 'left',
    customStyles: { width: 150 },
    render: (value: DeliveryPropertiesItem) => (
      <sinch-text type="s">{value.endpointType}</sinch-text>
    ),
  },
  {
    title: 'Carrier',
    index: 'carrier',
    sort: false,
    align: 'left',
    render: (value: DeliveryPropertiesItem) => (
      <sinch-text type="s">{value.carrier}</sinch-text>
    ),
  },
  {
    title: 'Actions',
    index: 'actions',
    sort: false,
    align: 'left',
    render: (value: DeliveryPropertiesItem) => (
      <div className={styles.btnGroup}>
        <div className={styles.editBtn}>
          <Button
            label="Edit"
            type="secondary"
            onClick={() => handleEdit(value)}
            disabled={!isEditable}
          />
        </div>
        <Button
          label="Delete"
          type="destructive"
          onClick={() => handleDelete(value)}
          disabled={!isEditable}
        />
      </div>
    ),
  },
]

function DeliveryProperties({ vendorId, accountId, isEditable }: DeliveryPropertiesProps) {
  const rootState = useSelector((state: RootState) => state || {});
  const [tokenQueue, setTokenQueue] = useState<string[]>([]);
  const [toasts, setToasts] = useState<ToastItem[]>([]);
  const { deliveryProperties } = rootState;
  const { data, loading, createDeliveryPropertiesLoading, updateDeliveryPropertiesLoading, deleteDeliveryPropertyLoading } = deliveryProperties || {};
  const [isDeliveryPropertiesModalVisible, setDeliveryPropertiesModalVisible] = useState(false);
  const [formValues, setFormValues] = useState<DeliveryPropertiesItem | object>({});
  const [isDeleteModalVisible, setDeleteModalVisible] =
    useState<boolean>(false);
  const [deleteItem, setDeleteItem] = useState<DeliveryPropertiesItem | null>(
    null
  );
  const [deleteText, setDeleteText] = useState('');

  const dispatch = useDispatch()

  useEffect(() => {
    dispatch(
      fetchDeliveryProperties({
        vendorId,
        accountId,
        size: DEFAULT_PAGE_SIZE,
      })
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    let newToasts = [];
    if (
      createDeliveryPropertiesLoading === LOADING_STATUS.SUCCEEDED ||
      createDeliveryPropertiesLoading === LOADING_STATUS.FAILED
    ) {
      const text =
        createDeliveryPropertiesLoading === LOADING_STATUS.SUCCEEDED
          ? `Successfully created new delivery properties`
          : `Failed to create new delivery properties`;
      newToasts = toasts.concat({
        id: formValues?.url,
        text,
        type:
          createDeliveryPropertiesLoading === LOADING_STATUS.SUCCEEDED
            ? 'success'
            : 'error',
      });
      setToasts(newToasts);
      setFormValues({});
      setDeliveryPropertiesModalVisible(false);
      setTimeout(() => {
        dispatch(
          fetchDeliveryProperties({
            vendorId,
            accountId,
            size: DEFAULT_PAGE_SIZE,
          })
        );
      }, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [createDeliveryPropertiesLoading]);

  useEffect(() => {
    if (!deleteItem) return;
    let newToasts = [];
    if (
      deleteDeliveryPropertyLoading === LOADING_STATUS.SUCCEEDED ||
      deleteDeliveryPropertyLoading === LOADING_STATUS.FAILED
    ) {
      const text =
        deleteDeliveryPropertyLoading === LOADING_STATUS.SUCCEEDED
          ? `Successfully deleted delivery property ${deleteItem.id}`
          : `Failed to delete delivery property ${deleteItem.id}`;
      newToasts = toasts.concat({
        id: deleteItem.id,
        text,
        type:
          deleteDeliveryPropertyLoading === LOADING_STATUS.SUCCEEDED
            ? 'success'
            : 'error',
      });
      setToasts(newToasts);
      setFormValues({});
      setDeleteModalVisible(false);
      setTimeout(() => {
        dispatch(
          fetchDeliveryProperties({
            vendorId,
            accountId,
            size: DEFAULT_PAGE_SIZE,
          })
        );
      }, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [deleteDeliveryPropertyLoading]);

  useEffect(() => {
    let newToasts = [];
    if (
      updateDeliveryPropertiesLoading === LOADING_STATUS.SUCCEEDED ||
      updateDeliveryPropertiesLoading === LOADING_STATUS.FAILED
    ) {
      const text =
        updateDeliveryPropertiesLoading === LOADING_STATUS.SUCCEEDED
          ? `Successfully updated delivery properties`
          : `Failed to update delivery properties`;
      newToasts = toasts.concat({
        id: formValues?.id,
        text,
        type:
          updateDeliveryPropertiesLoading === LOADING_STATUS.SUCCEEDED
            ? 'success'
            : 'error',
      });
      setToasts(newToasts);
      setFormValues({});
      setDeliveryPropertiesModalVisible(false);
      setTimeout(() => {
        dispatch(
          fetchDeliveryProperties({
            vendorId,
            accountId,
            size: DEFAULT_PAGE_SIZE,
          })
        );
      }, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [updateDeliveryPropertiesLoading]);

  const handlePrevious = () => {
    tokenQueue.pop();
    setTokenQueue(tokenQueue);
    const tokensLen = tokenQueue.length;

    setTimeout(() => {
      dispatch(
        fetchDeliveryProperties({
          vendorId,
          accountId,
          size: DEFAULT_PAGE_SIZE,
          ...tokensLen && { next: tokenQueue[tokensLen - 1] },
        })
      );
    });
  };

  const handleNext = (token: string) => {
    tokenQueue.push(token);
    setTokenQueue(tokenQueue);

    setTimeout(() => {
      dispatch(
        fetchDeliveryProperties({
          vendorId,
          accountId,
          size: DEFAULT_PAGE_SIZE,
          next: token,
        })
      );
    });
  };

  if (!data) {
    return (
      <div>
        <div className={styles.title}>
          <Text type="l" inline emphasized>
            Delivery Properties
          </Text>
        </div>
        <sinch-skeleton style={{ width: 500, height: 100 }}>
          <sinch-skeleton-item style={{ width: '70%', height: '100%' }} />
          <sinch-skeleton-item style={{ width: '100%', height: '100%' }} />
        </sinch-skeleton>
      </div>
    );
  }

  const handleAdd = () => {
    setFormValues({})
    setDeliveryPropertiesModalVisible(true);
  };

  const handleEdit = (item: DeliveryPropertiesItem) => {
    setFormValues(item);
    setDeliveryPropertiesModalVisible(true);
  };

  const handleDelete = (item: DeliveryPropertiesItem) => {
    setDeleteModalVisible(true);
    setDeleteItem(item);
  };

  const confirmDelete = () => {
    if (!deleteItem) return;
    dispatch(
      deleteDeliveryProperty({
        vendorId,
        accountId,
        id: deleteItem.id
      })
    );
    setDeleteModalVisible(false);
    setDeleteText('');
  };

  return (
    <div>
      <div className={styles.title}>
        <Text type="l" inline emphasized>
          Delivery Properties Details
        </Text>
      </div>
      <div className={styles.action}>
        <Button
          label="Add Delivery Property"
          type="secondary"
          onClick={handleAdd}
          disabled={!isEditable}
        />
      </div>
      <Table
        hasCheckbox={false}
        tableColumns={tableColumns({
          handleDelete,
          handleEdit,
          isEditable,
        })}
        tableData={data.resources || []}
        loading={loading}
        next={data.pagination?.next}
        previous={tokenQueue.length}
        handlePreviousPage={handlePrevious}
        handleNextPage={handleNext}
        scrollX
      />
      <DeliveryPropertiesModal
        isVisible={isDeliveryPropertiesModalVisible}
        setIsVisible={setDeliveryPropertiesModalVisible}
        formValues={formValues}
        setFormvalues={setFormValues}
        accountId={accountId}
        vendorId={vendorId}
      />
      <Dialog
        isOpen={isDeleteModalVisible}
        caption="Delete"
        onClose={() => {
          setDeleteModalVisible(false);
          setDeleteText('');
        }}
      >
        <div className={styles.formItem}>
          <Text>{`Are you sure you want to delete delivery property ${deleteItem?.id}`}</Text>
        </div>
        <div className={styles.formItem}>
          <Input
            label="Please type delete"
            value={deleteText}
            onChange={(val: string) => setDeleteText(val)}
          />
        </div>
        <div className={styles.formItem}>
          <Button
            label="Confirm"
            disabled={deleteText.toLowerCase() !== 'delete'}
            onClick={confirmDelete}
          />
        </div>
      </Dialog>
      <Toast toasts={toasts} setToasts={setToasts} />
    </div>
  )
}

export default DeliveryProperties
