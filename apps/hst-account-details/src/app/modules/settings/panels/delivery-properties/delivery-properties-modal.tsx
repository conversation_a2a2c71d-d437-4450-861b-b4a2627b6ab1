import _find from 'lodash/find';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import {
  Button,
  Checkbox,
  CountryAutoComplete,
  Dialog,
  Input,
  Select,
  Spinner,
} from 'nectary';

import {
  createDeliveryProperties,
  fetchDeliveryPropertyTypes,
  updateDeliveryProperties,
} from '../../../../redux/settings/delivery-properties/delivery-properties-slice';
import { DeliveryPropertiesItem } from '../../../../types/settings/delivery-properties';
import { ENDPOINT_TYPE_OPTIONS, MAXIMUM_ROUTING_ATTEMPTS } from './constants';

import { RootState } from 'apps/hst-account-details/src/app/types/store';
import { LOADING_STATUS } from '../../../../constants/index';
import styles from './delivery-properties-modal.module.less';

const FIELD_NAMES = {
  type: 'type',
  value: 'value',
  carrier: 'carrier',
  endpointType: 'endpointType',
  country: 'country',
};

const isFieldValid = (fieldName: string, value: any) => {
  switch (fieldName) {
    case FIELD_NAMES.type:
      return !!value;
    case FIELD_NAMES.value: {
      const { type, value: val } = value;
      if (type === MAXIMUM_ROUTING_ATTEMPTS) {
        return !isNaN(val);
      }
      return true;
    }
    case FIELD_NAMES.carrier:
    case FIELD_NAMES.endpointType:
    case FIELD_NAMES.country: {
      const { carrier, endpointType, country } = value || {};
      let truthyCount = 0;
      if (carrier) truthyCount += 1;
      if (endpointType) truthyCount += 1;
      if (country) truthyCount += 1;
      return truthyCount <= 1;
    }
    default:
      return true;
  }
};

const isFormValid = (payload: DeliveryPropertiesItem) => {
  let isValid = true;
  for (const [key, value] of Object.entries(payload)) {
    if (
      key === FIELD_NAMES.carrier ||
      key === FIELD_NAMES.endpointType ||
      key === FIELD_NAMES.country
    ) {
      if (
        !isFieldValid(key, {
          carrier: payload?.[FIELD_NAMES.carrier],
          endpointType: payload?.[FIELD_NAMES.endpointType],
          country: payload?.[FIELD_NAMES.country],
        })
      ) {
        isValid = false;
      }
    } else if (!isFieldValid(key, value)) {
      isValid = false;
    }
  }
  return isValid;
};

const DeliveryPropertiesModal = ({
  isVisible,
  setIsVisible,
  formValues = {},
  accountId,
  vendorId,
  setFormvalues,
}: {
  isVisible: boolean;
  setIsVisible: (isVisible: boolean) => void;
  formValues: DeliveryPropertiesItem | object;
  accountId: string;
  vendorId: string;
  setFormvalues: (item: DeliveryPropertiesItem | null) => void;
}) => {
  const rootState = useSelector((state: RootState) => state || {});
  const { deliveryProperties, accountCommonSettings } = rootState;
  const { deliveryPropertyTypes, deliveryPropertyTypesLoading } =
    deliveryProperties || {};
  const { carriers, carriersLoading, countries, countriesLoading } =
  accountCommonSettings || {};
  const { resources: carriersResources } = carriers || {};
  const { resources: countriesResources } = countries || {};
  const { resources: deliveryPropertyTypesResources } =
    deliveryPropertyTypes || {};

  const [type, setType] = useState(formValues.type || '');
  const [value, setValue] = useState(formValues.value || '');
  const [carrier, setCarrier] = useState(formValues.carrier || '');
  const [endpointType, setEndpointType] = useState(
    formValues.endpointType || ''
  );
  const COUNTRY_OPTIONS =
    (countriesResources || [])?.map(({ countryName, countryCode }) => ({
      label: countryName,
      value: countryCode
    })) || [];

  const initialCountryOption = _find(
    COUNTRY_OPTIONS,
    ({ label }: { label: string }) => label === formValues.country
  );
  const [countryOption, setCountryOption] = useState(
    initialCountryOption || {}
  );
  const [isValidated, setIsValidated] = useState(false);

  const dispatch = useDispatch();

  const CARRIER_OPTIONS =
    (carriersResources || [])?.map(({ name }) => ({
      label: name,
      value: name,
    })) || [];
  CARRIER_OPTIONS.unshift({ label: '', value: '' });

  const TYPE_OPTIONS =
    (deliveryPropertyTypesResources || [])?.map(({ name }) => ({
      value: name,
      label: name,
    })) || [];

  useEffect(() => {
    setType(formValues.type || '');

    if (formValues.type !== MAXIMUM_ROUTING_ATTEMPTS) {
      setValue(!!formValues.value);
    } else {
      setValue(formValues.value || '');
    }

    setCarrier(formValues.carrier || '');

    const nextCountryOption = _find(
      COUNTRY_OPTIONS,
      ({ label }: { label: string }) => label === formValues.country
    );
    setCountryOption(nextCountryOption || {});

    setEndpointType(formValues.endpointType || '');
  }, [formValues]);

  useEffect(() => {
    dispatch(
      fetchDeliveryPropertyTypes({
        vendorId,
        accountId,
      })
    );
  }, []);

  useEffect(() => {
    if (isVisible) setIsValidated(false);
  }, [isVisible]);

  const handleSubmit = () => {
    const payload = {
      id: formValues?.id,
      type,
      value,
      carrier,
      endpointType,
      country: countryOption.value,
    };
    setIsValidated(true);

    const isPayloadValid = isFormValid(payload);
    if (!isPayloadValid) return;

    Object.keys(payload).map((key) => {
      if (key !== 'value' && !payload[key]) {
        delete payload[key];
      }
    });

    if (formValues.id) {
      dispatch(
        updateDeliveryProperties({
          accountId,
          vendorId,
          payload,
        })
      );
      return;
    }

    dispatch(
      createDeliveryProperties({
        accountId,
        vendorId,
        payload,
      })
    );
  };

  const handleClose = () => {
    setFormvalues({});
    setIsVisible(false);
    setIsValidated(false);
  };

  const typeLabel = _find(
    TYPE_OPTIONS,
    (option) => option.value === type
  )?.label;
  const isValueCheckbox = typeLabel !== MAXIMUM_ROUTING_ATTEMPTS;

  return (
    <div className={styles.dialogWrap}>
      <Dialog
        isOpen={isVisible}
        caption="Delivery Properties"
        onClose={handleClose}
      >
        <form className={styles.filterWrapper}>
          <div className={styles.formItem}>
            {deliveryPropertyTypesLoading === LOADING_STATUS.LOADING && (
              <div className={styles.spinner}>
                <Spinner size="m" />
              </div>
            )}
            <Select
              label="Type (Required)"
              value={type}
              options={TYPE_OPTIONS}
              onSelect={(newVal) => {
                const newLabel = _find(
                  TYPE_OPTIONS,
                  (option) => option.value === newVal
                )?.label;
                if (newLabel === MAXIMUM_ROUTING_ATTEMPTS) {
                  setValue('');
                } else {
                  setValue(false);
                }
                setType(newVal);
              }}
              testId="type-select"
              customStyles={{ width: '70%' }}
              errorText={
                isValidated && !isFieldValid(FIELD_NAMES.type, type)
                  ? 'Type is required'
                  : ''
              }
            />
          </div>
          <div className={styles.formItem}>
            {isValueCheckbox ? (
              <Checkbox
                label="Value (Required)"
                value={!!value}
                onChange={(newVal) => {
                  setValue(newVal);
                }}
              />
            ) : (
              <Input
                label="Value (Required)"
                value={value}
                onChange={setValue}
                testId="value-input"
                fieldStyles={{ width: '70%' }}
                errorText={
                  isValidated && !isFieldValid(FIELD_NAMES.value, value)
                    ? 'Value must be a number'
                    : ''
                }
              />
            )}
          </div>
          <div className={styles.formItem}>
            {carriersLoading === LOADING_STATUS.LOADING && (
              <div className={styles.spinner}>
                <Spinner size="m" />
              </div>
            )}
            <Select
              label="Carrier"
              value={carrier}
              options={CARRIER_OPTIONS}
              onSelect={setCarrier}
              testId="carrier-select"
              customStyles={{ width: '70%' }}
              disabled={carriersLoading === LOADING_STATUS.LOADING}
              errorText={
                isValidated &&
                !isFieldValid(FIELD_NAMES.carrier, {
                  carrier,
                  endpointType,
                  country: countryOption.value,
                })
                  ? 'Carrier, EndpointType and Country cannot be set at the same time'
                  : ''
              }
            />
          </div>
          <div className={styles.formItem}>
            <Select
              label="Endpoint Type"
              value={endpointType}
              options={ENDPOINT_TYPE_OPTIONS}
              onSelect={setEndpointType}
              testId="endpoint-type-select"
              customStyles={{ width: '70%' }}
              errorText={
                isValidated &&
                !isFieldValid(FIELD_NAMES.endpointType, {
                  carrier,
                  endpointType,
                  country: countryOption.value,
                })
                  ? 'Carrier, EndpointType and Country cannot be set at the same time'
                  : ''
              }
            />
          </div>
          <div className={styles.formItem}>
            {countriesLoading === LOADING_STATUS.LOADING && (
              <div className={styles.spinner}>
                <Spinner size="m" />
              </div>
            )}
            <CountryAutoComplete
              onChange={setCountryOption}
              selectedOption={countryOption}
              countries={COUNTRY_OPTIONS}
              errorText={
                isValidated &&
                !isFieldValid(FIELD_NAMES.country, {
                  carrier,
                  endpointType,
                  country: countryOption.value,
                })
                  ? 'Carrier, EndpointType and Country cannot be set at the same time'
                  : ''
              }
              fieldStyles={{ width: '70%' }}
            />
          </div>

          <div className={styles.btnGroup}>
            <Button label="Submit" onClick={handleSubmit} />
            <Button label="Cancel" onClick={handleClose} />
          </div>
        </form>
      </Dialog>
    </div>
  );
};

export default DeliveryPropertiesModal;
