.wrapper {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  gap: 16px;
}

.titleRight {
  :first-child {
    margin-right: 15px;
  }
}

.left {
  width: 200px;
}

.right {
  margin-left: 50px;
}

.title {
  margin-bottom: 32px;
  display: flex;
  justify-content: space-between;
}

.row {
  display: flex;
  flex-direction: row;
  margin-bottom: 16px;
  .left {
    width: 30%;
  }
  .right {
    width: 70%;
  }
}

.btnGroup {
  display: flex;
  flex-direction: row;
}

.editBtn {
  margin-right: 4px;
}

.action {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 24px;
}

.formItem {
  margin-top: 16px;
}
