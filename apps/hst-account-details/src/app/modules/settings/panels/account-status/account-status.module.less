.wrapper {
  position: relative;
}

.spinnerWrap {
  width: 100%;
  height: 100%;
  position: absolute;
  opacity: 0.3;
  display: flex;
  align-items: center;
}

.left {
  width: 200px;
}

.right {
  margin-left: 50px;
}

.card {
  padding: 24px;
  margin: 20px 0;
  border-radius: var(--sinch-comp-card-shape-radius);
  border: 1px solid var(--sinch-comp-card-color-white-default-border-initial);
  background-color: var(
    --sinch-comp-card-color-white-default-background-initial
  );
  box-shadow: var(--sinch-comp-card-shadow-initial);
  overflow: hidden;
}

.title {
  margin-bottom: 32px;
  display: flex;
  justify-content: space-between;
}

.row {
  display: flex;
  flex-direction: row;
  margin-bottom: 16px;
  .left {
    width: 30%;
  }
  .right {
    width: 70%;
  }
}

.active,
.verified {
  align-items: flex-start;
  display: flex;
  justify-content: flex-start;
  sinch-text {
    color: var(--sinch-ref-color-complementary-olive-400);
  }
  --sinch-global-color-icon: var(--sinch-ref-color-complementary-olive-400);
}

.suspended {
  align-items: flex-start;
  display: flex;
  justify-content: flex-start;
  sinch-text {
    color: var(--sinch-ref-color-complementary-pumpkin-400);
  }
  --sinch-global-color-icon: var(--sinch-ref-color-pumpkin-400);
}

.unverified,
.cancelled {
  align-items: flex-start;
  display: flex;
  justify-content: flex-start;
  sinch-text {
    color: var(--sinch-ref-color-complementary-raspberry-400);
  }
  --sinch-global-color-icon: var(--sinch-ref-color-raspberry-400);
}

.actionBtn {
  display: inline-block;
  margin-left: 4px;
}
