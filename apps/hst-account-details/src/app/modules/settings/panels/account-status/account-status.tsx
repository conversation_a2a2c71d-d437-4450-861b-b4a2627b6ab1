/* eslint-disable @nx/enforce-module-boundaries */
/* eslint-disable @typescript-eslint/no-shadow */
import '@nectary/components/chip';
import '@nectary/components/skeleton';
import '@nectary/components/skeleton-item';
import { RootState } from 'apps/hst-account-details/src/app/types/store';
import _get from 'lodash/get';
import { Button, Spinner, Tag, Text, Toast } from 'nectary';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  ACTIVE,
  CANCELLED,
  LOADING_STATUS,
  REINSTATE,
  SUSPENDED,
  UNVERIFIED,
  VERIFIED,
} from '../../../../constants';
import { fetchAccountStatus } from '../../../../redux/settings/account-status/account-status-slice';
import { ToastItem } from '../../../../types';
import { AccountStatusType, ACTIONS } from '../../../../types/settings';
import UpdateStatusModal, {
  STATUS_MAPPING,
} from '../../components/update-status-modal';
import styles from './account-status.module.less';

type AccountStatusProps = {
  vendorId: string;
  accountId: string;
  isStatusEditable: boolean;
  isVerificationVisible: boolean;
  isVerificationEditable: boolean;
};

type FunctionRender = (data: AccountStatusType) => JSX.Element;

function AccountStatus({
  vendorId,
  accountId,
  isStatusEditable,
  isVerificationEditable,
  isVerificationVisible,
}: AccountStatusProps) {
  const rootState = useSelector((state: RootState) => state || {});
  const [isUpdateStatusModalVisible, setUpdateStatusModalVisible] =
    useState<boolean>(false);
  const [newStatus, setNewStatus] =
    useState<keyof typeof STATUS_MAPPING>(ACTIVE);
  const { accountStatus } = rootState;
  const { data, loading, updateLoading } = accountStatus || {};
  const { verificationStatus } = data || {};
  const [toasts, setToasts] = useState<ToastItem[]>([]);
  const dispatch = useDispatch();

  const renderRow = (
    title: string,
    data: AccountStatusType,
    render?: FunctionRender
  ) => (
    <div className={styles.row}>
      <div className={styles.left}>
        <Text type="s" inline>
          {title}
        </Text>
      </div>
      <div className={styles.right}>
        <Text type="s" inline emphasized>
          {render ? render(data) : data?.verificationReason || '-'}
        </Text>
      </div>
    </div>
  );

  const renderStatus = (data: AccountStatusType) => {
    const status = data?.status;
    if (status === ACTIVE) {
      return (
        <div className={styles.active}>
          <Tag key="light-green" color="light-green" text={ACTIVE} />
        </div>
      );
    }
    if (status === SUSPENDED) {
      return (
        <div className={styles.suspended}>
          <Tag key="orange" color="orange" text={SUSPENDED} />
        </div>
      );
    }
    return (
      <div className={styles.cancelled}>
        <Tag key="light-red" color="light-red" text={CANCELLED} />
      </div>
    );
  };

  const renderState = (data: AccountStatusType) => {
    const status = data?.status;
    const state = data?.state;
    if (status === ACTIVE) {
      return (
        <div className={styles.active}>
          <Text type="s">{state}</Text>
        </div>
      );
    }
    if (status === SUSPENDED) {
      return (
        <div className={styles.suspended}>
          <Text type="s">{state}</Text>
        </div>
      );
    }
    return (
      <div className={styles.cancelled}>
        <Text type="s">{state}</Text>
      </div>
    );
  };

  const renderVerificationStatus = (data: AccountStatusType) => {
    const status = data?.verificationStatus;
    if (status === VERIFIED) {
      return (
        <div className={styles.verified}>
          <Tag key="light-green" color="light-green" text={VERIFIED} />
        </div>
      );
    }
    if (status === UNVERIFIED) {
      return (
        <div className={styles.unverified}>
          <Tag key="light-red" color="light-red" text={UNVERIFIED} />
        </div>
      );
    }
    return <div className={styles.unverified}>-</div>;
  };

  useEffect(() => {
    dispatch(
      fetchAccountStatus({
        vendorId,
        accountId,
      })
    );
  }, [accountId, dispatch, vendorId]);

  useEffect(() => {
    let newToasts = [];
    if (
      updateLoading === LOADING_STATUS.SUCCEEDED ||
      updateLoading === LOADING_STATUS.FAILED
    ) {
      const text =
        updateLoading === LOADING_STATUS.SUCCEEDED
          ? `Successfully updated account status`
          : `Failed to update account status`;
      newToasts = [
        {
          id: accountId,
          text,
          type:
            updateLoading === LOADING_STATUS.SUCCEEDED ? 'success' : 'error',
        },
      ];
      setToasts(newToasts);
      setTimeout(() => {
        dispatch(
          fetchAccountStatus({
            vendorId,
            accountId,
          })
        );
      }, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [updateLoading]);

  const handleAction = (newStatus: keyof typeof STATUS_MAPPING) => {
    setUpdateStatusModalVisible(true);
    setNewStatus(newStatus);
  };

  const renderActions = () => {
    const actionButonMapping = {
      [ACTIONS.reinstate]: (
        <div className={styles.actionBtn}>
          <Button
            disabled={!isStatusEditable}
            label="Reinstate Account"
            type="secondary"
            onClick={() => handleAction(REINSTATE)}
          />
        </div>
      ),
      [ACTIONS.suspend]: (
        <div className={styles.actionBtn}>
          <Button
            disabled={!isStatusEditable}
            label="Suspend Account"
            type="secondary"
            onClick={() => handleAction(SUSPENDED)}
          />
        </div>
      ),
      [ACTIONS.cancel]: (
        <div className={styles.actionBtn}>
          <Button
            disabled={!isStatusEditable}
            label="Cancel Account"
            type="destructive"
            onClick={() => handleAction(CANCELLED)}
          />
        </div>
      ),
    };
    return data?.actions?.map((action: keyof typeof ACTIONS) =>
      _get(actionButonMapping, action, null)
    );
  };

  return (
    <div className={styles.wrapper}>
      {(loading === LOADING_STATUS.LOADING ||
        updateLoading === LOADING_STATUS.LOADING) && (
        <div className={styles.spinnerWrap}>
          <Spinner size="l" />
        </div>
      )}
      <div className={styles.title}>
        <Text type="l" inline emphasized>
          Update Account Status
        </Text>
        <div>
          {verificationStatus === UNVERIFIED && data?.status === ACTIVE && (
            <div className={styles.actionBtn}>
              <Button
                disabled={!isVerificationEditable}
                label="Verify Account"
                onClick={() => handleAction(VERIFIED)}
              />
            </div>
          )}
          {renderActions()}
        </div>
      </div>
      {renderRow('Account Status', data, renderStatus)}
      {renderRow('Account State', data, renderState)}
      {isVerificationVisible &&
        renderRow('Verification Status', data, renderVerificationStatus)}
      {isVerificationVisible && renderRow('Verification Reason', data)}

      <UpdateStatusModal
        isVisible={isUpdateStatusModalVisible}
        setIsVisible={setUpdateStatusModalVisible}
        vendorId={vendorId}
        accountId={accountId}
        newStatus={newStatus}
        currentStatus={data?.status}
      />
      <Toast toasts={toasts} setToasts={setToasts} />
    </div>
  );
}

export default AccountStatus;
