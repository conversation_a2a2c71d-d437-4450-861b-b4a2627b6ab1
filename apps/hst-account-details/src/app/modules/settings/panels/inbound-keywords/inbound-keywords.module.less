.wrapper {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  gap: 16px;
}

.titleRight {
  display: flex;
  gap: 8px;
}

.left {
  width: 200px;
}

.right {
  margin-left: 50px;
}

.title {
  display: flex;
  justify-content: space-between;
  margin-bottom: 32px;
}

.row {
  display: flex;
  flex-direction: row;
  margin-bottom: 16px;
  .left {
    width: 30%;
  }
  .right {
    width: 70%;
  }
}

.btnGroup {
  display: flex;
  flex-direction: row;
}

.editBtn {
  margin-right: 4px;
}

.spinnerWrap {
  align-items: center;
  display: flex;
  height: 100%;
  opacity: 0.8;
  position: absolute;
  top: 12px;
  width: 100%;
  z-index: 1000;
}
