import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';

// eslint-disable-next-line @nx/enforce-module-boundaries
import {
  Dialog,
  Select,
  TextArea,
  Button,
  Toggle,
  Text,
  Tooltip,
} from 'nectary';

// Import help icon
import '@nectary/assets/icons/help';

import { InboundKeywordsItem } from '../../../../../types/settings';
import {
  createInboundKeywords,
  updateInboundKeywords,
} from '../../../../../redux/settings/inbound-keywords/inbound-keywords-slice';

import styles from './inbound-keywords-form-modal.module.less';

const ACTIONS = [
  {
    value: 'DELEGATE',
    label: 'Delegate',
  },
  {
    value: 'GLOBAL_OPT_OUT',
    label: 'Global Opt Out',
  },
  {
    value: 'OPT_IN',
    label: 'Opt In',
  },
  {
    value: 'OPT_OUT',
    label: 'Opt Out',
  },
];

const InboundKeywordsFormModal = ({
  accountId,
  vendorId,
  isVisible,
  setIsVisible,
  formValues,
  setFormValues,
  onBulkCreateComplete,
}: {
  accountId: string;
  vendorId: string;
  isVisible: boolean;
  setIsVisible: (isVisible: boolean) => void;
  formValues: InboundKeywordsItem | null;
  setFormValues: (formValues: InboundKeywordsItem | null) => void;
  onBulkCreateComplete?: (successful: number, failed: number) => void;
}) => {
  const dispatch = useDispatch();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validating, setValidating] = useState(false);

  const { action, id, keyword, autoResponse, requiredForCompliance } =
    formValues || {
      action: '',
      id: '',
      keyword: '',
      autoResponse: '',
      requiredForCompliance: false,
    };

  const isEditing = !!id;

  const onChangeKeyword = (val: string) => {
    if (!formValues) return;
    setFormValues({
      ...formValues,
      keyword: val,
    });
  };

  const onChangeAutoResponse = (val: string) => {
    if (!formValues) return;
    setFormValues({
      ...formValues,
      autoResponse: val,
    });
  };

  const handleSelectAction = (val: string) => {
    if (!formValues) return;
    setFormValues({
      ...formValues,
      action: val,
    });
  };

  const handleRequiredForCompliance = (val: boolean) => {
    if (!formValues) return;
    setFormValues({
      ...formValues,
      requiredForCompliance: val,
    });
  };

  const handleClose = () => {
    setValidating(false);
    setIsVisible(false);
  };

  const handleSubmit = async () => {
    setValidating(true);
    if (!action || !keyword || !formValues) {
      return;
    }
    setIsSubmitting(true);

    try {
      if (isEditing) {
        await dispatch(
          updateInboundKeywords({
            accountId,
            vendorId,
            body: formValues,
          })
        );
        setIsSubmitting(false);
        handleClose();
      } else {
        const keywordsList = keyword
          .split(',')
          .map((k) => k.trim())
          .filter(Boolean);

        if (keywordsList.length === 1) {
          await dispatch(
            createInboundKeywords({
              accountId,
              vendorId,
              body: {
                ...formValues,
                keyword: keywordsList[0],
              },
            })
          );
          setIsSubmitting(false);
          handleClose();
        } else {
          const createPromises = keywordsList.map((singleKeyword) => {
            const body: Omit<InboundKeywordsItem, 'id'> = {
              keyword: singleKeyword,
              action,
              autoResponse,
              requiredForCompliance,
            };
            return dispatch(
              createInboundKeywords({
                accountId,
                vendorId,
                body,
              })
            );
          });

          const results = await Promise.all(createPromises);
          const successful = results.filter((r) =>
            (r as any).type.endsWith('/fulfilled')
          ).length;
          const failed = results.filter((r) =>
            (r as any).type.endsWith('/rejected')
          ).length;

          if (onBulkCreateComplete) {
            onBulkCreateComplete(successful, failed);
          }
          setIsSubmitting(false);
          handleClose();
        }
      }
    } catch (error) {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    setValidating(false);
  }, [formValues]);

  const keywordLabel = isEditing ? 'Keyword' : 'Keywords';

  return (
    <div className={styles.dialogWrapper}>
      <Dialog
        isOpen={isVisible}
        caption={isEditing ? 'Edit Inbound Keyword' : 'Add Inbound Keywords'}
        onClose={handleClose}
      >
        <form>
          <div className={styles.formItem}>
            <div style={{ marginBottom: '8px' }}>
              <div
                style={{ display: 'flex', alignItems: 'center', gap: '5px' }}
              >
                <Text inline emphasized>
                  {keywordLabel}
                </Text>
                {!isEditing && (
                  <Tooltip
                    tooltipType="help"
                    type="fast"
                    orientation="top"
                    text="Enter a single keyword or multiple keywords separated by commas to create multiple entries at once (e.g. KEYWORD1, KEYWORD2)"
                  >
                    <sinch-icon-help />
                  </Tooltip>
                )}
              </div>
            </div>
            <TextArea
              defaultValue={keyword}
              value={keyword}
              onChange={onChangeKeyword}
              testId="keyword"
              errorText={validating && !keyword ? 'Keyword is required' : ''}
              disabled={isSubmitting}
              placeholder={
                isEditing
                  ? 'Enter keyword'
                  : 'Enter keyword or keywords separated by commas'
              }
            />
          </div>
          <div className={styles.formItem}>
            <Select
              label="Action"
              options={ACTIONS}
              value={action}
              defaultValue={action}
              onSelect={handleSelectAction}
              testId="actions"
              errorText={validating && !action ? 'Action is required' : ''}
              disabled={isSubmitting}
            />
          </div>
          <div className={styles.formItem}>
            <TextArea
              label="Auto-response"
              defaultValue={autoResponse}
              value={autoResponse}
              onChange={onChangeAutoResponse}
              testId="autoResponse"
              disabled={isSubmitting}
            />
          </div>
          <div className={styles.formItem}>
            <span className={styles.toggleTitle}>
              <Text inline emphasized>
                Required for Compliance
              </Text>
            </span>
            <Toggle
              checked={requiredForCompliance}
              onChange={handleRequiredForCompliance}
              disabled={isSubmitting}
            />
          </div>
          <div className={styles.btnGroup}>
            <Button
              label={isEditing ? 'Update' : 'Submit'}
              onClick={handleSubmit}
              loading={isSubmitting}
              disabled={isSubmitting}
            />
            <Button
              label="Cancel"
              onClick={handleClose}
              disabled={isSubmitting}
            />
          </div>
        </form>
      </Dialog>
    </div>
  );
};

export default InboundKeywordsFormModal;
