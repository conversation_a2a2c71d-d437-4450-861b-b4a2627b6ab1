/* eslint-disable @nx/enforce-module-boundaries */
/* eslint-disable @typescript-eslint/no-shadow */
import '@nectary/assets/icons/add';
import '@nectary/components/chip';
import '@nectary/components/skeleton';
import '@nectary/components/skeleton-item';
import _isEmpty from 'lodash/isEmpty';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { RootState } from 'apps/hst-account-details/src/app/types/store';
import { But<PERSON>, Spinner, Table, Text, Toast } from 'nectary';

import { LOADING_STATUS } from '../../../../constants';
import {
  deleteInboundKeywords,
  fetchInboundKeywords,
} from '../../../../redux/settings/inbound-keywords/inbound-keywords-slice';
import { ToastItem } from '../../../../types/index';
import { InboundKeywordsItem } from '../../../../types/settings';
import DeleteModal from '../../components/delete-modal';
import InboundKeywordsFormModal from './components/inbound-keywords-form-modal';

import styles from './inbound-keywords.module.less';

const DEFAULT_PAGE_SIZE = 25;

type InboundKeywordsProps = {
  vendorId: string;
  accountId: string;
  isEditable: boolean;
};

const tableColumns = ({
  handleEdit,
  handleDelete,
  isEditable,
}: {
  handleEdit: (item: InboundKeywordsItem) => void;
  handleDelete: (item: InboundKeywordsItem) => void;
  isEditable: boolean;
}) => [
  {
    title: 'Keyword',
    index: 'keyword',
    sort: false,
    align: 'left',
    render: (value: InboundKeywordsItem) => (
      <sinch-text type="s">{value.keyword}</sinch-text>
    ),
  },
  {
    title: 'Action',
    index: 'action',
    sort: false,
    align: 'left',
    render: (value: InboundKeywordsItem) => (
      <sinch-text type="s">{value.action}</sinch-text>
    ),
  },
  {
    title: 'Content',
    index: 'autoResponse',
    sort: false,
    align: 'left',
    render: (value: InboundKeywordsItem) => (
      <sinch-text type="s">{value.autoResponse}</sinch-text>
    ),
  },
  {
    title: 'Required for Compliance',
    index: 'requiredForCompliance',
    sort: false,
    align: 'left',
    render: (value: InboundKeywordsItem) => (
      <sinch-text type="s">
        {value?.requiredForCompliance?.toString()}
      </sinch-text>
    ),
  },
  {
    title: 'Actions',
    index: 'actions',
    sort: false,
    align: 'left',
    render: (value: InboundKeywordsItem) => (
      <div className={styles.btnGroup}>
        <div className={styles.editBtn}>
          <Button
            label="Edit"
            type="secondary"
            onClick={() => handleEdit(value)}
            disabled={!isEditable}
          />
        </div>
        <Button
          label="Delete"
          type="destructive"
          onClick={() => handleDelete(value)}
          disabled={!isEditable}
        />
      </div>
    ),
  },
];

function InboundKeywords({
  vendorId,
  accountId,
  isEditable,
}: InboundKeywordsProps) {
  const rootState = useSelector((state: RootState) => state || {});
  const [isInboundKeywordsModalOpen, setInboundKeywordsModalOpen] =
    useState<boolean>(false);
  const [isDeleteModalOpen, setDeleteModalOpen] = useState<boolean>(false);
  const [deleteItem, setDeleteItem] = useState<InboundKeywordsItem | null>(
    null
  );
  const [formValues, setFormValues] = useState<InboundKeywordsItem | null>(
    null
  );
  const [tokenQueue, setTokenQueue] = useState<string[]>([]);
  const [toasts, setToasts] = useState<ToastItem[]>([]);
  const [pageSize, setPageSize] = useState<number>(DEFAULT_PAGE_SIZE);
  const { inboundKeywords } = rootState;
  const { data, loading, updateLoading, createLoading, deleteLoading } =
    inboundKeywords || {};
  const { resources, pagination } = data || {};
  const { next } = pagination || {};
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(
      fetchInboundKeywords({
        vendorId,
        accountId,
        size: pageSize,
      })
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pageSize]);

  useEffect(() => {
    if (_isEmpty(formValues)) return;
    let newToasts = [];
    if (
      updateLoading === LOADING_STATUS.SUCCEEDED ||
      updateLoading === LOADING_STATUS.FAILED
    ) {
      const text =
        updateLoading === LOADING_STATUS.SUCCEEDED
          ? `Successfully updated Inbound Keywords ${formValues.keyword}`
          : `Failed to update Inbound Keywords ${formValues.keyword}`;
      newToasts = toasts.concat({
        id: formValues.id,
        text,
        type: updateLoading === LOADING_STATUS.SUCCEEDED ? 'success' : 'error',
      });
      setToasts(newToasts);
      setFormValues(null);
      setInboundKeywordsModalOpen(false);
      setTimeout(() => {
        const tokensLen = tokenQueue.length;
        dispatch(
          fetchInboundKeywords({
            vendorId,
            accountId,
            size: pageSize,
            ...(tokensLen && { next: tokenQueue[tokensLen - 1] }),
          })
        );
      }, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [updateLoading, pageSize]);

  useEffect(() => {
    if (!formValues) return;
    let newToasts = [];
    if (
      createLoading === LOADING_STATUS.SUCCEEDED ||
      createLoading === LOADING_STATUS.FAILED
    ) {
      const text =
        createLoading === LOADING_STATUS.SUCCEEDED
          ? `Successfully created Inbound Keywords ${formValues.keyword}`
          : `Failed to create Inbound Keywords ${formValues.keyword}`;
      newToasts = toasts.concat({
        id: formValues.keyword,
        text,
        type: createLoading === LOADING_STATUS.SUCCEEDED ? 'success' : 'error',
      });
      setToasts(newToasts);
      setFormValues(null);
      setInboundKeywordsModalOpen(false);
      setTimeout(() => {
        const tokensLen = tokenQueue.length;
        dispatch(
          fetchInboundKeywords({
            vendorId,
            accountId,
            size: pageSize,
            ...(tokensLen && { next: tokenQueue[tokensLen - 1] }),
          })
        );
      }, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [createLoading, pageSize]);

  useEffect(() => {
    if (_isEmpty(deleteItem)) return;
    let newToasts = [];
    if (
      deleteLoading === LOADING_STATUS.SUCCEEDED ||
      deleteLoading === LOADING_STATUS.FAILED
    ) {
      const text =
        deleteLoading === LOADING_STATUS.SUCCEEDED
          ? `Successfully deleted Inbound Keywords ${deleteItem.keyword}`
          : `Failed to delete Inbound Keywords ${deleteItem.keyword}`;
      newToasts = toasts.concat({
        id: deleteItem.keyword,
        text,
        type: deleteLoading === LOADING_STATUS.SUCCEEDED ? 'success' : 'error',
      });
      setToasts(newToasts);
      setFormValues(null);
      setDeleteModalOpen(false);
      setTimeout(() => {
        const tokensLen = tokenQueue.length;
        dispatch(
          fetchInboundKeywords({
            vendorId,
            accountId,
            size: pageSize,
            ...(tokensLen && { next: tokenQueue[tokensLen - 1] }),
          })
        );
      }, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [deleteLoading, pageSize]);

  const handleEdit = (item: InboundKeywordsItem) => {
    setInboundKeywordsModalOpen(true);
    setFormValues(item);
  };

  const handleAdd = () => {
    setInboundKeywordsModalOpen(true);
    setFormValues({
      action: '',
      id: '',
      keyword: '',
      autoResponse: '',
      requiredForCompliance: false,
    });
  };

  const handleDelete = (item: InboundKeywordsItem) => {
    setDeleteModalOpen(true);
    setDeleteItem(item);
  };

  const onBulkCreateComplete = (successful: number, failed: number) => {
    const newToasts = [];
    if (successful > 0) {
      newToasts.push({
        id: `bulk-success-${Date.now()}`,
        text: `${successful} keywords created successfully.`,
        type: 'success',
      });
    }
    if (failed > 0) {
      newToasts.push({
        id: `bulk-fail-${Date.now()}`,
        text: `${failed} keywords failed to create.`,
        type: 'error',
      });
    }
    setToasts(toasts.concat(newToasts));

    const tokensLen = tokenQueue.length;
    dispatch(
      fetchInboundKeywords({
        vendorId,
        accountId,
        size: pageSize,
        ...(tokensLen && { next: tokenQueue[tokensLen - 1] }),
      })
    );
  };

  const handlePrevious = () => {
    tokenQueue.pop();
    setTokenQueue([...tokenQueue]);
    const tokensLen = tokenQueue.length;

    setTimeout(() => {
      dispatch(
        fetchInboundKeywords({
          vendorId,
          accountId,
          size: pageSize,
          ...(tokensLen && { next: tokenQueue[tokensLen - 1] }),
        })
      );
    });
  };

  const handleNext = () => {
    if (!next) return;
    tokenQueue.push(next);
    setTokenQueue([...tokenQueue]);

    setTimeout(() => {
      dispatch(
        fetchInboundKeywords({
          vendorId,
          accountId,
          size: pageSize,
          next,
        })
      );
    });
  };

  const handleChangePageSize = (newPageSize: number) => {
    setPageSize(newPageSize);
    setTokenQueue([]);
  };

  return (
    <div>
      <div className={styles.title}>
        <Text type="l" inline emphasized>
          Inbound Keywords Details
        </Text>
        <div className={styles.titleRight}>
          <Button
            label="Add Inbound Keywords"
            type="secondary"
            onClick={handleAdd}
            disabled={!isEditable}
          >
            <sinch-icon-add slot="icon" />
          </Button>
        </div>
      </div>
      <div>
        {loading === LOADING_STATUS.LOADING && (
          <div className={styles.spinnerWrap}>
            <Spinner size="m" />
          </div>
        )}
        <Table
          hasCheckbox={false}
          tableColumns={tableColumns({
            handleEdit,
            handleDelete,
            isEditable,
          })}
          tableData={resources || []}
          loading={loading}
          next={!!next}
          previous={tokenQueue.length > 0}
          handlePreviousPage={handlePrevious}
          handleNextPage={handleNext}
          pageSize={pageSize}
          handleChangePageSize={handleChangePageSize}
        />
      </div>
      <InboundKeywordsFormModal
        isVisible={isInboundKeywordsModalOpen}
        setIsVisible={setInboundKeywordsModalOpen}
        formValues={formValues}
        setFormValues={setFormValues}
        accountId={accountId}
        vendorId={vendorId}
        onBulkCreateComplete={onBulkCreateComplete}
      />
      <DeleteModal
        isVisible={isDeleteModalOpen}
        setIsVisible={setDeleteModalOpen}
        id={deleteItem?.id}
        accountId={accountId}
        vendorId={vendorId}
        type="Inbound Keywords"
        label={deleteItem?.keyword}
        deleteAction={deleteInboundKeywords}
      />
      <Toast toasts={toasts} setToasts={setToasts} />
    </div>
  );
}

export default InboundKeywords;
