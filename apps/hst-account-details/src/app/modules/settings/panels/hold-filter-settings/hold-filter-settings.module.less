.wrapper {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  gap: 16px;
}

.titleRight {
  :first-child {
    margin-right: 15px;
  }
}

.left {
  width: 200px;
}

.right {
  margin-left: 50px;
}

.card {
  padding: 24px;
  margin: 20px 0;
  border-radius: var(--sinch-comp-card-shape-radius);
  border: 1px solid var(--sinch-comp-card-color-white-default-border-initial);
  background-color: var(
    --sinch-comp-card-color-white-default-background-initial
  );
  box-shadow: var(--sinch-comp-card-shadow-initial);
  overflow: hidden;
}

.title {
  margin-bottom: 32px;
  display: flex;
  justify-content: space-between;
}

.row {
  display: flex;
  flex-direction: row;
  margin-bottom: 16px;
  .left {
    // width: 30%;
  }
  .right {
    width: 70%;
  }
}

.iconEdit {
  margin-left: 15px;
}
