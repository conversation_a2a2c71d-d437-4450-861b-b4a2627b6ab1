/* eslint-disable @nx/enforce-module-boundaries */
import { Text, Toggle, Toast } from 'nectary';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from 'apps/hst-account-details/src/app/types/store';
import { useEffect, useState } from 'react';
import {
  isNonEmptyString,
  customDateTimeFormatReadable,
} from 'helpers';
import { ToastItem } from 'apps/hst-account-details/src/app/types';
import { LOADING_STATUS } from 'apps/hst-account-details/src/app/constants';

import { fetchHoldFilterSettings, updateHoldFilters } from '../../../../redux/settings/hold-filter-settings/hold-filter-settings-slice';

import styles from "./hold-filter-settings.module.less"

type HoldFilterSettingsProps = {
  vendorId: string,
  accountId: string,
  isEditable: boolean,
}

const HoldFilterSettings = ({ vendorId, accountId, isEditable }: HoldFilterSettingsProps) => {
  const rootState = useSelector((state: RootState) => state || {});
  const { holdFilterSettings } = rootState;
  const { data, updateLoading } = holdFilterSettings || {};
  const [toasts, setToasts] = useState<ToastItem[]>([]);

  const dispatch = useDispatch()

  const handleAutoAprrove = (value: boolean) => {
    dispatch(updateHoldFilters
      ({
        vendorId,
        accountId,
        autoApprove: value
      }))
  }

  useEffect(() => {
    dispatch(
      fetchHoldFilterSettings({
        vendorId,
        accountId,
      })
    );
  }, [accountId, dispatch, vendorId]);

  const renderRow = (title: string, value?: string) => (
    <div className={styles.row}>
      <div className={styles.left}>
        <Text type="s" inline>
          {title}
        </Text>
      </div>
      <div className={styles.right}>
        <>
          <Text type="s" inline emphasized>
            {value || '-'}
          </Text>
        </>
      </div>
    </div>
  );
  useEffect(() => {
    let newToasts = [];
    if (
      updateLoading === LOADING_STATUS.SUCCEEDED ||
      updateLoading === LOADING_STATUS.FAILED
    ) {
      const text =
        updateLoading === LOADING_STATUS.SUCCEEDED
          ? `Successful`
          : `Failed`;
      newToasts = toasts.concat({
        id: accountId,
        text,
        type: updateLoading === LOADING_STATUS.SUCCEEDED ? 'success' : 'error',
      });
      setToasts(newToasts);
      dispatch(
        fetchHoldFilterSettings({
          vendorId,
          accountId,
        })
      );
    }
  }, [accountId, dispatch, updateLoading, vendorId]);

  const renderDate = (title: string, value?: string) => (
    <div className={styles.row}>
      <div className={styles.left}>
        <Text type="s" inline>
          {title}
        </Text>
      </div>
      <div className={styles.right}>
        <>
          <Text type="s" inline emphasized>
            {isNonEmptyString(value)
              ? customDateTimeFormatReadable({
                datetime: value,
                showTime: true,
              })
              : '-'}
          </Text>
        </>
      </div>
    </div>
  );

  const renderAutoApproveBroadcast = (title: string, value?: boolean, handleToggle?: (value: boolean) => void) => (
    <div className={styles.row} >
      <div className={styles.left}>
        <Text type="s" inline>
          {title}
        </Text>
      </div>
      <div className={styles.right}>
        <Toggle checked={value} onChange={handleToggle} disabled={updateLoading === LOADING_STATUS.LOADING || !isEditable} />
      </div>
    </div>
  );

  return (
    <div>
      <div className={styles.title}>
        <Text type="l" inline emphasized>
          Hold Filter Settings
        </Text>
      </div>
      {renderAutoApproveBroadcast('Auto approve broadcast', data?.autoApprove, handleAutoAprrove)}
      {renderRow('Account name', data?.updatedByUsername)}
      {renderDate('Date updated', data?.updatedAt)}
      <Toast toasts={toasts} setToasts={setToasts} />
    </div>
  );
}

export default HoldFilterSettings;
