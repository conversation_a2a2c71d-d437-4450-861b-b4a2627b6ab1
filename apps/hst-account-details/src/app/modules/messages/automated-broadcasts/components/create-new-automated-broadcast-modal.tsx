import {
  Button,
  Checkbox,
  Dialog,
  Input,
  RadioGroup,
  Select,
  Spinner,
  Text,
  TimePicker,
  Toast,
} from 'nectary';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { LOADING_STATUS } from '../../../../constants';
import {
  createAutomatedBroadcast,
  updateAutomatedBroadcast,
  resetStatus,
} from '../../../../redux/messages/automated-broadcast-slice';
import { fetchTemplates } from '../../../../redux/templates/templates-slice';
import { ToastItem } from '../../../../types';
import {
  CreateAutomatedBroadcastParams,
  FileSettings,
} from '../../../../types/automated-broadcast';
import { RootState } from '../../../../types/store';
import { BroadcastTemplateType } from '../../../../types/templates/templates';
import styles from './create-new-automated-broadcast-modal.less';
import { fetchAutomatedBroadcasts } from '../../../../redux/messages/messages-slice';

const DEFAULT_PAGE_SIZE = 10;
interface FormData {
  accountId: string;
  template: string;
  timeToSend: string;
  daysOfWeek: number[];
  sendPeriod: 'once' | 'period';
  sendPeriodHours?: string;
  path: string;
  sftpUsername: string;
  sftpPassword: string;
  host: string;
  notificationEmails: string;
  filenameOption: 'dynamic' | 'static' | 'custom';
  filename: string;
  phoneHeader: string;
  accountHeader: string;
  fileHeaderRow: boolean;
  fileHeaders: string[];
}

interface FormErrors {
  template?: string;
  path?: string;
  sftpUsername?: string;
  sftpPassword?: string;
  host?: string;
  notificationEmails?: string;
  phoneHeader?: string;
  fileHeaders?: string;
  filename?: string;
  sendPeriodHours?: string;
}

const getFilenameOption = (fileSettings: FileSettings) => {
  if (fileSettings.isCustomFilename) return 'custom';
  return fileSettings.filename ? 'static' : 'dynamic';
};

const CreateNewAutomatedBroadcastModal: React.FC<{
  accountId: string;
  vendorId: string;
  isVisible: boolean;
  isEdit: boolean;
  onCancel: () => void;
}> = ({ accountId, vendorId, isVisible, isEdit, onCancel }) => {
  const dispatch = useDispatch();
  const rootState = useSelector((state: RootState) => state);
  const { templates, automatedBroadcasts } = rootState || {};
  const { data: templatesData, loading: templatesLoading } = templates || {};
  const {
    data: { byId: automatedBroadcastById },
    loading: {
      create: createAutomatedBroadcastLoading,
      update: updateAutomatedBroadcastLoading,
      getById: getAutomatedBroadcastByIdLoading,
    },
    error,
  } = automatedBroadcasts || {};

  const [toasts, setToasts] = useState<ToastItem[]>([]);

  const templatesOptions = templatesData?.resources.map(
    (template: BroadcastTemplateType) => ({
      value: template.id,
      label: template.name,
    })
  );

  useEffect(() => {
    dispatch(fetchTemplates({ accountId, vendorId, size: 1000 }));

    if (isEdit && automatedBroadcastById) {
      setFormData({
        accountId,
        template: automatedBroadcastById.templateId,
        timeToSend: `${String(
          automatedBroadcastById.scheduleSettings.hour
        ).padStart(2, '0')}:${String(
          automatedBroadcastById.scheduleSettings.minute
        ).padStart(2, '0')}`,
        daysOfWeek: automatedBroadcastById.scheduleSettings.days,
        sendPeriod: automatedBroadcastById.scheduleSettings.staggerWindow
          ? 'period'
          : 'once',
        sendPeriodHours: automatedBroadcastById.scheduleSettings.staggerWindow
          ? (
              automatedBroadcastById.scheduleSettings.staggerWindow / 3600
            ).toString()
          : '',
        path: automatedBroadcastById.sftpSettings.path,
        sftpUsername: automatedBroadcastById.sftpSettings.username,
        sftpPassword: automatedBroadcastById.sftpSettings.password,
        host: automatedBroadcastById.sftpSettings.host,
        notificationEmails: automatedBroadcastById.customerEmails.join(', '),
        filenameOption: getFilenameOption(automatedBroadcastById.fileSettings),
        filename: automatedBroadcastById.fileSettings.filename,
        phoneHeader: automatedBroadcastById.fileSettings.phoneHeader,
        accountHeader: automatedBroadcastById.fileSettings.accountHeader,
        fileHeaderRow: automatedBroadcastById.fileSettings.fileHasHeaderRow,
        fileHeaders: automatedBroadcastById.fileSettings.headers || [''],
      });
    }
  }, [accountId, dispatch, vendorId, isEdit, automatedBroadcastById]);

  const [formData, setFormData] = useState<FormData>({
    accountId,
    template: '',
    timeToSend: '',
    daysOfWeek: [],
    sendPeriod: 'once',
    sendPeriodHours: '',
    path: '',
    sftpUsername: '',
    sftpPassword: '',
    host: '',
    notificationEmails: '',
    filenameOption: 'dynamic',
    filename: '',
    phoneHeader: '',
    accountHeader: '',
    fileHeaderRow: false,
    fileHeaders: [''],
  });

  const [errors, setErrors] = useState<FormErrors>({});

  const daysOfWeek = [
    { value: 1, label: 'Monday' },
    { value: 2, label: 'Tuesday' },
    { value: 3, label: 'Wednesday' },
    { value: 4, label: 'Thursday' },
    { value: 5, label: 'Friday' },
    { value: 6, label: 'Saturday' },
    { value: 7, label: 'Sunday' },
  ];

  const hostOptions = [
    { value: 'sftp.messagemedia.com', label: 'sftp.messagemedia.com' },
    {
      value: 'sftp-broadcasts.messagemedia.com',
      label: 'sftp-broadcasts.messagemedia.com',
    },
  ];

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.template) newErrors.template = 'Please select one option';
    if (!formData.path) newErrors.path = 'Path is required';
    if (!formData.sftpUsername)
      newErrors.sftpUsername = 'SFTP Username is required';
    if (!formData.sftpPassword)
      newErrors.sftpPassword = 'SFTP Password is required';
    if (!formData.host) newErrors.host = 'Please select one option';
    if (!formData.notificationEmails)
      newErrors.notificationEmails = 'Notification Emails is required';
    if (!formData.phoneHeader)
      newErrors.phoneHeader = 'Phone header is required';
    if (
      !formData.fileHeaderRow &&
      formData.fileHeaders.some((header) => !header)
    ) {
      newErrors.fileHeaders = 'All file headers are required';
    }
    if (formData.filenameOption !== 'dynamic' && !formData.filename) {
      newErrors.filename = 'Filename is required';
    }

    if (formData.sendPeriod === 'period') {
      if (!formData.sendPeriodHours) {
        newErrors.sendPeriodHours = 'Hours is required';
      } else {
        const hours = Number(formData.sendPeriodHours);
        if (isNaN(hours)) {
          newErrors.sendPeriodHours = 'Must be a number';
        } else if (hours < 0.5 || hours > 24) {
          newErrors.sendPeriodHours = 'Must be between 0.5 and 24';
        }
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const addToast = (text: any, type: any) => {
    const newToast = { id: Date.now().toString(), text, type };
    setToasts((prevToasts) => [...prevToasts, newToast]);
  };

  useEffect(
    () => () => {
      dispatch(resetStatus());
    },
    [dispatch]
  );

  useEffect(() => {
    if (createAutomatedBroadcastLoading === LOADING_STATUS.SUCCEEDED) {
      addToast('Automated broadcast created successfully', 'success');
    } else if (createAutomatedBroadcastLoading === LOADING_STATUS.FAILED) {
      addToast(
        error?.create || 'Failed to create automated broadcast',
        'error'
      );
    }
  }, [createAutomatedBroadcastLoading]);

  useEffect(() => {
    if (updateAutomatedBroadcastLoading === LOADING_STATUS.SUCCEEDED) {
      addToast('Automated broadcast updated successfully', 'success');
    } else if (updateAutomatedBroadcastLoading === LOADING_STATUS.FAILED) {
      addToast(
        error?.update || 'Failed to update automated broadcast',
        'error'
      );
    }
  }, [updateAutomatedBroadcastLoading]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      const selectedTemplate = templatesData?.resources.find(
        (template: BroadcastTemplateType) => template.id === formData.template
      );

      const fileSettings: FileSettings = {
        filename:
          formData.filenameOption === 'dynamic' ? '' : formData.filename,
        isCustomFilename: formData.filenameOption === 'custom',
        phoneHeader: formData.phoneHeader,
        ...(formData.accountHeader && {
          accountHeader: formData.accountHeader,
        }),
        fileHasHeaderRow: formData.fileHeaderRow,
        headers:
          formData.fileHeaderRow &&
          formData.fileHeaders.length === 1 &&
          formData.fileHeaders[0] === ''
            ? ['Default header']
            : formData.fileHeaders,
      };

      const payload: CreateAutomatedBroadcastParams = {
        vendorId,
        accountId,
        templateId: formData.template,
        ...(isEdit ? {} : { templateName: selectedTemplate?.name }),
        customerEmails: formData.notificationEmails
          .split(',')
          .map((email) => email.trim())
          .filter((email) => email.length > 0),
        fileSettings,
        scheduleSettings: {
          minute: formData.timeToSend
            ? parseInt(formData.timeToSend.split(':')[1], 10)
            : 0,
          hour: formData.timeToSend
            ? parseInt(formData.timeToSend.split(':')[0], 10)
            : 0,
          days: formData.daysOfWeek,
          ...(formData.sendPeriod === 'period' && {
            staggerWindow: Number(formData.sendPeriodHours) * 3600,
          }),
        },
        sftpSettings: {
          host: formData.host,
          username: formData.sftpUsername,
          password: formData.sftpPassword,
          path: formData.path,
        },
      };

      onCancel();
      if (isEdit) {
        await dispatch(
          updateAutomatedBroadcast({
            id: automatedBroadcastById.id,
            ...payload,
          })
        );
      } else {
        await dispatch(createAutomatedBroadcast(payload));
      }
      handleCancel();
    }
  };

  const handleAddHeader = () => {
    setFormData((prev) => ({
      ...prev,
      fileHeaders: [...prev.fileHeaders, ''],
    }));
  };

  const handleRemoveHeader = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      fileHeaders: prev.fileHeaders.filter((_, i) => i !== index),
    }));
  };

  const handleHeaderChange = (index: number, value: string) => {
    setFormData((prev) => ({
      ...prev,
      fileHeaders: prev.fileHeaders.map((header, i) =>
        i === index ? value : header
      ),
    }));
    setErrors((prev) => ({ ...prev, fileHeaders: undefined }));
  };

  const handleDayChange = (dayValue: number, checked: boolean) => {
    setFormData((prevData) => ({
      ...prevData,
      daysOfWeek: checked
        ? [...prevData.daysOfWeek, dayValue]
        : prevData.daysOfWeek.filter((day) => day !== dayValue),
    }));
  };

  const resetFormData = () => {
    setFormData({
      accountId,
      template: '',
      timeToSend: '',
      daysOfWeek: [],
      sendPeriod: 'once',
      sendPeriodHours: '',
      path: '',
      sftpUsername: '',
      sftpPassword: '',
      host: '',
      notificationEmails: '',
      filenameOption: 'dynamic',
      filename: '',
      phoneHeader: '',
      accountHeader: '',
      fileHeaderRow: false,
      fileHeaders: [''],
    });
  };

  const handleCancel = async () => {
    resetFormData();
    onCancel();
    await dispatch(
      fetchAutomatedBroadcasts({
        vendorId,
        accountId,
        size: DEFAULT_PAGE_SIZE,
      })
    );
  };

  return (
    <div className={styles['dialog-wrapper']}>
      <Dialog
        isOpen={isVisible}
        caption={
          isEdit ? 'Edit automated broadcast' : 'Automated broadcast scheduling'
        }
        onClose={handleCancel}
      >
        {getAutomatedBroadcastByIdLoading === LOADING_STATUS.LOADING ? (
          <div className={styles.spinnerWrap}>
            <Spinner size="m" />
          </div>
        ) : (
          <form onSubmit={handleSubmit} className={styles['form-container']}>
            <div className={styles['form-sections-wrapper']}>
              <div className={styles['form-section']}>
                <div className={styles['form-group']}>
                  <Input
                    label="Account ID"
                    value={formData.accountId}
                    disabled
                    fieldStyles={{ width: '100%' }}
                    p
                  />
                </div>

                <div className={styles['form-group']}>
                  <Text className={styles['form-label']}>Template</Text>
                  <Select
                    options={templatesOptions}
                    value={formData.template}
                    rows={10}
                    placeholder="-- Please select --"
                    onSelect={(value: string) => {
                      setFormData({ ...formData, template: value });
                      setErrors((prev) => ({ ...prev, template: undefined }));
                    }}
                  />
                  {errors.template && (
                    <p className={styles['error-text']}>{errors.template}</p>
                  )}
                </div>

                <div className={styles['form-group']}>
                  <Text className={styles['form-label']}>
                    Time of day to send
                  </Text>
                  <p className={styles['form-helper-text']}>
                    All times will use the account&apos;s time zone and account
                    for daylight savings.
                  </p>
                  <TimePicker
                    value={formData.timeToSend}
                    onChange={(value: string) =>
                      setFormData({ ...formData, timeToSend: value })
                    }
                  />
                </div>
              </div>

              <div className={styles['form-section']}>
                <h2 className={styles['form-title']}>Scheduling</h2>

                <div className={styles['form-group']}>
                  <Text className={styles['form-label']}>
                    Days of the week to send
                  </Text>
                  <div className={styles['checkbox-group']}>
                    {daysOfWeek.map((day) => (
                      <Checkbox
                        key={day.value}
                        label={day.label}
                        value={formData.daysOfWeek.includes(day.value)}
                        onChange={(checked: boolean) =>
                          handleDayChange(day.value, checked)
                        }
                      />
                    ))}
                  </div>
                </div>

                <div className={styles['form-group']}>
                  <Text className={styles['form-label']}>Send Period</Text>
                  <RadioGroup
                    value={formData.sendPeriod}
                    onSelect={(value: 'once' | 'period') =>
                      setFormData({
                        ...formData,
                        sendPeriod: value,
                        sendPeriodHours:
                          value === 'once' ? '' : formData.sendPeriodHours,
                      })
                    }
                    options={[
                      {
                        label: 'Send all messages at once',
                        value: 'once',
                        key: 'once',
                      },
                      {
                        label: 'Send messages over a period of time',
                        value: 'period',
                        key: 'period',
                      },
                    ]}
                  />

                  {formData.sendPeriod === 'period' && (
                    <div className={styles['form-group']}>
                      <Input
                        label="Send messages evenly over"
                        type="number"
                        value={formData.sendPeriodHours}
                        onChange={(value: string) => {
                          setFormData({ ...formData, sendPeriodHours: value });
                          setErrors((prev) => ({
                            ...prev,
                            sendPeriodHours: undefined,
                          }));
                        }}
                        placeholder="Hours"
                        fieldStyles={{ width: '100%' }}
                      />
                      {errors.sendPeriodHours && (
                        <p className={styles['error-text']}>
                          {errors.sendPeriodHours}
                        </p>
                      )}
                    </div>
                  )}
                </div>
              </div>

              <div className={styles['form-section']}>
                <h2 className={styles['form-title']}>Connection Details</h2>

                <div className={styles['form-group']}>
                  <Input
                    label="Path"
                    value={formData.path}
                    onChange={(value: string) => {
                      setFormData({ ...formData, path: value });
                      setErrors((prev) => ({ ...prev, path: undefined }));
                    }}
                    placeholder="directory/subdirectory"
                    fieldStyles={{ width: '100%' }}
                  />
                  {errors.path && (
                    <p className={styles['error-text']}>{errors.path}</p>
                  )}
                  <p className={styles['form-helper-text']}>
                    Example: &quot;directory/subdirectory&quot; - omit leading
                    and trailing forward slash.
                  </p>
                </div>

                <div>
                  <div className={styles['form-group']}>
                    <Input
                      label="SFTP Username"
                      value={formData.sftpUsername}
                      onChange={(value: string) => {
                        setFormData({ ...formData, sftpUsername: value });
                        setErrors((prev) => ({
                          ...prev,
                          sftpUsername: undefined,
                        }));
                      }}
                      fieldStyles={{ width: '100%' }}
                    />
                    {errors.sftpUsername && (
                      <p className={styles['error-text']}>
                        {errors.sftpUsername}
                      </p>
                    )}
                  </div>
                  <div className={styles['form-group']}>
                    <Input
                      label="SFTP Password"
                      type="password"
                      value={formData.sftpPassword}
                      onChange={(value: string) => {
                        setFormData({ ...formData, sftpPassword: value });
                        setErrors((prev) => ({
                          ...prev,
                          sftpPassword: undefined,
                        }));
                      }}
                      fieldStyles={{ width: '100%' }}
                    />
                    {errors.sftpPassword && (
                      <p className={styles['error-text']}>
                        {errors.sftpPassword}
                      </p>
                    )}
                  </div>
                </div>

                <div className={styles['form-group']}>
                  <Text className={styles['form-label']}>Host</Text>
                  <Select
                    options={hostOptions}
                    value={formData.host}
                    placeholder="-- Please select --"
                    onSelect={(value: string) => {
                      setFormData({ ...formData, host: value });
                      setErrors((prev) => ({ ...prev, host: undefined }));
                    }}
                  />
                  {errors.host && (
                    <p className={styles['error-text']}>{errors.host}</p>
                  )}
                </div>
              </div>

              <div className={styles['form-section']}>
                <div className={styles['form-group']}>
                  <Input
                    label="Notification Emails"
                    value={formData.notificationEmails}
                    onChange={(value: string) => {
                      setFormData({ ...formData, notificationEmails: value });
                      setErrors((prev) => ({
                        ...prev,
                        notificationEmails: undefined,
                      }));
                    }}
                    placeholder="Insert emails you want to notify"
                    fieldStyles={{ width: '100%' }}
                  />
                  <p className={styles['form-helper-text']}>
                    Example: <EMAIL>, <EMAIL> - The
                    text input accepts multiple values, separated by commas.
                  </p>
                  {errors.notificationEmails && (
                    <p className={styles['error-text']}>
                      {errors.notificationEmails}
                    </p>
                  )}
                </div>

                <div className={styles['form-group']}>
                  <Text className={styles['form-label']}>Filename options</Text>
                  <RadioGroup
                    value={formData.filenameOption}
                    onSelect={(value: 'dynamic' | 'static' | 'custom') =>
                      setFormData({
                        ...formData,
                        filenameOption: value,
                        filename: value === 'dynamic' ? '' : formData.filename,
                      })
                    }
                    options={[
                      {
                        label: 'Dynamic date filename (e.g. "ddmmyyyy.csv")',
                        value: 'dynamic',
                        key: 'dynamic',
                      },
                      {
                        label: 'Static filename',
                        value: 'static',
                        key: 'static',
                      },
                      {
                        label: 'Custom date filename',
                        value: 'custom',
                        key: 'custom',
                      },
                    ]}
                  />
                </div>

                {formData.filenameOption !== 'dynamic' && (
                  <div className={styles['form-group']}>
                    <Input
                      label="File name"
                      value={formData.filename}
                      onChange={(value: string) => {
                        setFormData({ ...formData, filename: value });
                        setErrors((prev) => ({ ...prev, filename: undefined }));
                      }}
                      placeholder={
                        formData.filenameOption === 'custom'
                          ? 'Enter custom filename pattern'
                          : 'Enter static filename'
                      }
                      fieldStyles={{ width: '100%' }}
                    />
                    {errors.filename && (
                      <p className={styles['error-text']}>{errors.filename}</p>
                    )}
                  </div>
                )}

                <div>
                  <div className={styles['form-group']}>
                    <Input
                      label="Phone header"
                      value={formData.phoneHeader}
                      onChange={(value: string) => {
                        setFormData({ ...formData, phoneHeader: value });
                        setErrors((prev) => ({
                          ...prev,
                          phoneHeader: undefined,
                        }));
                      }}
                      fieldStyles={{ width: '100%' }}
                    />
                    {errors.phoneHeader && (
                      <p className={styles['error-text']}>
                        {errors.phoneHeader}
                      </p>
                    )}
                  </div>
                  <div className={styles['form-group']}>
                    <Input
                      label="Account header"
                      value={formData.accountHeader}
                      onChange={(value: string) =>
                        setFormData({ ...formData, accountHeader: value })
                      }
                      fieldStyles={{ width: '100%' }}
                    />
                  </div>
                </div>

                <div className={styles['form-group']}>
                  <Text className={styles['form-label']}>File header row</Text>

                  <Checkbox
                    label="File contains column headings in the first row"
                    value={formData.fileHeaderRow}
                    onChange={(checked: boolean) =>
                      setFormData({
                        ...formData,
                        fileHeaderRow: checked,
                      })
                    }
                  />
                </div>

                {!formData.fileHeaderRow && (
                  <div className={styles['form-section']}>
                    <Text className={styles['form-label']}>File headers</Text>

                    {formData.fileHeaders.map((header, index) => (
                      <div className={styles['form-group']}>
                        <div className={styles['header-row']}>
                          <Input
                            value={header}
                            onChange={(value: string) =>
                              handleHeaderChange(index, value)
                            }
                            placeholder="Header value"
                            fieldStyles={{ width: '100%' }}
                          />
                          {index > 0 && (
                            <Button
                              label="Remove"
                              variant="secondary"
                              onClick={() => handleRemoveHeader(index)}
                              className={styles['remove-button']}
                            />
                          )}
                        </div>
                      </div>
                    ))}
                    {errors.fileHeaders && (
                      <p className={styles['error-text']}>
                        {errors.fileHeaders}
                      </p>
                    )}

                    <Button
                      label="Add header"
                      variant="secondary"
                      onClick={handleAddHeader}
                      className={styles['add-header-button']}
                    />
                  </div>
                )}
              </div>
            </div>

            <div className={styles['form-actions']}>
              <Button
                label="Cancel"
                variant="secondary"
                onClick={handleCancel}
                className={styles['btn-secondary']}
              />

              <Button
                label="Save automated broadcast"
                variant="primary"
                type="submit"
                className={styles['btn-primary']}
                onClick={handleSubmit}
              />
            </div>
          </form>
        )}
      </Dialog>
      <Toast toasts={toasts} setToasts={setToasts} />
    </div>
  );
};

export default CreateNewAutomatedBroadcastModal;
