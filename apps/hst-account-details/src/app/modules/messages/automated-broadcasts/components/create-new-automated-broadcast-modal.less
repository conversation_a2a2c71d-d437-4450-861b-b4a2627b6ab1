.dialog-wrapper {
  sinch-dialog {
    --sinch-comp-dialog-width: 700px;
    --sinch-comp-dialog-height: 95vh;
    --sinch-comp-dialog-max-width: 900px;
    --sinch-comp-dialog-max-height: 95vh;
  }
}
.form-container {
  max-width: 48rem;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  flex-direction: column;
  height: calc(90vh - 8rem);
}

.form-sections-wrapper {
  flex: 1;
  overflow-y: auto;
  padding-right: 1rem;
}

.form-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.form-section {
  margin-bottom: 2rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.25rem;
}

.form-input-disabled {
  background-color: #f7fafc;
}

.form-helper-text {
  font-size: 0.75rem;
  color: #718096;
  margin-top: 0.25rem;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.grid-2-cols {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e2e8f0;
  margin-top: auto;
  background: white;
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  cursor: pointer;
}

.btn-primary {
  background-color: #4a5568;
  color: white;

  &:hover {
    background-color: #2d3748;
  }
}

.btn-secondary {
  color: #4a5568;

  &:hover {
    color: #2d3748;
  }
}

.header-row {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.remove-button {
  flex-shrink: 0;
}

.add-header-button {
  margin-top: 1rem;
}

.error-text {
  color: red !important;
  font-size: 12px;
  margin-top: 4px;
}

.spinnerWrap {
  width: 100%;
  height: 100%;
  opacity: 0.3;
  display: flex;
  align-items: center;
}
