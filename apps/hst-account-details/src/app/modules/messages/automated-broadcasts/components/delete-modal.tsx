import { Dialog, Button, Text } from 'nectary';

import styles from './delete-modal.module.less';

const DeleteModal = ({
  isVisible,
  setIsVisible,
  broadcastName,
  onDelete
}: {
  isVisible: boolean;
  setIsVisible: (isVisible: boolean) => void;
  broadcastName: string;
  onDelete,
}) => (
  <div className={styles.dialogWrap}>
    <Dialog
      caption="Delete Automated Broadcast"
      isOpen={isVisible}
      onClose={() => setIsVisible(false)}
    >
      <Text>{`Are you sure you want to delete the automated broadcast "${broadcastName}"? This cannot be undone.`}</Text>
      <div className={styles.btnGroup}>
        <div className={styles.btnCancel}>
          <Button type="secondary" label="Cancel" onClick={() => setIsVisible(false)} />
        </div>

        <Button
          label="Delete"
          onClick={() => {
            onDelete();
            setIsVisible(false);
          }}
        />
      </div>
    </Dialog>
  </div>
);

export default DeleteModal;
