import _isEmpty from 'lodash/isEmpty';
import { Button, Table, Text, Toast } from 'nectary';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import {
  deleteAutomatedBroadcast,
  fetchAutomatedBroadcasts,
} from '../../../redux/messages/messages-slice';
import { AutomatedBroadcastType } from '../../../types/messages';

import { LOADING_STATUS } from '../../../constants/index';
import { getAutomatedBroadcastById } from '../../../redux/messages/automated-broadcast-slice';
import { ToastItem } from '../../../types';
import { RootState } from '../../../types/store';
import styles from './automated-broadcasts.module.less';
import CreateNewAutomatedBroadcastModal from './components/create-new-automated-broadcast-modal';
import DeleteModal from './components/delete-modal';

const DEFAULT_PAGE_SIZE = 10;

type AutomatedBroadcastsProps = {
  vendorId: string;
  accountId: string;
  isEditable: boolean;
};

const tableColumns = ({
  isEditable,
  handleEdit,
  handleDelete,
}: {
  isEditable: boolean;
  handleEdit: (broadcast: AutomatedBroadcastType) => void;
  handleDelete: (broadcast: AutomatedBroadcastType) => void;
}) => [
  {
    title: 'Broadcast Name',
    index: 'name',
    sort: false,
    align: 'left',
    render: (value: AutomatedBroadcastType) => (
      <Text type="s">{value.name}</Text>
    ),
  },
  {
    title: 'Recurrence',
    index: 'recurrence',
    sort: false,
    align: 'left',
    render: (value: AutomatedBroadcastType) => (
      <Text type="s">{value.recurrence}</Text>
    ),
  },
  {
    title: 'Broadcast Id',
    index: 'id',
    sort: false,
    align: 'left',
    render: (value: AutomatedBroadcastType) => <Text type="s">{value.id}</Text>,
  },
  {
    title: 'File',
    index: 'filePath',
    sort: false,
    align: 'left',
    render: (value: AutomatedBroadcastType) => (
      <Text type="s">{value.filePath}</Text>
    ),
  },
  {
    title: 'Actions',
    index: 'id',
    sort: false,
    align: 'left',
    render: (value: AutomatedBroadcastType) => (
      <div className={styles.btnGroup}>
        <div className={styles.editBtn}>
          <Button
            label="Edit"
            type="secondary"
            onClick={() => handleEdit(value)}
            disabled={!isEditable}
          />
        </div>
        <Button
          label="Delete"
          type="destructive"
          onClick={() => handleDelete(value)}
          disabled={!isEditable}
        />
      </div>
    ),
  },
];

const AutomatedBroadcasts = ({
  vendorId,
  accountId,
  isEditable,
}: AutomatedBroadcastsProps) => {
  const dispatch = useDispatch();
  const rootState = useSelector((state: RootState) => state || {});
  const [tokenQueue, setTokenQueue] = useState([]);
  const [isDeleteModalVisible, setDeleteModalVisible] = useState(false);
  const [deletedItem, setDeletedItem] = useState(null);
  const [toasts, setToasts] = useState<ToastItem[]>([]);
  const [isNewAutomatedBroadcastVisible, setIsNewAutomatedBroadcastVisible] =
    useState(false);
  const [isEditAutomatedBroadcast, setIsEditAutomatedBroadcast] =
    useState(false);
  const [pageSize, setPageSize] = useState<number>(DEFAULT_PAGE_SIZE);

  const { accountMessages } = rootState;
  const {
    automatedBroadcasts,
    automatedBroadcastsLoading,
    deleteAutomatedBroadcastLoading,
  } = accountMessages || {};
  const { resources, pagination } = automatedBroadcasts || {};
  const { next } = pagination || {};

  useEffect(() => {
    dispatch(
      fetchAutomatedBroadcasts({
        vendorId,
        accountId,
        size: pageSize,
      })
    );
  }, []);

  const handlePrevious = () => {
    tokenQueue.pop();
    setTokenQueue(tokenQueue);
    const tokensLen = tokenQueue.length;

    setTimeout(() => {
      dispatch(
        fetchAutomatedBroadcasts({
          accountId,
          vendorId,
          size: pageSize,
          next: tokensLen ? tokenQueue[tokensLen - 1] : undefined,
        })
      );
    });
  };

  useEffect(() => {
    if (_isEmpty(deletedItem)) return;
    let newToasts = [];
    if (
      deleteAutomatedBroadcastLoading === LOADING_STATUS.SUCCEEDED ||
      deleteAutomatedBroadcastLoading === LOADING_STATUS.FAILED
    ) {
      const text =
        deleteAutomatedBroadcastLoading === LOADING_STATUS.SUCCEEDED
          ? `Successfully deleted automated broadcast ${deletedItem.name}`
          : `Failed to delete automated broadcast ${deletedItem.name}`;
      newToasts = toasts.concat({
        id: deletedItem.id,
        text,
        type:
          deleteAutomatedBroadcastLoading === LOADING_STATUS.SUCCEEDED
            ? 'success'
            : 'error',
      });
      setToasts(newToasts);
      setDeletedItem(null);
      setDeleteModalVisible(false);
      setTimeout(() => {
        const tokensLen = tokenQueue.length;
        dispatch(
          fetchAutomatedBroadcasts({
            accountId,
            vendorId,
            size: pageSize,
            next: tokensLen ? tokenQueue[tokensLen - 1] : undefined,
          })
        );
      }, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [deleteAutomatedBroadcastLoading]);

  const handleNext = (token: string) => {
    tokenQueue.push(token);
    setTokenQueue(tokenQueue);

    setTimeout(() => {
      dispatch(
        fetchAutomatedBroadcasts({
          accountId,
          vendorId,
          size: pageSize,
          next: token,
        })
      );
    });
  };

  const handleCreateAutomatedBroadcast = () => {
    setIsNewAutomatedBroadcastVisible(true);
  };

  const handleCancelCreate = () => {
    setIsNewAutomatedBroadcastVisible(false);
    setIsEditAutomatedBroadcast(false);
  };

  const handleEdit = (item: AutomatedBroadcastType) => {
    dispatch(getAutomatedBroadcastById({ vendorId, accountId, id: item.id }));
    setIsEditAutomatedBroadcast(true);
    setIsNewAutomatedBroadcastVisible(true);
  };

  const handleDelete = (item: AutomatedBroadcastType) => {
    setDeleteModalVisible(true);
    setDeletedItem(item);
  };

  const confirmDelete = () => {
    if (!deletedItem) return;

    dispatch(
      deleteAutomatedBroadcast({
        vendorId,
        accountId,
        broadcastId: deletedItem.id,
      })
    );
  };

  const handleChangePageSize = (newSize: number) => {
    setPageSize(newSize);
    dispatch(
      fetchAutomatedBroadcasts({
        vendorId,
        accountId,
        size: newSize,
      })
    );
  };

  return (
    <div>
      <CreateNewAutomatedBroadcastModal
        vendorId={vendorId}
        accountId={accountId}
        onCancel={handleCancelCreate}
        isVisible={isNewAutomatedBroadcastVisible}
        isEdit={isEditAutomatedBroadcast}
      />
      <div className={styles.actionWrap}>
        <Button
          label="New Automated Broadcast"
          onClick={handleCreateAutomatedBroadcast}
          disabled={!isEditable}
          onCancel={handleCancelCreate}
        />
      </div>
      <div>
        <Table
          hasCheckbox={false}
          tableColumns={tableColumns({ isEditable, handleEdit, handleDelete })}
          tableData={resources || []}
          loading={automatedBroadcastsLoading}
          next={next}
          previous={tokenQueue.length}
          handlePreviousPage={handlePrevious}
          handleNextPage={handleNext}
          pageSize={pageSize}
          handleChangePageSize={handleChangePageSize}
        />
      </div>
      <DeleteModal
        isVisible={isDeleteModalVisible}
        setIsVisible={setDeleteModalVisible}
        broadcastName={deletedItem?.name}
        onDelete={confirmDelete}
      />
      <Toast toasts={toasts} setToasts={setToasts} />
    </div>
  );
};

export default AutomatedBroadcasts;
