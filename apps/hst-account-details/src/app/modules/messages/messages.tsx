import { useEffect, useState } from 'react';

import { getPermissionWith<PERSON><PERSON>, ROLES_KEY } from 'helpers';
import { Tab } from 'nectary';

import AutomatedBroadcasts from './automated-broadcasts/automated-broadcasts';
import DetailedReport from './detailed-report/detailed-report';

import CampaignReports from './campaign-reports/campaign-reports';
import styles from './messages.module.less';

const SUB_TAB_PARAM = 'subTab';

type TabItem = {
  value: number;
  tabName: string;
  text: string;
};

export const getTabs = ({ isAutomatedBroadcastsVisible }) => {
  const tabs = [
    {
      value: 1,
      tabName: 'detailedReport',
      text: 'Detailed Reports',
    },
    {
      value: 2,
      tabName: 'campaignReport',
      text: 'Campaign Reports',
    },
  ];
  if (isAutomatedBroadcastsVisible) {
    tabs.push({
      value: 3,
      tabName: 'automatedBroadcasts',
      text: 'Automated Broadcasts',
    });
  }
  return tabs;
};

const tabContent = ({
  accountId,
  vendorId,
  isDetailedReportVisible,
  isAutomatedBroadcastsEditable,
  isAutomatedBroadcastsVisible,
}: {
  accountId: string;
  vendorId: string;
  isDetailedReportVisible: boolean;
  isAutomatedBroadcastsVisible: boolean;
  isAutomatedBroadcastsEditable: boolean;
}) => {
  const tabList = [
    {
      value: 1,
      content: (
        <DetailedReport
          vendorId={vendorId}
          accountId={accountId}
          isVisible={isDetailedReportVisible}
        />
      ),
    },
    {
      value: 2,
      content: <CampaignReports vendorId={vendorId} accountId={accountId} />,
    },
  ];
  if (isAutomatedBroadcastsVisible) {
    tabList.push({
      value: 3,
      content: (
        <AutomatedBroadcasts
          vendorId={vendorId}
          accountId={accountId}
          isEditable={isAutomatedBroadcastsEditable}
        />
      ),
    });
  }
  return tabList;
};

type MessagesProps = {
  accountId: string;
  vendorId: string;
  roles: string[];
};

const getSubTabIndexByName = (
  subTabName: string | null,
  displayedTabs: TabItem[]
): string => {
  const currentTab = displayedTabs.find(
    (tab: TabItem) => tab.tabName === subTabName
  );
  return currentTab ? currentTab.value.toString() : '1';
};

const Messages = ({ accountId, vendorId, roles }: MessagesProps) => {
  const [currentSubTab, setCurrentSubTab] = useState('1');

  const {
    isVisible: isAutomatedBroadcastsVisible,
    isEditable: isAutomatedBroadcastsEditable,
  } = getPermissionWithKey(ROLES_KEY.ACCOUNT_AUTOMATED_BROADCASTS, roles);

  const { isVisible: isDetailedReportVisible } = getPermissionWithKey(
    ROLES_KEY.ACCOUNT_MESSAGES_DETAILS,
    roles
  );

  const queryParams = new URLSearchParams(window.location.search);
  const tabURLParam = queryParams.get(SUB_TAB_PARAM);
  const displayedTabs = getTabs({
    isAutomatedBroadcastsVisible,
  });

  const onChangeTab = (newTabVal) => {
    setCurrentSubTab(newTabVal);
    const newTab = displayedTabs.find(
      (tab: TabItem) => `${tab.value}` === newTabVal
    );
    queryParams.set(SUB_TAB_PARAM, newTab?.tabName);
    const newQueryString = queryParams.toString();
    const newUrl = `${window.location.origin}${window.location.pathname}?${newQueryString}`;
    window.history.replaceState({ path: newUrl }, '', newUrl);
  };

  useEffect(() => {
    setCurrentSubTab(getSubTabIndexByName(tabURLParam, displayedTabs));
  }, []);

  return (
    <div>
      <div className={styles.tab}>
        <Tab
          currentTab={currentSubTab}
          tabs={displayedTabs}
          tabContent={tabContent({
            accountId,
            vendorId,
            isDetailedReportVisible,
            isAutomatedBroadcastsVisible,
            isAutomatedBroadcastsEditable
          })}
          onChangeTab={onChangeTab}
        />
      </div>
    </div>
  );
};

export default Messages;
