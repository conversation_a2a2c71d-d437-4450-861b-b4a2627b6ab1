import _isEmpty from 'lodash/isEmpty';
import _orderBy from 'lodash/orderBy';
import moment from 'moment';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { RootState } from 'apps/hst-account-details/src/app/types/store';
import {
  Button,
  DateRange,
  Input,
  Link,
  MultiSelect,
  Select,
  Spinner,
  Tab,
  Table,
  Text,
} from 'nectary';

import { LOADING_STATUS } from '../../../constants';
import {
  fetchMessageDetailByMessageId,
  fetchMessagesDetail,
  fetchMessagesInsights,
  fetchMetakeys,
} from '../../../redux/messages/messages-slice';
import { fetchSubAccounts } from '../../../redux/users/users-slice';
import { Message } from '../../../types/messages';
import { SubAccount } from '../../../types/users/users';
import { BoundaryChart } from '../components/boundary-chart';
import { STATUS_OPTIONS } from '../constants/index';
import {
  getDataSets,
  getDateRange,
  getSummaryChartLabel,
} from '../helpers/charts';
import DetailedReportDetailsModal from './components/detailed-report-details-modal';
import styles from './detailed-report.module.less';

type DetailedReportProps = {
  vendorId: string;
  accountId: string;
  isVisible: boolean;
};

type OPTIONS = Array<{
  label: string;
  value: string;
}>;
let ACCOUNT_OPTIONS: OPTIONS = [];
const DEFAULT_DATE_RANGE = [
  moment().subtract('7', 'day').format('DD-MM-YYYY'),
  moment().format('DD-MM-YYYY'),
];

const TABS_INFO = {
  outbound: {
    value: 1,
    text: 'Sent (outbound)',
  },
  inbound: {
    value: 2,
    text: 'Received (inbound)',
  },
};

const DEFAULT_PAGE_SIZE = 25;

const DetailedReport = ({ vendorId, accountId, isVisible }: DetailedReportProps) => {
  const dispatch = useDispatch();
  const rootState = useSelector((state: RootState) => state);
  const { users, accountMessages, accountDetails } = rootState;
  const { messagesInsights, metakeys, messagesInsightsLoading, messagesDetailLoading } =
  accountMessages || {};
  const { details } = accountDetails || {}
  const { label } = details || {}
  const timezone = moment.tz.guess()
  const { subAccountsData } = users || {};
  const { resources: subAccounts = [] } = subAccountsData || {};
  const { resources: metakeyList } = metakeys || {};

  const { messagesDetail } = accountMessages || {};
  const { resources, pagination } = messagesDetail || {};
  const { next } = pagination || {};
  const queryParams = new URLSearchParams(window.location.search);

  const [accounts, setAccounts] = useState([accountId]);
  const [statuses, setStatuses] = useState([]);
  const [dateRange, setDateRange] = useState<string[]>(DEFAULT_DATE_RANGE);
  const [source, setSource] = useState('');
  const [metadataKey, setMetadataKey] = useState(queryParams.get('metadataKey') || '');
  const [metadataValue, setMetadataValue] = useState(queryParams.get('metadataValue') || '');
  const [isAdvancedFiltersVisible, toggleAdvancedFilters] = useState(!!queryParams.get('metadataKey'));
  const [isValidating, setIsValidating] = useState(false);
  const { totalSent, totalBillingUnits, totalReceived, summaries } =
    messagesInsights || {};
  const [currentTab, setCurrentTab] = useState<string>('1');
  const [tokenQueue, setTokenQueue] = useState<string[]>([]);
  const [inboundMessages, setInboundMessages] = useState<Message[]>([]);
  const [outboundMessages, setOutboundMessages] = useState<Message[]>([]);
  const [sorter, setSorter] = useState<{
    sortBy: string | null;
    sortDirection: string | null;
  }>({
    sortBy: null,
    sortDirection: null,
  });
  const [isDRDetailModalVisible, toggleDRDetailModal] = useState(false);
  const [pageSize, setPageSize] = useState<number>(DEFAULT_PAGE_SIZE);

  const [visibleOutboundColumns, setVisibleOutboundColumns] = useState(() => {
    const storedColumns = sessionStorage.getItem('outboundMessagesColumns');
    if (storedColumns) {
      return JSON.parse(storedColumns);
    }
    return [
      'timestamp',
      'sourceAddress',
      'destinationAddress',
      'content',
      'messageId',
      'format',
      'direction',
      'status',
      'statusCode',
      'statusDescription',
      'toCountry',
      'accountId',
      'units'
    ];
  });

  const [visibleInboundColumns, setVisibleInboundColumns] = useState(() => {
    const storedColumns = sessionStorage.getItem('inboundMessagesColumns');
    if (storedColumns) {
      return JSON.parse(storedColumns);
    }
    return [
      'timestamp',
      'sourceAddress',
      'destinationAddress',
      'content',
      'messageId',
      'format',
      'fromCountry',
      'accountId'
    ];
  });

  ACCOUNT_OPTIONS = subAccounts?.map((item: SubAccount) => ({
    value: item.accountId,
    label: item.accountLabel,
  }));

  ACCOUNT_OPTIONS = [{ value: accountId, label: label || accountId }, ...ACCOUNT_OPTIONS]

  ACCOUNT_OPTIONS = _orderBy(ACCOUNT_OPTIONS, [account => account.label?.toLowerCase()], ['asc']);

  const metakeyOptions = (metakeyList || [])?.map((key: string) => ({
    value: key,
    label: key,
  }));

  const getStartDateStr = (momentVal: string) => {
    if (!momentVal) return moment().subtract('7', 'day').toISOString();
    return moment(momentVal, 'DD-MM-YYYY').startOf('day').toISOString();
  };

  const getEndDateStr = (momentVal: string) => {
    if (!momentVal) return moment().toISOString();
    return moment(momentVal, 'DD-MM-YYYY').endOf('day').toISOString();
  };

  const dateRangeForLabels = getDateRange({
    dateRange: {
      startDate: getStartDateStr(dateRange?.[0]),
      endDate: getEndDateStr(dateRange?.[1]),
    },
  });

  const summaryChartLabel = getSummaryChartLabel(dateRangeForLabels);
  const { dates, labels, shortLabels, groupBy } = summaryChartLabel;
  const datasets = useMemo(
    () =>
      getDataSets({
        dates,
        summaries,
      }),
    [messagesInsightsLoading]
  );

  useEffect(() => {
    const from = getStartDateStr(dateRange?.[0]);
    const to = getEndDateStr(dateRange?.[1]);

    dispatch(
      fetchMessagesInsights({
        vendorId,
        accountId,
        date: [`after:${from}`, `before:${to}`],
        timezone: timezone || moment.tz.guess(),
      })
    );
    dispatch(
      fetchSubAccounts({
        vendorId,
        accountId,
        params: {
          size: 100,
        },
      })
    );
    dispatch(
      fetchMetakeys({
        vendorId,
        accountId,
        date: [`after:${from}`, `before:${to}`],
      })
    );
  }, []);

  useEffect(() => {
    const fetchData = () => {
      const isOutbound = currentTab === '1';
      dispatch(
        fetchMessagesDetail({
          vendorId,
          accountId,
          direction: isOutbound ? 'outbound' : 'inbound',
          date: [`after:${getStartDateStr(dateRange?.[0])}`, `before:${getEndDateStr(dateRange?.[1])}`],
          timezone: timezone || moment.tz.guess(),
          metadataKey,
          metadataValue,
          accounts,
          statuses,
          size: pageSize,
          ...(isOutbound ? { destination: source } : { source }),
        })
      );
      setTokenQueue([]);
    };
    fetchData();
  }, []);

  useEffect(() => {
    if (resources) {
      if (currentTab === '1') {
        setOutboundMessages(resources);
      } else {
        setInboundMessages(resources);
      }
    }
  }, [resources]);

  const handleNextPage = () => {
    if (!next) return;
    setTokenQueue([...tokenQueue, next]);
    const isOutbound = currentTab === '1';
    dispatch(
      fetchMessagesDetail({
        vendorId,
        accountId,
        direction: isOutbound ? 'outbound' : 'inbound',
        date: [`after:${getStartDateStr(dateRange?.[0])}`, `before:${getEndDateStr(dateRange?.[1])}`],
        timezone: timezone || moment.tz.guess(),
        metadataKey,
        metadataValue,
        accounts,
        statuses,
        size: pageSize,
        ...(isOutbound ? { destination: source } : { source }),
        next,
        ...(sorter?.sortBy ? { sortByType: sorter.sortBy.toUpperCase() } : {}),
        ...(sorter?.sortDirection ? { sortDirection: sorter.sortDirection.toUpperCase() } : {}),
      })
    );
  };

  const handlePreviousPage = () => {
    if (tokenQueue.length === 0) return;
    const newTokenQueue = [...tokenQueue];
    newTokenQueue.pop();
    setTokenQueue(newTokenQueue);
    const previousToken = newTokenQueue[newTokenQueue.length - 1];
    const isOutbound = currentTab === '1';
    dispatch(
      fetchMessagesDetail({
        vendorId,
        accountId,
        direction: isOutbound ? 'outbound' : 'inbound',
        date: [`after:${getStartDateStr(dateRange?.[0])}`, `before:${getEndDateStr(dateRange?.[1])}`],
        timezone: timezone || moment.tz.guess(),
        metadataKey,
        metadataValue,
        accounts,
        statuses,
        size: pageSize,
        ...(isOutbound ? { destination: source } : { source }),
        ...(previousToken && { next: previousToken }),
        ...(sorter.sortBy && { sortByType: sorter.sortBy.toUpperCase() }),
        ...(sorter.sortDirection && { sortDirection: sorter.sortDirection.toUpperCase() }),
      })
    );
  };

  const handleSort = (newSorter: { sortBy: string | null; sortDirection: string | null }) => {
    setSorter(newSorter);
    const isOutbound = currentTab === '1';
    dispatch(
      fetchMessagesDetail({
        vendorId,
        accountId,
        direction: isOutbound ? 'outbound' : 'inbound',
        date: [`after:${getStartDateStr(dateRange?.[0])}`, `before:${getEndDateStr(dateRange?.[1])}`],
        timezone: timezone || moment.tz.guess(),
        metadataKey,
        metadataValue,
        accounts,
        statuses,
        size: pageSize,
        ...(isOutbound ? { destination: source } : { source }),
        ...(tokenQueue[tokenQueue.length - 1] && { next: tokenQueue[tokenQueue.length - 1] }),
        ...(newSorter.sortBy && { sortByType: newSorter.sortBy.toUpperCase() }),
        ...(newSorter.sortDirection && { sortDirection: newSorter.sortDirection.toUpperCase() }),
      })
    );
    setTokenQueue([]);
  };

  const handleTabChange = (newTab: string) => {
    setCurrentTab(newTab);
    if (newTab === '1' && outboundMessages.length === 0) {
      dispatch(
        fetchMessagesDetail({
          vendorId,
          accountId,
          direction: 'outbound',
          date: [`after:${getStartDateStr(dateRange?.[0])}`, `before:${getEndDateStr(dateRange?.[1])}`],
          timezone: timezone || moment.tz.guess(),
          metadataKey,
          metadataValue,
          accounts,
          statuses,
          size: pageSize,
          destination: source,
        })
      );
    } else if (newTab === '2' && inboundMessages.length === 0) {
      dispatch(
        fetchMessagesDetail({
          vendorId,
          accountId,
          direction: 'inbound',
          date: [`after:${getStartDateStr(dateRange?.[0])}`, `before:${getEndDateStr(dateRange?.[1])}`],
          timezone: timezone || moment.tz.guess(),
          metadataKey,
          metadataValue,
          accounts,
          statuses,
          size: pageSize,
          source,
        })
      );
    }
  };

  const handleOutboundColumnChange = (selectedColumns) => {
    setVisibleOutboundColumns(selectedColumns);
    sessionStorage.setItem('outboundMessagesColumns', JSON.stringify(selectedColumns));
  };

  const handleInboundColumnChange = (selectedColumns) => {
    setVisibleInboundColumns(selectedColumns);
    sessionStorage.setItem('inboundMessagesColumns', JSON.stringify(selectedColumns));
  };

  const renderTable = (isOutbound: boolean) => {
    const columns = isOutbound ? outboundTableColumns : inboundTableColumns;
    const visibleColumns = isOutbound ? visibleOutboundColumns : visibleInboundColumns;
    const filteredColumns = columns.filter(col => visibleColumns.includes(col.index));

    return (
      <div className={styles.table}>
        {messagesDetailLoading === LOADING_STATUS.LOADING ? (
          <div className={styles.spinnerWrap}>
            <Spinner size="l" />
          </div>
        ) : (
          <Table
            hasCheckbox={false}
            tableColumns={filteredColumns}
            tableData={isOutbound ? outboundMessages : inboundMessages}
            loading={messagesDetailLoading}
            next={!!next}
            previous={tokenQueue.length > 0}
            handlePreviousPage={handlePreviousPage}
            handleNextPage={handleNextPage}
            handleSort={handleSort}
            sorter={sorter}
            scrollX
            pageSize={pageSize}
            handleChangePageSize={handleChangePageSize}
          />
        )}
      </div>
    );
  };

  const handleFilter = useCallback(() => {
    setIsValidating(true);
    const from = getStartDateStr(dateRange?.[0]);
    const to = getEndDateStr(dateRange?.[1]);

    dispatch(
      fetchMetakeys({
        vendorId,
        accountId,
        date: [`after:${from}`, `before:${to}`],
      })
    );

    dispatch(
      fetchMessagesInsights({
        vendorId,
        accountId,
        date: [`after:${from}`, `before:${to}`],
        accounts,
        statuses,
        source,
        metadataKey,
        metadataValue,
        timezone: timezone || moment.tz.guess(),
        groupBy,
      })
    );

    const isOutbound = currentTab === '1';
    setTokenQueue([]);
    setInboundMessages([]);
    setOutboundMessages([]);
    dispatch(
      fetchMessagesDetail({
        vendorId,
        accountId,
        direction: isOutbound ? 'outbound' : 'inbound',
        date: [`after:${from}`, `before:${to}`],
        timezone: timezone || moment.tz.guess(),
        metadataKey,
        metadataValue,
        accounts,
        statuses,
        size: pageSize,
        ...(isOutbound ? { destination: source } : { source }),
      })
    );
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dateRange, dispatch, vendorId, accountId, accounts, statuses, source, metadataKey, metadataValue, timezone, groupBy]);

  const handleClearFilters = () => {
    setSource('');
    setAccounts([]);
    setStatuses([]);
    setMetadataKey('');
    setMetadataValue('');
    setIsValidating(false);
  };

  const changedShortLabels = useMemo(
    () => shortLabels,
    [messagesInsightsLoading]
  );

  const changedLabels = useMemo(() => labels, [messagesInsightsLoading]);

  const handleOnKeyDown = (e: React.KeyboardEvent<HTMLFormElement>) => {
    if (isValidating && !metadataKey && metadataValue) return;
    if (e.key === 'Enter') {
      handleFilter();
    }
  };

  const isInvalidForm = () => {
    if (isValidating && !metadataKey && metadataValue) return true;
    if (isValidating && _isEmpty(dateRange)) return true;
    return false;
  };

  const formatDate = (timestamp: string | null | undefined) => {
    if (!timestamp) return '';
    const date = moment(timestamp);
    return date.isValid() ? date.format('DD MMM YYYY, hh:mm:ss a') : 'Invalid date';
  };

  const extractMetadataColumns = useMemo(() => {
    if (!metakeyList?.length) return [];

    return metakeyList.map((key: string) => ({
      title: `${key}`,
      index: `metadata_${key}`,
      sort: false,
      align: 'left',
      render: (msg: Message) => {
        const metaItem = msg.metadata?.find((m) => m.key === key);
        return <Text type="s">{metaItem?.value || ''}</Text>;
      },
    }));
  }, [metakeyList]);

  const handleLinkClick = (messageId: string) => {
    dispatch(fetchMessageDetailByMessageId({ messageId, vendorId, accountId }));
    toggleDRDetailModal(true);
  };

  const outboundTableColumns = useMemo(
    () => [
      {
        title: 'Date',
        index: 'timestamp',
        sortKey: 'timestamp',
        sort: true,
        align: 'left',
        render: (msg: Message) => (
          <Text type="s">{formatDate(msg.timestamp)}</Text>
        ),
      },
      {
        title: 'From',
        index: 'sourceAddress',
        sort: false,
        align: 'left',
        render: (msg: Message) => <Text type="s">{msg.sourceAddress}</Text>,
      },
      {
        title: 'To',
        index: 'destinationAddress',
        sort: false,
        align: 'left',
        render: (msg: Message) => (
          <Text type="s">{msg.destinationAddress}</Text>
        ),
      },
      {
        title: 'Content',
        index: 'content',
        sort: false,
        align: 'left',
        render: (msg: Message) => <Text type="s">{msg.content}</Text>,
      },
      {
        title: 'Message ID',
        index: 'messageId',
        sort: false,
        align: 'left',
        render: (msg: Message) => {
          const messageDate = moment(msg.timestamp);
          const isWithin30Days = moment().diff(messageDate, 'days') <= 30;

          return isWithin30Days ? (
            // eslint-disable-next-line jsx-a11y/anchor-is-valid
            <Link
              href="#"
              onClick={() => handleLinkClick(msg.messageId)}
              text={msg.messageId}
              aria-label="Link"
              preventDefault
            />
          ) : (
            <Text type="s">{msg.messageId}</Text>
          );
        },
      },
      {
        title: 'Format',
        index: 'format',
        sort: false,
        align: 'left',
        render: (msg: Message) => <Text type="s">{msg.format}</Text>,
      },
      {
        title: 'Direction',
        index: 'direction',
        sort: false,
        align: 'left',
        render: (msg: Message) => <Text type="s">{msg.direction}</Text>,
      },
      {
        title: 'Status',
        index: 'status',
        sortKey: 'status',
        sort: true,
        align: 'left',
        render: (msg: Message) => <Text type="s">{msg.status}</Text>,
      },
      {
        title: 'Status Code',
        index: 'statusCode',
        sort: false,
        align: 'left',
        render: (msg: Message) => <Text type="s">{msg.statusCode}</Text>,
      },
      {
        title: 'Status Description',
        index: 'statusDescription',
        sort: false,
        align: 'left',
        render: (msg: Message) => <Text type="s">{msg.statusDescription}</Text>,
      },
      {
        title: 'To Country',
        index: 'toCountry',
        sort: false,
        align: 'left',
        render: (msg: Message) => (
          <Text type="s">{msg.destinationAddressCountry}</Text>
        ),
      },
      {
        title: 'Account ID',
        index: 'accountId',
        sort: false,
        align: 'left',
        render: (msg: Message) => <Text type="s">{msg.accountId}</Text>,
      },
      {
        title: 'Units',
        index: 'units',
        sort: false,
        align: 'left',
        render: (msg: Message) => <Text type="s">{msg.units}</Text>,
      },
      ...extractMetadataColumns,
    ],
    [extractMetadataColumns]
  );

  const inboundTableColumns = useMemo(
    () => [
      {
        title: 'Date',
        index: 'timestamp',
        sortKey: 'timestamp',
        sort: true,
        align: 'left',
        render: (msg: Message) => (
          <Text type="s">{formatDate(msg.timestamp)}</Text>
        ),
      },
      {
        title: 'From',
        index: 'sourceAddress',
        sort: false,
        align: 'left',
        render: (msg: Message) => <Text type="s">{msg.sourceAddress}</Text>,
      },
      {
        title: 'To',
        index: 'destinationAddress',
        sort: false,
        align: 'left',
        render: (msg: Message) => (
          <Text type="s">{msg.destinationAddress}</Text>
        ),
      },
      {
        title: 'Content',
        index: 'content',
        sort: false,
        align: 'left',
        render: (msg: Message) => <Text type="s">{msg.content}</Text>,
      },
      {
        title: 'Message ID',
        index: 'messageId',
        sort: false,
        align: 'left',
        render: (msg: Message) => {
          const messageDate = moment(msg.timestamp);
          const isWithin30Days = moment().diff(messageDate, 'days') <= 30;

          return isWithin30Days ? (
            // eslint-disable-next-line jsx-a11y/anchor-is-valid
            <Link
              href="#"
              onClick={() => handleLinkClick(msg.messageId)}
              text={msg.messageId}
              aria-label="Link"
              preventDefault
            />
          ) : (
            <Text type="s">{msg.messageId}</Text>
          );
        },
      },
      {
        title: 'Format',
        index: 'format',
        sort: false,
        align: 'left',
        render: (msg: Message) => <Text type="s">{msg.format}</Text>,
      },
      {
        title: 'From Country',
        index: 'fromCountry',
        sort: false,
        align: 'left',
        render: (msg: Message) => (
          <Text type="s">{msg.sourceAddressCountry}</Text>
        ),
      },
      {
        title: 'Account ID',
        index: 'accountId',
        sort: false,
        align: 'left',
        render: (msg: Message) => <Text type="s">{msg.accountId}</Text>,
      },
      {
        title: 'Units',
        index: 'units',
        sort: false,
        align: 'left',
        render: (msg: Message) => <Text type="s">{msg.units}</Text>,
      },
      ...extractMetadataColumns,
    ],
    [extractMetadataColumns]
  );

  const handleChangePageSize = (newSize: number) => {
    setPageSize(newSize);
    const isOutbound = currentTab === '1';
    dispatch(
      fetchMessagesDetail({
        vendorId,
        accountId,
        direction: isOutbound ? 'outbound' : 'inbound',
        date: [`after:${getStartDateStr(dateRange?.[0])}`, `before:${getEndDateStr(dateRange?.[1])}`],
        timezone: timezone || moment.tz.guess(),
        metadataKey,
        metadataValue,
        accounts,
        statuses,
        size: newSize,
        ...(isOutbound ? { destination: source } : { source }),
      })
    );
    setTokenQueue([]);
  };

  if (!isVisible) {
    return (
      <div>
        <div className={styles.description}>
          <Text type="s">No Permissions</Text>
        </div>
      </div>
    );
  }

  return (
    <div>
      <DetailedReportDetailsModal
        isVisible={isDRDetailModalVisible}
        onCancel={() => toggleDRDetailModal(false)}
      />
      <form onKeyDown={handleOnKeyDown}>
        <div className={styles.filterWrapper}>
          <div className={styles.formItem}>
            <DateRange
              value={dateRange}
              label="Date"
              onChange={(newVal: string[]) => {
                setIsValidating(false);
                setDateRange(newVal);
              }}
              min={moment().subtract('1', 'years').format('YYYY-MM-DD')}
              max={moment().endOf('day').format('YYYY-MM-DD')}
              testId="date-range"
              errorText={
                isValidating && _isEmpty(dateRange)
                  ? 'Date range is required!'
                  : ''
              }
            />
          </div>
          <div className={styles.formItem}>
            <MultiSelect
              label="Accounts"
              value={accounts}
              onSelect={setAccounts}
              options={ACCOUNT_OPTIONS}
              rows={7}
              testId="accounts-select"
            />
          </div>
          <div className={styles.formItem}>
            <MultiSelect
              label="Status"
              value={statuses}
              onSelect={setStatuses}
              options={STATUS_OPTIONS}
              rows={10}
              testId="statuses-select"
            />
          </div>
          <div className={styles.formItem}>
            <Input
              label="Contact"
              value={source}
              onChange={setSource}
              testId="source"
            />
          </div>
          <div className={styles.formItemBtn}>
            <Button
              label="Apply Filters"
              onClick={handleFilter}
              disabled={isInvalidForm()}
            />
          </div>
          <div className={styles.formItemBtn}>
            <Button
              type="secondary"
              label="Clear Filters"
              onClick={handleClearFilters}
            />
          </div>
        </div>
        {/* Metadata */}
        <div className={styles.filterWrapper}>
          {isAdvancedFiltersVisible && (
            <>
              <div className={styles.formItem}>
                <Select
                  label="Metadata key"
                  value={metadataKey}
                  options={metakeyOptions}
                  onSelect={setMetadataKey}
                  testId="metadata-key"
                  errorText={
                    isValidating && !metadataKey && metadataValue
                      ? 'Metadata value requires Metadata key'
                      : ''
                  }
                />
              </div>
              <div className={styles.formItem}>
                <Input
                  label="Metadata value"
                  value={metadataValue}
                  onChange={(newVal: string) => {
                    setIsValidating(false);
                    setMetadataValue(newVal);
                  }}
                  testId="metadata-value"
                />
              </div>
              {/* <div className={styles.formItemBtn}>
                <Button
                  label='Clear metadata filters'
                  onClick={() => {
                    setMetadataKey('')
                    setMetadataValue('')
                    setIsValidating(false)
                  }}
                />
              </div> */}
            </>
          )}
          <div className={styles.formItemBtn}>
            <Button
              type="subtle-primary"
              label={
                isAdvancedFiltersVisible
                  ? 'Hide advanced filters'
                  : 'Show advanced filters'
              }
              onClick={() => toggleAdvancedFilters(!isAdvancedFiltersVisible)}
            />

          </div>
        </div>
      </form>
      <div className={styles.summaryChartWrapper}>
        {messagesInsightsLoading === LOADING_STATUS.LOADING && (
          <div className={styles.spinnerWrap}>
            <Spinner size="l" />
          </div>
        )}
        <div className={styles.chartItem}>
          <BoundaryChart
            title="Sent messages (outbound)"
            total={totalSent}
            dataset={datasets[0]}
            labels={changedLabels}
            shortLabels={changedShortLabels}
          />
        </div>
        <div className={styles.chartItem}>
          <BoundaryChart
            title="Sent message parts (billing units)"
            total={totalBillingUnits}
            dataset={datasets[1]}
            labels={changedLabels}
            shortLabels={changedShortLabels}
          />
        </div>
        <div className={styles.chartItem}>
          <BoundaryChart
            title="Received messages (inbound)"
            total={totalReceived}
            dataset={datasets[2]}
            labels={changedLabels}
            shortLabels={changedShortLabels}
          />
        </div>
      </div>
      <div className={styles.tabWrapper}>
        <div className={styles.tabHeader}>
          <Tab
            currentTab={currentTab}
            tabs={Object.values(TABS_INFO)}
            tabContent={[
              { value: 1, content: renderTable(true) },
              { value: 2, content: renderTable(false) },
            ]}
            onChangeTab={handleTabChange}
            auxiliaryContent={
              <MultiSelect
                options={currentTab === '1' ?
                  outboundTableColumns.map(col => ({ value: col.index, label: col.title })) :
                  inboundTableColumns.map(col => ({ value: col.index, label: col.title }))
                }
                value={currentTab === '1' ? visibleOutboundColumns : visibleInboundColumns}
                onSelect={currentTab === '1' ? handleOutboundColumnChange : handleInboundColumnChange}
                placeholder="Select columns"
              />
            }
          />
        </div>
      </div>
    </div>
  );
};

export default DetailedReport;
