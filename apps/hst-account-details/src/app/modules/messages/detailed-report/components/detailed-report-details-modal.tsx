import '@nectary/components/icon';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import moment from 'moment';
import { <PERSON><PERSON>, <PERSON><PERSON>, Spinner, Toast } from 'nectary';
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { LOADING_STATUS } from '../../../../constants';
import { ToastItem } from '../../../../types';
import { RootState } from '../../../../types/store';
import { detectEnvironment, detectRegion } from '../../../../utils';
import styles from './detailed-report-details-modal.module.less';

interface MessageDetails {
  messageId: string;
  submissionId: string;
  submissionDate: string;
  provider: string;
  status: 'Delivered' | 'Undelivered' | 'Other';
  providerErrorCode: string;
}

interface DetailedReportDetailsModalProps {
  isVisible: boolean;
  onCancel: () => void;
}

const DetailedReportDetailsModal: React.FC<DetailedReportDetailsModalProps> = ({
  isVisible,
  onCancel,
}) => {
  const [open, setOpen] = useState(isVisible);
  const rootState = useSelector((state: RootState) => state);
  const { accountMessages } = rootState;
  const { messageDetailByMessageId, messageDetailByMessageIdLoading } =
    accountMessages || {};
  const userTimezone = moment.tz.guess();

  const [toasts, setToasts] = useState<ToastItem[]>([]);

  useEffect(() => {
    setOpen(isVisible);
  }, [isVisible]);

  const handleClose = () => {
    setOpen(false);
    onCancel();
  };

  const addToast = (text: string, type: 'success' | 'error') => {
    const newToast = { id: Date.now().toString(), text, type };
    setToasts((prevToasts) => [...prevToasts, newToast]);
  };

  const copyToClipboard = (text: string) => {
    if (typeof navigator !== 'undefined' && navigator.clipboard) {
      navigator.clipboard
        .writeText(text)
        .then(() => {
          addToast('Copied to clipboard', 'success');
        })
        .catch(() => {
          addToast('Failed to copy text', 'error');
        });
    } else {
      addToast('Clipboard not available', 'error');
    }
  };

  const formatDate = (dateString: string): string => {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        throw new Error('Invalid date');
      }
      return date.toLocaleString('en-US', { timeZone: userTimezone || 'UTC' });
    } catch {
      return 'Invalid Date';
    }
  };

  const getStatusClass = (status: string | undefined): string => {
    if (!status) return styles.statusOther;
    switch (status.toLowerCase()) {
      case 'delivered':
        return styles.statusDelivered;
      case 'undelivered':
        return styles.statusUndelivered;
      default:
        return styles.statusOther;
    }
  };

  const renderDetailRow = (
    label: string,
    value: string | undefined,
    copyable = false
  ) => (
    <div className={styles.detailRow}>
      <span className={styles.label}>{label}:</span>
      <div className={styles.value}>
        {value || 'N/A'}
        {copyable && value && (
          <div className={styles.copyIconWrapper}>
            <sinch-icon-content-copy
              className={styles.copyIcon}
              onClick={() => copyToClipboard(value)}
            />
          </div>
        )}
      </div>
    </div>
  );

  const MESSAGE_INDEX_PATTERNS = {
    // Index pattern for STG APAC
    STG_APAC: '27d12530-a6c9-11ef-8a9e-c5d779476184',
    // Index pattern shared by STG EMEA, PRD APAC, and PRD EMEA
    SHARED: '0b466f90-5567-11eb-af90-4346f4a4edae',
  } as const;

  const getIndexPattern = () => {
    const region = detectRegion();
    const environment = detectEnvironment();

    // STG APAC uses a dedicated index pattern
    if (region === 'APAC' && environment === 'staging') {
      return MESSAGE_INDEX_PATTERNS.STG_APAC;
    }

    // All other combinations use the shared index pattern
    return MESSAGE_INDEX_PATTERNS.SHARED;
  };

  let content;
  if (messageDetailByMessageIdLoading === LOADING_STATUS.LOADING) {
    content = (
      <div className={styles.spinnerWrap}>
        <Spinner size="m" />
      </div>
    );
  } else if (
    messageDetailByMessageId &&
    messageDetailByMessageId.resources?.length > 0
  ) {
    const { resources } = messageDetailByMessageId;
    content = resources.map((detail: MessageDetails, index: number) => (
      <div key={detail.messageId} className={styles.detailContainer}>
        {renderDetailRow('Message ID', detail.messageId, true)}
        {renderDetailRow('Submission ID', detail.submissionId, true)}
        {renderDetailRow(
          `Submission Date (${userTimezone || 'UTC'})`,
          formatDate(detail.submissionDate)
        )}
        {renderDetailRow('Provider (do not share this information to the customer)', detail.provider)}
        <div className={styles.detailRow}>
          <span className={styles.label}>Status:</span>
          <span
            className={`${styles.statusValue} ${getStatusClass(detail.status)}`}
          >
            {detail.status
              ? detail.status.charAt(0).toUpperCase() + detail.status.slice(1)
              : 'N/A'}
          </span>
        </div>
        {renderDetailRow('Provider Error Code', detail.providerErrorCode)}
        {resources.length > 1 && index < resources.length - 1 && (
          <div className={styles.divider} />
        )}
      </div>
    ));
  } else {
    content = (
      <div className={styles.noData}>Delivery details are not available.</div>
    );
  }

  return (
    <div className={styles.modal}>
      <Dialog isOpen={open} onClose={handleClose} aria-labelledby="modal-title">
        <div className={styles.modalContent}>
          {/* <div className={styles.infoWrap}>
            <div className={styles.iconInfo}>
              <sinch-icon-warning />
            </div>
            <Text type="s">Do not share this information to the customer</Text>
          </div> */}
          <h2 id="modal-title" className={styles.heading}>
            Message Delivery Details
          </h2>
          <p className={styles.description}>
            The message delivery details are only available for messages within
            the last 30 days.
          </p>
          {content}
          <div className={styles.buttonContainer}>
            {messageDetailByMessageId &&
              messageDetailByMessageId.resources?.length > 0 && (
                <Button
                  label="Access OpenSearch"
                  type="primary"
                  onClick={() => {
                    const region = detectRegion();
                    const getOpenSearchBaseUrl = () => {
                      const baseUrl =
                        region === 'EMEA'
                          ? Endpoint.OPEN_SEARCH_EMEA
                          : Endpoint.OPEN_SEARCH_APAC;
                      return baseUrl;
                    };
                    const baseUrl = `${getOpenSearchBaseUrl()}/app/data-explorer/discover#?_a=(discover:(columns:!(_source),isDirty:!f,sort:!()),metadata:(indexPattern:'${getIndexPattern()}',view:discover))&_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-30d,to:now))&_q=(filters:!(('$state':(store:appState),meta:(alias:!n,disabled:!f,index:'${getIndexPattern()}',key:fromGatewayMessageId,negate:!f,params:(query:'`;
                    const url = `${baseUrl}${encodeURIComponent(
                      messageDetailByMessageId.resources[0].messageId
                    )}'),type:phrase),query:(match_phrase:(fromGatewayMessageId:'${encodeURIComponent(
                      messageDetailByMessageId.resources[0].messageId
                    )}')))),query:(language:kuery,query:''))`;
                    window.open(url, '_blank');
                  }}
                />
              )}
            <Button label="Close" type="secondary" onClick={handleClose} />
          </div>
        </div>
      </Dialog>
      <Toast toasts={toasts} setToasts={setToasts} />
    </div>
  );
};

export default DetailedReportDetailsModal;
