.modal {
  sinch-dialog {
    --sinch-comp-dialog-width: 700px;
    --sinch-comp-dialog-height: 95vh;
    --sinch-comp-dialog-max-width: 900px;
    --sinch-comp-dialog-max-height: 95vh;
  }
}

.modalContent {
  padding: 20px;
  max-height: 80vh;
  overflow-y: auto;
}

.heading {
  font-size: 24px;
  margin-bottom: 10px;
}

.description {
  margin-bottom: 20px;
}

.detailRow {
  display: flex;
  margin-bottom: 10px;
}

.label {
  font-weight: bold;
  width: 250px;
}

.value {
  display: flex;
  flex: 1;
}

.statusValue {
  font-weight: bold;
}

.copyIconWrapper{
  cursor: pointer;
  margin-left: 15px;
}


.statusDelivered {
  color: green;
}

.statusUndelivered {
  color: red;
}

.statusOther {
  color: orange;
}

.buttonContainer {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  gap: 10px;
}

.errorMessage {
  text-align: center;
}

.spinnerContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.divider {
  height: 1px;
  background-color: #ccc; // You can adjust the color as needed
  margin: 10px 0; // Adjust spacing as needed
}

.spinnerWrap {
  width: 100%;
  height: 100%;
  opacity: 0.3;
  display: flex;
  align-items: center;
}

// .infoWrap {
//   padding: 4px 8px;
//   display: inline-flex;
//   align-items: center;
//   flex-direction: row;
//   border-radius: 4px;
//   background-color: var(--sinch-sys-color-feedback-warning-background);
//   margin-bottom: 24px;

//   .iconInfo {
//     margin-right: 4px;
//     --sinch-global-size-icon: 24px;
//   }
//   --sinch-global-color-icon: var(--sinch-ref-color-main-honey-400);
// }

