import React from 'react'
import BoundaryLine<PERSON>hart, { getChartData } from './boundary-line'

describe('View: BoundaryLineChart', () => {
  const props = {
    title: 'test',
    number: '123',
    labels: ['January'],
  }
  it('Renders', () => {
    const wrapper = shallow(<BoundaryLineChart {...props} />)
    expect(wrapper).toMatchSnapshot()
  })

  it('getChartData', () => {
    props.labels = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]
    props.shortLabels = [1]
    const result = getChartData(props)
    expect(result.labels).toEqual([1])
  })

  it('getChartData return empty datset', () => {
    props.labels = []
    const result = getChartData(props)
    expect(result).toEqual({
      labels: ['nodataForThisTimePeriod'],
      datasets: [{ data: [1] }],
    })
  })
})
