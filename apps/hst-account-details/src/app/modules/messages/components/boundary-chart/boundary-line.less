@import '~@sinch-smb/styles/variables.less';

.container {
  background-color: @white;
  border-radius: @border-radius-base;
  box-shadow: 0 1px 3px 0 rgba(24, 24, 24, 0.15);
  margin: .proximity(0)[] .proximity(0)[] 0;
  padding: .rem(6)[];
}

.chartContainer {
  height: .rem(200)[];
  position: relative;
  width: .rem(400)[];
  .respondToMax(@desktop-large, {
    width: .rem(320)[];
  })
}

.title {
  display: block;
  font-size: 18px;
  margin-left: .rem(4)[];
  margin-top: .rem(8)[];

}

.number {
  display: block;
  font-size: 32px;
  font-weight: bold;
  margin-left: .rem(4)[];
}
