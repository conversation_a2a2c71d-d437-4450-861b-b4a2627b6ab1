import PropTypes from 'prop-types'
import _get from 'lodash/get'
import { Line } from 'react-chartjs-2'
import { defaultDatasets, getOptions, numberWithCommas } from './helpers'

import styles from './boundary-line.less'

export const getChartData = (props) => {
  if (props.labels.length === 0) {
    return {
      labels: ['No data for this time period'],
      datasets: [{ data: [1] }],
    }
  }

  const labels = (props.labels.length > 14 && props.shortLabels.length > 0)
    ? props.shortLabels
    : props.labels
  return {
    labels,
    datasets: [{
      ...defaultDatasets,
      fill: 'start',
      lineTension: 0.4,
      label: _get(props.dataset, 'label'),
      data: _get(props.dataset, 'data', []),
      backgroundColor: _get(props.dataset, 'backgroundColor'),
      borderColor: _get(props.dataset, 'borderColor'),
    }],
  }
}

const BoundaryChart = (props) => {
  const { title, total } = props
  return (
    <div className={styles.container}>
      <span className={styles.title}>{title}</span>
      <span data-test={title.replace(/ /g, '-')} className={styles.number}>{numberWithCommas(total)}</span>
      <div className={styles.chartContainer}>
        <Line
          data={getChartData(props)}
          options={getOptions(props)}
        />
      </div>
    </div>
  )
}

BoundaryChart.propTypes = {
  title: PropTypes.string,
  total: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
}

BoundaryChart.defaultProps = {
  title: undefined,
  total: undefined,
}

export default BoundaryChart
