
export const getOptions = (props) => ({
  responsive: true,
  legend: {
    display: false,
  },
  maintainAspectRatio: false,
  cutoutPercentage: 70,
  tooltips: {
    enabled: true,
    mode: 'single',
    backgroundColor: '#e7e7e7',
    displayColors: false,
    bodyFontFamily: '"Lato", sans-serif',
    bodyFontSize: 12,
    bodyFontStyle: 'normal',
    bodyFontColor: '#6d6e71',
    titleFontFamily: '"Lato", sans-serif',
    titleFontSize: 12,
    titleFontColor: '#6d6e71',
    xPadding: 14,
    yPadding: 10,
    callbacks: {
      label: (tooltipItems, data) => {
        const isOverlap = data.datasets.filter((e, i) => i !== tooltipItems.datasetIndex).some((dataset) => dataset.data[tooltipItems.index] === tooltipItems.yLabel)

        return isOverlap
          ? data.datasets.map((dataset) => `${dataset.label}: ${dataset.data[tooltipItems.index]}`)
          : `${data.datasets[tooltipItems.datasetIndex].label}: ${numberWithCommas(tooltipItems.yLabel)}`
      },
      title: (tooltipItems) => {
        const label = props.labels[tooltipItems[0].index]
        if (typeof label === 'string') return label
        return label.join(' ')
      },
    },
  },
  scales: {
    yAxes: [{
      ticks: {
        beginAtZero: true,
        fontColor: '#6d6e71',
        fontFamily: '"Lato", sans-serif',
        fontStyle: 'normal',
        fontSize: 12,
        fontWeight: 'normal',
        padding: 20,
      },
      gridLines: {
        borderDash: [3, 3],
        drawBorder: false,
        drawTicks: false,
        color: '#e7e7e7',
        zeroLineColor: '#e7e7e7',
      },
    }],
    xAxes: [{
      ticks: {
        fontColor: '#6d6e71',
        fontFamily: '"Lato", sans-serif',
        fontStyle: 'normal',
        fontSize: 12,
        fontWeight: 'normal',
      },
      gridLines: {
        display: false,
        drawBorder: false,
        zeroLineColor: 'transparent',
      },
    }],
  },
})


export const defaultDatasets = {
  fill: 'bottom',
  lineTension: 0,
  pointBackgroundColor: '#fff',
  borderCapStyle: 'butt',
  borderWidth: 4,
  borderDashOffset: 0.0,
  borderJoinStyle: 'miter',
  pointBorderWidth: 4,
  pointHoverRadius: 6,
  pointHoverBackgroundColor: '#fff',
  pointHoverBorderWidth: 4,
  pointRadius: 6,
  pointHitRadius: 6,
}

// https://stackoverflow.com/a/2901298/2617164
export const numberWithCommas = (number) => {
  if (number === undefined || number === null) return ''
  const [part1, part2] = number.toString().split('.')
  const part1Fixed = part1.replace(/[^0-9.]/g, '').replace(/\B(?=(\d{3})+(?!\d))/g, ',')

  return part2 ? [part1Fixed, part2].join('.') : part1Fixed
}

export const removeNotNumberic = (value = '') => value.replace(/[^0-9.]/g, '')

export const addCurrenySign = (value) => `$ ${value}`


export default defaultDatasets
