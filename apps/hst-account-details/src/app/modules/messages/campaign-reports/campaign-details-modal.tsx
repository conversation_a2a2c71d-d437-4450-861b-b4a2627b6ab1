import '@nectary/components/card';
import '@nectary/components/grid';
import '@nectary/components/grid-item';
import _find from 'lodash/find';
import moment from 'moment';
import { <PERSON><PERSON>, Dialog, Spinner, Text } from 'nectary';
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { LOADING_STATUS } from '../../../constants';
import {
  fetchBroadcastDetails,
  fetchBroadcastInsights,
} from '../../../redux/messages/broadcast-slice';
import {
  FetchBroadcastsMessagesResource,
  SummaryItemType,
} from '../../../types/broadcast';
import { RootState } from '../../../types/store';
import styles from './campaign-details-modal.module.less';
import StackedBarChart from './components/stacked-bar-chart';
import StatusTablesContainer from './components/status-table/status-tables-container';

export const COLOR_STATUS_OF = {
  SCHEDULED: '#C898F5',
  SENDING: '#81CBFF',
  SENT: '#096DD9',
  CANCELLED: '#757779',
  DELIVERED: '#2F8F0D',
  REJECTED: '#131313',
  FAILED: '#C2001B',
  EXPIRED: '#FD895B',
  STOPPED: '#F2B114',
  SUBMITTED: '#096DD9',
  QUEUED: '#531DAB',
};

const BROADCAST_STATUS = {
  SCHEDULED: 'SCHEDULED',
  FAILED: 'FAILED',
  SENDING: 'SENDING',
  REJECTED: 'REJECTED',
  SENT: 'SENT',
  STOPPED: 'STOPPED',
  SUBMITTED: 'SUBMITTED',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED',
  EXPIRED: 'EXPIRED',
  QUEUED: 'QUEUED',
};

type CampaignDetailsModalProps = {
  vendorId: string;
  accountId: string;
  campaign?: FetchBroadcastsMessagesResource;
  isVisible: boolean;
  setIsVisible: (isVisible: boolean) => void;
};

const MMS_FORMAT = 'MMS';

const formatDate = (timestamp: string | null | undefined) => {
  if (!timestamp) return '';
  const date = moment(timestamp);
  return date.isValid()
    ? date.format('DD MMM YYYY, hh:mm:ss a')
    : 'Invalid date';
};
const getStartDateStr = (momentVal?: string) =>
  moment(momentVal).startOf('day').toISOString();
const getToDateStr = () => moment().toISOString();

export const TABS_INFO = {
  outbound: {
    value: 1,
    text: 'Outbound',
  },
  inbound: {
    value: 2,
    text: 'Inbound',
  },
  optOuts: {
    value: 3,
    text: 'Opt-outs',
  },
  undelivered: {
    value: 4,
    text: 'Undelivered',
  },
};

const CampaignDetailsModal = ({
  vendorId,
  accountId,
  campaign,
  isVisible,
  setIsVisible,
}: CampaignDetailsModalProps) => {
  const dispatch = useDispatch();
  const { id, name, sendDate, sentFrom, content } = campaign || {};
  const rootState = useSelector((state: RootState) => state);
  const { broadcasts } = rootState || {};
  const {
    detailsData,
    detailsLoading,
    broadcastInsightsData,
    broadcastInsightsLoading,
  } = broadcasts || {};
  const { format, recipientErrorCount } = detailsData || {};
  const { summaries } = broadcastInsightsData || {};
  const from = getStartDateStr(sendDate);
  const to = getToDateStr();

  useEffect(() => {
    if (id) {
      dispatch(
        fetchBroadcastDetails({
          vendorId,
          accountId,
          campaignId: id,
        })
      );
      dispatch(
        fetchBroadcastInsights({
          vendorId,
          accountId,
          direction: 'outbound',
          date: [`after:${from}`, `before:${to}`],
          metadataKey: 'broadcastId',
          metadataValue: id,
          groupBy: ['STATUS'],
        })
      );
    }
  }, [id]);

  const renderOverview = () => (
    <div>
      <Text type="l" inline emphasized>
        Overview
      </Text>
      <div className={styles.info}>
        <div className={styles.infoRow}>
          <div className={styles.label}>
            <Text type="s">Send date</Text>
          </div>
          <div className={styles.value}>
            <Text type="s" emphasized>
              {formatDate(sendDate)}
            </Text>
          </div>
        </div>

        <div className={styles.infoRow}>
          <div className={styles.label}>
            <Text type="s">Send from</Text>
          </div>
          <div className={styles.value}>
            <Text type="s" emphasized>
              {sentFrom}
            </Text>
          </div>
        </div>

        <div className={styles.infoRow}>
          <div className={styles.label}>
            <Text type="s">Message channel</Text>
          </div>
          <div className={styles.value}>
            <Text type="s" emphasized>
              {format === MMS_FORMAT
                ? 'Attachment message (MMS)'
                : 'Text message (SMS)'}
            </Text>
          </div>
        </div>

        <div className={styles.infoRow}>
          <div className={styles.label}>
            <Text type="s">Message</Text>
          </div>
          <div className={styles.value}>
            <Text type="s" emphasized>
              {content}
            </Text>
          </div>
        </div>
      </div>
    </div>
  );

  const broadcastStatusKeys = Object.keys(BROADCAST_STATUS);
  const overviewStatusDataArr = broadcastStatusKeys.map((key: string) => {
    if (key === BROADCAST_STATUS.REJECTED) {
      return {
        name: key.toLowerCase(),
        value: recipientErrorCount,
        color: COLOR_STATUS_OF[key],
      };
    }
    const matchedKeySummaryItem: SummaryItemType | null = _find(
      summaries,
      (summary: SummaryItemType) => summary.group === key
    );
    if (matchedKeySummaryItem) {
      return {
        name: key.toLowerCase(),
        value: matchedKeySummaryItem.totalSent,
        color: COLOR_STATUS_OF[key],
      };
    }
    return {
      name: key.toLowerCase(),
      value: 0,
      color: COLOR_STATUS_OF[key],
    };
  });

  const overviewStatusData = overviewStatusDataArr.reduce((acc, curr) => {
    if (
      curr.name === BROADCAST_STATUS.CANCELLED.toLowerCase() ||
      curr.name === BROADCAST_STATUS.QUEUED.toLowerCase()
    ) {
      return acc;
    }

    if (
      curr.name === BROADCAST_STATUS.FAILED.toLowerCase() ||
      curr.name === BROADCAST_STATUS.EXPIRED.toLowerCase()
    ) {
      const sentKey = BROADCAST_STATUS.FAILED.toLowerCase();
      acc[sentKey] = {
        name: sentKey,
        value: (acc[sentKey]?.value || 0) + curr.value,
        color: COLOR_STATUS_OF[BROADCAST_STATUS.FAILED],
      };
      return acc;
    }

    if (
      curr.name === BROADCAST_STATUS.SENT.toLowerCase() ||
      curr.name === BROADCAST_STATUS.SUBMITTED.toLowerCase() ||
      curr.name === BROADCAST_STATUS.DELIVERED.toLowerCase()
    ) {
      const sentKey = BROADCAST_STATUS.SENT.toLowerCase();
      acc[sentKey] = {
        name: sentKey,
        value: (acc[sentKey]?.value || 0) + curr.value,
        color: COLOR_STATUS_OF[BROADCAST_STATUS.SENT],
      };
      return acc;
    }

    acc[curr.name] = curr;
    return acc;
  }, {});

  const deliveryStatusData = overviewStatusDataArr.reduce((acc, curr) => {
    if (
      curr.name === BROADCAST_STATUS.SUBMITTED.toLowerCase() ||
      curr.name === BROADCAST_STATUS.DELIVERED.toLowerCase()
    ) {
      acc[curr.name] = curr;
    }
    return acc;
  }, {});

  const chartStyles = {};

  const renderDeliveryStatus = () => (
    <div>
      <Text type="l" inline emphasized>
        Delivery status
      </Text>

      <StackedBarChart
        name="Overview"
        chartTitle="Overview"
        data={overviewStatusData}
        className="lgMarginBottom"
        legendClassName={chartStyles}
        showLegend
      />

      <StackedBarChart
        name="Delivery"
        chartTitle="Delivery rate of sent"
        data={deliveryStatusData}
        className="lgMarginBottom"
        legendClassName={chartStyles}
        showLegend
      />
    </div>
  );

  const handleViewDetailedReport = () => {
    if (!id) return
    setIsVisible(false)
    const queryParams = new URLSearchParams(window.location.search);
    queryParams.delete('subTab')
    queryParams.set('subTab', 'detailedReport')
    queryParams.set('metadataKey', 'broadcastId')
    queryParams.set('metadataValue', id)
    const newQueryString = queryParams.toString();
    const newUrl = `${window.location.origin}${window.location.pathname}?${newQueryString}`;
    window.history.replaceState({ path: newUrl }, '', newUrl);
    window.location.reload()
  }

  return (
    <div className={styles.dialogWrap}>
      <Dialog
        isOpen={isVisible}
        caption={name}
        onClose={() => setIsVisible(false)}
      >
        <div className={styles.innerWrap}>
          {(detailsLoading === LOADING_STATUS.LOADING ||
            broadcastInsightsLoading === LOADING_STATUS.LOADING) && (
            <div className={styles.spinnerWrap}>
              {' '}
              <Spinner size="l" />{' '}
            </div>
          )}
          <div className={styles.row}>
            <div className={styles.column}>
              <div slot="content" className={styles.card}>
                {renderOverview()}
              </div>
            </div>
            <div className={styles.column}>
              <div slot="content" className={styles.card}>
                {renderDeliveryStatus()}
              </div>
            </div>
          </div>
          <div>
            <Button
              type="subtle-primary"
              label="View detailed report"
              onClick={handleViewDetailedReport}
            />
          </div>
          <div slot="content" className={styles.card}>
            <StatusTablesContainer
              vendorId={vendorId}
              accountId={accountId}
              date={[`after:${from}`, `before:${to}`]}
              broadcastId={id}
            />
          </div>
        </div>
      </Dialog>
    </div>
  );
};

export default CampaignDetailsModal;
