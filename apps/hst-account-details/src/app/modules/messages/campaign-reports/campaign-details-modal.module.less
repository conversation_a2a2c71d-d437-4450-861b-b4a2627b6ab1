.dialogWrap {
  sinch-dialog {
    --sinch-comp-dialog-max-width: 1000px;
    --sinch-comp-dialog-max-height: 800px;
  }
}

.innerWrap {
  position: relative;
}

.row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  .column {
    width: 49%;
  }
}

.card {
  padding: 24px;
  margin: 20px 0;
  border-radius: var(--sinch-comp-card-shape-radius);
  border: 1px solid var(--sinch-comp-card-color-white-default-border-initial);
  background-color: var(
    --sinch-comp-card-color-white-default-background-initial
  );
  box-shadow: var(--sinch-comp-card-shadow-initial);
  overflow: hidden;
}

.info {
  margin-top: 16px;
  .infoRow {
    display: flex;
    flex-direction: row;
    margin-bottom: 8px;

    .label {
      width: 40%;
    }
    .value {
      width: 60%;
    }
  }
}

.spinnerWrap {
  width: 100%;
  height: 100%;
  position: absolute;
  opacity: 0.3;
  display: flex;
  align-items: center;
}
