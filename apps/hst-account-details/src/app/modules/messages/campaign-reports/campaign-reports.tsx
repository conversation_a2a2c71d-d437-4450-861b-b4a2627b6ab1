import moment from 'moment';
import { Button, Input, Link, Table, Text } from 'nectary';
import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchBroadcastsMessages } from '../../../redux/messages/broadcast-slice';
import { FetchBroadcastsMessagesResource } from '../../../types/broadcast';
import { RootState } from '../../../types/store';
import CampaignDetailsModal from './campaign-details-modal';
import styles from './campaign-reports.module.less';

interface CampaignReportsProps {
  vendorId: string;
  accountId: string;
}

const DEFAULT_SIZE = 10;

const formatDate = (timestamp: string | null | undefined) => {
  if (!timestamp) return '';
  const date = moment(timestamp);
  return date.isValid()
    ? date.format('DD MMM YYYY, hh:mm:ss a')
    : 'Invalid date';
};

const tableColumns = ({
  onViewCampaign,
}: {
  onViewCampaign: (campaign: FetchBroadcastsMessagesResource) => void;
}) => [
  {
    title: 'Message Name',
    index: 'name',
    sort: false,
    align: 'left',
    render: (campaign: FetchBroadcastsMessagesResource) => (
      <Link
        href="#campaign-details"
        text={campaign.name}
        preventDefault
        onClick={() => onViewCampaign(campaign)}
      />
    ),
  },
  {
    title: 'Recipients',
    index: 'recipientValue',
    sort: false,
    align: 'left',
    tooltipText:
      'Number of recipients for scheduled campaigns are estimated and will vary if contacts are added or removed from groups. If a recipient is found in multiple groups, they will be consolidated at send time.',
    render: (campaign: FetchBroadcastsMessagesResource) => {
      const { recipientsValue, estimatedRecipientCount } = campaign;
      let displayValue = 'No recipients assigned';

      if (recipientsValue && estimatedRecipientCount) {
        if (/^\+\d+$/.test(recipientsValue)) {
          displayValue = recipientsValue;
        } else if (
          estimatedRecipientCount &&
          Number(estimatedRecipientCount) > Number(recipientsValue)
        ) {
          displayValue = `${recipientsValue} of ${estimatedRecipientCount}`;
        } else {
          displayValue = recipientsValue;
        }
      }

      return <Text type="s">{displayValue}</Text>;
    },
  },
  {
    title: 'Content',
    index: 'content',
    sort: false,
    align: 'left',
    render: (campaign: FetchBroadcastsMessagesResource) => (
      <Text type="s">{campaign.content}</Text>
    ),
  },
  {
    title: 'Send Date',
    index: 'sendDate',
    sort: false,
    align: 'left',
    render: (campaign: FetchBroadcastsMessagesResource) => (
      <Text type="s">{formatDate(campaign.sendDate)}</Text>
    ),
  },
  {
    title: 'Send From',
    index: 'sentFrom',
    sort: false,
    align: 'left',
    render: (campaign: FetchBroadcastsMessagesResource) => (
      <Text type="s">{campaign.sentFrom}</Text>
    ),
  },
  {
    title: 'Status',
    index: 'status',
    sort: false,
    align: 'left',
    render: (campaign: FetchBroadcastsMessagesResource) => (
      <Text type="s">
        {campaign.status
          ? campaign.status.charAt(0).toUpperCase() +
            campaign.status.slice(1).toLowerCase()
          : ''}
      </Text>
    ),
  },
];

const CampaignReports = ({ vendorId, accountId }: CampaignReportsProps) => {
  const dispatch = useDispatch();
  const rootState = useSelector((state: RootState) => state);
  const { broadcasts } = rootState;
  const { data, loading } = broadcasts;
  const { resources, pagination } = data || {};
  const { next } = pagination || {};
  const [tokenQueue, setTokenQueue] = useState<string[]>([]);
  const [messageName, setMessageName] = useState('');
  const [selectedCampaign, setSelectedCampaign] = useState();
  const [campainDetailsModalVisible, setCampainDetailsModalVisible] =
    useState(false);
  const [pageSize, setPageSize] = useState<number>(DEFAULT_SIZE);

  const fetchData = useCallback(
    (nextToken?: string, nameFilter?: string) => {
      if (!vendorId || !accountId) return;

      dispatch(
        fetchBroadcastsMessages({
          vendorId,
          accountId,
          size: pageSize,
          ...(nextToken && { next: nextToken }),
          ...(nameFilter && { name: nameFilter }),
        })
      );
    },
    [dispatch, vendorId, accountId, pageSize]
  );

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleSearch = useCallback(
    (e?: React.FormEvent) => {
      e?.preventDefault();
      fetchData(undefined, messageName);
    },
    [fetchData, messageName]
  );

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSearch();
    }
  };

  const handleNextPage = useCallback(() => {
    if (!next) return;
    setTokenQueue((prevQueue) => [...prevQueue, next]);
    fetchData(next, messageName);
  }, [next, fetchData, messageName]);

  const handlePreviousPage = useCallback(() => {
    if (tokenQueue.length === 0) return;
    setTokenQueue((prevQueue) => {
      const newQueue = [...prevQueue];
      newQueue.pop();
      const previousToken = newQueue[newQueue.length - 1];
      fetchData(previousToken, messageName);
      return newQueue;
    });
  }, [tokenQueue, fetchData, messageName]);

  const onViewCampaign = (campaign: FetchBroadcastsMessagesResource) => {
    setSelectedCampaign(campaign);
    setCampainDetailsModalVisible(true);
  };

  const handleChangePageSize = (newSize: number) => {
    setPageSize(newSize);
    fetchData(undefined, messageName);
  };

  return (
    <div className={styles.campaignReports}>
      <form
        className={styles.filterWrapper}
        onSubmit={handleSearch}
        onKeyDown={handleKeyDown}
      >
        <div className={styles.formItem}>
          <Input
            label="Message Name"
            value={messageName}
            onChange={setMessageName}
            testId="message-name"
          />
        </div>
        <div className={styles.formItem}>
          <Button label="Search" onClick={handleSearch} />
        </div>
      </form>
      <div className={styles.tableWrapper}>
        <Table
          tableColumns={tableColumns({ onViewCampaign })}
          tableData={resources || []}
          loading={loading}
          next={!!next}
          previous={false}
          handlePreviousPage={handlePreviousPage}
          handleNextPage={handleNextPage}
          pageSize={pageSize}
          handleChangePageSize={handleChangePageSize}
          scrollX
        />
      </div>
      <CampaignDetailsModal
        accountId={accountId}
        vendorId={vendorId}
        campaign={selectedCampaign}
        isVisible={campainDetailsModalVisible}
        setIsVisible={setCampainDetailsModalVisible}
      />
    </div>
  );
};

export default CampaignReports;
