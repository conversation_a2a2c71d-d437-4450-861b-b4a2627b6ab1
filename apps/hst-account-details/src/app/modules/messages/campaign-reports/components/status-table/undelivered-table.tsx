import { customDateTimeFormatReadable, isNonEmptyString } from 'helpers';
import { Table, Tag, Text } from 'nectary';
import { useSelector } from 'react-redux';
import { Message } from '../../../../../types/messages/index';
import { RootState } from '../../../../../types/store';

const tableColumns = () => [
  {
    title: 'Recipient',
    index: 'recipient',
    sort: false,
    align: 'left',
    render: (value: Message) => <Text>{value.destinationAddress}</Text>,
  },
  {
    title: 'Outbound message',
    index: 'content',
    sort: false,
    align: 'left',
    render: (value: Message) => <Text>{value.content}</Text>,
  },
  {
    title: 'Status',
    index: 'status',
    sort: false,
    align: 'left',
    render: (value: Message) => <Tag key={value.status} color="light-red" text={value.status} />,
  },
  {
    title: 'Send Date',
    index: 'timestamp',
    sort: false,
    align: 'left',
    render: (value: Message) => (
      <Text>
        {isNonEmptyString(value.timestamp)
          ? customDateTimeFormatReadable({
              datetime: value.timestamp,
              showTime: true,
            })
          : '-'}
      </Text>
    ),
  },
];

type UndeliveredTableProps = {
  previous?: number;
  handlePrevious: () => void;
  next?: string;
  handleNext: (token: string) => void;
};

const UndeliveredTable = ({
  previous,
  handlePrevious,
  next,
  handleNext,
}: UndeliveredTableProps) => {
  const rootState = useSelector((state: RootState) => state);
  const { broadcasts } = rootState || {};
  const { broadcastDetailsTablesData, broadcastDetailsTablesLoading } =
    broadcasts || {};
  const { undelivered } = broadcastDetailsTablesData || {};
  const { resources } = undelivered || {};

  return (
    <div>
      <Table
        hasCheckbox={false}
        tableColumns={tableColumns()}
        tableData={resources || []}
        loading={broadcastDetailsTablesLoading}
        next={next}
        previous={previous}
        handlePreviousPage={handlePrevious}
        handleNextPage={handleNext}
      />
    </div>
  );
};

export default UndeliveredTable;
