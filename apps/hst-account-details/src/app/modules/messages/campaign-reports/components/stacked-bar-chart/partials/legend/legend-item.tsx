import classNames from 'classnames'
import PropTypes from 'prop-types'

import { Text, Tooltip } from 'nectary'
import { safePercentage } from '../../helpers/safe-calculator'

import { getFormattedNumber } from '../../helpers/numbers'
import styles from './legend.less'

const LegendItem = ({
  name,
  value,
  color,
  className,
  totalValue,
  ...restProps
}) => (
  <div
    {...restProps}
    className={classNames(styles.legendGroup, className)}
  >
    <div className={classNames(styles.colorBox, color)} style={{ backgroundColor: color }} />
    <div className={styles.name} title={name}>{name}</div>
    <div className={styles.legendValue}>
      <span className={styles.value} {...(value && { title: value })}>
        {/* <Tooltip title={getFormattedNumber({ value: String(value), expanded: true })}>
          {getFormattedNumber({ value: String(value), expanded: false })}
        </Tooltip> */}
        <Tooltip
          type="fast"
          orientation="top"
          text={getFormattedNumber({ value: String(value), expanded: true })}
        >
          <Text>{getFormattedNumber({ value: String(value), expanded: false })}</Text>
        </Tooltip>
      </span>
      <span className={styles.subValue}>{`${safePercentage(totalValue, value)}%`}</span>
    </div>
  </div>
)

LegendItem.propTypes = {
  name: PropTypes.string,
  value: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  totalValue: PropTypes.number,
  color: PropTypes.string,
  className: PropTypes.string,
}

LegendItem.defaultProps = {
  name: '',
  color: null,
  className: '',
  value: 0,
  totalValue: 0,
}

export default LegendItem
