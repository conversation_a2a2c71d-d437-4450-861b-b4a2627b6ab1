export const numberWithCommas = (number) => {
  if (number === undefined || number === null) return ''
  const [part1, part2] = number.toString().split('.')
  const part1Fixed = part1.replace(/[^0-9.]/g, '').replace(/\B(?=(\d{3})+(?!\d))/g, ',')

  return part2 ? [part1Fixed, part2].join('.') : part1Fixed
}
export const getUserFriendlyNumber = ({ number, options: { withCommas = false } = {} }) => {
  if (number >= 10000000) return `${Math.floor(number / 1000000)}m`
  if (number >= 10000) return `${Math.floor(number / 1000)}k`
  return withCommas ? numberWithCommas(number) : `${number}`
}


export const getFormattedNumber = ({ value, expanded }) => {
  if (value.trim().length === 0 || Number.isNaN(Number(value))) return '-'

  const number = Number(value)

  if (expanded) return numberWithCommas(number)

  return getUserFriendlyNumber({ number, options: { withCommas: true } })
}
