import { customDateTimeFormatReadable, isNonEmptyString } from 'helpers';
import { Table, Tag, Text } from 'nectary';
import { useSelector } from 'react-redux';
import { Message } from '../../../../../types/messages/index';
import { RootState } from '../../../../../types/store';

const tableColumns = () => [
  {
    title: 'Recipient',
    index: 'recipient',
    sort: false,
    align: 'left',
    render: (value: Message) => <Text>{value.destinationAddress}</Text>,
  },
  {
    title: 'Outbound message',
    index: 'content',
    sort: false,
    align: 'left',
    render: (value: Message) => <Text>{value.content}</Text>,
  },
  {
    title: 'Status',
    index: 'status',
    sort: false,
    align: 'left',
    render: (value: Message) => <Tag key={value.status} color="light-green" text={value.status} />
  },
  {
    title: 'Send Date',
    index: 'timestamp',
    sort: false,
    align: 'left',
    render: (value: Message) => (
      <Text>
        {isNonEmptyString(value.timestamp)
          ? customDateTimeFormatReadable({
              datetime: value.timestamp,
              showTime: true,
            })
          : '-'}
      </Text>
    ),
  },
];

type OutboundTableProps = {
  previous?: number;
  handlePrevious: () => void;
  next?: string;
  handleNext: (token: string) => void;
};

const OutboundTable = ({
  previous,
  handlePrevious,
  next,
  handleNext,
}: OutboundTableProps) => {
  const rootState = useSelector((state: RootState) => state);
  const { broadcasts } = rootState || {};
  const { broadcastDetailsTablesData, broadcastDetailsTablesLoading } =
    broadcasts || {};
  const { outbound } = broadcastDetailsTablesData || {};
  const { resources } = outbound || {};

  return (
    <div>
      <Table
        hasCheckbox={false}
        tableColumns={tableColumns()}
        tableData={resources || []}
        loading={broadcastDetailsTablesLoading}
        next={next}
        previous={previous}
        handlePreviousPage={handlePrevious}
        handleNextPage={handleNext}
      />
    </div>
  );
};

export default OutboundTable;
