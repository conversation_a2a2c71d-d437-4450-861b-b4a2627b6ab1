import classNames from 'classnames'
import PropTypes from 'prop-types'

import { safePercentage } from '../../helpers/safe-calculator'

import styles from './stacked-bar.less'

const StackedBarSegment = ({
  value,
  totalValue,
  color,
  className,
  ...restProps
}) => {
  const percentageValue = safePercentage(totalValue, value)

  if (!percentageValue) return null
  return (
    <div
      {...restProps}
      className={classNames(styles.stackedBarSegment, className, color)}
      style={{
        width: `${percentageValue}%`,
        backgroundColor: color,
      }}
    />
  )
}

StackedBarSegment.propTypes = {
  value: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  totalValue: PropTypes.number,
  color: PropTypes.string,
  className: PropTypes.string,
}

StackedBarSegment.defaultProps = {
  color: null,
  className: '',
  value: 0,
  totalValue: 0,
}

export default StackedBarSegment
