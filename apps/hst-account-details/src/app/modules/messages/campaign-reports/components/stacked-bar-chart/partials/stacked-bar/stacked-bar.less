@import '~@sinch-smb/styles/variables.less';

// Progress
.stackedBar {
  display: block;
  font-size: 6px;
}

.stackedBarGroup {
  background-color: @grey-7;
  border-radius: 0.5em;
  display: flex;
  height: 1em;
  justify-content: stretch;
  margin: 1em 0;

  .stackedBarShowInfo & {
    margin-bottom: 4em;
    position: relative;

    &:before,
    &:after {
      font-size: .rem(12px) [];
      margin-top: 0.25em;
      position: absolute;
      top: 100%;
    }

    &:before {
      content: "0";
      left: 0;
    }
    &:after {
      content: "100%";
      right: 0;
    }
  }
}

.stackedBarSegment {
  background-color: @mm-blue-4;
  flex: 0 1 auto;
  margin-right: 1px;
  transition: width 0.3s ease-in-out;

  &:first-child {
    border-bottom-left-radius: 0.5em;
    border-top-left-radius: 0.5em;
  }

  &:last-child {
    border-bottom-right-radius: 0.5em;
    border-top-right-radius: 0.5em;
    margin-right: 0;
  }
}
