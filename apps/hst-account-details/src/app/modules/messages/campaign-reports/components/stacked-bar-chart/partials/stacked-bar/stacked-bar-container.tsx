import React from 'react'
import PropTypes from 'prop-types'
import classNames from 'classnames'
import _get from 'lodash/get'

import StackedBarSegment from './stacked-bar-segment'

import styles from './stacked-bar.less'

const StackedBarContainer = ({
  className,
  showInfo,
  data,
  totalValue,
}) => (
  <div
    className={
      classNames(
        styles.stackedBar,
        className,
        {
          [styles.stackedBarShowInfo]: showInfo,
        },
      )
    }
  >
    <div className={styles.stackedBarGroup}>
      {Object.keys(data).map((key) => (_get(data[key], 'value') ? <StackedBarSegment key={`segment-${key}`} {...data[key]} totalValue={totalValue} /> : null))}
    </div>
  </div>
)

StackedBarContainer.propTypes = {
  totalValue: PropTypes.number,
  data: PropTypes.shape({}),
  className: PropTypes.string,
  showInfo: PropTypes.bool,
}

StackedBarContainer.defaultProps = {
  className: '',
  showInfo: false,
  data: {},
  totalValue: 0,
}

export default StackedBarContainer
