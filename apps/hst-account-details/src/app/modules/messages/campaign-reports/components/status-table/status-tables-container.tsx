import { MessageStatus } from 'apps/hst-account-details/src/app/types/broadcast';
import moment from 'moment';
import { Tab } from 'nectary';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  fetchBroadcastInsightsForTables,
  fetchMessagesDetail,
} from '../../../../../redux/messages/broadcast-slice';
import { RootState } from '../../../../../types/store';
import InboundTable from './inbound-table';
import OptOutTable from './opt-out-table';
import OutboundTable from './outbound-table';
import UndeliveredTable from './undelivered-table';

type StatusTablesContainerProps = {
  vendorId: string;
  accountId: string;
  date: string[];
  broadcastId?: string;
};

const DEFAULT_PAGE_SIZE = 10;

const getPercent = (percent: number, total: number) => {
  if (isNaN(percent) || isNaN(total) || !percent) return 0;
  return ((percent / total) * 100).toFixed(2);
};

const StatusTablesContainer = ({
  vendorId,
  accountId,
  date,
  broadcastId,
}: StatusTablesContainerProps) => {
  const rootState = useSelector((state: RootState) => state);
  const { broadcasts } = rootState || {};
  const { broadcastDetailsTablesData, broadcastInsightsTablesData } =
    broadcasts || {};
  // detail
  const { outbound, inbound, optOut, undelivered } =
    broadcastDetailsTablesData || {};
  const { pagination: outboundPagination } = outbound || {};
  const { pagination: inboundPagination } = inbound || {};
  const { pagination: optOutPagination } = optOut || {};
  const { pagination: undeliveredPagination } = undelivered || {};
  // insights
  const {
    outbound: outboundInsights,
    inbound: inboundInsights,
    optOut: optOutInsights,
    undelivered: undeliveredInsights,
  } = broadcastInsightsTablesData || {};
  const { totalSent: outboundTotal } = outboundInsights || {};
  const { totalReceived: inboundTotal } = inboundInsights || {};
  const { totalOptOut: optOutTotal } = optOutInsights || {};
  const { totalSent: undeliveredTotal } = undeliveredInsights || {};
  const totalMessages = outboundTotal + inboundTotal + optOutTotal;
  const outboundPercent = getPercent(
    outboundTotal - undeliveredTotal,
    totalMessages
  );
  const inboundPercent = getPercent(inboundTotal, totalMessages);
  const optOutPercent = getPercent(optOutTotal, totalMessages);
  const undeliveredPercent = getPercent(undeliveredTotal, outboundTotal);

  const TABS_INFO = {
    outbound: {
      value: 1,
      text: `Outbound - ${outboundTotal || 0}${
        outboundPercent ? ` (${outboundPercent}%)` : ''
      }`,
    },
    inbound: {
      value: 2,
      text: `Inbound - ${inboundTotal || 0}${
        inboundPercent ? ` (${inboundPercent}%)` : ''
      }`,
    },
    optOuts: {
      value: 3,
      text: `Opt-outs - ${optOutTotal || 0}${
        optOutPercent ? ` (${optOutPercent}%)` : ''
      }`,
    },
    undelivered: {
      value: 4,
      text: `Undelivered - ${undeliveredTotal || 0}${
        undeliveredPercent ? ` (${undeliveredPercent}%)` : ''
      }`,
    },
  };

  const dispatch = useDispatch();
  const TABS_INFO_ARRAY = Object.values(TABS_INFO);
  const [currentTab, setCurrentTab] = useState('1');
  const [tokenQueue, setTokenQueue] = useState<string[]>([]);

  useEffect(() => {
    if (!broadcastId) return;
    switch (currentTab) {
      case '1':
        dispatch(
          fetchMessagesDetail({
            direction: 'outbound',
            statuses: [
              MessageStatus.SUBMITTED,
              MessageStatus.DELIVERED,
            ],
            vendorId,
            accountId,
            date,
            timezone: moment.tz.guess(),
            metadataKey: 'broadcastId',
            metadataValue: broadcastId,
            size: DEFAULT_PAGE_SIZE,
          })
        );
        break;
      case '2':
        dispatch(
          fetchMessagesDetail({
            direction: 'inbound',
            vendorId,
            accountId,
            date,
            timezone: moment.tz.guess(),
            metadataKey: 'broadcastId',
            metadataValue: broadcastId,
            size: DEFAULT_PAGE_SIZE,
          })
        );
        break;
      case '3':
        dispatch(
          fetchMessagesDetail({
            direction: 'inbound',
            optOut: true,
            vendorId,
            accountId,
            date,
            timezone: moment.tz.guess(),
            metadataKey: 'broadcastId',
            metadataValue: broadcastId,
            size: DEFAULT_PAGE_SIZE,
          })
        );
        break;
      case '4':
        dispatch(
          fetchMessagesDetail({
            direction: 'outbound',
            statuses: [
              MessageStatus.FAILED,
              MessageStatus.EXPIRED,
              MessageStatus.REJECTED,
              MessageStatus.HELD,
              MessageStatus.UNDEFINED,
            ],
            vendorId,
            accountId,
            date,
            timezone: moment.tz.guess(),
            metadataKey: 'broadcastId',
            metadataValue: broadcastId,
            size: DEFAULT_PAGE_SIZE,
          })
        );
        break;
    }
    setTokenQueue([]);
  }, [broadcastId, currentTab]);

  useEffect(() => {
    if (!broadcastId) return;
    dispatch(
      fetchBroadcastInsightsForTables({
        direction: 'outbound',
        statuses: [MessageStatus.SUBMITTED, MessageStatus.DELIVERED],
        vendorId,
        accountId,
        date,
        metadataKey: 'broadcastId',
        metadataValue: broadcastId,
      })
    );
    dispatch(
      fetchBroadcastInsightsForTables({
        direction: 'outbound',
        statuses: [
          MessageStatus.FAILED,
          MessageStatus.EXPIRED,
          MessageStatus.REJECTED,
          MessageStatus.HELD,
          MessageStatus.UNDEFINED,
        ],
        vendorId,
        accountId,
        date,
        metadataKey: 'broadcastId',
        metadataValue: broadcastId,
      })
    );
    dispatch(
      fetchBroadcastInsightsForTables({
        direction: 'inbound',
        vendorId,
        accountId,
        date,
        metadataKey: 'broadcastId',
        metadataValue: broadcastId,
      })
    );
    dispatch(
      fetchBroadcastInsightsForTables({
        direction: 'inbound',
        optOut: true,
        vendorId,
        accountId,
        date,
        metadataKey: 'broadcastId',
        metadataValue: broadcastId,
      })
    );
  }, [broadcastId]);

  const getTypeParams = () => {
    let typeParams = {};
    switch (currentTab) {
      case '1':
        typeParams = {
          direction: 'outbound',
        };
        break;
      case '2':
        typeParams = {
          direction: 'inbound',
        };
        break;
      case '3':
        typeParams = {
          direction: 'inbound',
          optOut: true,
        };
        break;
      case '4':
        typeParams = {
          direction: 'outbound',
          statuses: [
            MessageStatus.FAILED,
            MessageStatus.EXPIRED,
            MessageStatus.REJECTED,
            MessageStatus.HELD,
            MessageStatus.UNDEFINED,
          ],
        };
        break;
    }
    return typeParams;
  };

  const handlePrevious = () => {
    tokenQueue.pop();
    setTokenQueue(tokenQueue);
    const tokensLen = tokenQueue.length;

    const tableParams = getTypeParams();

    setTimeout(() => {
      dispatch(
        fetchMessagesDetail({
          ...tableParams,
          vendorId,
          accountId,
          date,
          timezone: moment.tz.guess(),
          metadataKey: 'broadcastId',
          metadataValue: broadcastId,
          size: DEFAULT_PAGE_SIZE,
          next: tokensLen ? tokenQueue[tokensLen - 1] : undefined,
        })
      );
    });
  };

  const handleNext = (token: string) => {
    tokenQueue.push(token);
    setTokenQueue(tokenQueue);
    const tableParams = getTypeParams();

    setTimeout(() => {
      dispatch(
        fetchMessagesDetail({
          ...tableParams,
          vendorId,
          accountId,
          date,
          timezone: moment.tz.guess(),
          metadataKey: 'broadcastId',
          metadataValue: broadcastId,
          size: DEFAULT_PAGE_SIZE,
          next: token,
        })
      );
    });
  };

  const tabContent = () => [
    {
      value: 1,
      content: (
        <OutboundTable
          previous={tokenQueue.length}
          handlePrevious={handlePrevious}
          next={outboundPagination?.next}
          handleNext={handleNext}
        />
      ),
    },
    {
      value: 2,
      content: (
        <InboundTable
          previous={tokenQueue.length}
          handlePrevious={handlePrevious}
          next={inboundPagination?.next}
          handleNext={handleNext}
        />
      ),
    },
    {
      value: 3,
      content: (
        <OptOutTable
          previous={tokenQueue.length}
          handlePrevious={handlePrevious}
          next={optOutPagination?.next}
          handleNext={handleNext}
        />
      ),
    },
    {
      value: 4,
      content: (
        <UndeliveredTable
          previous={tokenQueue.length}
          handlePrevious={handlePrevious}
          next={undeliveredPagination?.next}
          handleNext={handleNext}
        />
      ),
    },
  ];

  return (
    <Tab
      currentTab={currentTab}
      tabs={TABS_INFO_ARRAY}
      tabContent={tabContent()}
      onChangeTab={setCurrentTab}
    />
  );
};

export default StatusTablesContainer;
