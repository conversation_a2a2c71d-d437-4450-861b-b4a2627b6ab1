@import '~@sinch-smb/styles/variables.less';

.divider {
  margin: .proximity(0.75)[] 0;
}

// Legend
.legendTitle {
  color: @grey-19;
  font-size: @font-size-base;
  font-weight: 600;
  margin-bottom: .proximity(0)[];
  margin-top: .proximity(0)[];

  /* stylelint-disable max-nesting-depth, selector-class-pattern, selector-max-compound-selectors */
  :global {
    .anticon-question-circle svg {
      font-size: @font-size-base;
    }
  }
  /* stylelint-enable */
}


.legend {
  margin-top: .proximity(2)[];

  .legendContainer {
    column-count: 2;
    font-size: @font-size-base;
    margin-left: -1em;
    margin-right: -1em;
  }

  .legendGroup {
    align-items: center;
    color: @grey-19;
    display: flex;
    line-height: 1.5;
    margin-bottom: .proximity(0)[];
    padding-left: 1em;
    padding-right: 1em;
    white-space: nowrap;
    width: 100%;
  }

  .colorBox {
    flex: 0 0 0.6875em;
    height: 0.6875em;
    margin-right: 0.375em;
  }

  .name {
    flex: 0 1 auto;
    margin-right: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    &::first-letter {
      text-transform: uppercase;
    }
  }

  .legendValue {
    align-items: flex-end;
    display: inline-flex;
    flex: 1 1 auto;
    justify-content: flex-end;
    white-space: nowrap;
    width: 0;

    .value {
      font-weight: 600;
      text-align: right;
    }

    .subValue {
      color: @grey-17;
      display: inline-block;
      flex: 0 0 2.5em;
      font-size: 0.875em;
      margin-left: 0.25em;
      text-align: right;
    }
  }
}

.iconInfo {
  margin-left: 8px;
}
