import classNames from 'classnames'
import PropTypes from 'prop-types'

// import { Divider } from 'antd'
import { Tooltip } from 'nectary'
import LegendItem from './legend-item'

import legendStyles from './legend.less'

const LegendContainer = ({
  title,
  tooltip,
  className,
  data,
  totalValue,
}) => (
  <section className={classNames(legendStyles.legend, className, { [className.legend]: className.legend })}>
    {title && (
      <>
        <h5 className={legendStyles.legendTitle}>
          <span>{title}</span>
          {' '}
          {/* {tooltip && <QuestionTooltip placement="top" title={tooltip} arrowPointAtCenter />} */}
          {tooltip && <Tooltip
            type="fast"
            orientation="top"
            text={tooltip}
          >
            <div className={legendStyles.iconInfo}>
              <sinch-icon-info />
            </div>
          </Tooltip>}
        </h5>
        {/* <Divider className={legendStyles.divider} /> */}
      </>
    )}
    <div className={classNames(legendStyles.legendContainer, { [className.legendContainer]: className.legendContainer })}>
      {Object.keys(data).map((key) => (
        <LegendItem
          {...data[key]}
          key={`legend-${key}`}
          totalValue={totalValue}
          className={classNames({
            [data[key].className]: data[key] && data[key].className,
            [className.legendGroup]: className.legendGroup,
          })}
        />
      ))}
    </div>
  </section>
)

LegendContainer.propTypes = {
  totalValue: PropTypes.number,
  data: PropTypes.shape({}),
  className: PropTypes.string,
  title: PropTypes.string,
  tooltip: PropTypes.string,
}

LegendContainer.defaultProps = {
  data: {},
  title: '',
  tooltip: '',
  className: '',
  totalValue: 0,
}

export default LegendContainer
