import classNames from 'classnames'
import PropTypes from 'prop-types'

import styles from './title.less'

const Title = ({ children, className }) => {
  if (typeof children === 'object' || children === null) {
    return children
  }

  return <h4 className={classNames(styles.title, className)}>{children}</h4>
}

Title.propTypes = {
  className: PropTypes.string,
  children: PropTypes.oneOfType([
    PropTypes.node,
    PropTypes.string,
  ]),
}

Title.defaultProps = {
  className: '',
  children: null,
}

export default Title
