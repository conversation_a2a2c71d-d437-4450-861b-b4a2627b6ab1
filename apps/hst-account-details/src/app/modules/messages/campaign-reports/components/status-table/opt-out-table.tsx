import { customDateTimeFormatReadable, isNonEmptyString } from 'helpers';
import { Table, Text } from 'nectary';
import { useSelector } from 'react-redux';
import { Message } from '../../../../../types/messages/index';
import { RootState } from '../../../../../types/store';

const tableColumns = () => [
  {
    title: 'Recipient',
    index: 'recipient',
    sort: false,
    align: 'left',
    render: (value: Message) => <Text>{value.destinationAddress}</Text>,
  },
  {
    title: 'Inbound message',
    index: 'content',
    sort: false,
    align: 'left',
    render: (value: Message) => <Text>{value.content}</Text>,
  },
  {
    title: 'Send Date',
    index: 'timestamp',
    sort: false,
    align: 'left',
    render: (value: Message) => (
      <Text>
        {isNonEmptyString(value.timestamp)
          ? customDateTimeFormatReadable({
              datetime: value.timestamp,
              showTime: true,
            })
          : '-'}
      </Text>
    ),
  },
  {
    title: 'Action',
    index: 'timestamp',
    sort: false,
    align: 'left',
    render: (value: Message) => <Text>-</Text>,
  },
];

type OptOutTableProps = {
  previous?: number;
  handlePrevious: () => void;
  next?: string;
  handleNext: (token: string) => void;
};

const OptOutTable = ({
  previous,
  handlePrevious,
  next,
  handleNext,
}: OptOutTableProps) => {
  const rootState = useSelector((state: RootState) => state);
  const { broadcasts } = rootState || {};
  const { broadcastDetailsTablesData, broadcastDetailsTablesLoading } =
    broadcasts || {};
  const { optOut } = broadcastDetailsTablesData || {};
  const { resources } = optOut || {};


  return (
    <div>
      <Table
        hasCheckbox={false}
        tableColumns={tableColumns()}
        tableData={resources || []}
        loading={broadcastDetailsTablesLoading}
        next={next}
        previous={previous}
        handlePreviousPage={handlePrevious}
        handleNextPage={handleNext}
      />
    </div>
  );
};

export default OptOutTable;
