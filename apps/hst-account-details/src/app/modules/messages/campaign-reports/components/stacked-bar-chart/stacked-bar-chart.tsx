import PropTypes from 'prop-types'

import { safeSum } from './helpers/safe-calculator'
import Legend from './partials/legend/legend-container'
import StackedBar from './partials/stacked-bar/stacked-bar-container'
import Title from './partials/title/title'

export const getTotalValue = (data) => Object.values(data).reduce((acc, { value }) => safeSum(acc, value), 0)

const StackedBarChart = ({
  data,
  showInfo,
  showLegend,
  chartTitle,
  legendTitle,
  legendTooltip,
  legendClassName,
  children,
  ...restProps
}) => {
  const totalValue = getTotalValue(data)

  return (
    <div {...restProps}>
      {children || (
        <>
          <Title>{chartTitle}</Title>
          <StackedBar showInfo={showInfo} data={data} totalValue={totalValue} />
          {showLegend && <Legend title={legendTitle} tooltip={legendTooltip} data={data} totalValue={totalValue} className={legendClassName} />}
        </>
      )}
    </div>
  )
}

StackedBarChart.propTypes = {
  children: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  data: PropTypes.shape({}),
  showInfo: PropTypes.bool,
  showLegend: PropTypes.bool,
  chartTitle: PropTypes.string,
  legendTitle: PropTypes.string,
  legendTooltip: PropTypes.string,
  legendClassName: PropTypes.string,
}
StackedBarChart.defaultProps = {
  children: null,
  data: {},
  showInfo: false,
  showLegend: false,
  chartTitle: null,
  legendTitle: null,
  legendTooltip: null,
  legendClassName: '',
}

export default StackedBarChart
