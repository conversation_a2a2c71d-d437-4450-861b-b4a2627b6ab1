import moment from 'moment'
import _find from 'lodash/find';

export const getDateRange = ({ dateRange }) => {
  const { startDate, endDate } = dateRange

  return (startDate instanceof moment && endDate instanceof moment)
    ? dateRange
    : {
      startDate: moment(startDate),
      endDate: moment(endDate),
    }
}

export const getSummaryChartLabel = ({ startDate, endDate }) => {
  const tempEndDate = endDate && endDate.isValid() ? endDate : startDate
  const numberOfDays = tempEndDate.diff(startDate, 'days') + 1
  const endDateDiff = endDate.diff(startDate, 'day')
  const isGroupByMonth = endDateDiff > 90
  const isGroupByWeek = endDateDiff > 31 && endDateDiff <= 90

  let dates = []
  let labels = []
  let shortLabels = []
  let groupBy = ['DAY']
  const sDate = startDate.clone()
  if (isGroupByMonth) {
    while (!sDate.isAfter(endDate, 'month')) {
      dates.push(sDate.format('YYYY-MM'))
      labels.push([sDate.format('MMM'), sDate.format('YYYY')])
      sDate.add(1, 'M')
    }
    groupBy = ['MONTH']
  } else if (isGroupByWeek) {
    while (!sDate.isAfter(endDate, 'week')) {
      dates.push(`${sDate.format('YYYY')}#${sDate.get('week')}`)
      labels.push(`${sDate.clone().startOf('w').format('MMM D')}-${sDate.clone().endOf('w').format('MMM D')}`)
      sDate.add(1, 'w')
    }
    groupBy = ['WEEK']
  } else {
    dates = [...Array(numberOfDays)].map((v, i) => startDate.clone().add(i, 'd').format('YYYY-MM-DD'))
    labels = dates.map((date) => [moment(date).format('ddd'), moment(date).format('MMM D')])
    shortLabels = dates.map((date) => moment(date).format('MMM D'))
  }
  return {
    labels,
    shortLabels,
    dates,
    groupBy,
  }
}

export const getDataSets = ({ dates, summaries }) => {
  const sentData = dates.map((date) => {
    const matchedSummaryItem = _find(summaries, (item) => item.group === date)
    if (matchedSummaryItem) {
      return matchedSummaryItem.totalSent
    }
    return 0
  })

  const unitsData = dates.map((date) => {
    const matchedSummaryItem = _find(summaries, (item) => item.group === date)
    if (matchedSummaryItem) {
      return matchedSummaryItem.totalBillingUnits
    }
    return 0
  })
  const receivedData = dates.map((date) => {
    const matchedSummaryItem = _find(summaries, (item) => item.group === date)
    if (matchedSummaryItem) {
      return matchedSummaryItem.totalReceived
    }
    return 0
  })

  const datasets = [
    {
      label: 'Outbound',
      data: sentData,
      backgroundColor: 'rgba(122, 193, 66, .1)',
      borderColor: '#7ac142',
    },
    {
      label: 'Units',
      data: unitsData,
      backgroundColor: 'rgba(0, 69, 124, .1)',
      borderColor: '#00457c',
    },
    {
      label: 'Inbound',
      data: receivedData,
      backgroundColor: 'rgba(116, 131, 139, .1)',
      borderColor: '#8a8b8d',
    },
  ].filter((dataset) => !!dataset.data)

  return datasets;
}

