.container {
  padding: 24px;
}

.description {
  margin-bottom: 24px;
}

.featureList {
  margin-bottom: 24px;

  .header {
    display: grid;
    grid-template-columns: 2fr 120px 1fr 2fr;
    padding: 12px 16px;
    background-color: #f5f5f5;
    font-weight: 600;
    border-radius: 4px 4px 0 0;
    border: 1px solid #e8e8e8;
  }

  .featureItem {
    display: grid;
    grid-template-columns: 2fr 120px 1fr 2fr;
    padding: 12px 16px;
    border: 1px solid #e8e8e8;
    border-top: none;
    align-items: center;

    &:last-child {
      border-radius: 0 0 4px 4px;
    }

    &:hover {
      background-color: #f5f5f5;
    }
  }
}

.dialogContent {
  padding: 24px;

  .dialogSection {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 32px;
  }
}

.tableAccounts {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.formGroup {
  margin-bottom: 16px;

  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
  }
}

.numberInput {
  width: 100%;
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;

  &:focus {
    outline: none;
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  &::-webkit-inner-spin-button,
  &::-webkit-outer-spin-button {
    opacity: 1;
  }
}

.buttonGroup {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.settingsContainer {
  font-size: 14px;
  > div {
    margin-bottom: 4px;
    &:last-child {
      margin-bottom: 0;
    }
  }
}

// Toggle dialog specific styles
.dialogWrapper {
  .dialogContent {
    .dialogSection {
      margin-bottom: 24px;
    }

    .footer {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
    }
  }
}
