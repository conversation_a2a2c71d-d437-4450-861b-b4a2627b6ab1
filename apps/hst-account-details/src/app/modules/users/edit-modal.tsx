import '@nectary/components/spinner';
import { useEffect, useState } from 'react';

import { checkPhoneValid, getCountryCallingCode, getPhoneInfo } from 'helpers';
import { Button, Dialog, PhoneInput, Text } from 'nectary';

import { UserType } from '../../types/users/users';

import styles from './edit-modal.module.less';

const EditModal = ({
  isVisible,
  setIsVisible,
  data,
  onSubmit,
  loading,
}: {
  isVisible: boolean;
  setIsVisible: (isVisible: boolean) => void;
  data: UserType | null;
  onSubmit: (phoneVal?: string) => void;
  loading: boolean;
}) => {
  const [errorText, setErrorText] = useState('');
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [countryCode, setCountryCode] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');

  const hanldeConfirm = async () => {
    if (!countryCode && !phoneNumber) {
      onSubmit('');
      return;
    }

    const isPhoneValid = await checkPhoneValid({
      payload: {
        countryCode: countryCode?.toUpperCase(),
        numbers: [phoneNumber],
      },
    });

    if (isPhoneValid) {
      const countryCodeNumber = getCountryCallingCode(
        countryCode?.toUpperCase()
      );
      const phoneStr = `+${countryCodeNumber}${phoneNumber}`.replaceAll(
        '-',
        ''
      );
      onSubmit(phoneStr);
      return;
    }
    setErrorText('Invalid phone number');
  };

  const hanldeDelete = () => {
    onSubmit('');
  };

  useEffect(() => {
    if (!data) return;
    const phoneInfo = getPhoneInfo(data.phone);
    if (phoneInfo) {
      setCountryCode(phoneInfo.country);
      setPhoneNumber(phoneInfo.nationalNumber);
    }
    setIsEditing(false);
    setErrorText('');
  }, [data, isVisible]);

  const renderEditRow = (label: string, value?: string) => (
    <div className={styles.row}>
      <div className={styles.left}>
        <Text type="m" inline>
          {label}
        </Text>
      </div>
      <div className={styles.right}>
        {!isEditing ? (
          <div>
            <Text type="m" inline emphasized>
              {value || '-'}
            </Text>
            <span className={styles.iconEdit}>
              <Button
                type="subtle-secondary"
                onClick={() => setIsEditing(true)}
                icon={<sinch-icon-edit slot="icon" />}
              />
            </span>
          </div>
        ) : (
          <div className={styles.fieldWrapper}>
            <PhoneInput
              countryCode={countryCode?.toLowerCase()}
              setCountryCode={setCountryCode}
              phoneNumber={phoneNumber}
              setPhoneNumber={setPhoneNumber}
              errorText={errorText}
            />
            <div className={styles.buttonWrapper}>
              <Button
                label="Save"
                type="primary"
                disabled={loading}
                onClick={hanldeConfirm}
                icon={loading ? <sinch-spinner slot="icon" /> : null}
              />
              <Button
                label="Delete"
                type="destructive"
                disabled={loading}
                onClick={hanldeDelete}
                icon={loading ? <sinch-spinner slot="icon" /> : null}
              />
              <Button
                label="Cancel"
                type="secondary"
                disabled={loading}
                onClick={() => setIsEditing(false)}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );

  const renderStaticRow = (label: string, value: string) => (
    <div className={styles.row}>
      <div className={styles.left}>
        <Text type="m" inline>
          {label}
        </Text>
      </div>
      <div className={styles.right}>
        <Text type="m" inline emphasized>
          {value || '-'}
        </Text>
      </div>
    </div>
  );

  if (!data) return null;

  let accountNames = data.accounts.map((account) => account.accountName);
  accountNames = accountNames.join(', ');

  return (
    <div className={styles.dialogWrap}>
      <Dialog
        isOpen={isVisible}
        caption="Update User Phone Number"
        onClose={() => setIsVisible(false)}
      >
        <div className={styles.modalWrap} slot="content">
          {renderStaticRow('Account(s):', accountNames)}
          {renderStaticRow('User name:', data.username)}
          {renderStaticRow('Email:', data.email)}
          {renderEditRow('Phone number:', data.phone)}
        </div>
      </Dialog>
    </div>
  );
};

export default EditModal;
