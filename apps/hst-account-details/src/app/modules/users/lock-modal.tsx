import { Button, Dialog, TextArea } from 'nectary';
import { Field, Form } from 'react-final-form';
import { useDispatch } from 'react-redux';
import styles from './lock-modal.module.less';
import { lockUser } from '../../redux/users/users-slice';

type FormValues = {
  reason?: string;
};

type LockModalProps = {
  isVisible: boolean;
  setIsVisible: (isVisible: boolean) => void;
  vendorId: string;
  accountId: string;
  userId: string;
};

const validate = (values: FormValues): Partial<FormValues> => {
  const errors: Partial<FormValues> = {};

  if (!values.reason) {
    errors.reason = 'Reason is required';
  } else if (values.reason.length < 5) {
    errors.reason = 'Reason must be at least 5 characters long';
  } else if (values.reason.length > 100) {
    errors.reason = 'Reason must not exceed 100 characters';
  }

  return errors;
};
const LockModal = ({
  isVisible,
  setIsVisible,
  userId,
  accountId,
  vendorId,
}: LockModalProps) => {
  const dispatch = useDispatch();
  const onSubmit = (values: FormValues) => {
    dispatch(
      lockUser({
        payload: {
          reason: 'FRAUD',
          reasonDetails: values.reason ?? '',
        },
        vendorId,
        accountId,
        userId,
      })
    );
  };
  return (
    <div className={styles.dialogWrap}>
      <Dialog
        isOpen={isVisible}
        caption="Provide a reason"
        onClose={() => setIsVisible(false)}
      >
        <Form<FormValues>
          onSubmit={onSubmit}
          validate={validate}
          initialValues={{ reason: '' }}
          render={({ handleSubmit, form, submitting, pristine }) => (
            <form>
              <Field
                name="reason"
                label="lock-reason-field"
                data-test="lock-reason-field"
              >
                {({ input, meta }) => (
                  <div slot="content" className={styles.content}>
                    <TextArea
                      fieldStyles={{ width: '100%' }}
                      {...input}
                      testId="lockReasonInput"
                      errorText={meta.error && meta.touched ? meta.error : ''}
                    />
                  </div>
                )}
              </Field>

              <i className={styles.note}>
                * At least 5 characters and maximum 100 characters
              </i>

              <div className={styles.btnGroup}>
                <Button
                  type="secondary"
                  onClick={() => setIsVisible(false)}
                  label="Cancel"
                  disabled={submitting}
                />

                <Button
                  type="primary"
                  onClick={handleSubmit}
                  label="Lock user"
                  disabled={submitting || pristine}
                  icon={submitting ? <sinch-spinner slot="icon" /> : null}
                />
              </div>
            </form>
          )}
        />
      </Dialog>
    </div>
  );
};

export default LockModal;
