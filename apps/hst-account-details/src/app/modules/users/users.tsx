import '@nectary/components/skeleton';
import '@nectary/components/skeleton-item';
import {
  customDateTimeFormatReadable,
  getPermissionWithKey,
  isNonEmptyString,
  ROLES_KEY,
} from 'helpers';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _pluralize from 'pluralize';
import { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { RootState } from 'apps/hst-account-details/src/app/types/store';
import {
  Button,
  Dialog,
  FULL_TREE_MULTI_SELECT,
  Input,
  MultiSelect,
  Spinner,
  Table,
  Tag,
  Toast,
} from 'nectary';

import {
  ACCEPTED,
  ASCENDING,
  CANCELLED,
  DECLINED,
  LOADING_STATUS,
  PENDING,
  SUSPENDED,
} from '../../constants';
import {
  fetchSubAccounts,
  fetchUsers,
  updateUser,
} from '../../redux/users/users-slice';
import { ToastItem } from '../../types';
import { AccountType, SubAccount, UserType } from '../../types/users/users';
import EditModal from './edit-modal';

import LockModal from './lock-modal';
import UserAction from './user-action';
import styles from './users.module.less';

const USER_ROLE_MAPPING = {
  admin: 'admin',
  user: 'user',
  basic: 'basic',
};

const MAX_ACCOUNT_TOOLTIP_CAN_BE_SHOWN = 5;

const spaceStringTo = (rawString: string, replacedStr = '-') =>
  rawString.replace(/ /g, replacedStr);

export const renderTooltipContentForMultipleAccounts = (
  accounts: AccountType[]
) => {
  const accountNames = accounts
    .slice(0, MAX_ACCOUNT_TOOLTIP_CAN_BE_SHOWN)
    .map((a) => (
      <li
        className={styles.tooltipItem}
        key={spaceStringTo(a.accountName, '-')}
      >
        {a.accountName} ({_get(USER_ROLE_MAPPING, a.role, a.role)}) -{' '}
        {a.invitationStatus}
      </li>
    ));
  const restAccountCount = accounts.length - MAX_ACCOUNT_TOOLTIP_CAN_BE_SHOWN;
  return (
    <ul className={styles.tooltipWrapper}>
      {accountNames}
      {restAccountCount > 0 && (
        <li className={styles.tooltipItem}>
          +{restAccountCount}
          {_pluralize(' Account', restAccountCount)}
        </li>
      )}
    </ul>
  );
};

export const renderAccountsCellDisplayText = (
  c: UserType,
  currentAccountId: string,
  showAccountsModal: (item: UserType) => void
) => {
  const currentAccountInList = c.accounts.filter(
    (a) => a.accountId === currentAccountId
  );
  let showingContent = `Has access to ${c.accounts.length} accounts`;
  if (currentAccountInList.length) {
    const account = currentAccountInList[0];
    const otherAccountsCount = c.accounts.length - 1;
    showingContent = `${account.accountName} (${_get(
      USER_ROLE_MAPPING,
      account.role,
      account.role
    )} - ${
      account.invitationStatus
    }) and ${otherAccountsCount} other ${_pluralize(
      'account',
      otherAccountsCount
    )}`;
  }

  return (
    <>
      {showingContent}
      <Button
        label="Show more accounts"
        type="tertiary"
        size="xs"
        onClick={() => {
          showAccountsModal(c);
        }}
      />
    </>
  );
};

const renderStatus = (status: string) =>
  ({
    [ACCEPTED]: <Tag key={status} color="light-green" text={status} />,
    [PENDING]: <Tag key={status} color="light-orange" text={status} />,
    [DECLINED]: <Tag key={status} color="light-red" text={status} />,
  }[status]);

export const renderAccountsCell = (
  c: UserType,
  currentAccountId: string,
  showAccountsModal: (item: UserType) => void
) => {
  const accounts = _get(c, 'accounts', []);
  let showingContent;

  if (accounts.length > 1) {
    showingContent = renderAccountsCellDisplayText(
      c,
      currentAccountId,
      showAccountsModal
    );
  } else {
    const account = accounts[0];
    showingContent = `${account.accountName} (${_get(
      USER_ROLE_MAPPING,
      account.role,
      account.role
    )})`;
  }

  return (
    <div className={styles.accountsCell}>
      {showingContent}
      {accounts.length === 1 && renderStatus(accounts[0].invitationStatus)}
    </div>
  );
};

const tableColumns = (
  accountId: string,
  showAccountsModal: (item: UserType) => void,
  showEditModal: (item: UserType) => void,
  showLockModal: (item: UserType) => void,
  isUsersEditable: boolean
) => [
  {
    title: 'Name',
    index: 'id',
    sort: false,
    align: 'left',
    render: (value: UserType) => (
      <sinch-text type="s">{`${value.firstName} ${value.lastName}`}</sinch-text>
    ),
  },
  {
    title: 'Email',
    index: 'email',
    sort: false,
    align: 'left',
    render: (value: UserType) => (
      <sinch-text type="s">{value.email}</sinch-text>
    ),
  },
  {
    title: 'Phone',
    index: 'c',
    sort: false,
    align: 'left',
    render: (value: UserType) => (
      <sinch-text type="s">{value.phone}</sinch-text>
    ),
  },
  {
    title: 'User Id',
    index: 'id',
    sort: false,
    align: 'left',
    render: (value: UserType) => <sinch-text type="s">{value.id}</sinch-text>,
  },
  {
    title: 'Accounts',
    index: 'id',
    sort: false,
    align: 'left',
    render: (value: UserType) =>
      renderAccountsCell(value, accountId, showAccountsModal),
  },
  {
    title: 'Status',
    index: 'id',
    sort: false,
    align: 'left',
    render: (value: UserType) => {
      if (value.locked) {
        return <Tag key="locked" color="light-red" text="Locked" />;
      }
      return <Tag key="unlocked" color="light-green" text="Unlocked" />;
    },
  },
  {
    title: 'Last Login',
    index: 'lastLoggedIn',
    sort: false,
    align: 'left',
    render: (value: UserType) => (
      <sinch-text type="s">
        {isNonEmptyString(value.lastLoggedIn)
          ? customDateTimeFormatReadable({
              datetime: value.lastLoggedIn,
              showTime: false,
            })
          : '-'}
      </sinch-text>
    ),
  },
  {
    title: 'Action',
    index: 'id',
    sort: false,
    align: 'center',
    render: (value: UserType) => (
      <UserAction
        handleEdit={() => showEditModal(value)}
        handleLock={() => showLockModal(value)}
        isDisabledEdit={!isUsersEditable}
        isDisabledLock={value.locked || !isUsersEditable}
      />
    ),
  },
];

const ROLE_OPTIONS = [
  { value: 'admin', label: 'Administrator' },
  { value: 'user', label: 'User' },
  { value: 'basic', label: 'Basic' },
];

const PAGE_SIZE = 10;
type OPTIONS = Array<{
  label: string;
  value: string;
}>;
let ACCOUNT_OPTIONS: OPTIONS = [];

const Users = ({
  accountId,
  vendorId,
  roles: accountRoles,
}: {
  accountId: string;
  vendorId: string;
  roles: string[];
}) => {
  const dispatch = useDispatch();
  const rootState = useSelector((state: RootState) => state || {});
  const { users, accountDetails } = rootState;
  const { details } = accountDetails || {};
  const {
    data,
    loading,
    subAccountsData,
    loadingSubAccounts,
    updateLoading,
    lockLoading,
  } = users || {};
  const { pagination, resources } = data || {};
  const { resources: subAccounts } = subAccountsData || {};

  const [filter, setFilter] = useState('');
  const [accountIds, setAccountIds] = useState([accountId]);
  const [roles, setRoles] = useState([
    ROLE_OPTIONS[0].value,
    ROLE_OPTIONS[1].value,
    ROLE_OPTIONS[2].value,
  ]);
  const [currentPageSize, setCurrentPageSize] = useState(PAGE_SIZE);
  // const [sortBy, setSortBy] = useState(undefined);
  const [isAccountsModalVisible, setIsAccountsModalVisible] = useState(false);
  const [selectedItem, setSelectedItem] = useState<UserType | null>(null);
  const [isEditModalVisible, setEditModalVisible] = useState(false);
  const [isLockModalVisible, setIsLockModalVisible] = useState(false);
  const [editedItem, setEditedItem] = useState<UserType | null>(null);
  const [toasts, setToasts] = useState<ToastItem[]>([]);
  const [currentUrlParams, setCurrentUrlParams] = useState(null);
  const [lockedUserId, setLockedUserId] = useState('');

  useEffect(() => {
    dispatch(
      fetchUsers({
        accountId,
        params: {
          filter,
          ...(!accountIds.includes(FULL_TREE_MULTI_SELECT) && { accountIds }),
          roles,
          // sortBy,
          sortDirection: ASCENDING,
          page: 1,
          pageSize: currentPageSize,
          vendorId,
        },
      })
    );
    dispatch(
      fetchSubAccounts({
        accountId,
        vendorId,
        params: {
          size: 100,
        },
      })
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const refetchUserData = useCallback(
    () =>
      setTimeout(() => {
        const urlParam = currentUrlParams || new URLSearchParams();
        const page = urlParam.get('page') ? Number(urlParam.get('page')) : 1;
        dispatch(
          fetchUsers({
            accountId,
            params: {
              filter,
              ...(!accountIds.includes(FULL_TREE_MULTI_SELECT) && {
                accountIds,
              }),
              roles,
              // sortBy,
              sortDirection: ASCENDING,
              page,
              pageSize: currentPageSize,
              vendorId,
            },
          })
        );
      }, 500),

    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      accountId,
      accountIds,
      currentPageSize,
      currentUrlParams,
      filter,
      roles,
      vendorId,
    ]
  );

  useEffect(() => {
    if (!lockedUserId) return;
    let newToasts = toasts;
    if (
      lockLoading === LOADING_STATUS.SUCCEEDED ||
      lockLoading === LOADING_STATUS.FAILED
    ) {
      const text =
        lockLoading === LOADING_STATUS.SUCCEEDED
          ? `Successfully locked user`
          : `Failed to lock user`;
      newToasts = toasts.concat({
        id: lockedUserId,
        text,
        type: lockLoading === LOADING_STATUS.SUCCEEDED ? 'success' : 'error',
      });
      setToasts(newToasts);
      setIsLockModalVisible(false);
      setLockedUserId('');
      refetchUserData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [lockLoading, refetchUserData]);

  useEffect(() => {
    if (!editedItem) return;
    let newToasts = toasts;
    if (
      updateLoading === LOADING_STATUS.SUCCEEDED ||
      updateLoading === LOADING_STATUS.FAILED
    ) {
      const text =
        updateLoading === LOADING_STATUS.SUCCEEDED
          ? `Successfully updated user phone number`
          : `Failed to update user phone number`;
      newToasts = toasts.concat({
        id: editedItem.username,
        text,
        type: updateLoading === LOADING_STATUS.SUCCEEDED ? 'success' : 'error',
      });
      setToasts(newToasts);
      setEditModalVisible(false);
      setEditedItem(null);
      refetchUserData();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [updateLoading, refetchUserData]);

  const handleOnKeyDown = (e: React.KeyboardEvent<HTMLFormElement>) => {
    if (e.key === 'Enter') {
      handleSubmit();
    }
  };

  if (subAccounts && details) {
    let mappedSubAccountsData = [];
    // eslint-disable-next-line func-names
    const nonSuspendedMappedSubAccountsData = subAccounts.filter(
      (item: SubAccount) =>
        item.status !== SUSPENDED && item.status !== CANCELLED
    );

    mappedSubAccountsData = nonSuspendedMappedSubAccountsData?.map(
      (item: SubAccount) => ({
        value: item.accountId,
        label: item.accountLabel,
      })
    );
    if (mappedSubAccountsData)
      ACCOUNT_OPTIONS = [
        // { value: FULL_TREE, label: 'Select Full tree' },
        { value: accountId, label: details.label },
        ...mappedSubAccountsData,
      ];
    else {
      ACCOUNT_OPTIONS = [{ value: accountId, label: details.label }];
    }
  }

  const handlePrevious = () => {
    if (pagination?.previous) {
      const urlParam = new URLSearchParams(pagination.previous);
      setCurrentUrlParams(urlParam);
      const page = urlParam.get('page') ? Number(urlParam.get('page')) : 1;
      setTimeout(() => {
        dispatch(
          fetchUsers({
            accountId,
            params: {
              filter,
              ...(!accountIds.includes(FULL_TREE_MULTI_SELECT) && {
                accountIds,
              }),
              roles,
              // sortBy: undefined,
              sortDirection: ASCENDING,
              page,
              pageSize: currentPageSize,
              vendorId,
            },
          })
        );
      });
    }
  };

  const handleNext = () => {
    if (pagination?.next) {
      const urlParam = new URLSearchParams(pagination.next);
      setCurrentUrlParams(urlParam);
      const page = urlParam.get('page') ? Number(urlParam.get('page')) : 1;
      setTimeout(() => {
        dispatch(
          fetchUsers({
            accountId,
            params: {
              filter,
              ...(!accountIds.includes(FULL_TREE_MULTI_SELECT) && {
                accountIds,
              }),
              roles,
              // sortBy: undefined,
              sortDirection: ASCENDING,
              page,
              pageSize: currentPageSize,
              vendorId,
            },
          })
        );
      });
    }
  };

  const renderAccounts = () => {
    if (!selectedItem) return null;
    return (
      <ul>
        {selectedItem.accounts.map((account: AccountType) => (
          <li
            className={styles.accountItem}
            key={spaceStringTo(account.accountName, '-')}
          >
            {account.accountName} (
            {_get(USER_ROLE_MAPPING, account.role, account.role)}) -{' '}
            <span className={styles.statusTag}>
              {renderStatus(account.invitationStatus)}
            </span>
          </li>
        ))}
      </ul>
    );
  };

  const showAccountsModal = (user: UserType) => {
    setSelectedItem(user);
    setIsAccountsModalVisible(true);
  };

  const showEditModal = (user: UserType) => {
    setEditedItem(user);
    setEditModalVisible(true);
  };

  const showLockModal = (user: UserType) => {
    setLockedUserId(user.id);
    setIsLockModalVisible(true);
  };

  const handleUpdateUser = (phoneVal?: string) => {
    if (!editedItem) return;

    dispatch(
      updateUser({
        vendorId,
        accountId,
        userId: editedItem.id,
        params: {
          phone: phoneVal || '',
        },
      })
    );
  };

  const handleSubmit = () => {
    if (_isEmpty(roles)) return;

    dispatch(
      fetchUsers({
        accountId,
        params: {
          filter,
          ...(!accountIds.includes(FULL_TREE_MULTI_SELECT) && { accountIds }),
          roles,
          // sortBy: undefined,
          sortDirection: ASCENDING,
          page: 1,
          pageSize: currentPageSize,
          vendorId,
        },
      })
    );
  };

  const handleChangePageSize = (newSize: number) => {
    setCurrentPageSize(newSize);
    dispatch(
      fetchUsers({
        accountId,
        params: {
          filter,
          ...(!accountIds.includes(FULL_TREE_MULTI_SELECT) && { accountIds }),
          roles,
          sortDirection: ASCENDING,
          page: 1,
          pageSize: newSize,
          vendorId,
        },
      })
    );
  };

  const handleSelectAccounts = (newAccountIds: string[]) => {
    if (!accountIds.includes(FULL_TREE_MULTI_SELECT)) {
      setAccountIds(newAccountIds);
      return;
    }

    setAccountIds(newAccountIds);
  };

  const { isEditable: isUsersEditable } = getPermissionWithKey(
    ROLES_KEY.USERS,
    accountRoles
  );

  return (
    <div className={styles.wrapper}>
      {/* eslint-disable-next-line jsx-a11y/no-noninteractive-element-interactions */}
      <form onKeyDown={handleOnKeyDown}>
        <div className={styles.formWrap}>
          <div className={styles.formItem}>
            <Input label="User" onChange={setFilter} />
          </div>
          <div className={styles.formItem}>
            {loadingSubAccounts === LOADING_STATUS.LOADING && (
              <div className={styles.spinnerWrap}>
                <Spinner size="m" />
              </div>
            )}
            <MultiSelect
              label="Account"
              value={accountIds || []}
              options={ACCOUNT_OPTIONS}
              onSelect={handleSelectAccounts}
              allLabel="Select All Subaccounts"
              hasFullTree
            />
          </div>
          <div className={styles.formItem}>
            <MultiSelect
              label="Role"
              value={roles}
              options={ROLE_OPTIONS}
              onSelect={setRoles}
              errorText={_isEmpty(roles) ? 'Role is required' : ''}
            />
          </div>
          <div className={styles.formItem}>
            <div className={styles.submitBtn}>
              <Button
                label="Search"
                onClick={handleSubmit}
                disabled={_isEmpty(roles)}
              />
            </div>
          </div>
        </div>
      </form>
      <div>
        <Table
          hasCheckbox={false}
          tableColumns={tableColumns(
            accountId,
            showAccountsModal,
            showEditModal,
            showLockModal,
            isUsersEditable
          )}
          tableData={resources}
          loading={loading}
          next={pagination?.next}
          previous={pagination?.previous}
          handlePreviousPage={handlePrevious}
          handleNextPage={handleNext}
          pageSize={currentPageSize}
          handleChangePageSize={handleChangePageSize}
        />
      </div>
      <Dialog
        isOpen={isAccountsModalVisible}
        caption={`Accounts for ${selectedItem?.email}`}
        onClose={() => {
          setIsAccountsModalVisible(false);
        }}
      >
        {renderAccounts()}
      </Dialog>
      <EditModal
        data={editedItem}
        isVisible={isEditModalVisible}
        setIsVisible={setEditModalVisible}
        onSubmit={handleUpdateUser}
        loading={updateLoading === LOADING_STATUS.LOADING}
      />

      <LockModal
        isVisible={isLockModalVisible}
        setIsVisible={setIsLockModalVisible}
        vendorId={vendorId}
        accountId={accountId}
        userId={lockedUserId}
      />

      <Toast toasts={toasts} setToasts={setToasts} />
    </div>
  );
};

export default Users;
