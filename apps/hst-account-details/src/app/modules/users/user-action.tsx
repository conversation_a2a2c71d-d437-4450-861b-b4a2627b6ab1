import { ActionMenu } from 'nectary';
import type { FC } from 'react';

import '@nectary/components/action-menu';
import '@nectary/components/action-menu-option';
import '@nectary/components/avatar';
import '@nectary/components/button';
import '@nectary/components/popover';

type UserActionProps = {
  handleEdit: () => void;
  isDisabledEdit: boolean;
  handleLock: () => void;
  isDisabledLock: boolean;
};

const UserAction: FC<UserActionProps> = ({
  handleEdit,
  isDisabledEdit,
  handleLock,
  isDisabledLock,
}) => {
  const UserMenu = [
    {
      text: 'Edit',
      disabled: isDisabledEdit,
      onClick: () => {
        handleEdit();
      },
    },
    {
      text: 'Lock',
      disabled: isDisabledLock,
      onClick: () => {
        handleLock();
      },
    },
  ];

  return <ActionMenu menus={UserMenu} orientation="bottom-left" />;
};

export default UserAction;
