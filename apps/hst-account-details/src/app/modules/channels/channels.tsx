import { useState, useEffect } from 'react';

import { Tab } from 'nectary';
import { getPermissionWithKey, ROLES_KEY } from 'helpers';

import SenderAddresses from './tabs/sender-addresses/sender-addresses';
import EmailToSMS from './tabs/email-to-sms/email-to-sms';
import DedicatedNumbers from './tabs/dedicated-numbers';

import styles from './channels.module.less';

const SUB_TAB_PARAM = 'subTab';

export type TabItem = {
  tabName: string;
  value: number;
  text: string;
};

export const TABS_INFO = ({
  isChannelsNumbersVisible,
  isChannelsSocialVisible,
}: {
  isChannelsNumbersVisible: boolean;
  isChannelsSocialVisible: boolean;
}) => {
  const tabs = [];
  let tabValue = 1;

  if (isChannelsNumbersVisible) {
    tabs.push({
      value: tabValue,
      tabName: 'dedicatedNumbers',
      text: 'Dedicated Numbers',
    });
    tabValue += 1;
    tabs.push({
      value: tabValue,
      tabName: 'senderAddresses',
      text: 'Sender Addresses',
    });
  }
  if (isChannelsSocialVisible) {
    tabValue += 1;
    tabs.push({
      value: tabValue,
      tabName: 'rcs',
      text: 'RCS',
    });
  }

  tabValue += 1;
  tabs.push({
    value: tabValue,
    tabName: 'emailToSMS',
    text: 'Email To SMS',
  });

  return tabs;
};

const tabContent = ({
  accountId,
  vendorId,
  isChannelsNumbersVisible,
  isChannelsSocialVisible,
  isChannelsNumbersEditable,
  roles
}: {
  accountId: string;
  vendorId: string;
  isChannelsNumbersVisible: boolean;
  isChannelsSocialVisible: boolean;
  isChannelsNumbersEditable: boolean;
  roles?: string[];
}) => {
  const tabContents = [];
  let value = 1;
  if (isChannelsNumbersVisible) {
    tabContents.push({
      value,
      tabName: 'dedicatedNumbers',
      content: <DedicatedNumbers accountId={accountId} vendorId={vendorId} roles={roles} />,
    });

    value += 1;
    tabContents.push({
      value,
      tabName: 'senderAddresses',
      content: (
        <SenderAddresses
          accountId={accountId}
          vendorId={vendorId}
          isVisible={isChannelsNumbersVisible}
        />
      ),
    });
  }

  if (isChannelsSocialVisible) {
    value += 1;
    tabContents.push({
      value,
      tabName: 'rcs',
      content: (
        <mfe-loader
          name="ChannelsDashboard"
          base-path={window.location.pathname}
        />
      ),
    });
  }

  value += 1;
  tabContents.push({
    value,
    tabName: 'emailToSMS',
    content: (
      <EmailToSMS
        accountId={accountId}
        vendorId={vendorId}
        isChannelsNumbersVisible={isChannelsNumbersVisible}
        isChannelsNumbersEditable={isChannelsNumbersEditable}
      />
    ),
  });

  return tabContents;
};

type ChannelsProps = {
  accountId: string;
  vendorId: string;
  roles: string[];
};

const getSubTabIndexByName = (
  dislayedTabs: TabItem[],
  subTabName: string | null
): string => {
  const currentTab = dislayedTabs.find(
    (tab: TabItem) => tab.tabName === subTabName
  );
  return currentTab ? currentTab.value.toString() : '1';
};

const Channels = ({ accountId, vendorId, roles }: ChannelsProps) => {
  const [currentSubTab, setCurrentSubTab] = useState('1');

  const queryParams = new URLSearchParams(window.location.search);
  const tabURLParam = queryParams.get(SUB_TAB_PARAM);

  const {
    isVisible: isChannelsNumbersVisible,
    isEditable: isChannelsNumbersEditable,
  } = getPermissionWithKey(ROLES_KEY.ACCOUNT_CHANNELS_NUMBERS, roles, true);

  const { isVisible: isChannelsSocialVisible } = getPermissionWithKey(
    ROLES_KEY.ACCOUNT_CHANNELS_SOCIAL,
    roles,
    true
  );

  const dislayedTabs = TABS_INFO({
    isChannelsNumbersVisible,
    isChannelsSocialVisible,
  });

  useEffect(() => {
    setCurrentSubTab(getSubTabIndexByName(dislayedTabs, tabURLParam));
  }, []);

  const onChangeTab = (newTabVal) => {
    setCurrentSubTab(newTabVal);
    const newTab = dislayedTabs.find(
      (tab: TabItem) => `${tab.value}` === newTabVal
    );
    queryParams.set(SUB_TAB_PARAM, newTab?.tabName);
    const newQueryString = queryParams.toString();
    const newUrl = `${window.location.origin}${window.location.pathname}?${newQueryString}`;
    window.history.replaceState({ path: newUrl }, '', newUrl);
  };

  return (
    <div>
      <div className={styles.tab}>
        <Tab
          currentTab={currentSubTab}
          tabs={dislayedTabs}
          tabContent={tabContent({
            accountId,
            vendorId,
            isChannelsNumbersVisible,
            isChannelsSocialVisible,
            isChannelsNumbersEditable,
            roles
          })}
          onChangeTab={onChangeTab}
        />
      </div>
    </div>
  );
};

export default Channels;
