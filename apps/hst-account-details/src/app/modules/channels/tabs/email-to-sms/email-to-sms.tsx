import { useState } from 'react';

import { Tab } from 'nectary';

import Emails from './emails/emails';
import Domains from './domains/domains';
import Settings from './settings/settings';

import styles from './email-to-sms.module.less';

const TABS_INFO = ({ isChannelsNumbersVisible }: { isChannelsNumbersVisible: boolean }) => {
  const tabs = [
    {
      value: 1,
      tabName: 'emails',
      text: 'Emails',
    },
    {
      value: 2,
      tabName: 'domains',
      text: 'Domains',
    },
  ];
  if (isChannelsNumbersVisible) {
    tabs.push({
      value: 3,
      tabName: 'domains',
      text: 'Settings',
    });
  }
  return tabs;
};

const tabContent = ({
  accountId,
  vendorId,
  isChannelsNumbersEditable,
}: {
  accountId: string;
  vendorId: string;
  isChannelsNumbersEditable: boolean;
}) => [
  {
    value: 1,
    tabName: 'emails',
    content: <Emails accountId={accountId} vendorId={vendorId} />,
  },
  {
    value: 2,
    tabName: 'domains',
    content: <Domains accountId={accountId} vendorId={vendorId} />,
  },
  {
    value: 3,
    tabName: 'domains',
    content: <Settings accountId={accountId} vendorId={vendorId} isEditable={isChannelsNumbersEditable} />,
  },
];

type EmailToSMSProps = {
  accountId: string;
  vendorId: string;
  isChannelsNumbersVisible: boolean;
  isChannelsNumbersEditable: boolean;
};

const EmailToSMS = ({
  accountId,
  vendorId,
  isChannelsNumbersVisible,
  isChannelsNumbersEditable,
}: EmailToSMSProps) => {
  const [currentTab, setCurrentTab] = useState('1');

  const onChangeTab = () => {};

  return (
    <div>
      {/* <div className={styles.description}>
        <Text type="s">This page displays Alpha tags, Dedicated numbers (except Toll-free numbers) and Own Numbers</Text>
      </div> */}
      <div className={styles.tabWrap}>
        <Tab
          currentTab={currentTab}
          tabs={TABS_INFO({
            isChannelsNumbersVisible,
          })}
          tabContent={tabContent({ accountId, vendorId, isChannelsNumbersEditable })}
          onChangeTab={onChangeTab}
        />
      </div>
    </div>
  );
};

export default EmailToSMS;
