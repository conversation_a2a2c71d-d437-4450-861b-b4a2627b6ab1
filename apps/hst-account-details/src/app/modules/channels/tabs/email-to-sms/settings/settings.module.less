
.card {
  padding: 24px;
  margin: 20px 0;
  border-radius: var(--sinch-comp-card-shape-radius);
  border: 1px solid var(--sinch-comp-card-color-white-default-border-initial);
  background-color: var(
    --sinch-comp-card-color-white-default-background-initial
  );
  box-shadow: var(--sinch-comp-card-shadow-initial);
  overflow: hidden;
  position: relative;
}

.title {
  margin-bottom: 24px;
}

.formWrap {
  max-width: 500px;
}

.formItem {
  margin-bottom: 24px;
}

.row {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-end;

  .left {
    min-width: 200px;
  }
}

.spinnerWrap {
  width: 100%;
  height: 100%;
  position: absolute;
  opacity: 0.3;
  display: flex;
  align-items: center;
}
