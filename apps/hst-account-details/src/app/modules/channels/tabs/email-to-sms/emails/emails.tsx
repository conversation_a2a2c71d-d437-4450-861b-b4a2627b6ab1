import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Table, Text, Input, Button } from 'nectary';
import { isNonEmptyString, customDateTimeFormatReadable } from 'helpers'
import { EmailItem } from 'apps/hst-account-details/src/app/types/channels';
import { RootState } from 'apps/hst-account-details/src/app/types/store';

import { fetchEmails } from '../../../../../redux/channels/channels-slice'

import styles from './emails.module.less'

const tableColumns = () => [
  {
    title: 'Email Address',
    index: 'value',
    sort: false,
    align: 'left',
    render: (value: EmailItem) => (
      <Text type="s">{value.value}</Text>
    ),
  },
  {
    title: 'Created Date',
    index: 'createdAt',
    sort: false,
    align: 'left',
    render: (values: EmailItem) => {
      const displayedTime = isNonEmptyString(values.createdAt) ? customDateTimeFormatReadable({
        datetime: values.createdAt,
        showTime: true,
      }) : '-'
      return <Text type="s">{displayedTime}</Text>
    }
  }
];

type EmailsProps = {
  accountId: string,
  vendorId: string,
}

const DEFAULT_PAGE_SIZE = 10

const Emails = ({ accountId, vendorId }: EmailsProps) => {
  const dispatch = useDispatch()
  const { accountChannels } = useSelector((state: RootState) => state);
  const { emails, emailsLoading } = accountChannels || {}
  const { resources, pagination } = emails || {}
  const { next } = pagination || {}

  const [tokenQueue, setTokenQueue] = useState<string[]>([])
  const [filter, setFilter] = useState('')

  useEffect(() => {
    dispatch(fetchEmails({
      filter: filter || undefined,
      vendorId,
      accountId,
      size: DEFAULT_PAGE_SIZE
    }))
  }, [])

  const handlePrevious = () => {
    tokenQueue.pop();
    setTokenQueue(tokenQueue);
    const tokensLen = tokenQueue.length;

    setTimeout(() => {
      dispatch(fetchEmails({
        filter: filter || undefined,
        accountId,
        vendorId,
        size: DEFAULT_PAGE_SIZE,
        ...(tokensLen && { next: tokenQueue[tokensLen - 1] }),
      }))
    });
  }
  const handleNext = (token: string) => {
    tokenQueue.push(token);
    setTokenQueue(tokenQueue);

    setTimeout(() => {
      dispatch(fetchEmails({
        filter: filter || undefined,
        accountId,
        vendorId,
        size: DEFAULT_PAGE_SIZE,
        next: token
      }))
    });
  }

  const handleSearch = () => {
    setTimeout(() => {
      dispatch(fetchEmails({
        filter: filter || undefined,
        accountId,
        vendorId,
        size: DEFAULT_PAGE_SIZE,
      }))
    });
  }

  const handleOnKeyDown = (e: React.KeyboardEvent<HTMLFormElement>) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <div>
      <form className={styles.formWrap} onKeyDown={handleOnKeyDown}>
        <div className={styles.formItem}>
          <Input
            label="Email"
            value={filter}
            onChange={setFilter}
          />
        </div>
        <div className={styles.buttonSearch}>
          <Button
            label="Search"
            onClick={handleSearch}
          />
        </div>
      </form>
      <div>
        <Table
          hasCheckbox={false}
          tableColumns={tableColumns()}
          tableData={resources}
          loading={emailsLoading}
          next={next}
          previous={tokenQueue.length}
          handlePreviousPage={handlePrevious}
          handleNextPage={handleNext}
        />
      </div>
    </div>
  );
};

export default Emails;
