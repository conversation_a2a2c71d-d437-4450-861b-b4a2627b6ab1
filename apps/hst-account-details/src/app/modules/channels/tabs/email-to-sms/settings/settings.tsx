import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Text, Toggle, Spinner, Toast } from 'nectary';
import { EmailItem } from 'apps/hst-account-details/src/app/types/channels';
import { RootState } from 'apps/hst-account-details/src/app/types/store';
import { ToastItem } from 'apps/hst-account-details/src/app/types';

import {
  fetchEmail2SMSSettings,
  updateEmail2SMSSettings,
} from '../../../../../redux/channels/channels-slice';
import { LOADING_STATUS } from '../../../../../constants/index';
import { EditableInput } from '../../../../../components/editable-field/editable-input';

import styles from './settings.module.less';

type SettingsProps = {
  accountId: string;
  vendorId: string;
  isEditable: boolean;
};

const isMaxSmsFragmentsValid = (value) =>
  !value || (!isNaN(value) && Number(value) >= 0 && Number(value) <= 999);

const Settings = ({ accountId, vendorId, isEditable }: SettingsProps) => {
  const dispatch = useDispatch();
  const { accountChannels } = useSelector((state: RootState) => state);
  const {
    email2SMSSettings,
    email2SMSSettingsLoading,
    updateEmail2SMSSettingsLoading,
  } = accountChannels || {};

  const [maxSmsFragments, setMaxSmsFragments] = useState(
    email2SMSSettings?.maxSmsFragments || ''
  );
  const [bodyOnly, setSetBodyOnly] = useState(
    email2SMSSettings?.bodyOnly || false
  );
  const [isValidated, setIsValidated] = useState(false);
  const [toasts, setToasts] = useState<ToastItem[]>([]);
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    dispatch(
      fetchEmail2SMSSettings({
        vendorId,
        accountId,
      })
    );
  }, []);

  useEffect(() => {
    if (email2SMSSettings) {
      setMaxSmsFragments(email2SMSSettings.maxSmsFragments || '');
      setSetBodyOnly(email2SMSSettings.bodyOnly || false);
    }
  }, [email2SMSSettings]);

  useEffect(() => {
    let newToasts = [];
    if (
      updateEmail2SMSSettingsLoading === LOADING_STATUS.SUCCEEDED ||
      updateEmail2SMSSettingsLoading === LOADING_STATUS.FAILED
    ) {
      const text =
        updateEmail2SMSSettingsLoading === LOADING_STATUS.SUCCEEDED
          ? `Successfully updated Email2SMS Settings`
          : `Failed to update Email2SMS Settings`;
      newToasts = toasts.concat({
        id: accountId,
        text,
        type:
          updateEmail2SMSSettingsLoading === LOADING_STATUS.SUCCEEDED
            ? 'success'
            : 'error',
      });
      setToasts(newToasts);
      setIsValidated(false);
      setIsEditing(false);
      setTimeout(() => {
        dispatch(
          fetchEmail2SMSSettings({
            vendorId,
            accountId,
          })
        );
      }, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [updateEmail2SMSSettingsLoading]);

  const handleSubmit = (formValues) => {
    setIsValidated(true);

    if (!isMaxSmsFragmentsValid(maxSmsFragments)) return;

    dispatch(
      updateEmail2SMSSettings({
        vendorId,
        accountId,
        payload: {
          maxSmsFragments: !formValues.maxSmsFragments
            ? null
            : Number(formValues.maxSmsFragments),
          bodyOnly: formValues.bodyOnly,
        },
      })
    );
  };

  return (
    <div className={styles.card}>
      {email2SMSSettingsLoading === LOADING_STATUS.LOADING && (
        <div className={styles.spinnerWrap}>
          <Spinner size="l" />
        </div>
      )}
      <div className={styles.title}>
        <Text type="l" inline emphasized>
          Update Email2SMS Settings
        </Text>
      </div>
      <form className={styles.formWrap}>
        <div className={styles.formItem}>
          <EditableInput
            title="Max Fragments"
            value={maxSmsFragments}
            isEditing={isEditing}
            setIsEditing={setIsEditing}
            loading={updateEmail2SMSSettingsLoading === LOADING_STATUS.LOADING}
            handleChangeValue={setMaxSmsFragments}
            handleSave={() =>
              handleSubmit({
                maxSmsFragments,
                bodyOnly,
              })
            }
            handleCancel={() => setMaxSmsFragments(initMaxSmsFragments)}
            errorText={
              isValidated && !isMaxSmsFragmentsValid(maxSmsFragments)
                ? 'Max Fragments must be a number'
                : ''
            }
          />
        </div>
        <div className={styles.formItem}>
          <div className={styles.row}>
            <div className={styles.left}>
              <Text type="s">Body Only</Text>
            </div>
            <Toggle
              disabled={!isEditable}
              checked={bodyOnly}
              loading={
                updateEmail2SMSSettingsLoading === LOADING_STATUS.LOADING
              }
              onChange={(newVal: boolean) => {
                setSetBodyOnly(newVal);
                handleSubmit({
                  maxSmsFragments,
                  bodyOnly: newVal,
                });
              }}
            />
          </div>
        </div>
      </form>
      <Toast toasts={toasts} setToasts={setToasts} />
    </div>
  );
};

export default Settings;
