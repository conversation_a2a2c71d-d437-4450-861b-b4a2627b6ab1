import { array, boolean, InferType, object, string } from "yup";

export const scheme = object({
  classification: string().required("Classification is required"),
  capabilities: array().of(string().required()).min(1).required("Capabilities is required"),
  status: string().nullable(),
  providerId: string().required("Provider is required"),
  dedicatedReceiver: boolean(),
})

export type EditFormValues = InferType<typeof scheme>;
