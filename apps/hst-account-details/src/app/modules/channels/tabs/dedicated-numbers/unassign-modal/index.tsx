import { useEffect, useState } from 'react';
import { Dialog, Button } from 'nectary';
import styles from './styles.module.less';

import '@nectary/components/checkbox'
import { NumberType, NumberVerificationStatus } from '../../../../../types/numbers';

interface UnassignModalProps {
  number?: string;
  numberId?: string;
  accountId?: string;
  onConfirm: (checked: boolean) => void;
  onClose: () => void;
  isOpen?: boolean;
  status?: NumberVerificationStatus
  type?: NumberType

}

const UnassignModal = ({ number, numberId, accountId, onConfirm, onClose, isOpen, status, type }: UnassignModalProps) => {
  const [isChecked, setChecked] = useState(false);

  
  useEffect(() => {
    if (['HOSTED_TEN_DLC', 'TEN_DLC'].includes(type || '') && status === 'ASSIGNED') {
      setChecked(true);
    } else {
      setChecked(false);
    }
  }, [type, status])

  const handleCheckbox = (e: CustomEvent<boolean>) => setChecked(e.detail)


  const handleClose = () => {
    setChecked(false);
    onClose()
  };

  const handleConfirm = () => {
    onConfirm(isChecked);
    handleClose();
  };

  return (
    <div className={styles.dialogWrap}>
      <Dialog
        isOpen={isOpen}
        caption="Unassign Number"
        onClose={() => handleClose()}
        footer={
          <>
            <Button size="m" slot="buttons" label="Unassign number" onClick={handleConfirm} />
            <Button type="secondary" size="m" slot="buttons" label='Cancel' onClick={handleClose} />
          </>
        }
      >
        <div>
          <div className={styles.confirmText}>This will unassign number {number} ({numberId}) from Account {accountId}<br />Please confirm.</div>
          {type && ['HOSTED_TEN_DLC', 'TEN_DLC'].includes(type) && status === 'ASSIGNED' && <div className={styles.checkbox}>
            <sinch-checkbox
              text="Release number to Sinch and remove from Numbers service (10DL cleanup) - prevents messaging issues if number gets reassigned"
              aria-label="unassign-checkbox"
              checked={isChecked}
              on-change={handleCheckbox}
            />
          </div>}
        </div>
      </Dialog>
    </div>
  )
}

export default UnassignModal;