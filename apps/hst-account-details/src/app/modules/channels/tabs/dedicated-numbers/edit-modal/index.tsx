import {
  But<PERSON>,
  Dialog,
  Text,
} from 'nectary';
import { <PERSON>Field, CheckboxField } from 'components';
import { SubmitHandler, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup'

import { useEffect } from 'react';
import styles from './styles.module.less';
import { EditFormValues, scheme } from './schema';
import { CAPABILITIES, CAPABILITIES_OPTIONS, CLASSIFICATION_OPTIONS, NUMBER_TYPE, VERIFICATION_STATUS_OPTIONS } from '../../../../../constants/numbers';

type EditNumberModalProps = {
  isVisible: boolean;
  setIsVisible: (isVisible: boolean) => void;
  number?: string;
  type?: string;
  numberId?: string;
  values?: EditFormValues;
  isAssigned?: boolean;
  providerOptions?: { value: string; label: string }[];
  onSubmit: SubmitHandler<EditFormValues>;
  loading?: boolean;
};

const EditNumberModal = ({
  isVisible,
  setIsVisible,
  numberId,
  number,
  type,
  values,
  providerOptions,
  isAssigned,
  onSubmit,
  loading
}: EditNumberModalProps) => {
  const { control, handleSubmit, reset } = useForm<EditFormValues>({
    resolver: yupResolver(scheme),
    values,
    mode: 'all'
  });

  const capabilityOptions = type && [
    NUMBER_TYPE.HOSTED_TEN_DLC,
  ].includes(type)
    ? CAPABILITIES_OPTIONS.filter((option) =>
      [CAPABILITIES.SMS, CAPABILITIES.MMS].includes(option.value)
    )
    : CAPABILITIES_OPTIONS;

  useEffect(() => {
    if (!isVisible) {
      reset()
    }
  }, [isVisible, reset])

  return <div>
    <Dialog
      isOpen={isVisible}
      caption="Edit Number Details"
      onClose={() => setIsVisible(false)}
      footer={
        <>
          <Button loading={loading} size="m" slot="buttons" label="Submit" onClick={handleSubmit(onSubmit)} />
          <Button type="secondary" size="m" slot="buttons" label="Cancel" onClick={() => setIsVisible(false)} />
        </>
      }
    >
      <form>
        <div className={styles.formItem}>
          <div>
            <Text>Number:</Text>
          </div>
          <div>
            <Text>{number}</Text>
          </div>
        </div>
        <div className={styles.formItem}>
          <div>
            <Text>Type:</Text>
          </div>
          <div>
            <Text>{type}</Text>
          </div>
        </div>
        <div className={styles.formItem}>
          <div>
            <Text>ID:</Text>
          </div>
          <div>
            <Text>{numberId}</Text>
          </div>
        </div>
        <div className={styles.formItem}>
          <SelectField searchable={false} options={CLASSIFICATION_OPTIONS} label="Classification" name="classification" control={control} />
        </div>
        <div className={styles.formItem}>
          <SelectField multiple searchable={false} options={capabilityOptions} label="Capabilities" name="capabilities" control={control} />
        </div>
        <div className={styles.formItem}>
          <SelectField
            disabled={type && ![NUMBER_TYPE.TOLL_FREE].includes(type)}
            options={VERIFICATION_STATUS_OPTIONS}
            label="Status"
            name="status"
            control={control}
            searchable={false}
          />
        </div>
        <div className={styles.formItem}>
          <SelectField rows={7} options={providerOptions} label="Provider" name="providerId" control={control} />
        </div>
        <div className={styles.formItem}>
          <CheckboxField label="Dedicated Receiver" name="dedicatedReceiver" control={control} />
        </div>
        {/* <div className={styles.formItem}>
          <CheckboxField disabled={!!isAssigned} label="Available After" name="availableAfter.explicitNull" control={control} />
        </div>
        <div className={styles.formItem}>
          <DatePickerField disabled={!!watchAvailableAfterExplicitNull || !!isAssigned} label="Available After" name="availableAfter.value" control={control} />
        </div> */}
      </form>
    </Dialog>
  </div>
};

export default EditNumberModal;
