import { Table, ActionMenu, Select, Button, Input, SelectV2, Link, MultiSelect } from 'nectary'
import { getPermission<PERSON>ith<PERSON>ey, ROLES_KEY, getCountryCallingCode, rudderTrack } from "helpers";
import { usePagination, useToast } from "hooks";
import { useEffect, useMemo, useState } from 'react';
import { SubmitHandler } from 'react-hook-form';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { CountryCode } from 'libphonenumber-js';
import moment from 'moment';
import { useFeatureFlag } from 'configcat-react';
import { FetchNumbersParams, NumberItem, ProviderItem } from "../../../../types/numbers";
import { useAddNumberMutation, useAssignNumberMutation, useGetAccountDetailSubAccountQuery, useGetNumbersQuery, useGetProvidersQuery, useGetSubAccountQuery, useReassignNumberMutation, useUnassignNumberMutation, useUpdateNumberMutation } from "../../../../redux/dedicated-numbers/dedicated-numbers-api";
import styles from './dedicated-numbers.module.less';
import { columns } from "./columns";
import { ASSIGNMENT_STATUS, NUMBER_TYPE, useNumberTypeOptions, VERIFICATION_STATUS_OPTIONS } from '../../../../constants/numbers';
import EditNumberModal from './edit-modal';
import { EditFormValues } from './edit-modal/schema';
import { LOADING_STATUS, ONENZ, VODANZ } from '../../../../constants';
import { RootState } from '../../../../types/store';
import AddNumberModal from './add-modal';
import { AddFormValues } from './add-modal/schema';
import { SubAccount } from '../../../../types/sub-accounts';
import ReassignNumberModal from './reassign-modal';
import { ReassignFormValues } from './reassign-modal/schema';
import UnassignModal from './unassign-modal';

const SUSPENDED = 'SUSPENDED';
const CANCELLED = 'CANCELLED';

export interface DedicatedNumbersProps {
  vendorId?: string;
  accountId?: string;
  roles?: string[];
}

const DedicatedNumbers = (props: DedicatedNumbersProps) => {
  const { isEditable } = getPermissionWithKey(
    ROLES_KEY.TOLL_FREE_NUMBER,
    props.roles,
  );
  const navigate = useNavigate();
  const numberTypesOptions = useNumberTypeOptions();
  const { showToast, toast } = useToast();
  const [editedNumber, setEditedNumber] = useState<NumberItem | null>(null);
  const [openAddModal, setOpenAddModal] = useState(false);
  const [openReassignModal, setOpenReassignModal] = useState<NumberItem | null>(null);
  const { loading: featureFlagLoading, value: hostNumberEnabled } = useFeatureFlag('hostedNumberOptions', false);
  const [unassignItem, setUnassignItem] = useState<NumberItem | null>(null);

  const [unassign, { isLoading: isUnassigning }] = useUnassignNumberMutation();
  const [updateNumber, { isLoading: isEditing }] = useUpdateNumberMutation();
  const [assignNumber, { isLoading: isAssigning }] = useAssignNumberMutation();
  const [addNumber, { isLoading: isAdding }] = useAddNumberMutation();
  const [reassign, { isLoading: isReassigning }] = useReassignNumberMutation()
  const { pagination, handleChangePageSize, handleNextPage, handlePreviousPage, tokenQueue } = usePagination();
  const [filterValues, setFilterValues] = useState<Pick<FetchNumbersParams, 'country' | 'matching' | 'status' | 'providerId' | 'types'> & { assigned?: string, accounts?: string[] }>({
    types: numberTypesOptions.map(option => option.value),
    accounts: props.accountId ? [props.accountId] : []
  });
  const [filter, setFilter] = useState<Pick<FetchNumbersParams, 'country' | 'matching' | 'status' | 'providerId' | 'types' | 'assigned' | 'accounts'>>({
    types: numberTypesOptions.map(option => option.value),
    accounts: props.accountId
  });

  const rootState = useSelector((state: RootState) => state || {});
  const { accountDetails } = rootState;
  const { details } = accountDetails || {};
  const parentAccountId = details?.resources.parentAccountId

  useEffect(() => {
    if (hostNumberEnabled) {
      setFilter(prev => ({...prev, types: Object.keys(NUMBER_TYPE) }))
      setFilterValues(prev => ({...prev, types: Object.keys(NUMBER_TYPE) }))
    }
  }, [hostNumberEnabled])

  const { data: parentSubAccounts } = useGetSubAccountQuery({
    vendorId: props.vendorId || '',
    accountId: parentAccountId || '',
    size: 100
  }, {
    skip: !props.vendorId || !parentAccountId || !!(parentAccountId && props.vendorId && parentAccountId === props.vendorId), refetchOnMountOrArgChange: true,
  });

  const { data: accountDetailSubAccounts } = useGetAccountDetailSubAccountQuery({
    vendorId: props.vendorId || '',
    accountId: props.accountId || '',
    size: 100
  }, {
    skip: !props.vendorId || !props.accountId, refetchOnMountOrArgChange: true,
  });

  const { data, isFetching } = useGetNumbersQuery({
    vendorId: props.vendorId,
    accountId: props.accountId,
    ...pagination,
    ...filter,
  }, {
    skip: !props.vendorId || featureFlagLoading,
    refetchOnMountOrArgChange: true,
  });

  const { data: providers } = useGetProvidersQuery({
    vendorId: props.vendorId || '',
    accountId: props.accountId || '',
  }, {
    skip: !props.vendorId || !props.accountId, refetchOnMountOrArgChange: true,
  });

  const subAccountResources = accountDetailSubAccounts?.resources
  const providerOptions =
    (providers?.resources || [])?.map(({ name, providerId: tempId }: ProviderItem) => ({
      value: tempId,
      label: name,
    })) || [];

  // Simplified reassign account options logic
  const reassignAccountOptions = useMemo(() => {
    const excludeIds = [props.accountId || '', props.vendorId || ''];
    
    // Combine all sub-accounts from current and parent account
    const allSubAccounts = [
      ...(subAccountResources || []),
      ...(parentSubAccounts?.resources || [])
    ];
    
    // Filter valid accounts and create a map for deduplication
    const uniqueAccountsMap = new Map<string, SubAccount>();
    
    allSubAccounts.forEach(account => {
      // Only include valid accounts
      if (account && 
          account.status !== SUSPENDED && 
          account.status !== CANCELLED && 
          account.accountId && 
          !excludeIds.includes(account.accountId)) {
        uniqueAccountsMap.set(account.accountId, account);
      }
    });
    
    // Convert to option format
    const options = Array.from(uniqueAccountsMap.values()).map(account => ({
      value: account.accountId,
      label: account.accountId
    }));
    
    // Add parent account if valid
    if (parentAccountId && 
        !excludeIds.includes(parentAccountId)) {
      options.unshift({
        value: parentAccountId,
        label: parentAccountId,
      });
    }

    return options;
  }, [subAccountResources, parentSubAccounts?.resources, props.accountId, props.vendorId, parentAccountId]);

  const handleUpdateNumber: SubmitHandler<EditFormValues> = (values) => {
    updateNumber({ numberId: editedNumber?.number.id || '', payload: values }).unwrap().then(() => {
      showToast({
        type: 'success',
        text: `Successfully updated number ${editedNumber?.number.id}`,
      })
      setEditedNumber(null);
    })
      .catch(() => {
        showToast({
          type: 'error',
          text: `Failed to update number ${editedNumber?.number.id}`,
        });
      })
  }

  const handleUnassign = (shouldRelease: boolean) => {
    if (unassignItem) {
      unassign({ numberId: unassignItem.number.id, release: shouldRelease }).unwrap()
        .then(() => showToast({
          type: 'success',
          text: `Successfully unassigned number ${unassignItem.number.id}`,
        }))
        .catch((error) => {
          showToast({
            type: 'error',
            text: `Failed to unassign number ${unassignItem.number.id}`,
          });
        });
    }
  }

  const handleAssignNumber = async (values: AddFormValues) => {
    if (values.availableNow) {
      const yesterdayValue = `${moment().subtract(1, 'day').format('YYYY-MM-DDT00:00:00.000')}Z`;
      await updateNumber({
        numberId: values.numberId || '',
        shouldInvalidate: false,
        payload: {
          availableAfter: {
            value: yesterdayValue,
            explicitNull: false,
          },
        },
      }).unwrap()
    }
    assignNumber({
      numberId: values?.numberId || '',
      payload: {
        accountId: values.accountId,
        label: values.label,
        billable: true,
        vendorId: props.vendorId || '',
      },
    }).unwrap()
      .then(() => {
        showToast({
          type: 'success',
          text: `Successfully assigned number ${values.numberId}`,
        });
        setOpenAddModal(false);
      })
      .catch(() => {
        showToast({
          type: 'error',
          text: `Failed to assign number ${values.numberId}`,
        });
      });
  }

  const handleAddNumber = (values: AddFormValues) => {
    const countryCodeNumber = getCountryCallingCode(values.phone?.country as CountryCode);
    const phoneNumber =
      values.type === NUMBER_TYPE.SHORT_CODE
        ? values.phone?.phoneNumber || ''
        : `+${countryCodeNumber}${values.phone?.phoneNumber || ''}`.replaceAll('-', '');

    rudderTrack('Add Hosted Number', {
      phoneNumber,
      capabilities: values.capabilities || [],
      country: values.phone?.country?.toUpperCase() || '',
      providerId: values.providerId || '',
      type: values.type || '',
      classification: values.classification || '',
    });
    addNumber({
      type: values.type,
      capabilities: values.capabilities || [],
      providerId: values.providerId || '',
      classification: values.classification || '',
      country: values.phone?.country || '',
      phoneNumber,
      shouldInvalidate: false,
    }).unwrap()
      .then((result) => {
        handleAssignNumber({
          ...values,
          numberId: result.id,
        });
      })
      .catch(() => {
        showToast({
          type: 'error',
          text: `Failed to add number ${values.numberId}`,
        });
      });
  }

  const handleReassign: SubmitHandler<ReassignFormValues> = (values) => {
    if (openReassignModal && props.accountId && props.vendorId) {
      reassign({
        numberId: openReassignModal?.number.id, payload: {
          vendorId: props.vendorId,
          accountId: values.accountId,
          metadata: openReassignModal.assignment?.metadata,
          label: openReassignModal.assignment?.label
        }
      }).unwrap()
        .then(() => {
          setOpenReassignModal(null)
          showToast({
            type: 'success',
            text: `Successfully reassigned number ${openReassignModal.number.phoneNumber}`
          })
        }).catch(() => {
          showToast({
            type: 'error',
            text: `Failed to reassign number ${openReassignModal.number.phoneNumber}. Please try again.`,
          });
        })
    }
  }


  const handleSearch = () => {
    setFilter({
      ...filterValues,
      assigned: filterValues.assigned ? filterValues.assigned === ASSIGNMENT_STATUS.ASSIGNED : undefined,
      accounts: filterValues.accounts?.length ? filterValues.accounts.join(',') : props.accountId,
      country: filterValues.country ? filterValues.country.toUpperCase() : undefined,
      types: (filterValues.types || [])?.length > 0 ? filterValues.types : numberTypesOptions.map(option => option.value)
    });
  }

  const handleOnKeyDown = (e: React.KeyboardEvent<HTMLFormElement>) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  };

  const handleViewAccount = (accountId: string, vendorId: string) => {
    let baseName = vendorId.toLowerCase();
    if (baseName === VODANZ) baseName = ONENZ;
    navigate(`/accounts/${baseName}/${accountId}`, {
      state: { from: 'support' },
    });
  }

  const tableColumns = [
    columns.phoneNumber,
    columns.type,
    {
      title: 'Account',
      index: 'id',
      sort: false,
      align: 'left',
      render: (value: NumberItem) => (
        <Link
          id="account-id"
          href="/#"
          preventDefault
          text={value.assignment?.accountId}
          onClick={() => handleViewAccount(value.assignment?.accountId || '', value.assignment?.vendorId || '')}
        />
      ),
    },
    columns.brand,
    columns.status,
    columns.assignment,
    columns.name,
    columns.country,
    columns.id,
    columns.capabilities,
    columns.classification,
    columns.price,
    {
      title: 'Action',
      index: 'id',
      sort: false,
      align: 'left',
      render: (value: NumberItem) => (
        <div>
          <ActionMenu
            orientation="botton-left"
            menus={[
              {
                text: 'Edit',
                icon: 'edit',
                disabled: !isEditable,
                onClick: () => {
                  setEditedNumber(value);
                },
              },
              {
                text: 'Reassign',
                icon: 'swap_horiz',
                disabled: !isEditable,
                onClick: () => {
                  setOpenReassignModal(value);
                },
              },
              {
                text: 'Unassign',
                icon: 'person_remove',
                disabled: !isEditable || !value.assignment,
                onClick: () => setUnassignItem(value),
              },
              // {
              //   text: 'Delete',
              //   icon: 'delete',
              //   disabled: !isEditable || !!value.assignment,
              //   onClick: () => {
              //     handleDelete(value);
              //   },
              // },
            ]}
          />
        </div>
      ),
    },
  ];

  return (
    <div>
      <div className={styles.section}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: "flex-end" }}>
          {/* eslint-disable-next-line jsx-a11y/no-noninteractive-element-interactions */}
          <form className={styles.searchForm} onKeyDown={handleOnKeyDown}>
            <Input
              label="Number"
              value={filterValues.matching}
              onChange={(newVal?: string) => setFilterValues((prev) => ({ ...prev, matching: newVal }))}
              testId="number-input"
            />
            <SelectV2
              label="Account"
              value={filterValues.accounts || []}
              options={reassignAccountOptions}
              onChange={(newVal: string[]) => {
                setFilterValues((prev) => ({ ...prev, accounts: newVal }));
              }}
              customStyles={{ width: '280px' }}
              allLabel="Select All"
              multiple
              rows={7}
              searchable={false}
            />
            <Select
              label="Status"
              value={filterValues.status}
              onSelect={(newVal: string) => {
                setFilterValues((prev) => ({ ...prev, status: newVal }));
              }}
              options={VERIFICATION_STATUS_OPTIONS}
              rows={7}
              testId="verification-statuses-select"
            />
            <MultiSelect
              label="Type"
              value={filterValues.types}
              onSelect={(newVal: string[]) => {
                setFilterValues((prev) => ({ ...prev, types: newVal }));
              }}
              options={numberTypesOptions}
              rows={7}
              testId="types-select"
            />
            <Button size="m" label="Search" onClick={handleSearch} testId="search-btn" />
          </form>
          <Button onClick={() => setOpenAddModal(true)} size="m" label="Add number" testId="add-btn" />
        </div>
      </div >

      <div>
        <Table
          keyField="id"
          tableColumns={tableColumns}
          tableData={data?.resources || []}
          loading={(isFetching || isUnassigning) && LOADING_STATUS.LOADING}
          next={data?.pagination?.next || ''}
          previous={tokenQueue.length}
          handlePreviousPage={handlePreviousPage}
          handleNextPage={handleNextPage}
          scrollX
          handleChangePageSize={handleChangePageSize}
          pageSize={pagination.size}
        />
      </div>
      <EditNumberModal
        values={editedNumber ?
          {
            capabilities: editedNumber?.number.capabilities || [],
            classification: editedNumber?.number.classification || '',
            status: editedNumber?.number?.status || null,
            providerId: editedNumber?.number.providerId || '',
            dedicatedReceiver: editedNumber?.number.dedicatedReceiver || false,
          } : undefined}
        numberId={editedNumber?.number.id}
        number={editedNumber?.number.phoneNumber} type={editedNumber?.number.type}
        isVisible={!!editedNumber}
        providerOptions={providerOptions}
        isAssigned={!!editedNumber?.assignment}
        setIsVisible={() => { setEditedNumber(null) }}
        onSubmit={handleUpdateNumber}
        loading={isEditing}
      />
      <UnassignModal
        isOpen={!!unassignItem}
        number={unassignItem?.number.phoneNumber}
        numberId={unassignItem?.number.id}
        accountId={unassignItem?.assignment?.accountId}
        onConfirm={handleUnassign}
        onClose={() => setUnassignItem(null)}
        type={unassignItem?.number.type}
        status={unassignItem?.number.status}
      />
      {toast}
      <AddNumberModal isVisible={openAddModal} setIsVisible={() => setOpenAddModal(false)} accountId={props.accountId} loading={isAssigning || isAdding || isEditing} onSubmit={(values) => values.existing ? handleAssignNumber(values) : handleAddNumber(values)} providerOptions={providerOptions} values={{ existing: true, accountId: props.accountId || '', type: '' }} />
      <ReassignNumberModal loading={isReassigning} phoneNumber={openReassignModal?.number.phoneNumber} accountId={openReassignModal?.assignment?.accountId} accountOptions={reassignAccountOptions} isVisible={!!openReassignModal} setIsVisible={() => setOpenReassignModal(null)} onSubmit={handleReassign} />
    </div>
  );
}

export default DedicatedNumbers;