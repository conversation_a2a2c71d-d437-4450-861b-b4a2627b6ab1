import '@nectary/assets/icons/help';
import { Button, Dialog, Text } from 'nectary';
import { SubmitHandler, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { SelectField } from 'components';

import { useEffect } from 'react';
import styles from './styles.module.less';
import { ReassignFormValues, schema } from './schema';

type ReassignNumberModalProps = {
  isVisible: boolean;
  setIsVisible: (isVisible: boolean) => void;
  accountOptions: { value: string, label: string }[];
  loading?: boolean;
  onSubmit: SubmitHandler<ReassignFormValues>;
  phoneNumber?: string;
  accountId?: string;
};

const ReassignNumberModal = ({
  isVisible,
  setIsVisible,
  accountOptions,
  loading,
  onSubmit,
  phoneNumber,
  accountId
}: ReassignNumberModalProps) => {
  const { control, handleSubmit, reset } = useForm<ReassignFormValues>({ resolver: yupResolver(schema), mode: 'all' })

  useEffect(() => {
    if (!isVisible) {
      reset()
    }
  }, [isVisible, reset])

  return (
    <div>
      <Dialog
        isOpen={isVisible}
        caption="Reassign Number"
        onClose={() => setIsVisible(false)}
        footer={
          <>
            <Button
              label="Cancel"
              type="secondary"
              onClick={() => setIsVisible(false)}
              disabled={loading}
              size="m" slot="buttons"
            />
            <Button
              label="Reassign Number"
              onClick={handleSubmit(onSubmit)}
              size="m"
              slot="buttons"
              loading={loading}
              disabled={loading || accountOptions.length === 0}
            />
          </>
        }
      >
        <form onSubmit={handleSubmit(onSubmit)}>

          <div className={styles.formItem}>
            <Text>
              Reassigning number {phoneNumber} from account {accountId}
            </Text>
          </div>
          <div>
            <SelectField
              control={control}
              tooltipText="Only accounts in the same account hierarchy are available to select"
              name="accountId"
              label="Account"
              testId="account-select"
              placeholder={
                accountOptions.length === 0
                  ? 'No accounts available'
                  : 'Select Account'
              }
              rows={10}
              options={accountOptions}
              disabled={!accountOptions.length}
            />
            {accountOptions.length === 0 && (
              <div style={{ marginTop: '8px' }}>
                <Text style={{ color: '#666', fontSize: '14px' }}>
                  No available accounts found in the same hierarchy to reassign
                  this number to.
                </Text>
              </div>
            )}
          </div>
        </form>
      </Dialog>
    </div>
  );
};

export default ReassignNumberModal;
