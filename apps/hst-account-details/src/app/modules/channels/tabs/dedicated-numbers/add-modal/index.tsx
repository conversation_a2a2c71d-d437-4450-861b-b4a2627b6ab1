import {
  But<PERSON>,
  Dialog,
  Text,
} from 'nectary';
import { <PERSON>Field, InputField, PhoneInputField, ToggleField, CheckboxField } from 'components';
import { SubmitHandler, useForm, useWatch } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup'

import { useCallback, useEffect, useMemo, useState } from 'react';
import { countries } from '@nectary/components/utils/countries';
import moment from 'moment';
import { customDateTimeFormatReadable, rudderTrack } from 'helpers';

import debounce from 'lodash/debounce';
import { useConfirmModal } from 'hooks';
import styles from './styles.module.less';
import { AddFormValues, scheme } from './schema';
import { CAPABILITIES, CAPABILITIES_OPTIONS, CLASSIFICATION_OPTIONS, NUMBER_TYPE, US_NUMBER_TYPES, US_SUPPORT_COUNTRIES, US_SUPPORT_COUNTRIES_OPTIONS, useNumberTypeOptions } from '../../../../../constants/numbers';
import { useGetAllNumbersQuery, useParseNumberMutation } from '../../../../../redux/dedicated-numbers/dedicated-numbers-api';

const countryList = Object.keys(countries).map((key) => ({
  label: countries[key].name,
  value: key,
}));

type AddNumberModalProps = {
  isVisible: boolean;
  setIsVisible: (isVisible: boolean) => void;
  values?: AddFormValues;
  providerOptions?: { value: string; label: string }[];
  onSubmit: SubmitHandler<AddFormValues>;
  loading?: boolean;
  accountId?: string;
};

const AddNumberModal = ({
  isVisible,
  setIsVisible,
  values,
  providerOptions,
  onSubmit,
  loading,
  accountId
}: AddNumberModalProps) => {
  const { control, handleSubmit, reset, setValue, setError, getValues } = useForm<AddFormValues>({
    resolver: yupResolver(scheme),
    values,
    mode: 'all'
  });
  const [numberSearchKeyword, setNumberSearchKeyword] = useState<string | undefined>();
  const { modal, open } = useConfirmModal()
  const watchExisting = useWatch({ name: "existing", control });
  const watchType = useWatch({ name: "type", control });
  const watchCountry = useWatch({ name: "country", control });
  const watchNumberId = useWatch({ name: "numberId", control });

  const isAvailableToAssign = useWatch({ name: "availableNow", control });

  const { data, isFetching } = useGetAllNumbersQuery({ size: 100, assigned: false, types: [watchType], country: watchCountry, matching: numberSearchKeyword }, { skip: !watchType || !watchExisting || !watchCountry, refetchOnMountOrArgChange: true });
  const phoneNumberOptions = data?.resources.map((item) => ({
    label: item.phoneNumber,
    value: item.id,
  })) || [];
  const [parseNumber, { isLoading: isParsing }] = useParseNumberMutation()

  const selectedNumber = useMemo(() => data?.resources.find((item) => item.id === watchNumberId), [data?.resources, watchNumberId]);
  const isFutureAvailable = selectedNumber?.availableAfter && moment(selectedNumber.availableAfter).isAfter(moment());
  const isFormDisabled = watchExisting && (isFutureAvailable || (!selectedNumber?.availableAfter && !!watchNumberId)) && !isAvailableToAssign;


  const typeOptions = useNumberTypeOptions()
  const capabilityOptions = watchType && [
    NUMBER_TYPE.HOSTED_TEN_DLC,
  ].includes(watchType)
    ? CAPABILITIES_OPTIONS.filter((option) =>
      [CAPABILITIES.SMS, CAPABILITIES.MMS].includes(option.value)
    )
    : CAPABILITIES_OPTIONS;

  useEffect(() => {
    if (!isVisible) {
      reset()
      setNumberSearchKeyword(undefined);
    }
  }, [isVisible, reset])

  useEffect(() => {
    if (watchNumberId) {
      setValue('availableNow', false)
    }
  }, [setValue, watchNumberId])

  const handleExistingChange = (value: boolean) => {
    if (value) {
      reset({
        existing: true,
        type: watchType,
        numberId: '',
        phone: undefined,
        capabilities: [],
        classification: '',
        providerId: '',
        accountId: accountId || '',
        label: ''
      });
    } else {
      reset({
        existing: false,
        type: watchType,
        numberId: '',
        phone: { country: '', phoneNumber: '' },
        capabilities: [],
        classification: '',
        providerId: '',
        accountId: accountId || '',
        label: '',
        country: '',
        availableNow: false,
      });
      setNumberSearchKeyword(undefined);
    }
  };

  const handleTypeChange = (value: string) => {
    const formValues = getValues()
    if (US_NUMBER_TYPES.includes(value) && watchCountry && !['ca', 'us', 'pr'].includes(watchCountry.toLowerCase())) {
      setValue('country', undefined);
    }
    if (US_NUMBER_TYPES.includes(value) && formValues.phone?.country && !['ca', 'us', 'pr'].includes(formValues.phone.country.toLowerCase())) {
      setValue('phone', { ...formValues.phone, country: 'us' });
    }
    if (watchExisting) {
      setValue('numberId', undefined);
    }
    if (!watchExisting && [NUMBER_TYPE.HOSTED_TEN_DLC].includes(
      value
    ) &&
      (formValues.capabilities || []).some(
        (val) => ![CAPABILITIES.SMS, CAPABILITIES.MMS].includes(val)
      )) {
      setValue('capabilities', []);
    }
  };

  const handleCountryChange = (value: string) => {
    setValue('numberId', undefined);
  };

  const debouncedSearch = useMemo(
    () =>
      debounce((value: string) => {
        setNumberSearchKeyword(value);
      }, 500),
    []
  );

  const handleNumberSearchChange = useCallback((value: string) => {
    debouncedSearch(value);
  }, [debouncedSearch]);

  const handleSubmitForm: SubmitHandler<AddFormValues> = async (val) => {
    rudderTrack('Confirm Number Modal', {
      numberType: val.type,
      shouldAssign: true,
      ...val,
    });
    if ([NUMBER_TYPE.TEN_DLC, NUMBER_TYPE.HOSTED_TEN_DLC].includes(val.type)) {
      open({
        title: 'Warning',
        content: 'Verify the 10DLC number using the verification process before sending.',
        onConfirm: async () => {
          rudderTrack('Select Verify Option', {
            ...val
          });
          if (!val?.existing) {
            const result = await parseNumber({
              countryCode: val.phone?.country || '',
              numbers: [val.phone?.phoneNumber || '']
            }).unwrap();
            if (result.resources[0].valid) {
              onSubmit(val);
            } else {
              setError('phone', {
                type: 'manual',
                message: 'Invalid phone number',
              });
            }
          } else onSubmit(val)
        },
        onClose: () => {
          rudderTrack('Cancel Regulatory Alert', {
            ...val,
          });
        }
      })
    } else if (!val?.existing && val.type !== NUMBER_TYPE.SHORT_CODE) {
      const result = await parseNumber({
        countryCode: val.phone?.country || '',
        numbers: [val.phone?.phoneNumber || '']
      }).unwrap();
      if (result.resources[0].valid) {
        onSubmit(val);
      } else {
        setError('phone', {
          type: 'manual',
          message: 'Invalid phone number',
        });
      }
    } else onSubmit(val)
  }

  const handleCloseModal = (closeMethod?: string) => {
    const formValues = getValues();
    rudderTrack('Cancel Number Modal', {
      ...formValues,
      closeMethod
    });
    setIsVisible(false);
  };

  return <div>
    <Dialog
      isOpen={isVisible}
      caption="Add Number"
      onClose={() => handleCloseModal('dialog')}
      footer={
        <>
          <Button disabled={isFormDisabled} loading={loading || isParsing} size="m" slot="buttons" label="Submit" onClick={handleSubmit(handleSubmitForm)} />
          <Button type="secondary" size="m" slot="buttons" label="Cancel" onClick={handleCloseModal} />
        </>
      }
    >
      <form onSubmit={handleSubmit(handleSubmitForm)}>
        <div className={styles.formItem}>
          <SelectField onChange={handleTypeChange} searchable={false} options={typeOptions} label="Type" name="type" control={control} />
        </div>
        <div className={styles.formItem}>
          <ToggleField onChange={handleExistingChange} name="existing" control={control} label="Existing number" />
        </div>

        {!watchExisting &&
          <div className={styles.formItem}>
            <PhoneInputField name="phone" control={control} data={US_NUMBER_TYPES.includes(watchType)
              ? US_SUPPORT_COUNTRIES
              : undefined} />
          </div>}
        {
          watchExisting &&
          <>
            <div className={styles.formItem}>
              <SelectField onChange={handleCountryChange} label="Country" rows={7} options={US_NUMBER_TYPES.includes(watchType) ? US_SUPPORT_COUNTRIES_OPTIONS : countryList} name="country" control={control} />
            </div>
            <div className={styles.formItem}>
              <SelectField onSearchChange={handleNumberSearchChange} isLoading={isFetching} disabled={!watchType || !watchCountry} tooltipText="Please select the type and country you want" label="Phone number" rows={7} options={phoneNumberOptions} name="numberId" control={control} />
            </div>
          </>
        }
        {
          ((selectedNumber?.availableAfter && isFutureAvailable) || (!selectedNumber?.availableAfter && watchNumberId)) && watchExisting &&
          <div className={styles.formItem} style={{ marginTop: 8 }}>
            {selectedNumber?.availableAfter && <Text style={{ marginBottom: 8 }}><span style={{ fontWeight: 500, fontSize: 16 }}>
              Available after:</span> {customDateTimeFormatReadable({
                datetime: selectedNumber.availableAfter,
                showTime: true,
              })}
            </Text>}
            <CheckboxField
              name="availableNow"
              control={control}
              label="Make the number available now"
              defaultValue={false}
            />
          </div>
        }
        {!watchExisting && <>
          <div className={styles.formItem}>
            <SelectField multiple searchable={false} options={capabilityOptions} label="Capabilities" name="capabilities" control={control} />
          </div>
          <div className={styles.formItem}>
            <SelectField searchable={false} options={CLASSIFICATION_OPTIONS} label="Classification" name="classification" control={control} />
          </div>
          <div className={styles.formItem}>
            <SelectField rows={7} options={providerOptions} label="Provider" name="providerId" control={control} />
          </div>
        </>}
        <div className={styles.formItem}>
          <InputField options={providerOptions} label="Account ID" name="accountId" disabled control={control} />
        </div>
        <div className={styles.formItem}>
          <InputField options={providerOptions} label="Label" name="label" control={control} />
        </div>
      </form>
      {modal}
    </Dialog>
  </div>
};

export default AddNumberModal;
