import { Text, Tag } from 'nectary';
import { NumberItem } from "../../../../types/numbers";
import { NUMBER_VERIFICATION_STATUS } from "../../../../constants/numbers";
import { getPriceRule } from '../../../../utils/index';

export const columns = {
  phoneNumber: {
    title: 'Number',
    index: 'phoneNumber',
    sort: false,
    align: 'left',
    render: (value: NumberItem) => (
      <Text type="s">{value.number.phoneNumber}</Text>
    ),
  },
  type: {
    title: 'Type',
    index: 'type',
    sort: false,
    align: 'left',
    render: (value: NumberItem) => <Text type="s">{value.number.type}</Text>,
  },
  brand: {
    title: 'Brand',
    index: 'id',
    sort: false,
    align: 'left',
    render: (value: NumberItem) => (
      <Text type="s">{value.assignment?.vendorId}</Text>
    ),
  },
  status: {
    title: 'Verification',
    index: 'status',
    sort: false,
    align: 'left',
    render: (value: NumberItem) => {
      if (!value.number.status) return '-';

      return {
        [NUMBER_VERIFICATION_STATUS.ASSIGNED]: (
          <Tag key={value.number.status} color="light-green" text={value.number.status} />
        ),
        [NUMBER_VERIFICATION_STATUS.UNVERIFIED]: (
          <Tag key={value.number.status} color="light-red" text={value.number.status} />
        ),
        [NUMBER_VERIFICATION_STATUS.PENDING]: (
          <Tag key={value.number.status} color="light-orange" text={value.number.status} />
        ),
      }[value.number.status];
    },
  },
  assignment: {
    title: 'Assignment',
    index: 'id',
    sort: false,
    align: 'left',
    render: (value: NumberItem) => {
      if (!value.assignment) return '-';
      return <Tag key="" color="light-green" text="Assigned" />;
    },
  },
  name: {
    title: 'Name',
    index: 'id',
    sort: false,
    align: 'left',
    render: (value: NumberItem) => (
      <Text type="s">{value.assignment?.label}</Text>
    ),
  },
  country: {
    title: 'Country',
    index: 'country',
    sort: false,
    align: 'left',
    render: (value: NumberItem) => <Text type="s">{value.number.country}</Text>,
  },
  id: {
    title: 'ID',
    index: 'id',
    sort: false,
    align: 'left',
    render: (value: NumberItem) => <Text type="s">{value.number.id}</Text>,
  },
  capabilities: {
    title: 'Capabilities',
    index: 'id',
    sort: false,
    align: 'left',
    render: (value: NumberItem) => (
      <Text type="s">{value.number.capabilities?.join(', ')}</Text>
    ),
  },
  classification: {
    title: 'Classification',
    index: 'classification',
    sort: false,
    align: 'left',
    render: (value: NumberItem) => (
      <Text type="s">{value.number.classification}</Text>
    ),
  },
  price: {
    title: 'Price',
    index: 'price',
    sort: false,
    align: 'left',
    render: (values: NumberItem) =>
      getPriceRule(values.number || {}),
  },
};
