import { array, boolean, InferType, object, string } from "yup";
import { NUMBER_TYPE } from "../../../../../constants/numbers";

export const scheme = object({
  existing: boolean().default(true),
  type: string().required("Type is required"),
  country: string().uppercase().when('existing', ([existing], schema) => {
    if (existing) {
      return schema.required("Country is required");
    }
    return schema;
  }),
  capabilities: array().of(string().required()).when('existing', ([existing], schema) => {
    if (!existing) {
      return schema.min(1, 'Capabilities are required').required("Capabilities are required");
    }
    return schema;
  }),
  providerId: string().when('existing', ([existing], schema) => {
    if (!existing) {
      return schema.required("Provider is required");
    }
    return schema;
  }),
  classification: string().when('existing', ([existing], schema) => {
    if (!existing) {
      return schema.required("Classification is required");
    }
    return schema;
  }),
  numberId: string().when('existing', ([existing], schema) => {
    if (existing) {
      return schema.required("Phone number is required");
    }
    return schema;
  }),
  availableNow: boolean(),
  phone: object(({
    phoneNumber: string().trim(),
    country: string().uppercase()
  })).notRequired().test('test-phone', (value, ctx) => {
    if (!ctx.parent.existing && value?.phoneNumber && ctx.parent.type === NUMBER_TYPE.SHORT_CODE && (value.phoneNumber.length < 3 || value.phoneNumber.length > 8)) {
      return ctx.createError({ message: 'Invalid short code' });
    }
    if (!ctx.parent.existing && !value?.phoneNumber && !value?.country) {
      return ctx.createError({ message: 'Phone number is required' });
    }
    if (!ctx.parent.existing && ((!value?.country && value?.phoneNumber) || (value?.country && !value.phoneNumber))) {
      return ctx.createError({ message: 'Invalid phone number' });
    }
    return true
  }),
  accountId: string().required("Account ID is required"),
  label: string().trim()
})

export type AddFormValues = InferType<typeof scheme>;
