import { DESCENDING } from 'apps/hst-account-details/src/app/constants';
import { customDateTimeFormatReadable, isNonEmptyString } from 'helpers';
import _isEmpty from 'lodash/isEmpty';
import { Button, Input, MultiSelect, Select, Table, Tag, Text } from 'nectary';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchRequests } from '../../../../../redux/channels/channels-slice';
import { RequestItem } from '../../../../../types/channels';
import { RootState } from '../../../../../types/store';
import {
  REQUEST_STATUSES,
  STATUSES_OPTIONS,
  useUsageTypeOptions,
} from '../constants';
import { getParamsBySenderAddressType } from '../helpers';
import styles from './requests.module.less';

type RequestsProps = {
  accountId: string;
  vendorId: string;
};

const DEFAULT_PAGE_SIZE = 10;
const CREATED_DATE = 'CREATED_DATE';
const LAST_MODIFIED_DATE = 'LAST_MODIFIED_DATE';

const tableColumns = () => [
  {
    title: 'Alpha Tag',
    index: 'senderAddress',
    sort: false,
    align: 'left',
    render: (values: RequestItem) => (
      <Text type="s">{values.senderAddress}</Text>
    ),
  },
  {
    title: 'Address Type',
    index: 'senderAddressType',
    sort: false,
    align: 'left',
    render: (values: RequestItem) => (
      <Text type="s">{values.senderAddressType}</Text>
    ),
  },
  {
    title: 'Reason',
    index: 'reason',
    sort: false,
    align: 'left',
    render: (values: RequestItem) => <Text type="s">{values.reason}</Text>,
  },
  {
    title: 'Email Address',
    index: 'createdByEmail',
    sort: false,
    align: 'left',
    render: (values: RequestItem) => (
      <Text type="s">{values.createdByEmail}</Text>
    ),
  },
  {
    title: 'Time Requested',
    index: 'createdDate',
    sort: true,
    sortKey: CREATED_DATE,
    align: 'left',
    render: (values: RequestItem) => {
      const displayedTime = isNonEmptyString(values.createdDate)
        ? customDateTimeFormatReadable({
            datetime: values.createdDate,
            showTime: true,
          })
        : '-';
      return <Text type="s">{displayedTime}</Text>;
    },
  },
  {
    title: 'Time Actioned',
    index: 'lastModifiedDate',
    sortKey: LAST_MODIFIED_DATE,
    sort: true,
    align: 'left',
    render: (values: RequestItem) => {
      const displayedTime = isNonEmptyString(values.lastModifiedDate)
        ? customDateTimeFormatReadable({
            datetime: values.lastModifiedDate,
            showTime: true,
          })
        : '-';
      return <Text type="s">{displayedTime}</Text>;
    },
  },
  {
    title: 'Last Modified By',
    index: 'lastModifiedByEmail',
    sort: false,
    align: 'left',
    render: (values: RequestItem) => (
      <Text type="s">{values.lastModifiedByEmail}</Text>
    ),
  },
  {
    title: 'Status',
    index: 'status',
    sort: false,
    align: 'left',
    render: (values: RequestItem) =>
      ({
        [REQUEST_STATUSES.APPROVED]: (
          <Tag key={values.status} color="light-green" text={values.status} />
        ),
        [REQUEST_STATUSES.REJECTED]: (
          <Tag key={values.status} color="light-red" text={values.status} />
        ),
        [REQUEST_STATUSES.PENDING]: (
          <Tag key={values.status} color="light-orange" text={values.status} />
        ),
        [REQUEST_STATUSES.OPEN]: (
          <Tag key={values.status} color="light-orange" text="SUBMITTED" />
        ),
      }[values.status]),
  },
];

const Requests = ({ accountId, vendorId }: RequestsProps) => {
  const initialStatuses = STATUSES_OPTIONS.map((status) => status.value);
  const dispatch = useDispatch();
  const { accountChannels } = useSelector((state: RootState) => state);
  const { requests, requestsLoading } = accountChannels || {};
  const { resources, pagination } = requests || {};
  const [tokenQueue, setTokenQueue] = useState<string[]>([]);
  const [statuses, setStatuses] = useState(initialStatuses);
  const [senderAddress, setSenderAddress] = useState('');
  const usageTypeOptions = useUsageTypeOptions();
  const [usageType, setUsageType] = useState(usageTypeOptions[0].value);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const { next } = pagination || {};
  const [sorter, setSorter] = useState({
    sortBy: CREATED_DATE,
    sortDirection: DESCENDING,
  });

  useEffect(() => {
    dispatch(
      fetchRequests({
        accountId,
        statuses,
        vendorId,
        size: pageSize,
        senderAddress,
        ...getParamsBySenderAddressType(usageType),
      })
    );
  }, []);

  const handlePrevious = () => {
    tokenQueue.pop();
    setTokenQueue(tokenQueue);
    const tokensLen = tokenQueue.length;

    setTimeout(() => {
      dispatch(
        fetchRequests({
          accountId,
          statuses,
          vendorId,
          size: pageSize,
          senderAddress,
          ...getParamsBySenderAddressType(usageType),
          ...(tokensLen && { next: tokenQueue[tokensLen - 1] }),
        })
      );
    });
  };
  const handleNext = (token: string) => {
    tokenQueue.push(token);
    setTokenQueue(tokenQueue);

    setTimeout(() => {
      dispatch(
        fetchRequests({
          accountId,
          statuses,
          vendorId,
          size: pageSize,
          senderAddress,
          ...getParamsBySenderAddressType(usageType),
          next: token,
        })
      );
    });
  };

  const handleChangePageSize = (newSize: number) => {
    setPageSize(newSize);
    setTokenQueue([]);
    dispatch(
      fetchRequests({
        accountId,
        statuses,
        vendorId,
        size: newSize,
        senderAddress,
        ...getParamsBySenderAddressType(usageType),
      })
    );
  };

  const handleOnKeyDown = (e: React.KeyboardEvent<HTMLFormElement>) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const handleSearch = () => {
    const { sortBy, sortDirection } = sorter;
    setTokenQueue([]);
    dispatch(
      fetchRequests({
        accountId,
        statuses,
        vendorId,
        size: pageSize,
        senderAddress,
        ...getParamsBySenderAddressType(usageType),
        sortBy,
        sortDirection,
      })
    );
  };

  const handleSort = (sorterVal: { sortBy: string; sortDirection: string }) => {
    const { sortBy, sortDirection } = sorterVal;
    const tokensLen = tokenQueue.length;
    setSorter(sorterVal);

    dispatch(
      fetchRequests({
        sortBy,
        sortDirection,
        statuses,
        accountId,
        vendorId,
        senderAddress,
        size: pageSize,
        ...getParamsBySenderAddressType(usageType),
        ...(tokensLen && { next: tokenQueue[tokensLen - 1] }),
      })
    );
    setTokenQueue([]);
  };

  return (
    <div>
      <form className={styles.filterWrapper} onKeyDown={handleOnKeyDown}>
        <div className={styles.formItem}>
          <MultiSelect
            label="Status"
            value={statuses}
            options={STATUSES_OPTIONS}
            onSelect={setStatuses}
            errorText={_isEmpty(statuses) ? 'Status is required' : ''}
          />
        </div>
        <div className={styles.formItem}>
          <Input
            label="Source Address"
            value={senderAddress}
            onChange={setSenderAddress}
          />
        </div>
        <div className={styles.formItem}>
          <Select
            label="Source Address Type"
            value={usageType}
            options={usageTypeOptions}
            onSelect={setUsageType}
            testId="usage-type-select"
          />
        </div>
        <div className={styles.formItem}>
          <Button
            label="Search"
            onClick={handleSearch}
            disabled={!statuses.length}
          />
        </div>
      </form>
      <div className={styles.table}>
        <Table
          hasCheckbox={false}
          tableColumns={tableColumns()}
          tableData={resources || []}
          loading={requestsLoading}
          next={next}
          previous={tokenQueue.length}
          handlePreviousPage={handlePrevious}
          handleNextPage={handleNext}
          pageSize={pageSize}
          handleChangePageSize={handleChangePageSize}
          sorter={sorter}
          handleSort={handleSort}
          scrollX
        />
      </div>
    </div>
  );
};

export default Requests;
