import { ADDRESS_TYPE, USAGE_TYPE } from './constants'

export const getParamsBySenderAddressType = (type: string) => {
  if (type === ADDRESS_TYPE.ALPHANUMERIC) {
    return {
      senderAddressType: ADDRESS_TYPE.ALPHANUMERIC,
      usageType: USAGE_TYPE.ALPHANUMERIC,
    }
  }
  return {
    senderAddressType: ADDRESS_TYPE.INTERNATIONAL,
    usageType: type,
  }
}

export default {
  getParamsBySenderAddressType,
}
