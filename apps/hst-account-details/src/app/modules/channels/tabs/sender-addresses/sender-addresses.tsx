import { useState } from 'react';

import { Text, Tab } from 'nectary';
import { NoPermissions } from 'components';

import Requests from './requests/requests';
import TrustedAddresses from './trusted-addresses/trusted-addresses';

import styles from './sender-addresses.module.less'

const TABS_INFO = {
  requests: {
    value: 1,
    text: 'Requests',
  },
  trustedAddresses: {
    value: 2,
    text: 'Trusted Addresses',
  },
}

const tabContent = ({ accountId, vendorId }: { accountId: string, vendorId: string }) => [
  {
    value: 1,
    content: <Requests accountId={accountId} vendorId={vendorId} />,
  },
  {
    value: 2,
    content: <TrustedAddresses accountId={accountId} vendorId={vendorId} />,
  },
]


type SenderAddressesProps = {
  accountId: string,
  vendorId: string,
  isVisible: boolean,
}

const SenderAddresses = ({ accountId, vendorId, isVisible }: SenderAddressesProps) => {
  const [currentTab, setCurrentTab] = useState('1')

  const onChangeTab = () => {}

  if (!isVisible) return <NoPermissions />

  return (
    <div>
      <div className={styles.description}>
        <Text type="s">This page displays Alpha tags, Dedicated numbers (except Toll-free numbers) and Own Numbers</Text>
      </div>
      <div className={styles.tabWrap}>
        <Tab
          currentTab={currentTab}
          tabs={Object.values(TABS_INFO)}
          tabContent={tabContent({ accountId, vendorId })}
          onChangeTab={onChangeTab}
        />
      </div>
    </div>
  )
}

export default SenderAddresses
