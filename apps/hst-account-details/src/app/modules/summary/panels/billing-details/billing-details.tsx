/* eslint-disable @nx/enforce-module-boundaries */
import { LOADING_STATUS } from 'apps/hst-account-details/src/app/constants';
import {
  createAccountCredit,
  fetchBillingBalance,
  fetchBillingDetails,
  fetchBillingDetailsInvoice,
  clearBillingDetailsState,
} from 'apps/hst-account-details/src/app/redux/summary/billing-details-slice';
import { ToastItem } from 'apps/hst-account-details/src/app/types';
import { RootState } from 'apps/hst-account-details/src/app/types/store';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import {
  customDateTimeFormatReadable,
  formatPrepaidCredits,
  isNonEmptyString,
} from 'helpers';
import _camelCase from 'lodash/camelCase';
import _get from 'lodash/get';
import { Button, Table, Text, Title, Toast } from 'nectary';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import AddCreditModal from './components/add-credit-modal';
import DownloadableInvoice from './components/download-invoice';

import styles from './billing-details.module.less';

export const tableColumns = (accountId: string, vendorId: string) => [
  {
    title: 'Date',
    index: 'invoiceDate',
    sort: false,
    align: 'left',
    render: (item: any) => (
      <sinch-text type="s">
        {isNonEmptyString(item.invoiceDate)
          ? customDateTimeFormatReadable({
              datetime: item.invoiceDate,
              showTime: true,
            })
          : '-'}
      </sinch-text>
    ),
  },
  {
    title: 'Invoice number',
    index: 'invoiceNumber',
    sort: false,
    align: 'left',
    render: (item: any) => (
      // <sinch-text type="s">{item.invoiceNumber}</sinch-text>
      <DownloadableInvoice
        invoiceId={item.id}
        withPrefix={false}
        text={item.invoiceNumber}
        accountId={accountId}
        vendorId={vendorId}
        url={`${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/account-detail/billing-details/invoices/${item.id}/download`}
      />
    ),
  },
  {
    title: 'Amount',
    index: 'amount',
    sort: false,
    align: 'left',
    render: (item: any) => <sinch-text type="s">{item.amount}</sinch-text>,
  },
];

type BillingDetailsProp = {
  vendorId: string;
  accountId: string;
  isEditable: boolean;
};

function BillingDetails(props: BillingDetailsProp) {
  const { vendorId, accountId, isEditable } = props;
  const dispatch = useDispatch();
  const [tokenQueue, setTokenQueue] = useState<string[]>([]);
  const [toasts, setToasts] = useState<ToastItem[]>([]);
  const [isAddCreditModalVisible, setAddCreditModalVisible] = useState(false);
  const pageSize = 5;
  const rootState: RootState = useSelector((state) => state || {});
  const { billingDetails, accountDetails } = rootState || {};
  const {
    details,
    invoice,
    loadingInvoice,
    createAcccountCreditLoading,
    billingBalance,
  } = billingDetails || {};
  const { billingType, currency, billingAccountId, balance, volume } =
    details || {};
  const accountCountry = _get(
    accountDetails,
    'details.resources.operatingCountry',
    ''
  );
  // billing balance
  const { recentExpiredBalance = {} } =
    billingBalance || {};
  const { expiredAt, credits } = recentExpiredBalance || {};
  const expired = expiredAt ?? '';
  const expiredBalance = credits?.MT_SMS ?? 0;

  useEffect(() => {
    dispatch(clearBillingDetailsState());

    dispatch(
      fetchBillingDetails({
        vendorId,
        accountId,
      })
    );
    dispatch(
      fetchBillingDetailsInvoice({
        vendorId,
        accountId,
        size: pageSize,
      })
    );
  }, [accountId, dispatch, vendorId]);

  useEffect(() => {
    let newToasts = [];
    if (
      createAcccountCreditLoading === LOADING_STATUS.SUCCEEDED ||
      createAcccountCreditLoading === LOADING_STATUS.FAILED
    ) {
      const type =
        createAcccountCreditLoading === LOADING_STATUS.SUCCEEDED
          ? 'success'
          : 'error';
      const message =
        createAcccountCreditLoading === LOADING_STATUS.SUCCEEDED
          ? 'Successfully added credit'
          : 'Failed to add credit';

      newToasts = toasts.concat({
        id: accountId,
        text: message,
        type,
      });
      setToasts(newToasts);
      // refreshing data
      if (createAcccountCreditLoading === LOADING_STATUS.SUCCEEDED) {
        setTimeout(() => {
          dispatch(
            fetchBillingDetails({
              vendorId,
              accountId,
            })
          );
          dispatch(
            fetchBillingDetailsInvoice({
              vendorId,
              accountId,
              size: pageSize,
            })
          );
        }, 500);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [createAcccountCreditLoading]);

  useEffect(() => {
    if (billingType) {
      dispatch(
        fetchBillingBalance({
          accountId,
          vendorId,
          billingType: _camelCase(billingType),
        })
      );
    }
  }, [billingType]);

  const handlePrevious = () => {
    tokenQueue.pop();
    setTokenQueue(tokenQueue);
    const tokensLen = tokenQueue.length;

    setTimeout(() => {
      dispatch(
        fetchBillingDetailsInvoice({
          vendorId,
          accountId,
          size: pageSize,
          next: tokensLen ? tokenQueue[tokensLen - 1] : '',
        })
      );
    });
  };

  const handleNext = (token: string) => {
    tokenQueue.push(token);
    setTokenQueue(tokenQueue);

    setTimeout(() => {
      dispatch(
        fetchBillingDetailsInvoice({
          vendorId,
          accountId,
          size: pageSize,
          next: token,
        })
      );
    });
  };

  const handleAddCredit = ({
    quantity,
    reason,
  }: {
    quantity: string;
    reason: string;
  }) => {
    dispatch(
      createAccountCredit({
        vendorId,
        accountId,
        payload: {
          quantity,
          reason,
          accountCountry,
        },
      })
    );
  };

  const renderRow = (label, value) => (
    <div className={styles.row}>
      <div className={styles.left}>{label}</div>
      <div className={styles.right}>{value}</div>
    </div>
  );

  return (
    <>
      <div className={styles.wrapper}>
        <Title type="s" text="Billing Details" />
        <div>
          <Button type="subtle-primary" label=" View billing" />
        </div>
      </div>
      <div>
        {renderRow(
          'Billing Type',
          <Text type="s" inline emphasized>
            {billingType || '-'}
          </Text>
        )}
        {renderRow('Currency', currency || '-')}
        {renderRow('Billing Account ID', billingAccountId || '-')}
        {renderRow(
          'Prepaid Balance',
          billingType === 'PREPAID' || billingType === 'PREPAID_MONEY' ? (
            <div>
              <span className={styles.creditVal}>
                <Text type="s" inline emphasized>
                  {balance}
                </Text>
              </span>
              <Button
                type="secondary"
                label="Add credits"
                onClick={() => setAddCreditModalVisible(true)}
                disabled={!isEditable}
              />
            </div>
          ) : (
            '-'
          )
        )}
        {renderRow('MTD Volume', volume || '-')}
        {billingType !== 'POSTPAID' && (
          <>
            {renderRow(
              'Expired balance',
              formatPrepaidCredits(billingType, expiredBalance)
            )}
            {renderRow(
              'Expired on',
              isNonEmptyString(expired)
                ? customDateTimeFormatReadable({
                    datetime: expired,
                    timeFormat: '',
                    datetimeSeparate: '',
                    dateFormat: 'Do MMM YYYY',
                  })
                : '-'
            )}
          </>
        )}
      </div>

      <div className={styles.wrapper}>
        <Title type="s" text="Most Recent Invoices" />
      </div>
      <div className={styles.invoiceWrapper}>
        <Table
          keyField="id"
          hasCheckbox={false}
          tableColumns={tableColumns(accountId, vendorId)}
          tableData={invoice?.resources}
          loading={loadingInvoice}
          next={invoice?.pagination?.next}
          previous={tokenQueue.length}
          handlePreviousPage={handlePrevious}
          handleNextPage={handleNext}
          scrollX
          testId="billing-details-invoices"
        />
      </div>
      <Toast toasts={toasts} setToasts={setToasts} />
      <AddCreditModal
        isVisible={isAddCreditModalVisible}
        setIsVisible={setAddCreditModalVisible}
        onSubmit={handleAddCredit}
      />
    </>
  );
}

export default BillingDetails;
