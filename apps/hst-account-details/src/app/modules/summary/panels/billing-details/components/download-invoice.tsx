/* eslint-disable @nx/enforce-module-boundaries */
import React, { useState } from 'react'
import download from 'downloadjs'
import { Link as NectaryLink } from 'nectary'
import '@nectary/components/spinner';

import StorageAPI from '../../../../../../../../hst-core/src/apis/storage'

type DownloadInvoiceProps = {
  text: string,
  vendorId: string,
  accountId: string,
  invoiceId: string,
  withPrefix: boolean,
  url: string,
}

function DownloadInvoice({ text, vendorId, accountId, invoiceId, withPrefix, url }: DownloadInvoiceProps) {
  const [isDownloading, setIsDownloading] = useState(false)
  // eslint-disable-next-line @typescript-eslint/no-shadow
  const downloadInvoice = async (invoiceId: string, withPrefix: boolean) => {
    setIsDownloading(true)
    const { blob, fileName, contentType } = await StorageAPI.downloadFileAsBlob({ withPrefix, url })
    setIsDownloading(false)
    download(blob, fileName, contentType)
  }
  return (
    <div style={{ cursor: 'pointer' }}>

      {isDownloading ? (
        <sinch-spinner size='s' />
      ) :
        <NectaryLink
          text={text}
          onClick={() => {
            downloadInvoice(invoiceId, withPrefix)
          }} />
      }
      {/* {isDownloading ? (
        <Spinner size='s' />
      ) : <Button
        type="subtle-primary"
        label={text}
        onClick={() => {
          downloadInvoice(invoiceId, withPrefix)
        }} />} */}
    </div>
  )
}

export default DownloadInvoice
