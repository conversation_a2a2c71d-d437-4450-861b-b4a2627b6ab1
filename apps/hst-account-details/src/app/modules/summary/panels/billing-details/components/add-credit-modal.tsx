import { useState } from 'react';
import { Dialog, Input, Button, Select } from 'nectary';

import styles from './add-credit-modal.module.less';

const REASON_OPTIONS = [
  {
    label: 'Paid top up',
    value: 'PAID_TOP_UP',
  },
  {
    label: 'Free credit',
    value: 'FREE_CREDIT',
  },
  {
    label: 'Refund',
    value: 'REFUND',
  },
  {
    label: 'Trial credit',
    value: 'TRIAL_CREDIT',
  },
  {
    label: 'Parent allocation',
    value: 'PARENT_ALLOCATION',
  },
  /**
   * https://messagemedia.atlassian.net/browse/SUPPORT-12564
   * Will include this reason when the BE schema is updated
   * */
  // {
  //   label: 'Reinstate expired credit',
  //   value: 'REINSTATE_EXPIRED_CREDIT',
  // },
  {
    label: 'Not specified',
    value: 'NOT_SPECIFIED',
  },
];

const AddCreditModal = ({
  isVisible,
  setIsVisible,
  onSubmit,
}: {
  isVisible: boolean;
  setIsVisible: (isVisible: boolean) => void;
  onSubmit: ({
    quantity,
    reason,
  }: {
    quantity: string;
    reason: string;
  }) => void;
}) => {
  const [quantity, setQuantity] = useState('');
  const [quantityError, setQuantityError] = useState('');
  const [reason, setReason] = useState('');

  return (
    <div className={styles.dialogWrap}>
      <Dialog
        isOpen={isVisible}
        caption="Allocate Credits"
        onClose={() => setIsVisible(false)}
      >
        <form>
          <div className={styles.formItem}>
            <Input
              label="Number of credits"
              value={quantity}
              onChange={setQuantity}
              testId="quantity"
              errorText={quantityError}
            />
          </div>
          <div className={styles.formItem}>
            <Select
              label="Reason for credits allocation"
              value={reason}
              options={REASON_OPTIONS}
              onSelect={setReason}
              testId="user-select"
            />
          </div>
          <div className={styles.btnGroup}>
            <div className={styles.btnCancel}>
              <Button label="Cancel" onClick={() => setIsVisible(false)} />
            </div>

            <Button
              label="Transfer"
              onClick={() => {
                if (isNaN(quantity)) {
                  setQuantityError('Invalid number')
                  return
                }
                onSubmit({ quantity: Number(quantity), reason })
                setQuantityError('')
                setQuantity('')
                setReason('')
                setIsVisible(false)
              }}
              disabled={!reason || !quantity}
            />
          </div>
        </form>
      </Dialog>
    </div>
  );
};

export default AddCreditModal;
