import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import moment from 'moment';
import _find from 'lodash/find';

import { Text } from 'nectary';
import { fetchMessages } from 'apps/hst-account-details/src/app/redux/summary/messaging-slice';
import { RootState } from 'apps/hst-account-details/src/app/types/store';
import { LOADING_STATUS } from 'apps/hst-account-details/src/app/constants';
import { SummaryType } from 'apps/hst-account-details/src/app/types/summary';

import styles from './messaging.module.less';

const Messaging = ({
  vendorId,
  accountId,
}: {
  vendorId: string;
  accountId: string;
}) => {
  const dispatch = useDispatch();
  const rootStore = useSelector((state: RootState) => state);
  const { messaging } = rootStore || {};
  const { loading, data } = messaging || {};

  useEffect(() => {
    const from = moment().startOf('month').format();
    const to = moment().format();
    dispatch(
      fetchMessages({
        vendorId,
        accountId,
        date: [`after:${from}`, `before:${to}`],
        timezone: moment.tz.guess()
      })
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (loading === LOADING_STATUS.LOADING) {
    return (
      <sinch-skeleton style={{ width: 200, height: 70 }}>
        <sinch-skeleton-item style={{ width: '70%', height: '100%' }} />
        <sinch-skeleton-item style={{ width: '100%', height: '100%' }} />
      </sinch-skeleton>
    );
  }

  if (!messaging || !data) {
    return (
      <div className={styles.title}>
        <Text type="l" emphasized>
          Messaging
        </Text>
      </div>
    );
  }

  const { summaries } = data;
  const todayMessage =
    _find(summaries, (item: SummaryType) =>
      moment(item.date).isSame(new Date(), 'day')
    );

  return (
    <div>
      <div className={styles.title}>
        <Text type="l" emphasized>
          Messaging
        </Text>
      </div>
      <div>
        <Text type="s" emphasized>
          Outbound
        </Text>
      </div>
      <div className={styles.row}>
        <div className={styles.column}>
          <Text type="s" emphasized>
            {todayMessage?.totalSent || 0}
          </Text>
          <div className={styles.grayText}>
            <Text type="xs">Today</Text>
          </div>
        </div>
        <div className={styles.column}>
          <Text type="s" emphasized>
            {data.totalSent || 0}
          </Text>
          <div className={styles.grayText}>
            <Text type="xs">This month</Text>
          </div>
        </div>
      </div>
      <div>
        <Text type="s" emphasized>
          Inbound
        </Text>
      </div>
      <div className={styles.row}>
        <div className={styles.column}>
          <Text type="s" emphasized>
            {todayMessage?.totalReceived || 0}
          </Text>
          <div className={styles.grayText}>
            <Text type="xs">Today</Text>
          </div>
        </div>
        <div className={styles.column}>
          <Text type="s" emphasized>
            {data.totalReceived || 0}
          </Text>
          <div className={styles.grayText}>
            <Text type="xs">This month</Text>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Messaging;
