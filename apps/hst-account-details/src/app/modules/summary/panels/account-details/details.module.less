.wrapper {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  gap: 16px;
}

.leftColumn {
  width: 70%;
}

.rightColumn {
  width: 30%;
}

.card {
  padding: 24px;
  margin: 20px 0;
  border-radius: var(--sinch-comp-card-shape-radius);
  border: 1px solid var(--sinch-comp-card-color-white-default-border-initial);
  background-color: var(--sinch-comp-card-color-white-default-background-initial);
  box-shadow: var(--sinch-comp-card-shadow-initial);
  overflow: hidden;
}

.title {
  margin-bottom: 32px;
}

.row {
  display: flex;
  flex-direction: row;
  margin-bottom: 16px;

  .left {
    width: 30%;
  }

  .right {
    width: 70%;
  }

  .ACTIVE {
    width: 70%;
    --sinch-global-color-text: var(--sinch-ref-color-complementary-olive-400);
  }

  .SUSPENDED {
    width: 70%;
    --sinch-global-color-text: var(--sinch-ref-color-complementary-bolt-400);
  }

  .CANCELLED {
    width: 70%;
    --sinch-global-color-text: var(--sinch-ref-color-complementary-jasper-400);
  }

  .DORMANT {
    width: 70%;
    --sinch-global-color-text: var(--sinch-ref-color-complementary-violet-400);
  }
}

.unrestricted {
  align-items: flex-start;
  display: flex;
  justify-content: flex-start;

  sinch-text {
    color: var(--sinch-ref-color-complementary-olive-400);
  }

  --sinch-global-color-icon: var(--sinch-ref-color-complementary-olive-400);
}

.restricted {
  align-items: flex-start;
  display: flex;
  justify-content: flex-start;

  sinch-text {
    color: var(--sinch-ref-color-complementary-jasper-400);
  }

  --sinch-global-color-icon: var(--sinch-ref-color-complementary-jasper-400);
}

.moreInfoLink {
  margin-left: 24px;
}


.tooltipRightContent {
  display: flex;
  gap: 10px;
  align-items: center;
}
