/* eslint-disable @nx/enforce-module-boundaries */
import '@nectary/assets/icons/check-circle-outline';
import '@nectary/assets/icons/not-interested';
import '@nectary/components/skeleton';
import '@nectary/components/skeleton-item';
import '@nectary/components/text';
import { useSelector } from 'react-redux';
import { Link } from 'react-router-dom';

import {
  NO_SINCH_PROJECT_APP_MODAL_DES,
  SINCH_PROJECT_APP_MODAL_ASP_DES,
} from 'apps/hst-account-details/src/app/constants/account-types';
import { RootState } from 'apps/hst-account-details/src/app/types/store';
import { customDateTimeFormatReadable, isNonEmptyString } from 'helpers';
import { Text, Tooltip } from 'nectary';
import { COUNTRIES_MAPPING } from '../../../../constants/geoip';

import { CANCELLED, LOADING_STATUS } from '../../../../constants';

import styles from './details.module.less';

const Details = () => {
  const rootState = useSelector((state: RootState) => state || {});
  const { accountDetails } = rootState;
  const { details, detailsLoading } = accountDetails || {};

  const renderRow = (title: string, value: string) => (
    <div className={styles.row}>
      <div className={styles.left}>
        <Text type="s" inline>
          {title}
        </Text>
      </div>
      <div className={styles.right}>
        <Text type="s" inline emphasized>
          {value || '-'}
        </Text>
      </div>
    </div>
  );

  const renderRowWithTooltip = (title: string, value: string) => (
    <div className={styles.row}>
      <div className={styles.left}>
        <Text type="s" inline>
          {title}
        </Text>
      </div>
      <div className={styles.tooltipRightContent}>
        <Text type="s" inline emphasized>
          {value || '-'}
        </Text>
        <Tooltip
          type="fast"
          orientation="right"
          style={{ whiteSpace: 'pre-line' }}
          text={
            !value
              ? NO_SINCH_PROJECT_APP_MODAL_DES
              : SINCH_PROJECT_APP_MODAL_ASP_DES
          }
        >
          <div className={styles.iconInfo}>
            <sinch-icon-info />
          </div>
        </Tooltip>
      </div>
    </div>
  );

  if (detailsLoading === LOADING_STATUS.LOADING) {
    return (
      <sinch-skeleton style={{ width: 500, height: 100 }}>
        <sinch-skeleton-item style={{ width: '70%', height: '100%' }} />
        <sinch-skeleton-item style={{ width: '100%', height: '100%' }} />
      </sinch-skeleton>
    );
  }

  if (!details) {
    return (
      <div className={styles.title}>
        <Text type="l" inline emphasized>
          Account Details
        </Text>
      </div>
    );
  }

  const renderState = (
    title: string,
    value: string | undefined,
    status: string
  ) => {
    let stateText;
    if (!value) {
      stateText = '-';
    } else {
      stateText = value.toLowerCase().split('_');
      stateText = stateText
        .map((word) => word[0].toUpperCase() + word.substring(1))
        .join(' ');
    }
    const textStyle = status ? styles[status] : CANCELLED;

    return (
      <div className={styles.row}>
        <div className={styles.left}>
          <Text type="s" inline>
            {title}
          </Text>
        </div>
        <div className={textStyle}>
          <sinch-text type="s" inline>
            {stateText}
          </sinch-text>
          <span className={styles.moreInfoLink}>
            <Link to="https://messagemedia.atlassian.net/wiki/spaces/RD/pages/**********/Account+Status+and+State">
              Click for more details
            </Link>
          </span>
        </div>
      </div>
    );
  };

  const {
    sinchAccountId,
    sinchAppId,
    sinchAppRegion,
    sinchProjectAppModel,
    sinchProjectId,
  } = details;

  const { resources } = details || {};
  const {
    parentAccountName,
    parentAccountId,
    timezone,
    operatingCountry,
    signupSource = '',
    ownerName,
    successManagerName,
    carrierBillingNumber,
  } = resources || {};

  const formattedCreatedAt = isNonEmptyString(details.createdAt)
    ? customDateTimeFormatReadable({
        datetime: details.createdAt,
        showTime: true,
        timezone,
      })
    : '-';

  return (
    <div>
      <div className={styles.title}>
        <Text type="l" inline emphasized>
          Account Details
        </Text>
      </div>
      {renderRow('Parent Account Name', parentAccountName)}
      {renderRow('Parent Account ID', parentAccountId)}
      {renderRow('Sinch Account ID', sinchAccountId)}
      {renderRow('Sinch App ID', sinchAppId)}
      {renderRow('Sinch Project ID', sinchProjectId)}
      {renderRow('Sinch App Region', sinchAppRegion)}
      {renderRowWithTooltip('Sinch Project App Model', sinchProjectAppModel)}
      {renderRow('Account Owner', ownerName)}
      {renderRow('Customer Success Manager', successManagerName)}
      {renderRow('Timezone', timezone)}
      {renderRow(
        'Country',
        COUNTRIES_MAPPING?.[operatingCountry] || operatingCountry
      )}
      {renderRow('Date Created', formattedCreatedAt)}
      {renderRow('Carrier Billing Number (MSISDN)', carrierBillingNumber || '')}
      {renderRow('Sign-up source', signupSource.toUpperCase())}
      {renderState('State', details.state, details.status)}
    </div>
  );
};

export default Details;
