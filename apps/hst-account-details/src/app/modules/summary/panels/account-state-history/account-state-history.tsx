/* eslint-disable @nx/enforce-module-boundaries */
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchAccountStateHistory } from 'apps/hst-account-details/src/app/redux/summary/account-state-history-slice';
import { RootState } from 'apps/hst-account-details/src/app/types/store';
import { Text, Table } from 'nectary';
import { isNonEmptyString, customDateTimeFormatReadable } from 'helpers';

import { StateHistoryType } from 'apps/hst-account-details/src/app/types/account-state-history';

import { LOADING_STATUS } from '../../../../constants/index';
import styles from './account-state-history.module.less';


const tableColumns = () => [
  {
    title: 'State',
    index: 'state',
    sort: false,
    align: 'left',
    render: (value: StateHistoryType) => {
      const { state } = value || { state: ''}
      const stateText = state.toLowerCase().split("_")
      const stateDisplay = stateText.map((word) => word[0].toUpperCase() + word.substring(1)).join(' ');
      return (
        <sinch-text type="s">{stateDisplay}</sinch-text>
      )
  }
  },
  {
    title: 'State Changed',
    index: 'stateChangedAt',
    sort: false,
    align: 'left',
    render: (value: StateHistoryType) => {
      const formattedDate = isNonEmptyString(value.stateChangedAt)
        ? customDateTimeFormatReadable({
            datetime: value.stateChangedAt,
            showTime: true,
          })
        : '-';
      return (
        <sinch-text type="s">{formattedDate}</sinch-text>
      )
    }
  },
  {
    title: 'State Expiry',
    index: 'stateExpiresAt',
    sort: false,
    align: 'left',
    render: (value: StateHistoryType) => {
      const formattedDate = isNonEmptyString(value.stateExpiresAt)
        ? customDateTimeFormatReadable({
            datetime: value.stateExpiresAt,
            showTime: true,
          })
        : '-';
      return (
        <sinch-text type="s">{formattedDate}</sinch-text>
      )
    }
  },
  {
    title: 'Reason',
    index: 'reason',
    sort: false,
    align: 'left',
    render: (value: StateHistoryType) => (
      <sinch-text type="s">{value.reason}</sinch-text>
    ),
  }
]

const AccountStateHistory = ({
  vendorId,
  accountId,
}: {
  vendorId: string;
  accountId: string;
}) => {
  const dispatch = useDispatch();
  const rootStore = useSelector((state: RootState) => state);
  const { accountStateHistory } = rootStore || {};
  const { loading, data } = accountStateHistory || {};
  const { resources } = data || {}


  useEffect(() => {
    dispatch(
      fetchAccountStateHistory({
        vendorId,
        accountId,
      })
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (loading === LOADING_STATUS.LOADING || !accountStateHistory) {
    return (
      <sinch-skeleton style={{ width: 500, height: 100 }}>
        <sinch-skeleton-item style={{ width: '70%', height: '100%' }} />
        <sinch-skeleton-item style={{ width: '100%', height: '100%' }} />
      </sinch-skeleton>
    );
  }

  return (
    <div>
      <div className={styles.title}>
        <Text type="l" inline emphasized>
          Account State History
        </Text>
      </div>
      <Table
        hasCheckbox={false}
        tableColumns={tableColumns()}
        tableData={resources || []}
        hidePagination
      />
    </div>
  );
};

export default AccountStateHistory;
