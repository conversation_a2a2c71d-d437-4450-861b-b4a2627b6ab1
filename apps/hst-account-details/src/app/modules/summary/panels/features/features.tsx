
import { useSelector } from 'react-redux';
import '@nectary/assets/icons/check';

import { Text } from 'nectary';
import { RootState } from 'apps/hst-account-details/src/app/types/store';
import { LOADING_STATUS } from 'apps/hst-account-details/src/app/constants';
import { Feature } from 'apps/hst-account-details/src/app/types/summary';

import styles from './features.module.less';
import { FEATURE_TYPE } from '../../../../constants/index';

const Features = ({
  vendorId,
  accountId,
}: {
  vendorId: string;
  accountId: string;
}) => {
  const rootStore = useSelector((state: RootState) => state);
  const { accountDetailFeatures } = rootStore || {};
  const { loading, data } = accountDetailFeatures || {};

  if (loading === LOADING_STATUS.LOADING) {
    return (
      <sinch-skeleton style={{ width: 200, height: 70 }}>
        <sinch-skeleton-item style={{ width: '70%', height: '100%' }} />
        <sinch-skeleton-item style={{ width: '100%', height: '100%' }} />
      </sinch-skeleton>
    );
  }

  if (!accountDetailFeatures || !data) {
    return (
      <div>
        <Text type="l" emphasized>
          Features - Enabled
        </Text>
      </div>
    );
  }

  return (
    <div>
      <div className={styles.title}>
        <Text type="l" emphasized>
          Features - Enabled
        </Text>
      </div>
      {data.features.map((feature: Feature) => (
        <div className={styles.row}>
            <div className={styles.icon}>
              <sinch-icon-check
                style={{ marginRight: 10 }}
              />
            </div>
            <div className={styles.label}>
              <sinch-text type="s">{feature.label}</sinch-text>
            </div>
            <div className={styles.type}>
              {feature.type === FEATURE_TYPE.ACCOUNT_ONLY ? (
                <sinch-text type="s">Account</sinch-text>
              ) : (
                <sinch-text type="s">Account & Children</sinch-text>
              )}
            </div>
        </div>
      ))}
    </div>
  );
};

export default Features;
