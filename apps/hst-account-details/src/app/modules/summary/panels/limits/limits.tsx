import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import _isNumber from 'lodash/isNumber';

import { Text } from 'nectary';
import { fetchLimits } from 'apps/hst-account-details/src/app/redux/summary/limits-slice';
import { RootState } from 'apps/hst-account-details/src/app/types/store';
import { LOADING_STATUS } from 'apps/hst-account-details/src/app/constants';

import styles from './limits.module.less';

const numberWithCommas = (number: number) => {
  if (!_isNumber(number)) return null;
  return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

const Limits = ({
  vendorId,
  accountId,
}: {
  vendorId: string;
  accountId: string;
}) => {
  const dispatch = useDispatch();
  const rootStore = useSelector((state: RootState) => state);
  const { limits } = rootStore || {};
  const { loading, data } = limits || {};

  useEffect(() => {
    dispatch(
      fetchLimits({
        vendorId,
        accountId,
      })
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (loading === LOADING_STATUS.LOADING) {
    return (
      <sinch-skeleton style={{ width: 200, height: 70 }}>
        <sinch-skeleton-item style={{ width: '70%', height: '100%' }} />
        <sinch-skeleton-item style={{ width: '100%', height: '100%' }} />
      </sinch-skeleton>
    );
  }

  if (!limits || !data) {
    return (
      <div>
        <Text type="l" emphasized>
          SMS Limits
        </Text>
      </div>
    );
  }

  const { hourlyLimit, dailyLimit, weeklyLimit, monthlyLimit } = data;

  return (
    <div>
      <div className={styles.title}>
        <Text type="l" emphasized>
          SMS Limits
        </Text>
      </div>
      <div className={styles.row}>
        <div className={styles.heading}>
          <Text type="s">Hourly</Text>
        </div>
        <Text type="s" emphasized={!!numberWithCommas(hourlyLimit)}>
          {numberWithCommas(hourlyLimit) || 'Unlimited'}
        </Text>
      </div>
      <div className={styles.row}>
        <div className={styles.heading}>
          <Text type="s">Daily</Text>
        </div>
        <Text type="s" emphasized={!!numberWithCommas(dailyLimit)}>
          {numberWithCommas(dailyLimit) || 'Unlimited'}
        </Text>
      </div>
      <div className={styles.row}>
        <div className={styles.heading}>
          <Text type="s">Weekly</Text>
        </div>
        <Text type="s" emphasized={!!numberWithCommas(weeklyLimit)}>
          {numberWithCommas(weeklyLimit) || 'Unlimited'}
        </Text>
      </div>
      <div className={styles.row}>
        <div className={styles.heading}>
          <Text type="s">Monthly</Text>
        </div>
        <Text type="s" emphasized={!!numberWithCommas(monthlyLimit)}>
          {numberWithCommas(monthlyLimit) || 'Unlimited'}
        </Text>
      </div>
      <div className={styles.row}>
        <div className={styles.heading}>
          <Text type="s">Yearly</Text>
        </div>
        <Text type="s">
          N/A
        </Text>
      </div>
    </div>
  );
};

export default Limits;
