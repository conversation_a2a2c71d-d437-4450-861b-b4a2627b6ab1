import { Bar } from 'react-chartjs-2';
import PropTypes from 'prop-types';

import styles from './react-bar-chart.module.less';

type ReactBarChartPropsType = {
  xAxisLabels: string[],
  datasets: Array<{
    label:string,
    data: Array<string | number>,
    backgroundColor: string,
  }>,
  yAxislabelTag?: string
  legend?: string
}

const ReactBarChart = (props: ReactBarChartPropsType) => {
  const data = {
    labels: props.xAxisLabels,
    datasets: props.datasets,
  };
  return (
    <Bar
      data={data}
      options={{
        responsive: true,
        maintainAspectRatio: true,
        legend: {
          position: 'bottom',
          labels: {
            fontSize: 10,
            usePointStyle: true,
          },
          display: props.legend,
        },
        scales: {
          xAxes: [
            {
              barThickness: 15,
              categoryPercentage: 0.4,
              categorySpacing: 1,
              gridLines: {
                color: styles['-x-gridline-color'],
              },
              ticks: {
                autoSkip: false,
              },
            },
          ],
          yAxes: [
            {
              ticks: {
                beginAtZero: true,
                suggestedMax: 10,
                padding: 5,
                callback(label: number) {
                  return `${Math.floor(label)}${props.yAxislabelTag}`;
                },
              },
              gridLines: {
                borderDash: [4, 3],
                color: styles['-y-gridline-color'],
                drawBorder: false,
              },
            },
          ],
        },
      }}
    />
  );
};

ReactBarChart.propTypes = {
  xAxisLabels: PropTypes.arrayOf(PropTypes.string),
  yAxislabelTag: PropTypes.string,
  legend: PropTypes.bool,
  datasets: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string,
      data: PropTypes.arrayOf(
        PropTypes.oneOfType([PropTypes.string, PropTypes.number])
      ),
      backgroundColor: PropTypes.string,
    })
  ),
};

ReactBarChart.defaultProps = {
  xAxisLabels: [],
  datasets: [],
  legend: true,
  yAxislabelTag: '',
};

export default ReactBarChart;
