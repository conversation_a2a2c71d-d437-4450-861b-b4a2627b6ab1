/* eslint-disable @nx/enforce-module-boundaries */
import moment from 'moment';
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Text } from 'nectary';

import { fetchUsage } from 'apps/hst-account-details/src/app/redux/summary/usage-slice';
import { RootState } from 'apps/hst-account-details/src/app/types/store';
import { SummaryType } from 'apps/hst-account-details/src/app/types/summary';

import ReactBarChart from './components/react-bar-chart';

import { LOADING_STATUS } from '../../../../constants/index';
import styles from './usage.module.less';

const REPORT_RANGE = 14;

const Usage = ({
  vendorId,
  accountId,
}: {
  vendorId: string;
  accountId: string;
}) => {
  const dispatch = useDispatch();
  const rootStore = useSelector((state: RootState) => state);
  const { usage } = rootStore || {};
  const { loading, data } = usage || {};
  const { summaries } = data || {}

  const startDate = moment()
    .subtract(REPORT_RANGE - 1, 'days')
    .startOf('day');

  useEffect(() => {
    const from = startDate.startOf('day').format();
    const to = moment().endOf('day').format();
    dispatch(
      fetchUsage({
        vendorId,
        accountId,
        date: [`after:${from}`, `before:${to}`],
        timezone: moment.tz.guess()
      })
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  let dateLabels = [...Array(REPORT_RANGE)].fill(null);
  dateLabels = dateLabels.map((v, i) =>
    startDate.clone().add(i, 'd').format('YYYY-MM-DD')
  );

  const dateLabelsFormatted = [...Array(REPORT_RANGE)]
    .fill(null)
    .map((v, i) => startDate.clone().add(i, 'd').format('YYYY-MM-DD'));

  const getDataOutbound = (summaryList: SummaryType[]) =>
    dateLabelsFormatted.map((date) => {
      const foundUsage = summaryList.find((item: SummaryType) => item.group === date);
      return foundUsage ? foundUsage.totalSent : 0;
    });

  const getDataInbound = (summaryList: SummaryType[]) =>
    dateLabelsFormatted.map((date) => {
      const foundUsage = summaryList.find(
        (item: SummaryType) => item.group === date
      );
      return foundUsage ? foundUsage.totalReceived : 0;
    });

  const outbound = summaries?.map((item: SummaryType) => ({
    ...item,
    value: item.totalSent,
  })) || [];

  const inbound = summaries?.map((item: SummaryType) => ({
    ...item,
    value: item.totalReceived,
  })) || [];
  const datasets = [
    {
      label: 'Outbound',
      backgroundColor: styles['-outbound-bar-color'],
      data: getDataOutbound(outbound),
    },
    {
      label: 'Inbound',
      backgroundColor: styles['-inbound-bar-color'],
      data: getDataInbound(inbound),
    },
  ];

  if (loading === LOADING_STATUS.LOADING || !usage) {
    return (
      <sinch-skeleton style={{ width: 500, height: 100 }}>
        <sinch-skeleton-item style={{ width: '70%', height: '100%' }} />
        <sinch-skeleton-item style={{ width: '100%', height: '100%' }} />
      </sinch-skeleton>
    );
  }

  return (
    <div>
      <div className={styles.title}>
        <Text type="l" inline emphasized>
          {`Usage (Last ${REPORT_RANGE} days)`}
        </Text>
      </div>
      <ReactBarChart xAxisLabels={dateLabels} datasets={datasets} />
    </div>
  );
};

export default Usage;
