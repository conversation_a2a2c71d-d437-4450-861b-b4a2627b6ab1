.wrapper {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  gap: 16px;
}

.leftColumn {
  width: 70%;
}

.rightColumn {
  width: 30%;
}

.card {
  padding: 24px;
  margin: 20px 0;
  border-radius: var(--sinch-comp-card-shape-radius);
  border: 1px solid var(--sinch-comp-card-color-white-default-border-initial);
  background-color: var(
    --sinch-comp-card-color-white-default-background-initial
  );
  box-shadow: var(--sinch-comp-card-shadow-initial);
  overflow: hidden;
}
