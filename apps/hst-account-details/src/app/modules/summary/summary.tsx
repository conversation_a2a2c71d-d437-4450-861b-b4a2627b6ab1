import {
  ROLES_KEY,
  getPermissionWithKey,
} from 'helpers';

import BillingDetails from './panels/billing-details/billing-details'
import Details from './panels/account-details/details'
import Usage from './panels/usage/usage'
import Messaging from './panels/messaging/messaging'
import Limits from './panels/limits/limits';
import Features from './panels/features/features';
import AccountStateHistory from './panels/account-state-history/account-state-history';

import styles from "./summary.module.less"

type SummaryProp = {
  vendorId: string,
  accountId: string,
  roles: Array<string>
}


export function Summary(props: SummaryProp) {
  const { vendorId, accountId, roles } = props
  const { isVisible: isAccountSummaryVisible } = getPermissionWithKey(
    ROLES_KEY.ACCOUNT_SUMMARY,
    roles,
    true
  );
  const { isEditable: isAccountBillingEditable } = getPermissionWithKey(
    ROLES_KEY.BILLING_DETAILS,
    roles,
    true
  );

  if (!isAccountSummaryVisible) return <>No permissions</>

  return (
    <div className={styles.wrapper}>
      <div className={styles.leftColumn}>
        <div className={styles.card}>
          <Details />
        </div>
        <div className={styles.card}>
          <AccountStateHistory vendorId={vendorId} accountId={accountId} />
        </div>
        <div className={styles.card}>
          <Usage vendorId={vendorId} accountId={accountId} />
        </div>
        {/* <div className={styles.card}>
          <Text type="s" inline emphasized>Failure Rate</Text>
        </div>
        <div className={styles.card}>
          <Text type="s" inline emphasized>Undelivered Messages</Text>
        </div>
        <div className={styles.card}>
          <Text type="s" inline emphasized>Usage by Email</Text>
        </div> */}
      </div>
      <div className={styles.rightColumn}>
        <div className={styles.card}>
          <BillingDetails vendorId={vendorId} accountId={accountId} isEditable={isAccountBillingEditable} />
        </div>
        <div className={styles.card}>
          <Messaging vendorId={vendorId} accountId={accountId} />
        </div>
        {/* <div className={styles.card}>
          <Text type="s" inline emphasized>Numbers & Channels</Text>
        </div> */}
        <div className={styles.card}>
          <Limits vendorId={vendorId} accountId={accountId} />
        </div>
        <div className={styles.card}>
          <Features vendorId={vendorId} accountId={accountId} />
        </div>
      </div>
    </div>
  )
}

export default Summary
