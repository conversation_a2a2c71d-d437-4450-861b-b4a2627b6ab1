.description {
  margin-top: 8px;
  margin-bottom: 24px;
}

.formWrap {
  min-height: 100px;
  display: flex;
  align-items: flex-start;

  .formItem {
    margin-right: 16px;
  }

  .buttonSearch {
    align-self: flex-start;
    margin-top: 28px;
  }
}

.active {
  align-items: flex-start;
  display: flex;
  justify-content: flex-start;
  sinch-text {
    color: var(--sinch-ref-color-complementary-olive-400);
  }
  --sinch-global-color-icon: var(--sinch-ref-color-complementary-olive-400);
}

.inactive {
  align-items: flex-start;
  display: flex;
  justify-content: flex-start;
  sinch-text {
    color: var(--sinch-ref-color-complementary-jasper-400);
  }
  --sinch-global-color-icon: var(--sinch-ref-color-complementary-jasper-400);
}

.modalFormItem {
  margin-bottom: 24px;
}

.dialogWrap {
  sinch-dialog {
    --sinch-comp-dialog-width: 1000px;
    --sinch-comp-dialog-height: 1000px;
    --sinch-comp-dialog-max-width: 1200px;
    --sinch-comp-dialog-max-height: 900px;
  }
}

.backButton {
  margin-right: 15px;
}

.tag {
  display: inline-block;
  margin-left: 4px;
}

.dialog {
  z-index: -1;
}
