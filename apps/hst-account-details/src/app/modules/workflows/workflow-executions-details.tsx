import { useEffect, useState } from 'react';

import { Button, Dialog, Select, Table, Tag } from 'nectary';
import { useDispatch, useSelector } from 'react-redux';

import { getWorkflowExecutions } from '../../redux/workflows/workflows-slice';
import { RootState } from '../../types/store';
import {
  Execution,
  ExecutionStatus,
  FetchWorkflowsDetailParams
} from '../../types/workflows/workflows';
import ExecutionDetailView from './execution-details/execution';
import styles from './workflow-detail.module.less';
import { STATUSES_OPTIONS } from '../channels/tabs/sender-addresses/constants';

const WorkflowDetailView = ({
  workflowId,
  accountId,
  vendorId,
  loading,
  isEditable,
}: {
  workflowId: string;
  accountId: string;
  vendorId: string;
  loading: boolean;
  isEditable: boolean;
}) => {
  const dispatch = useDispatch();
  const { accountWorkflows } = useSelector((state: RootState) => state);
  const { executionsData } = accountWorkflows || {};
  const { pagination, resources } = executionsData || {};
  const [tokenQueue, setTokenQueue] = useState<string[]>([]);
  const { next } = pagination || {};
  const [status, setStatus] = useState(STATUSES_OPTIONS[0].value);
  const [isExecutionDetailsVisible, setExecutionDetailsVisible] =
    useState(false);
  const [executionId, setExecutionId] = useState<string>('');
  const handlePrevious = () => {
    tokenQueue.pop();
    setTokenQueue(tokenQueue);
    const tokensLen = tokenQueue.length;

    setTimeout(() => {
      const params: FetchWorkflowsDetailParams = {
        vendorId,
        accountId,
        next: tokensLen ? tokenQueue[tokensLen - 1] : undefined,
        size: 10,
        workflowID: workflowId,
      };
      if (status !== 'ALL') {
        params.status = status;
      }
      dispatch(getWorkflowExecutions(params));
    });
  };
  const handleNext = (token: string) => {
    tokenQueue.push(token);
    setTokenQueue(tokenQueue);
    setTimeout(() => {
      const params: FetchWorkflowsDetailParams = {
        vendorId,
        accountId,
        next: token,
        size: 10,
        workflowID: workflowId,
      };
      if (status !== 'ALL') {
        params.status = status;
      }
      dispatch(getWorkflowExecutions(params));
    });
  };

  useEffect(() => {
    if (workflowId) {
      dispatch(
        getWorkflowExecutions({
          size: 10,
          workflowID: workflowId,
          accountId,
          vendorId,
        })
      );
    }
  }, [workflowId]);

  const handleSearch = (e?: React.FormEvent) => {
    if (e) {
      e.preventDefault();
    }
    setTokenQueue([]);
    const params: FetchWorkflowsDetailParams = {
      size: 10,
      workflowID: workflowId,
      accountId,
      vendorId,
    };

    if (status !== 'ALL') {
      params.status = status;
    }

    dispatch(getWorkflowExecutions(params));
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSearch();
    }
  };

  const showDialog = (ex: Execution) => {
    setExecutionId(ex.name);
    setExecutionDetailsVisible(true);
  };

  return (
    <div className={styles.dialogWrap}>
      <form
        className={styles.filterWrapper}
        onSubmit={handleSearch}
        onKeyDown={handleKeyDown}
      >
        <div className={styles.formItem}>
          <Select
            label="Status"
            value={status}
            options={OPTIONS}
            onSelect={setStatus}
            isRequired
          />
        </div>
        <div className={styles.formItem}>
          <Button label="Search" onClick={handleSearch} />
        </div>
      </form>
      <Table
        hasCheckbox={false}
        tableColumns={tableColumns({ showDialog, isEditable })}
        tableData={resources || []}
        loading={loading}
        next={next}
        previous={tokenQueue.length}
        handlePreviousPage={handlePrevious}
        handleNextPage={handleNext}
      />
      <div className={styles.dialogWrap}>
        <Dialog
          isOpen={isExecutionDetailsVisible}
          id="dialogWrap"
          caption="Execution Management"
          onClose={() => {
            setExecutionId('');
            setExecutionDetailsVisible(false);
          }}
        >
          <div>
            <ExecutionDetailView
              executionId={executionId}
              accountId={accountId}
              vendorId={vendorId}
              workflowId={workflowId}
            />
          </div>
        </Dialog>
      </div>
    </div>
  );
};

const renderExecutionStatus = (value: string) => {
  switch (value) {
    case ExecutionStatus.FAILED: {
      return <Tag key={value} color="light-red" text={value} />;
    }
    case ExecutionStatus.RUNNING: {
      return <Tag key={value} color="light-green" text={value} />;
    }
    case ExecutionStatus.SUCCEEDED: {
      return <Tag key={value} color="light-blue" text={value} />;
    }
    case ExecutionStatus.TIMED_OUT: {
      return <Tag key={value} color="light-orange" text={value} />;
    }
    default: {
      return <Tag key={value} color="light-grey" text={value} />;
    }
  }
};
const tableColumns = ({
  showDialog,
  isEditable,
}: {
  showDialog: (item: Execution) => void;
  isEditable: boolean;
}) => [
    {
      title: 'Name',
      index: 'name',
      sort: false,
      align: 'left',
      render: (value: Execution) => (
        <sinch-text type="s">{value.name}</sinch-text>
      ),
    },
    {
      title: 'Status',
      key: 'status',
      index: 'status',
      dataIndex: 'status',
      width: '15%',
      render: (value: Execution) => <>{renderExecutionStatus(value.status)}</>,
    },
    {
      title: 'Start time',
      index: 'startTime',
      sort: false,
      align: 'left',
      render: (value: Execution) => (
        <sinch-text type="s">{value.startDate}</sinch-text>
      ),
    },
    {
      title: 'Stop time',
      index: 'stopTime',
      sort: false,
      align: 'left',
      render: (value: Execution) => (
        <sinch-text type="s">{value.stopDate}</sinch-text>
      ),
    },
    {
      title: 'Action',
      index: 'action',
      sort: false,
      align: 'left',
      render: (value: Execution) => (
        <Button
          label="View Execution"
          onClick={() => showDialog(value)}
          disabled={!isEditable}
        />
      ),
    },
  ];
const OPTIONS = [
  { value: '', label: 'ALL' },
  { value: 'RUNNING', label: 'RUNNING' },
  { value: 'SUCCEEDED', label: 'SUCCEEDED' },
  { value: 'FAILED', label: 'FAILED' },
  { value: 'TIMED_OUT', label: 'TIMED_OUT' },
  { value: 'ABORTED', label: 'ABORTED' },
];
export default WorkflowDetailView;
