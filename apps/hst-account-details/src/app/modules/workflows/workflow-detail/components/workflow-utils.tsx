import jsonpath from 'jsonpath';
import _get from 'lodash/get';
import VARIBLES from './workflow-variables';

export const findVariables = (workflow, actionPath) => {
  const variables = new Set()

  const triggerId = _get(workflow, 'triggers[0].trigger_id', null)

  if (!workflow || !triggerId || !actionPath) {
    return []
  }

  /* Get all variables of the first trigger. The real workflow only have the input of the first workflow */
  findInputVariables(workflow.triggers[0].trigger_id).forEach((v) => variables.add(v))

  /* Get all variables of actions */
  getAllPreviousComponents(workflow, actionPath).forEach((component) => {
    findOutputVariables(component.componentId, component.componentName).forEach((v) => variables.add(v))
  })

  return Array.from(variables)
}

export const getAllPreviousComponents = (workflow, actionPath) => {
  const components = []
  const processedComponents = new Set()
  let componentName = actionPath.split('.').pop()
  let previousComponents
  do {
    previousComponents = jsonpath.nodes(workflow, `$..*[?(@.next === '${componentName}' || @.default === '${componentName}')]`)
    if (!previousComponents.length) {
      break
    }

    const component = previousComponents[0]
    componentName = getComponentName(component)
    if (processedComponents.has(componentName)) {
      /* Circular loop */
      break
    }
    processedComponents.add(componentName)
    if (component.value.component_id) {
      const componentId = component.value.component_id
      components.push({
        componentId,
        componentName,
      })
    }
  }
  while (previousComponents.length)

  return components
}

export const getComponentName = (component) => {
  if (component.path.includes('conditions')) {
    return component.path[component.path.indexOf('conditions') - 1]
  }
  return component.path[component.path.length - 1]
}

export const findInputVariables = (componentId) => (componentId in VARIBLES ? VARIBLES[componentId].map((variable) => `$input.${variable}`) : [])

export const findOutputVariables = (componentId, componentName) => (componentId in VARIBLES ? VARIBLES[componentId].map((variable) => `$lookup.${componentName}.${variable}`) : [])

export default findVariables
