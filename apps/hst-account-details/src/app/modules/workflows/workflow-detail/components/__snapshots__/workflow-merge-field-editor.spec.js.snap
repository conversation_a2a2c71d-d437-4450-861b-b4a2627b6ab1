// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`WorkflowMergeField render default 1`] = `
<Col
  key="definition.states.send_opt_out_confirm"
  span={24}
>
  <Heading>
     
    send_opt_out_confirm
     
  </Heading>
  <Select
    bordered={false}
    className="dropdownContainer"
    data-test="select-chooseATemplate"
    dropDown={true}
    dropdownMatchSelectWidth={false}
    id="definition.states.send_opt_out_confirm"
    name="mergefieldsDropdown"
    onChange={[Function]}
    optionFilterProp="title"
    options={
      [
        {
          "key": "$input.content",
          "value": "$input.content",
        },
        {
          "key": "$input.source_number",
          "value": "$input.source_number",
        },
        {
          "key": "$input.message_id",
          "value": "$input.message_id",
        },
        {
          "key": "$input.destination_number",
          "value": "$input.destination_number",
        },
        {
          "key": "$input.outbound_message_id",
          "value": "$input.outbound_message_id",
        },
        {
          "key": "$input.metadata",
          "value": "$input.metadata",
        },
        {
          "key": "$input.outbound_content",
          "value": "$input.outbound_content",
        },
        {
          "key": "$input.timestamp",
          "value": "$input.timestamp",
        },
        {
          "key": "$input.outbound_timestamp",
          "value": "$input.outbound_timestamp",
        },
        {
          "key": "$input.source_number_country",
          "value": "$input.source_number_country",
        },
        {
          "key": "$input.destination_number_country",
          "value": "$input.destination_number_country",
        },
      ]
    }
    placeholder={
      <div
        className="placeholder"
      >
        <span>
          Merge fields
        </span>
      </div>
    }
    style={
      {
        "width": "150px",
      }
    }
    value=""
  />
  <TextArea
    className="sms-input"
    data-test="sms-text-area"
    id="definition.states.send_opt_out_confirm"
    onChange={[Function]}
    style={
      {
        "height": 150,
      }
    }
    value="Hello there"
  />
</Col>
`;
