import React, { useRef } from 'react'
import { createJSONEditor } from 'vanilla-jsoneditor'

const WorkflowEditor = (props) => {
  const refContainer = useRef(null)
  const editorRef = useRef(null)

  React.useEffect(() => {
    editorRef.current = createJSONEditor({
      target: refContainer.current!,
      props: {}
    })

    return () => {
      // destroy editor
      if (editorRef.current) {
        editorRef.current.destroy()
        editorRef.current = null
      }
    }
  }, [])

  React.useEffect(() => {
    // update props
    if (editorRef.current) {
      editorRef.current.updateProps(props)
    }
  }, [props])

  return (
    <>
      <div
        data-test="workflow-json-editor"
        style={{ height: 500 }}
        className="jsonEditor"
        ref={refContainer}
      />
    </>
  )
}

export default WorkflowEditor
