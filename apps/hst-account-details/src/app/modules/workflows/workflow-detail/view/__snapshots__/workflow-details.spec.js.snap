// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`workflow-detail Renders 1`] = `
<div
  data-test="update-workflow-page"
>
  <SubMenu
    title="Workflows"
  >
    <Col
      lg={18}
      sm={12}
      xl={21}
    >
      <Heading>
        Workflows
      </Heading>
      <div>
        <Link
          name="support"
          to="/undefined"
        >
          <ForwardRef(CaretLeftOutlined) />
          back to support dashboard
        </Link>
      </div>
    </Col>
  </SubMenu>
  <div>
    <Panel
      data-test="workflow-information-panel"
    >
      <Row
        gutter={24}
        justify="center"
      >
        <Col
          span={6}
        >
          <Typography>
            Id
          </Typography>
        </Col>
        <Col
          span={10}
        >
          <Typography>
            Name
          </Typography>
        </Col>
        <Col
          span={4}
        >
          <Typography>
            Status
          </Typography>
        </Col>
        <Col
          span={4}
        >
          <Typography>
            Type
          </Typography>
        </Col>
      </Row>
      <Row
        gutter={24}
      >
        <Col
          span={6}
        >
          <Typography />
        </Col>
        <Col
          span={10}
        >
          <Typography />
        </Col>
        <Col
          span={4}
        >
          <Typography />
        </Col>
        <Col
          span={4}
        >
          <Typography>
            Managed Workflow
          </Typography>
        </Col>
      </Row>
    </Panel>
    <Panel>
      <Row
        justify="end"
        style={
          {
            "marginBottom": 20,
            "marginTop": 20,
          }
        }
      >
        <Col>
          <Alert
            data-test="changing-workflow-alert"
            message="You are changing the workflow logic."
            showIcon={true}
            type="warning"
          />
        </Col>
      </Row>
      <Row
        gutter={24}
        justify="space-between"
        style={
          {
            "marginBottom": 20,
          }
        }
      >
        <Col
          span={8}
        >
          <Row
            align="middle"
            className="editFields"
            justify="center"
            style={
              {
                "width": "100%",
              }
            }
          />
        </Col>
        <Col
          span={14}
        >
          <WorkflowEditor
            content={false}
            mode="text"
            onChange={[Function]}
          />
        </Col>
      </Row>
      <Row
        gutter={24}
        justify="end"
      >
        <Button
          name="reset-workflow"
          onClick={[Function]}
          type="none"
        >
          Cancel
        </Button>
        <Button
          disabled={false}
          name="show-confirm-workflow-modal"
          onClick={[Function]}
        >
          Save
        </Button>
      </Row>
    </Panel>
  </div>
  <Modal
    cancelButtonProps={
      {
        "data-test": "button-cancel-save-workflow",
      }
    }
    cancelText="NO"
    confirmLoading={false}
    maskTransitionName="fade"
    okButtonProps={
      {
        "data-test": "button-save-workflow",
      }
    }
    okText="YES"
    okType="primary"
    onCancel={[Function]}
    onOk={[Function]}
    title="Update workflow confirmation"
    transitionName="zoom"
    visible={false}
    width={520}
  >
    You have changed the logic of this workflow. Are you sure you want to save?
  </Modal>
</div>
`;

exports[`workflow-detail Renders automated workflow 1`] = `
<div
  data-test="update-workflow-page"
>
  <SubMenu
    title="Workflows"
  >
    <Col
      lg={18}
      sm={12}
      xl={21}
    >
      <Heading>
        Workflows
      </Heading>
      <div>
        <Link
          name="support"
          to="/undefined"
        >
          <ForwardRef(CaretLeftOutlined) />
          back to support dashboard
        </Link>
      </div>
    </Col>
  </SubMenu>
  <div>
    <Panel
      data-test="workflow-information-panel"
    >
      <Row
        gutter={24}
        justify="center"
      >
        <Col
          span={6}
        >
          <Typography>
            Id
          </Typography>
        </Col>
        <Col
          span={10}
        >
          <Typography>
            Name
          </Typography>
        </Col>
        <Col
          span={4}
        >
          <Typography>
            Status
          </Typography>
        </Col>
        <Col
          span={4}
        >
          <Typography>
            Type
          </Typography>
        </Col>
      </Row>
      <Row
        gutter={24}
      >
        <Col
          span={6}
        >
          <Typography />
        </Col>
        <Col
          span={10}
        >
          <Typography />
        </Col>
        <Col
          span={4}
        >
          <Typography />
        </Col>
        <Col
          span={4}
        >
          <Typography>
            Managed Workflow
          </Typography>
        </Col>
      </Row>
    </Panel>
    <Panel>
      <Row
        justify="end"
        style={
          {
            "marginBottom": 20,
            "marginTop": 20,
          }
        }
      >
        <Col>
          <Alert
            data-test="changing-workflow-alert"
            message="You are changing the workflow logic."
            showIcon={true}
            type="warning"
          />
        </Col>
      </Row>
      <Row
        gutter={24}
        justify="space-between"
        style={
          {
            "marginBottom": 20,
          }
        }
      >
        <Col
          span={8}
        >
          <Row
            align="middle"
            className="editFields"
            justify="center"
            style={
              {
                "width": "100%",
              }
            }
          />
        </Col>
        <Col
          span={14}
        >
          <WorkflowEditor
            content={false}
            mode="text"
            onChange={[Function]}
          />
        </Col>
      </Row>
      <Row
        gutter={24}
        justify="end"
      >
        <Button
          name="reset-workflow"
          onClick={[Function]}
          type="none"
        >
          Cancel
        </Button>
        <Button
          disabled={false}
          name="show-confirm-workflow-modal"
          onClick={[Function]}
        >
          Save
        </Button>
      </Row>
    </Panel>
  </div>
  <Modal
    cancelButtonProps={
      {
        "data-test": "button-cancel-save-workflow",
      }
    }
    cancelText="NO"
    confirmLoading={false}
    maskTransitionName="fade"
    okButtonProps={
      {
        "data-test": "button-save-workflow",
      }
    }
    okText="YES"
    okType="primary"
    onCancel={[Function]}
    onOk={[Function]}
    title="Update workflow confirmation"
    transitionName="zoom"
    visible={false}
    width={520}
  >
    You have changed the logic of this workflow. Are you sure you want to save?
  </Modal>
</div>
`;

exports[`workflow-detail render workflow merge field 1`] = `
<div
  data-test="update-workflow-page"
>
  <SubMenu
    title="Workflows"
  >
    <Col
      lg={18}
      sm={12}
      xl={21}
    >
      <Heading>
        Workflows
      </Heading>
      <div>
        <Link
          name="support"
          to="/undefined"
        >
          <ForwardRef(CaretLeftOutlined) />
          back to support dashboard
        </Link>
      </div>
    </Col>
  </SubMenu>
  <div>
    <Panel
      data-test="workflow-information-panel"
    >
      <Row
        gutter={24}
        justify="center"
      >
        <Col
          span={6}
        >
          <Typography>
            Id
          </Typography>
        </Col>
        <Col
          span={10}
        >
          <Typography>
            Name
          </Typography>
        </Col>
        <Col
          span={4}
        >
          <Typography>
            Status
          </Typography>
        </Col>
        <Col
          span={4}
        >
          <Typography>
            Type
          </Typography>
        </Col>
      </Row>
      <Row
        gutter={24}
      >
        <Col
          span={6}
        >
          <Typography />
        </Col>
        <Col
          span={10}
        >
          <Typography>
            A test workflow
          </Typography>
        </Col>
        <Col
          span={4}
        >
          <Typography>
            ACTIVE
          </Typography>
        </Col>
        <Col
          span={4}
        >
          <Typography>
            Managed Workflow
          </Typography>
        </Col>
      </Row>
    </Panel>
    <Panel>
      <Row
        justify="end"
        style={
          {
            "marginBottom": 20,
            "marginTop": 20,
          }
        }
      >
        <Col />
      </Row>
      <Row
        gutter={24}
        justify="space-between"
        style={
          {
            "marginBottom": 20,
          }
        }
      >
        <Col
          span={8}
        >
          <Row
            align="middle"
            className="editFields"
            justify="center"
            style={
              {
                "width": "100%",
              }
            }
          >
            <WorkflowMergeField
              onContentChange={[Function]}
              wfId="definition.states.send_welcome_sms"
              wfValue={
                {
                  "component_id": "send-sms-v1",
                  "end": true,
                  "label": "send_welcome_sms",
                  "properties": {
                    "content": "Hello there",
                    "destination_number": "$input.source_number",
                    "source_number": "123456",
                  },
                  "retry_max_attempts": 3,
                  "type": "ACTION",
                }
              }
              workflow={
                {
                  "config": {},
                  "createTimestamp": "2022-05-04T11:14:18.554595+00:00",
                  "definition": {
                    "start_at": "publish_to_integrations_sns",
                    "states": {
                      "publish_to_integrations_sns": {
                        "component_id": "integrations-sms-event-publisher-v1",
                        "end": true,
                        "properties": {
                          "content": "$input.content",
                          "destination_number": "$input.destination_number",
                          "message_id": "$input.message_id",
                          "platform": "hubspot",
                          "source_number": "$input.source_number",
                        },
                        "type": "ACTION",
                      },
                      "send_welcome_sms": {
                        "component_id": "send-sms-v1",
                        "end": true,
                        "properties": {
                          "content": "Hello there",
                          "destination_number": "$input.source_number",
                          "source_number": "123456",
                        },
                        "retry_max_attempts": 3,
                        "type": "ACTION",
                      },
                    },
                  },
                  "description": null,
                  "expiry_in_days": null,
                  "id": "3792f751-18d5-4689-bf6c-0fea1d67c843",
                  "labels": [],
                  "metadata": {},
                  "name": "A test workflow",
                  "status": "ACTIVE",
                  "templated_workflow_id": null,
                  "triggers": [
                    {
                      "properties": {},
                      "trigger_id": "mo-sms-v1",
                    },
                  ],
                  "updateTimestamp": "2022-05-04T11:14:18.554607+00:00",
                }
              }
            />
          </Row>
        </Col>
        <Col
          span={14}
        >
          <WorkflowEditor
            content={
              {
                "config": {},
                "createTimestamp": "2022-05-04T11:14:18.554595+00:00",
                "definition": {
                  "start_at": "publish_to_integrations_sns",
                  "states": {
                    "publish_to_integrations_sns": {
                      "component_id": "integrations-sms-event-publisher-v1",
                      "end": true,
                      "properties": {
                        "content": "$input.content",
                        "destination_number": "$input.destination_number",
                        "message_id": "$input.message_id",
                        "platform": "hubspot",
                        "source_number": "$input.source_number",
                      },
                      "type": "ACTION",
                    },
                    "send_welcome_sms": {
                      "component_id": "send-sms-v1",
                      "end": true,
                      "properties": {
                        "content": "Hello there",
                        "destination_number": "$input.source_number",
                        "source_number": "123456",
                      },
                      "retry_max_attempts": 3,
                      "type": "ACTION",
                    },
                  },
                },
                "description": null,
                "expiry_in_days": null,
                "id": "3792f751-18d5-4689-bf6c-0fea1d67c843",
                "labels": [],
                "metadata": {},
                "name": "A test workflow",
                "status": "ACTIVE",
                "templated_workflow_id": null,
                "triggers": [
                  {
                    "properties": {},
                    "trigger_id": "mo-sms-v1",
                  },
                ],
                "updateTimestamp": "2022-05-04T11:14:18.554607+00:00",
              }
            }
            mode="text"
            onChange={[Function]}
          />
        </Col>
      </Row>
      <Row
        gutter={24}
        justify="end"
      >
        <Button
          name="reset-workflow"
          onClick={[Function]}
          type="none"
        >
          Cancel
        </Button>
        <Button
          disabled={false}
          name="show-confirm-workflow-modal"
          onClick={[Function]}
        >
          Save
        </Button>
      </Row>
    </Panel>
  </div>
  <Modal
    cancelButtonProps={
      {
        "data-test": "button-cancel-save-workflow",
      }
    }
    cancelText="NO"
    confirmLoading={false}
    maskTransitionName="fade"
    okButtonProps={
      {
        "data-test": "button-save-workflow",
      }
    }
    okText="YES"
    okType="primary"
    onCancel={[Function]}
    onOk={[Function]}
    title="Update workflow confirmation"
    transitionName="zoom"
    visible={false}
    width={520}
  >
    You have changed the logic of this workflow. Are you sure you want to save?
  </Modal>
</div>
`;
