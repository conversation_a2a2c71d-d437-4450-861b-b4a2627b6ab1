import '@nectary/components/grid';
import '@nectary/components/grid-item';
import '@nectary/components/spinner';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import Button from '@sinch-smb/button';
import Panel from '@sinch-smb/panel';
import Typography from '@sinch-smb/typography';

import jsonpath from 'jsonpath';
import _isEqual from 'lodash/isEqual';
import _isEmpty from 'lodash/isEmpty';

import { InlineAlert } from 'apps/hst-ten-dlc/src/components/reactNectary';
import WorkflowAPI from '../../../../redux/workflows/workflows-api';
import { RootState } from '../../../../types/store';

import {
  getWorkflows,
  getWorkflowDetails,
} from '../../../../redux/workflows/workflows-slice';
import WorkflowEditor from '../components/workflow-editor';
import WorkflowMergeField from '../components/workflow-merge-field-editor';
import styles from './workflow-details.less';

export const WorkflowDetailView = ({
  accountId,
  vendorId,
  workflowId,
  setUpdateStatus,
  updateSuccess,
  isEditable,
}: {
  workflowId: string;
  accountId: string;
  vendorId: string;
  setUpdateStatus: (params: any) => void;
  updateSuccess: any;
  isEditable: boolean;
}) => {
  const dispatch = useDispatch();

  const { accountWorkflows } = useSelector((state: RootState) => state);
  const { workflow } = accountWorkflows || {};

  const [wf, setWorkflow] = useState(undefined);
  const [content, setContent] = useState({ json: workflow });
  const [smsActions, setSmsActions] = useState({});
  const [isInvalidJsonContent, setIsInvalidJsonContent] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const querySendSmsActionNodes = () => {
    const pathUrl =
      "$..*[?(@.type==='ACTION' && (@.component_id === 'send-sms-v1' || @.component_id === 'send-sms-v2' || @.component_id === 'send-broadcast-sms-v1'))]";
    return jsonpath.nodes(wf, pathUrl);
  };

  const checkChangingWorkflow = () => !_isEqual(workflow, wf);

  const getSmsActions = () => {
    const actionNodes = querySendSmsActionNodes();
    const result = {};
    for (const actionNode of actionNodes) {
      const { path, value } = actionNode;
      result[path.slice(1).join('.')] = {
        ...value,
        label: path[path.length - 1],
      };
    }
    return result;
  };

  useEffect(() => {
    if (workflow?.id) {
      setWorkflow(workflow);
      setContent({ json: workflow });
    }
  }, [workflow]);

  useEffect(() => {
    /* istanbul ignore next */
    if (onContentChange && typeof onContentChange === 'function') {
      onContentChange({ json: workflow });
    }
  }, [workflow]);

  useEffect(() => {
    if (wf) {
      const actions = getSmsActions();
      setSmsActions(actions);
    }
    checkChangingWorkflow();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [wf]);

  useEffect(() => {
    if (!workflowId) return;
    setWorkflow(undefined);
    setContent({ json: {} });
    setSmsActions({});
    dispatch(
      getWorkflowDetails({ vendorId, accountId, workflowID: workflowId })
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [accountId, workflowId]);

  const onContentChange = (newContent) => {
    try {
      setContent(newContent);
      const newWorkflow = newContent.json || JSON.parse(newContent?.text);
      setIsInvalidJsonContent(false);
      delete newWorkflow.id;
      setWorkflow(newWorkflow);
    } catch (error) {
      setIsInvalidJsonContent(true);
    }
  };

  const saveWorkflow = async () => {
    try {
      const body = { ...wf };
      delete body.id;
      delete body.createTimestamp;
      delete body.updateTimestamp;
      await WorkflowAPI.updateWorkflow({
        vendorId,
        accountId,
        workflowId,
        body,
      });
      setUpdateStatus({
        successStatus: true,
        message: 'Save workflow successfully',
      });
      await dispatch(
        getWorkflowDetails({ vendorId, accountId, workflowID: workflowId })
      );
    } catch (error) {
      setUpdateStatus({ successStatus: false, message: error.message });
    } finally {
      setIsSubmitting(false);
      dispatch(
        getWorkflows({ accountId, vendorId, params: { next: '', size: 10 } })
      );
    }
  };

  const cancelChangeWorkflow = () => {
    onContentChange({ json: workflow });
    setWorkflow(workflow);
    setUpdateStatus({ successStatus: undefined, message: '' });
  };

  const submitHandle = () => {
    setIsSubmitting(true);
    saveWorkflow();
  };

  const renderEditorMenu = (items, context) => {
    const newItems = [...items];
    newItems.shift();
    return newItems;
  };
  const renderContent = () => (
    <div>
      <Panel data-test="workflow-information-panel">
        <sinch-grid>
          <sinch-grid-item slot="item" xl={3} l={4} m={4} s={4}>
            <div slot="content">
              <Typography>Id</Typography>
              <Typography>{workflowId}</Typography>
            </div>
          </sinch-grid-item>
          <sinch-grid-item slot="item" xl={3} l={4} m={4} s={4}>
            <div slot="content">
              <Typography>Name</Typography>
              <Typography>{workflow?.name}</Typography>
            </div>
          </sinch-grid-item>
          <sinch-grid-item slot="item" xl={3} l={4} m={4} s={4}>
            <div slot="content">
              <Typography>Status</Typography>
              <Typography>{workflow?.status}</Typography>
            </div>
          </sinch-grid-item>
          <sinch-grid-item slot="item" xl={3} l={4} m={4} s={4}>
            <div slot="content">
              <Typography>Type</Typography>
              <Typography>
                {
                  /* istanbul ignore next */ workflow?.labels?.includes(
                    'account-automation'
                  )
                    ? 'Account Automation'
                    : 'Managed Workflow'
                }
              </Typography>
            </div>
          </sinch-grid-item>
        </sinch-grid>
      </Panel>
      <Panel>
        <div className={styles.alertMessage}>
          <span className={styles.success}>
            {updateSuccess.successStatus !== undefined && (
              <InlineAlert
                data-test="update-workflow-success-alert"
                caption=""
                text={updateSuccess.message}
                type={updateSuccess.successStatus ? 'success' : 'error'}
                showIcon
                isClosable
              />
            )}
          </span>
          <span className={styles.warning}>
            {checkChangingWorkflow() && (
              <InlineAlert
                data-test="changing-workflow-alert"
                caption=""
                text="You are changing the workflow logic."
                type="warn"
                showIcon
              />
            )}
          </span>
        </div>
        <div className={styles.editView}>
          {!_isEmpty(Object.entries(smsActions)) && (
            <div
              slot="content"
              className={styles.editFields}
              style={{ width: '30%' }}
            >
              {Object.entries(smsActions).map(([key, value]) => (
                <WorkflowMergeField
                  wfValue={value}
                  onContentChange={onContentChange}
                  wfId={key}
                  workflow={wf}
                />
              ))}
            </div>
          )}
          <div
            slot="content"
            style={
              !_isEmpty(Object.entries(smsActions))
                ? { width: '70%' }
                : { width: '100%' }
            }
          >
            <WorkflowEditor
              mode="tree"
              onRenderMenu={renderEditorMenu}
              content={content}
              onChange={onContentChange}
            />
          </div>
        </div>
        <div className={styles.submitBtn}>
          <Button
            type="none"
            name="reset-workflow"
            onClick={cancelChangeWorkflow}
          >
            Cancel changes
          </Button>
          <Button
            name="show-confirm-workflow-modal"
            disabled={!isEditable || isInvalidJsonContent}
            onClick={submitHandle}
            loading={isSubmitting}
          >
            Save
          </Button>
        </div>
      </Panel>
    </div>
  );

  return (
    <div data-test="update-workflow-page">
      {wf ? renderContent() : <sinch-spinner size="l" />}
    </div>
  );
};

export default WorkflowDetailView;
