import {
  findInputVariables, findOutputVariables, getComponentName, getAllPreviousComponents, findVariables,
} from './workflow-utils'
import workflow from '../../../fixtures/workflow/full-workflow-fixture'
import cyclicWorkflow from '../../../fixtures/workflow/cyclic-workflow-fixture'

describe('Workflow utils', () => {
  it('findInputVariables', () => {
    expect(findInputVariables('mo-sms-v1')).toEqual(
      ['$input.content',
        '$input.source_number',
        '$input.message_id',
        '$input.destination_number',
        '$input.outbound_message_id',
        '$input.metadata',
        '$input.outbound_content',
        '$input.timestamp',
        '$input.outbound_timestamp',
        '$input.source_number_country',
        '$input.destination_number_country'])
  })

  it('findInputVariables with no matched trigger', () => {
    expect(findInputVariables('mo-sms-vzzz')).toEqual([])
  })

  it('findOutputVariables', () => {
    expect(findOutputVariables('mo-sms-v1', 'test')).toEqual(
      ['$lookup.test.content',
        '$lookup.test.source_number',
        '$lookup.test.message_id',
        '$lookup.test.destination_number',
        '$lookup.test.outbound_message_id',
        '$lookup.test.metadata',
        '$lookup.test.outbound_content',
        '$lookup.test.timestamp',
        '$lookup.test.outbound_timestamp',
        '$lookup.test.source_number_country',
        '$lookup.test.destination_number_country'])
  })

  it('findOutputVariables with no matched action', () => {
    expect(findOutputVariables('mo-sms-vzzz')).toEqual([])
  })

  it('getComponentName with conditions type', () => {
    const component = {
      path: ['$', 'definition', 'states', 'check_reject_keyword', 'conditions', 0],
      value: { next: 'send_opt_out_confirm', or: [{ variable: '$input.content', string_starts_with_ignore_case: 'STOP' }] },
    }
    expect(getComponentName(component)).toEqual('check_reject_keyword')
  })

  it('getComponentName with action type', () => {
    const component = {
      path: ['$', 'definition', 'states', 'send_opt_out_confirm'],
      value: {
        next: 'format_timestamp', component_id: 'send-sms-v1', retry_max_attempts: 3, type: 'ACTION', properties: { destination_number: '$input.source_number', content: 'test' },
      },
    }
    expect(getComponentName(component)).toEqual('send_opt_out_confirm')
  })

  it('getAllPreviousComponents', () => {
    expect(getAllPreviousComponents(workflow, 'definition.states.send_welcome')).toEqual([
      {
        componentId: 'hub-contact-lookup-phone-number-v1',
        componentName: 'lookup_contact',
      },
      {
        componentId: 'invoke-webhook-v1',
        componentName: 'post_contact_detail_to_sinch',
      },
      {
        componentId: 'manipulate-datetime-v1',
        componentName: 'format_timestamp',
      },
      {
        componentId: 'send-sms-v1',
        componentName: 'send_opt_out_confirm',
      },
    ])
  })

  it('findVariables', () => {
    expect(findVariables(workflow, 'definition.states.send_welcome')).toEqual([
      '$input.content',
      '$input.source_number',
      '$input.message_id',
      '$input.destination_number',
      '$input.outbound_message_id',
      '$input.metadata',
      '$input.outbound_content',
      '$input.timestamp',
      '$input.outbound_timestamp',
      '$input.source_number_country',
      '$input.destination_number_country',
      '$lookup.lookup_contact.id',
      '$lookup.lookup_contact.mode',
      '$lookup.lookup_contact.phone_number',
      '$lookup.lookup_contact.alias',
      '$lookup.lookup_contact.first_name',
      '$lookup.lookup_contact.last_name',
      '$lookup.lookup_contact.email_address',
      '$lookup.lookup_contact.country',
      '$lookup.lookup_contact.groups',
      '$lookup.post_contact_detail_to_sinch.response_code',
      '$lookup.post_contact_detail_to_sinch.response_body',
      '$lookup.format_timestamp.utc_datetime',
      '$lookup.send_opt_out_confirm.message_id',
    ])
  })

  it('findVariables for cyclic workflow', () => {
    expect(findVariables(cyclicWorkflow, 'definition.states.send_opt_out_confirm')).toEqual([
      '$input.content',
      '$input.source_number',
      '$input.message_id',
      '$input.destination_number',
      '$input.outbound_message_id',
      '$input.metadata',
      '$input.outbound_content',
      '$input.timestamp',
      '$input.outbound_timestamp',
      '$input.source_number_country',
      '$input.destination_number_country',
      '$lookup.send_opt_out_confirm.message_id',
    ])
  })

  it('findVariables empty', () => {
    expect(findVariables('', '')).toEqual([
    ])
  })
})
