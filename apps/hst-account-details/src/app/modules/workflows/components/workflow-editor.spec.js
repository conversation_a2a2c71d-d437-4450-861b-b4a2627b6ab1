import React from 'react'
import WorkflowEditorView from './workflow-editor'

jest.mock('vanilla-jsoneditor', () => ({
  JSONEditor: jest.fn().mockImplementation((fn) => jest.fn()),
}))

describe('workflow-editor', () => {
  const props = {
  }

  let useRef
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  let cleanupFunc
  const mockUseRef = (value) => useRef.mockImplementation(() => ({ current: value }))

  beforeEach(() => {
    const useEffect = jest.spyOn(React, 'useEffect')
    const mockUseEffect = () => useEffect.mockImplementation((f) => {
      if (typeof f === 'function') {
        cleanupFunc = f()
      }
    })
    useRef = jest.spyOn(React, 'useRef')
    mockUseEffect()
  })

  afterAll(() => {
    jest.clearAllMocks()
  })

  it('Renders', () => {
    const wrapper = shallow(<WorkflowEditorView {...props} />)
    mockUseRef({ updateProps: () => {} })
    expect(wrapper).toMatchSnapshot()
  })

  it('test onRef', () => {
    const wrapper = shallow(<WorkflowEditorView {...props} />)
    mockUseRef(null)
    wrapper.find('div').getElement().ref('test')
    expect(wrapper).toMatchSnapshot()
  })

  it('test onRef with ref is null', () => {
    const wrapper = shallow(<WorkflowEditorView {...props} />)
    mockUseRef('test')
    wrapper.find('div').getElement().ref('test')
    expect(wrapper).toMatchSnapshot()
  })
})
