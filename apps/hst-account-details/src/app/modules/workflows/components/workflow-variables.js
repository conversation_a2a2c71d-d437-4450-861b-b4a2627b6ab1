export default {
  'mo-sms-v1': [
    'content',
    'source_number',
    'message_id',
    'destination_number',
    'outbound_message_id',
    'metadata',
    'outbound_content',
    'timestamp',
    'outbound_timestamp',
    'source_number_country',
    'destination_number_country',
  ],
  'mo-mms-v2': [
    'content',
    'source_number',
    'message_id',
    'attachments',
    'destination_number',
    'outbound_message_id',
    'metadata',
    'outbound_content',
    'timestamp',
    'outbound_timestamp',
    'source_number_country',
    'destination_number_country',
  ],
  'dr-sms-v1': [
    'delivery_status',
    'source_number',
    'message_id',
    'outbound_message_id',
    'outbound_content',
    'destination_number',
    'metadata',
    'timestamp',
    'outbound_timestamp',
    'content',
    'source_number_country',
    'destination_number_country',
  ],
  'inbound-email-v1': [
    'from_address',
    'to_addresses',
    'cc_addresses',
    'bcc_addresses',
    'subject',
    'body',
    'attachments',
    'headers',
  ],
  'mlp-trigger-v1': [
    'campaign_id',
    'recipient_id',
    'number',
    'event',
    'source',
    'data',
    'event_timestamp',
    'source_number',
    'content',
    'metadata',
  ],
  'mo-mms-v1': [
    'source_number',
    'date_received',
    'message_id',
    'reply_id',
    'account_id',
    'attachments',
    'destination_number',
  ],
  'mt-sms-v1': [
    'content',
    'destination_number',
    'message_id',
    'mt_event_type',
    'event',
    'source_number',
    'metadata',
    'timestamp',
    'is_rerouted',
    'source_number_country',
    'destination_number_country',
  ],
  'sftp-download-v1': [
    'host',
    'port',
    'username',
    'password',
    'key',
    'folderPath',
    'doneFolderPath',
    'pattern',
    'bucketLocation',
  ],
  'send-sms-v1': [
    'message_id',
  ],
  'send-sms-v2': [
    'messages',
  ],
  'duplication-check-v1': [
    'is_duplicate',
  ],
  'invoke-webhook-v1': [
    'response_code',
    'response_body',
  ],
  'manipulate-datetime-v1': [
    'utc_datetime',
  ],
  'parse-text-by-delimiter-v1': [
    'delimiter_field_mapping',
    'post_processed_text',
  ],
  'send-message-get-response-v1': [
    'mo_content',
  ],
  'atomic-counter-v1': [
    'count',
  ],
  'batch-records-v1': [
    's3_url',
  ],
  'create-contact-v1': [
    'id',
    'phone_number',
    'mode',
    'alias',
    'first_name',
    'last_name',
    'email_address',
    'country',
    'groups',
    'date_of_birth',
    'custom_field1',
    'custom_field2',
    'custom_field3',
    'location',
  ],
  'download-file-from-sftp-v1': [
    'fileList',
  ],
  'duplication-check-v2': [
    'is_duplicate',
  ],
  'generate-uuid-v1': [
    'uuid',
  ],
  'invoke-mm-webhook-v1': [
    'response_code',
    'response_body',
  ],
  'mnet-reply-type-invoke-m1-v1': [
    'response_code',
    'response_body',
    'service_no',
    'mobile',
    'content',

  ],
  'parser-phone-numbers-v1': [
    'success',
    'phone_number',
    'post_processed_text',
  ],
  'replace-string-v1': [
    'result_string',
  ],
  'transform-json-list-v1': [
    'output_list',
  ],
  'update-contact-v1': [
    'id',
    'phone_number',
    'mode',
    'alias',
    'first_name',
    'last_name',
    'email_address',
    'country',
    'groups',
    'date_of_birth',
    'custom_field1',
    'custom_field2',
    'custom_field3',
  ],
  'dynamodb-lookup-v1': [
    'item',
  ],
  'hub-contact-lookup-phone-number-v1': [
    'id',
    'mode',
    'phone_number',
    'alias',
    'first_name',
    'last_name',
    'email_address',
    'country',
    'groups',
  ],
  'hub-contact-lookup-v1': [
    'id',
    'mode',
    'phone_number',
    'alias',
    'first_name',
    'last_name',
    'email_address',
    'country',
    'groups',
    'gender',
  ],
  'hub-template-lookup-v1': [
    'id',
    'name',
    'message_text',
    'default',
  ],
  'parameter-store-lookup-v1': [
    'value',
  ],
  'ams-account-details-lookup-v1': [
    'account_id',
    'account_name',
    'parent_account_id',
    'operating_country',
    'default_traffic_class',
    'status',
    'billing_type',
    'timezone',
    'feature_flags',
    'effective_feature_flags',
  ],
  'ams-account-opt-out-lookup-v1': [
    'automatic_unsubscribe',
    'opt_out_list',
  ],
  'array-iterator-v1': [
    'index',
    'non_zero_index',
    'item',
    'in_loop',
  ],
  'convert-xml-to-json-v1': [
    'convention',
    'preserve_root',
    'json',
  ],
  'dedicated-number-info-lookup-v1': [
    'label',
  ],
  'exponential-backoff-lookup-v1': [
    'value',
  ],
  'get-contacts-from-group-v1': [
    'contacts',
  ],
  'get-int-auth-tokens-v1': [
    'access_token',
    'expiry_timestamp',
    'scope',
    'access_token_type',
  ],
  'hub-broadcast-lookup-v1': [
    'id',
    'name',
    'message',
    'format',
    'recipients',
    'metadata',
    'source',
    'scheduled',
    'scheduled_display_timezone',
    'stagger_window',
    'rich_link',
    'landing_page',
    'status',
    'estimated_recipient_count',
    'recipient_count',
    'recipient_error_count',
    'processed_count',
    'processed_error_count',
    'estimated_billing_units',
    'messages_queued',
    'queue_failures',
  ],
  'hub-contact-group-lookup-v1': [
    'id',
    'account_id',
    'mode',
    'name',
    'alias',
    'total',
    'subscribed',
    'unsubscribed',
    'shared_account_ids',
  ],
  'hub-contact-lookup-alias-v1': [
    'id',
    'mode',
    'phone_number',
    'alias',
    'first_name',
    'last_name',
    'email_address',
    'country',
    'groups',
  ],
  'is-rcs-enabled-v1': [
    'rcs_status',
  ],
  'lookup-phone-number-in-contact-group-v1': [
    'is_present',
    'contact',
  ],
  'mnet-reply-type-email-template-lookup-v1': [
    'body',
    'subject',
    'to_addresses',
    'from_address',
  ],
  'mnet-reply-type-lookup-v1': [
    'reply_type',
    'reply_path',
    'reply_track',
  ],
  'next-schedule-in-cron-v1': [
    'next_schedule',
  ],
  'replace-data-v1': [
    'output',
  ],
  'smsc-reply-type-lookup-v1': [
    'eply_type',
    'reply_path',
  ],
  'blacklist-contact-status-lookup-v1': [
    'subscribed_contacts',
    'unsubscribed_contacts',
  ],
}
