import React from 'react'
import PropTypes from 'prop-types'
import Heading from '@sinch-smb/heading'
import { Col, Input } from 'antd'
import Select from '@sinch-smb/antd-select'
import styles from '../view/workflow-details.less'
import { findVariables } from './workflow-utils'

type TProps = {
  wfValue: Record<string , string>;
  wfId: string;
  onContentChange: () => void;
  workflow: Record<string , string>;
}

let mergeFields = []
const WorkflowMergeField = (props: TProps) => {
  const {
    wfValue, wfId, onContentChange, workflow,
  } = props

  mergeFields = findVariables(workflow, wfId).map((variable) => ({ key: variable, value: variable }))

  const [content, setContent] = React.useState(wfValue.properties?.content)

  const objectFieldChange = (id: string, value: string, newWorkflow) => {
    // from the json path array, reduce to get reference of action
    /* istanbul ignore next */
    const properties = id.split('.')
    const objField = properties.reduce((r, u) => r[u], newWorkflow)
    objField.properties.content = value
  }

  const onSmsTextChange = (e) => {
    const { value, id } = e.target
    setContent(value)
    const newWorkflow = JSON.parse(JSON.stringify(workflow))
    objectFieldChange(id, value, newWorkflow)
    onContentChange({ json: newWorkflow })
  }

  const handleMergeFieldClick = (id: string) => (selectValue: string) => {
    const newWorkflow = JSON.parse(JSON.stringify(workflow))
    const value = `${content}${selectValue}`
    setContent(value)
    objectFieldChange(id, value, newWorkflow)

    onContentChange({ json: newWorkflow })
  }
  return (
    <Col span={24} key={wfId}>
      <Heading>
        {' '}
        {wfValue.label.toString()}
        {' '}
      </Heading>
      <Select
        bordered={false}
        id={wfId}
        className={styles.dropdownContainer}
        name="mergefieldsDropdown"
        optionFilterProp="title"
        style={{ width: '150px' }}
        value=""
        dropDown
        dropdownMatchSelectWidth={false}
        onChange={handleMergeFieldClick(wfId)}
        placeholder={(
          <div className={styles.placeholder}>
            <span>Merge fields</span>
          </div>
        )}
        data-test="select-chooseATemplate"
        options={mergeFields}
      />
      <Input.TextArea
        data-test="sms-text-area"
        id={wfId}
        className="sms-input"
        value={content}
        onChange={onSmsTextChange}
        style={{ height: 150 }}
      />
    </Col>
  )
}

WorkflowMergeField.propTypes = {
  wfValue: PropTypes.shape({
    label: PropTypes.string,
    properties: PropTypes.shape({
      content: PropTypes.string,
    }),
  }).isRequired,
  workflow: PropTypes.shape({}).isRequired,
  onContentChange: PropTypes.func.isRequired,
  wfId: PropTypes.string.isRequired,
}

export default WorkflowMergeField
