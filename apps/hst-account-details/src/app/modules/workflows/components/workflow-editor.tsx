import React from 'react'
import { JSONEditor } from 'vanilla-jsoneditor'

const WorkflowEditor = (props) => {
  const editorRef = React.useRef(null)

  const onRef = (ref) => {
    if (ref && !editorRef.current) {
      editorRef.current = new JSONEditor({
        target: ref,
        props,
      })
    }
  }

  React.useEffect(() => {
    if (editorRef && editorRef.current && editorRef.current.updateProps) {
      editorRef.current.updateProps(props)
    }
  }, [props])

  return (
    <>
      <div
        data-test="workflow-json-editor"
        style={{ height: 500 }}
        className="jsonEditor"
        ref={onRef}
      />
    </>
  )
}

export default WorkflowEditor
