import React from 'react'
import 'regenerator-runtime/runtime'
import WorkflowMergeField from './workflow-merge-field-editor'

jest.mock('@sinch-smb/heading', () => 'Heading')

const workflow = {
  metadata: {},
  config: {},
  triggers: [
    {
      properties: {},
      trigger_id: 'mo-sms-v1',
    },
  ],
  status: 'ACTIVE',
  name: 'A test workflow',
  expiry_in_days: null,
  labels: [],
  updateTimestamp: '2022-05-04T11:14:18.554607+00:00',
  templated_workflow_id: null,
  definition: {
    start_at: 'publish_to_integrations_sns',
    states: {
      publish_to_integrations_sns: {
        component_id: 'integrations-sms-event-publisher-v1',
        next: 'send_opt_out_confirm',
        type: 'ACTION',
        properties: {
          message_id: '$input.message_id',
          destination_number: '$input.destination_number',
          platform: 'hubspot',
          source_number: '$input.source_number',
          content: '$input.content',
        },
      },
      send_opt_out_confirm: {
        end: true,
        component_id: 'send-sms-v1',
        retry_max_attempts: 3,
        type: 'ACTION',
        properties: {
          source_number: '123456',
          destination_number: '$input.source_number',
          content: 'Hello there',
        },
      },
    },
  },
  description: null,
  id: '3792f751-18d5-4689-bf6c-0fea1d67c843',
  createTimestamp: '2022-05-04T11:14:18.554595+00:00',
}

describe('WorkflowMergeField', () => {
  const props = {
    wfValue: {
      component_id: 'send-sms-v1',
      label: 'send_opt_out_confirm',
      next: 'format_timestamp',
      properties: {
        content: 'Hello there',
        destination_number: '$input.source_number',
      },
      retry_max_attempts: 3,
      type: 'ACTION',
    },
    wfId: 'definition.states.send_opt_out_confirm',
    onContentChange: jest.fn(),
    workflow,
  }
  const setState = jest.fn()
  const useState = jest.spyOn(React, 'useState')

  it('render default', () => {
    const wrapper = shallow(<WorkflowMergeField {...props} />)
    expect(wrapper).toMatchSnapshot()
  })

  it('render workflow onSmsTextChange', () => {
    useState.mockReturnValueOnce(['Hello there', setState])
    const wrapper = shallow(<WorkflowMergeField {...props} />)
    const ele = wrapper.find('TextArea').getElement()
    ele.props.onChange({
      target: {
        value: 'Hello there',
        id: 'definition.states.send_opt_out_confirm',
      },
    })
  })

  it('render workflow merge field select', () => {
    useState.mockReturnValueOnce(['Hello', setState])
    const wrapper = shallow(<WorkflowMergeField {...props} />)
    const select = wrapper.find('Select')
    select.props().onChange('$input.email')
  })
},
)
