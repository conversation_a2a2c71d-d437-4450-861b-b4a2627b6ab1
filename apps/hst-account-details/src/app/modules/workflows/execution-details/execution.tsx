import { useEffect, useState } from 'react';

import { Table, Tag } from 'nectary';
import { useDispatch, useSelector } from "react-redux"
import { RootState } from '../../../types/store';
import { ExecutionEvent, ExecutionStatus } from "../../../types/workflows/workflows"
import { getExecutionDetails } from "../../../redux/workflows/workflows-slice"
import styles from './execution-details.module.less';


const ExecutionDetailView = ({
                               executionId,
                               workflowId,
                               accountId,
                               vendorId,
                             }: {
  executionId: string;
  workflowId: string;
  accountId: string;
  vendorId: string;
}) => {
  const dispatch = useDispatch();
  const {accountWorkflows} = useSelector((state: RootState) => state);
  const {executionData, executionLoading} = accountWorkflows || {};
  const {execution} = executionData || {};

  const [executionEvents, setExecutionEvents] = useState(() =>
    (execution?.events || []).map((event: ExecutionEvent) => ({
      ...event,
      isExpanded: false,
    }))
  );

  useEffect(() => {
    if (executionId) {
      dispatch(
        getExecutionDetails({vendorId, accountId, workflowId, executionId})
      );
    }
  }, [executionId]);

  useEffect(() => {
    if (execution?.events) {
      setExecutionEvents(
        execution.events.map((event: ExecutionEvent) => ({
          ...event,
          isExpanded: false,
        }))
      );
    }
  }, [execution]);

  const toggleVisibility = (eventId: string) => {
    setExecutionEvents((prevEvents: ExecutionEvent[]) =>
      prevEvents.map((event) =>
        event.id === eventId
          ? {...event, isExpanded: !event.isExpanded}
          : event
      )
    );
  };

  const renderExecutionStatus = (status: string) => {
    const statusColors: Record<string, string> = {
      [ExecutionStatus.FAILED]: 'light-red',
      [ExecutionStatus.RUNNING]: 'light-green',
      [ExecutionStatus.SUCCEEDED]: 'light-blue',
      [ExecutionStatus.TIMED_OUT]: 'light-orange',
    };

    const color = statusColors[status] || 'light-grey';
    return <Tag key={status} color={color} text={status}/>;
  };

  const renderEventOutput = (event: ExecutionEvent) => {
    const details =
      event.type === 'TaskStateExited'
        ? event.stateExitedEventDetails?.output
        : event.lambdaFunctionFailedEventDetails?.cause;

    try {
      return JSON.stringify(JSON.parse(details), null, 4);
    } catch {
      return details;
    }
  };

  const renderStatus = (event: ExecutionEvent) => {
    if (event.type === 'TaskStateExited') {
      return renderExecutionStatus(ExecutionStatus.SUCCEEDED);
    }
    return renderExecutionStatus(ExecutionStatus.FAILED);
  }

  const columns = [
    {
      title: 'Step Name',
      index: 'step-name',
      align: 'left',
      render: (event: ExecutionEvent) =>
        event.type === 'TaskStateExited' ? (
          <sinch-text type="s">
            {event.stateExitedEventDetails?.name}
          </sinch-text>
        ) : (
          <sinch-text type="s">
            {event.name}
          </sinch-text>
        ),
    },
    {
      title: 'Status',
      align: 'left',
      index: 'status',
      render: (event: ExecutionEvent) => (<>{renderStatus(event)}</>)
    },
    {
      title: 'Execution Details',
      index: 'output',
      align: 'left',
      render: (event: ExecutionEvent) => (
        <div>
          <button
            onClick={() => toggleVisibility(event.id)}
            style={{
              marginBottom: '10px',
              padding: '5px',
              backgroundColor: '#007bff',
              color: '#fff',
            }}
          >
            {event.isExpanded ? 'Collapse' : 'Expand'}
          </button>
          {event.isExpanded && (
            <pre
              style={{
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word',
                backgroundColor: '#f8f9fa',
                padding: '10px',
                borderRadius: '5px',
              }}
            >
              {renderEventOutput(event)}
            </pre>
          )}
        </div>
      ),
    },
  ];

  return (
    <div className={styles.dialogWrap}>
      <sinch-text type="s">Name: {execution?.name}</sinch-text>
      <sinch-text type="s">{renderExecutionStatus(execution?.status)}</sinch-text>
      <sinch-text type="s">Started date: {execution?.startDate}</sinch-text>
      <sinch-text type="s">Stopped date: {execution?.stopDate}</sinch-text>
      <sinch-text type="s">Redrive count: {execution?.redriveCount}</sinch-text>
      <Table
        hasCheckbox={false}
        tableColumns={columns}
        tableData={executionEvents}
        loading={executionLoading}
      />
    </div>
  );
};

export default ExecutionDetailView;
