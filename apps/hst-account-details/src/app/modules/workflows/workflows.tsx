import '@nectary/assets/icons/check-circle-outline';
import '@nectary/assets/icons/not-interested';
import { Button, Dialog, Tab, Table, Tag } from 'nectary';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { getPermission<PERSON>ith<PERSON><PERSON>, ROLES_KEY } from 'helpers';
import { getWorkflows } from '../../redux/workflows/workflows-slice';
import { TWorkflowsData } from '../../types/workflows/workflows';
import { RootState } from '../types/store';
import WorkflowDetailsView from './workflow-detail/view/workflow-details';
import WorkflowExecutionsDetailView from './workflow-executions-details';

import { TABS_WORKFLOWS } from '../../constants/tabs';
import styles from './workflows.module.less';

const DEFAULT_PAGESIZE = 10;

const WorkflowsTable = ({
  accountId,
  vendorId,
  roles,
}: {
  accountId: string;
  vendorId: string;
  roles: string[];
}) => {
  const dispatch = useDispatch();
  const { accountWorkflows } = useSelector((state: RootState) => state);
  const [tokenQueue, setTokenQueue] = useState<string[]>([]);
  const [workflowId, setWorkflowId] = useState<string>('');
  const [isEditModalVisible, setEditModalVisible] = useState(false);
  const [pageSize, setPageSize] = useState<number>(DEFAULT_PAGESIZE);
  const { loading, data, executionsLoading } = accountWorkflows || {};
  const { pagination, resources } = data || {};
  const { pathname } = window.location;
  const pathItems = pathname.split('/');
  const queryParams = new URLSearchParams(window.location.search);
  const TABS_INFO_ARRAY = Object.values(TABS_WORKFLOWS);
  const { next } = pagination || {};
  const [labels, setLabels] = useState<string[]>([]);
  const [updateSuccess, setUpdateStatus] = useState({
    successStatus: undefined,
    message: '',
  });

  const { isEditable, isVisible } = getPermissionWithKey(
    ROLES_KEY.ACCOUNT_WORKFLOWS,
    roles
  );

  useEffect(() => {
    dispatch(
      getWorkflows({
        accountId,
        vendorId,
        params: { next: '', size: pageSize },
      })
    );
  }, [accountId, dispatch, vendorId, pageSize]);
  const getTabByName = (tabName: string | null) =>
    TABS_INFO_ARRAY.find((t) =>
      t.text
        .toLowerCase()
        .replace(/\s+/g, '-')
        .includes(tabName?.toLowerCase() || '')
    );

  const getTabIndexByName = (tabName: string | null): string => {
    const tab = getTabByName(tabName);
    return tab ? tab.value.toString() : TABS_INFO_ARRAY[0].value.toString();
  };

  const pathTabName = pathItems[pathItems.length - 1];
  const tabURLParam = queryParams.get('tab') || pathTabName;
  const [currentTab, setCurrentTab] = useState(getTabIndexByName(tabURLParam));

  const handlePrevious = () => {
    tokenQueue.pop();
    setTokenQueue(tokenQueue);
    const tokensLen = tokenQueue.length;
    setTimeout(() => {
      dispatch(
        getWorkflows({
          vendorId,
          accountId,
          params: {
            next: tokensLen ? tokenQueue[tokensLen - 1] : undefined,
            size: pageSize,
          },
        })
      );
    });
  };

  const showEditModal = (wf: TWorkflowsData) => {
    setWorkflowId(wf.id);
    setEditModalVisible(true);
    setLabels(wf.labels);
  };
  const onChangeTab = (tab: string) => {
    const tabName = TABS_INFO_ARRAY.find((t) => t.value.toString() === tab)
      ?.text.toLowerCase()
      .replace(/\s+/g, '-');
    if (tabName) {
      setCurrentTab(tab);
    }
  };

  const handleNext = (token: string) => {
    tokenQueue.push(token);
    setTokenQueue(tokenQueue);
    setTimeout(() => {
      dispatch(
        getWorkflows({
          vendorId,
          accountId,
          params: {
            next: token,
            size: pageSize,
          },
        })
      );
    });
  };

  const handleChangePageSize = (newSize: number) => {
    setPageSize(newSize);
    dispatch(
      getWorkflows({
        accountId,
        vendorId,
        params: { next: '', size: newSize },
      })
    );
  };

  const tabContent = () => [
    {
      value: 1,
      content: (
        <div>
          {isManagedWorkflow(labels) ? (
            <span className={styles.link}>
              <WorkflowDetailsView
                workflowId={workflowId}
                accountId={accountId}
                vendorId={vendorId}
                updateSuccess={updateSuccess}
                setUpdateStatus={setUpdateStatus}
                isEditable={isEditable}
              />
            </span>
          ) : (
            `You cannot see and edit this workflow details.`
          )}
        </div>
      ),
    },
    {
      value: 2,
      content: (
        <WorkflowExecutionsDetailView
          workflowId={workflowId}
          accountId={accountId}
          vendorId={vendorId}
          loading={executionsLoading}
          isEditable={isEditable}
        />
      ),
    },
  ];

  return (
    <div>
      <sinch-text type="s">
        Here, you can find all workflows. Direct the customer to the self-serve
        pages in the Hub to add new workflows.
      </sinch-text>
      <Table
        hasCheckbox={false}
        tableColumns={tableColumns({ showEditModal, isVisible })}
        tableData={resources || []}
        loading={loading}
        next={next}
        previous={tokenQueue.length}
        handlePreviousPage={handlePrevious}
        handleNextPage={handleNext}
        pageSize={pageSize}
        handleChangePageSize={handleChangePageSize}
      />
      <div className={styles.dialogWrap}>
        <Dialog
          isOpen={isEditModalVisible}
          id="dialogWrap"
          caption="Workflows Management"
          disableCloseViaBackdrop
          onClose={() => {
            setWorkflowId('');
            setEditModalVisible(false);
            onChangeTab('1');
            setUpdateStatus({ successStatus: undefined, message: '' });
          }}
        >
          <div>
            <Tab
              currentTab={currentTab}
              tabs={TABS_INFO_ARRAY}
              tabContent={tabContent()}
              onChangeTab={onChangeTab}
            />
          </div>
        </Dialog>
      </div>
    </div>
  );
};

const isManagedWorkflow = (type: string[]) =>
  !type.includes('account-automation');

const renderType = (value: string[]) => {
  if (isManagedWorkflow(value)) {
    return (
      <Tag key="Managed workflow" color="light-blue" text="Managed workflow" />
    );
  }
  return (
    <Tag
      key="Account Automation"
      color="light-yellow"
      text="Account Automation"
    />
  );
};

const tableColumns = ({
  showEditModal,
  isVisible,
}: {
  showEditModal: (item: TWorkflowsData) => void;
  isVisible: boolean;
}) => [
  {
    title: 'Workflow Name',
    index: 'name',
    sort: false,
    align: 'left',
    render: (value: TWorkflowsData) => (
      <sinch-text type="s">{value.name}</sinch-text>
    ),
  },
  {
    title: 'Workflow ID',
    index: 'id',
    sort: false,
    align: 'left',
    render: (value: TWorkflowsData) => (
      <sinch-text type="s">{value.id}</sinch-text>
    ),
  },
  {
    title: 'Connection Status',
    key: 'status',
    index: 'status',
    dataIndex: 'status',
    width: '15%',
    render: (value: TWorkflowsData) => {
      if (value.status === 'SUSPENDED') {
        return <Tag key={value.status} color="light-red" text={value.status} />;
      }
      return <Tag key={value.status} color="light-green" text={value.status} />;
    },
  },
  {
    title: 'Type',
    key: 'type',
    render: (value: TWorkflowsData) => <>{renderType(value.labels)}</>,
  },
  {
    title: 'Action',
    index: 'action',
    sort: false,
    align: 'left',
    render: (value: TWorkflowsData) => (
      <Button
        label="View Workflow"
        onClick={() => showEditModal(value)}
        disabled={!isVisible}
      />
    ),
  },
];
export default WorkflowsTable;
