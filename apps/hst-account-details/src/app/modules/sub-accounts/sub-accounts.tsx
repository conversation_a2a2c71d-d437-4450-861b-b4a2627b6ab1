import { ROLES_KEY, getPermission<PERSON><PERSON><PERSON>ey } from 'helpers';
import moment from 'moment';
import { Button, DateRange, Input, Select, Table, Text } from 'nectary';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { RootState } from '../../types/store';

import { fetchSubAccountsDetails } from '../../redux/summary/sub-accounts-slice';
import { BILLING_TYPES, STATUSES, VERIFICATION_STATUSES } from './constants';
import styles from './sub-accounts.module.less';

interface SubAccount {
  label: string;
  accountId: string;
  vendorId: string;
  status: string;
  verificationStatus: string;
  billingType: string;
  createdAt: string;
}

const DEFAULT_PAGE_SIZE = 10;

const SubAccounts = ({
  accountId,
  vendorId,
  roles,
}: {
  accountId: string;
  vendorId: string;
  roles: Array<string>;
}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { data: subAccounts, loading } = useSelector(
    (state: RootState) => state.subAccounts
  );
  const { resources } = subAccounts || {};
  const { pagination } = subAccounts || {};
  const { next } = pagination || {};
  const [tokenQueue, setTokenQueue] = useState<string[]>([]);
  const [pageSize, setPageSize] = useState<number>(DEFAULT_PAGE_SIZE);
  const [status, setStatus] = useState('');
  const [verificationStatus, setVerificationStatus] = useState('');
  const [term, setTerm] = useState('');
  const [billingType, setBillingType] = useState('');
  const [dateRange, setDateRange] = useState<string[]>([]);
  const [isClearedAll, setIsClearedAll] = useState(false);
  const { isVisible: isSubAccountsVisible } = getPermissionWithKey(
    ROLES_KEY.ACCOUNT_SUMMARY,
    roles,
    true
  );

  useEffect(() => {
    dispatch(
      fetchSubAccountsDetails({
        vendorId,
        accountId,
        size: pageSize,
      })
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (!isSubAccountsVisible) {
    return (
      <div>
        <div className={styles.description}>
          <Text type="s">No Permissions</Text>
        </div>
      </div>
    );
  }

  const formatDate = (timestamp: string | null | undefined) => {
    if (!timestamp) {
      return '';
    }
    const date = moment(timestamp);
    return date.isValid()
      ? date.format('DD MMM YYYY, hh:mm:ss a')
      : 'Invalid date';
  };

  const formatString = (str: string | null | undefined) => {
    if (!str) {
      return '';
    }
    return str
      .split(/[_\s]/)
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  const tableColumns = [
    {
      title: 'Account name',
      index: 'label',
      sort: false,
      align: 'left',
      render: (record: SubAccount) => <Text type="s">{record.label}</Text>,
    },
    {
      title: 'Account ID',
      index: 'accountId',
      sort: false,
      align: 'left',
      render: (record: SubAccount) => <Text type="s">{record.accountId}</Text>,
    },
    {
      title: 'Vendor ID',
      index: 'vendorId',
      sort: false,
      align: 'left',
      render: (record: SubAccount) => <Text type="s">{record.vendorId}</Text>,
    },
    {
      title: 'Status',
      index: 'status',
      sort: false,
      align: 'left',
      render: (record: SubAccount) => (
        <Text type="s">{formatString(record.status)}</Text>
      ),
    },
    {
      title: 'Verification Status',
      index: 'verificationStatus',
      sort: false,
      align: 'left',
      render: (record: SubAccount) => (
        <Text type="s">{formatString(record.verificationStatus)}</Text>
      ),
    },
    {
      title: 'Billing Type',
      index: 'billingType',
      sort: false,
      align: 'left',
      render: (record: SubAccount) => (
        <Text type="s">{formatString(record.billingType)}</Text>
      ),
    },
    {
      title: 'Created At',
      index: 'createdAt',
      sort: false,
      align: 'left',
      render: (record: SubAccount) => (
        <Text type="s">{formatDate(record.createdAt)}</Text>
      ),
    },
    {
      title: 'Action',
      index: 'action',
      sort: false,
      align: 'left',
      render: (record: SubAccount) => (
        <Button
          label="View Accounts"
          onClick={() => handleViewAccounts(record.accountId, record.vendorId)}
        />
      ),
    },
  ];

  const handleViewAccounts = (
    subAccountId: string,
    subAccountVendorId: string
  ) => {
    const normalizedVendorId = subAccountVendorId.toLowerCase();

    navigate(`/accounts/${normalizedVendorId}/${subAccountId}?tab=summary`, {
      state: { from: 'subaccountsdetails' },
    });
  };

  const getStartDateStr = (momentVal?: string) => {
    if (!momentVal) return '';
    return moment(momentVal, 'DD-MM-YYYY').startOf('day').toISOString();
  };

  const getEndDateStr = (momentVal?: string) => {
    if (!momentVal) return '';
    return moment(momentVal, 'DD-MM-YYYY').endOf('day').toISOString();
  };

  const handleFilter = () => {
    const fromDate = getStartDateStr(dateRange?.[0]);
    const toDate = getEndDateStr(dateRange?.[1]);

    dispatch(
      fetchSubAccountsDetails({
        vendorId,
        accountId,
        term,
        size: pageSize,
        status,
        verificationStatus,
        billingType,
        createdAt:
          fromDate && toDate ? [`after:${fromDate}`, `before:${toDate}`] : [],
      })
    );
  };

  const handleOnKeyDown = (e: React.KeyboardEvent<HTMLFormElement>) => {
    if (e.key === 'Enter') {
      handleFilter();
    }
  };

  const handleClearFilters = () => {
    setTerm('');
    setStatus('');
    setVerificationStatus('');
    setBillingType('');
    setDateRange([]);
    setIsClearedAll(true);
  };

  const renderFilters = () => (
    // eslint-disable-next-line jsx-a11y/no-noninteractive-element-interactions
    <form onKeyDown={handleOnKeyDown} className={styles.filterWrapper}>
      <div className={styles.formItem}>
        <Input
          label="Account Name/ID"
          value={term}
          onChange={setTerm}
          testId="term"
          fieldStyles={{ width: '100%' }}
        />
      </div>
      <div className={styles.formItem}>
        <Select
          label="Status"
          placeholder="All"
          options={STATUSES}
          status={status}
          onSelect={setStatus}
          isClearedAll={isClearedAll}
          testId="status-select"
        />
      </div>
      <div className={styles.formItem}>
        <Select
          label="Verification Status"
          value={verificationStatus}
          placeholder="All"
          options={VERIFICATION_STATUSES}
          onSelect={setVerificationStatus}
          isClearedAll={isClearedAll}
          testId="verification-status-select"
        />
      </div>
      <div className={styles.formItem}>
        <Select
          label="Billing Type"
          value={billingType}
          placeholder="All"
          options={BILLING_TYPES}
          onSelect={setBillingType}
          isClearedAll={isClearedAll}
          testId="billing-type-select"
        />
      </div>
      <div className={styles.formItem}>
        <DateRange
          label="Created Date"
          value={dateRange}
          onChange={setDateRange}
          min={moment().subtract('1', 'years').format('YYYY-MM-DD')}
          max={moment().endOf('day').format('YYYY-MM-DD')}
          testId="date-range"
        />
      </div>
      <div className={styles.formItem}>
        <Button label="Search" onClick={handleFilter} />
      </div>
      <div className={styles.formItem}>
        <Button
          type="secondary"
          label="Clear all"
          onClick={handleClearFilters}
        />
      </div>
    </form>
  );

  const handlePreviousPage = () => {
    tokenQueue.pop();
    setTokenQueue([...tokenQueue]);
    const tokensLen = tokenQueue.length;

    const fromDate = getStartDateStr(dateRange?.[0]);
    const toDate = getEndDateStr(dateRange?.[1]);

    setTimeout(() => {
      dispatch(
        fetchSubAccountsDetails({
          vendorId,
          accountId,
          term,
          size: pageSize,
          status,
          verificationStatus,
          billingType,
          createdAt:
            fromDate && toDate ? [`after:${fromDate}`, `before:${toDate}`] : [],
          ...(tokensLen && { next: tokenQueue[tokensLen - 1] }),
        })
      );
    });
  };

  const handleNextPage = () => {
    tokenQueue.push(next);
    setTokenQueue([...tokenQueue]);

    const fromDate = getStartDateStr(dateRange?.[0]);
    const toDate = getEndDateStr(dateRange?.[1]);

    setTimeout(() => {
      dispatch(
        fetchSubAccountsDetails({
          vendorId,
          accountId,
          term,
          size: pageSize,
          status,
          verificationStatus,
          billingType,
          createdAt:
            fromDate && toDate ? [`after:${fromDate}`, `before:${toDate}`] : [],
          ...(next && { next }),
        })
      );
    });
  };

  const handleChangePageSize = (newSize: number) => {
    setPageSize(newSize);
    dispatch(
      fetchSubAccountsDetails({
        vendorId,
        accountId,
        size: newSize,
      })
    );
  };

  return (
    <div className={styles.subAccounts}>
      {renderFilters()}
      <div className={styles.tableWrapper}>
        <Table
          hasCheckbox={false}
          tableColumns={tableColumns}
          tableData={resources || []}
          loading={loading}
          next={!!next}
          previous={tokenQueue.length > 0}
          handlePreviousPage={handlePreviousPage}
          handleNextPage={handleNextPage}
          pageSize={pageSize}
          handleChangePageSize={handleChangePageSize}
        />
      </div>
    </div>
  );
};

export default SubAccounts;
