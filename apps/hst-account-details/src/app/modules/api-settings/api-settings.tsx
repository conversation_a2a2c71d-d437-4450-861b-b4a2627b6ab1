import { useEffect, useState } from 'react';

import { ROLES_KEY, getPermissionWith<PERSON>ey, haveAPISettings } from 'helpers';
import { Tab } from 'nectary';

import BasicAuthencation from './tabs/basic-authentication/basic-authentication';
import HmacAuthentication from './tabs/hmac-authentication/hmac-authentication';
import LegacyApi from './tabs/legacy-api/legacy-api';

import { TabItem } from '../channels/channels';
import styles from './api-settings.module.less';
import ApiAssignment from './components/api-assignment';
import Webhooks from './tabs/webhooks/webhooks';

type APISettingsProp = {
  vendorId: string;
  accountId: string;
  roles: Array<string>;
};

type TabItem = {
  value: number;
  tabName: string;
  text: string;
};

const tabContent = ({
  vendorId,
  accountId,
  isAPISettingsBasicAuthenEditable,
  isAPISettingsHMACAuthenEditable,
  isAPISettingsLegacyApiEditable,
  isApiAssignmentVisible,
  isApiAssignmentEditable,
  isWebhookEditable,
  isWebhookVisible,
}: {
  vendorId: string;
  accountId: string;
  isAPISettingsBasicAuthenEditable: boolean;
  isAPISettingsHMACAuthenEditable: boolean;
  isAPISettingsLegacyApiEditable: boolean;
  isApiAssignmentVisible: boolean;
  isApiAssignmentEditable: boolean;
  isWebhookEditable: boolean;
  isWebhookVisible: boolean;
}) => {
  const tabs = [
    {
      value: 1,
      content: (
        <BasicAuthencation
          vendorId={vendorId}
          accountId={accountId}
          isEditable={isAPISettingsBasicAuthenEditable}
        />
      ),
    },
    {
      value: 2,
      content: (
        <HmacAuthentication
          vendorId={vendorId}
          accountId={accountId}
          isEditable={isAPISettingsHMACAuthenEditable}
        />
      ),
    },
    {
      value: 3,
      content: (
        <LegacyApi
          vendorId={vendorId}
          accountId={accountId}
          isEditable={isAPISettingsLegacyApiEditable}
        />
      ),
    },
  ];

  let tabValue = 3;
  if (isWebhookVisible) {
    tabValue += 1;
    tabs.push({
      value: tabValue,
      content: (
        <Webhooks
          vendorId={vendorId}
          accountId={accountId}
          isEditable={isWebhookEditable}
        />
      ),
    });
  }

  if (isApiAssignmentVisible) {
    tabValue += 1;
    tabs.push({
      value: tabValue,
      content: (
        <ApiAssignment
          vendorId={vendorId}
          accountId={accountId}
          isEditable={isApiAssignmentEditable}
        />
      ),
    });
  }

  return tabs;
};

const TABS_INFO = ({
  isApiAssignmentVisible,
  isWebhookVisible,
}: {
  isApiAssignmentVisible: boolean;
  isWebhookVisible: boolean;
}) => {
  const tabs = [
    {
      value: 1,
      tabName: 'basicAuthentication',
      text: 'Basic Authentication',
    },
    {
      value: 2,
      tabName: 'HMACAuthentication',
      text: 'HMAC Authentication',
    },
    {
      value: 3,
      tabName: 'legacyApi',
      text: 'Legacy API',
    },
  ];
  let index = 3;

  if (isWebhookVisible) {
    index += 1;
    tabs.push({
      value: index,
      tabName: 'webhooks',
      text: 'Webhooks (HTTP notifications)',
    });
  }

  if (isApiAssignmentVisible) {
    index += 1;
    tabs.push({
      value: index,
      tabName: 'APIAssignment',
      text: 'API Assignment',
    });
  }

  return tabs;
};

const SUB_TAB_PARAM = 'subTab';

const getSubTabIndexByName = (
  subTabName: string | null,
  displayedTabs: TabItem[]
): string => {
  const currentTab = displayedTabs.find((tab) => tab.tabName === subTabName);
  return currentTab ? currentTab.value.toString() : '1';
};

export function APISettings(props: APISettingsProp) {
  const { vendorId, accountId, roles } = props;
  const {
    isVisible: isAPISettingsBasicAuthenVisible,
    isEditable: isAPISettingsBasicAuthenEditable,
  } = getPermissionWithKey(ROLES_KEY.BASIC_AUTHEN, roles);
  const {
    isVisible: isAPISettingsHMACAuthenVisible,
    isEditable: isAPISettingsHMACAuthenEditable,
  } = getPermissionWithKey(ROLES_KEY.HMAC_AUTHEN, roles);
  const {
    isVisible: isAPISettingsLegacyApiVisible,
    isEditable: isAPISettingsLegacyApiEditable,
  } = getPermissionWithKey(ROLES_KEY.LEGACY_API, roles);

  const {
    isVisible: isApiAssignmentVisible,
    isEditable: isApiAssignmentEditable,
  } = getPermissionWithKey(ROLES_KEY.BASIC_AUTHEN, roles);

  const { isVisible: isWebhookVisible, isEditable: isWebhookEditable } =
    getPermissionWithKey(ROLES_KEY.ACCOUNT_WEBHOOKS, roles);

  const displayedTabs = TABS_INFO({
    isApiAssignmentVisible,
    isWebhookVisible,
  });

  const [currentSubTab, setCurrentSubTab] = useState('1');

  const queryParams = new URLSearchParams(window.location.search);
  const tabURLParam = queryParams.get(SUB_TAB_PARAM);

  const onChangeTab = (newTabVal: string) => {
    setCurrentSubTab(newTabVal);
    const newTab = displayedTabs.find(
      (tab: TabItem) => `${tab.value}` === newTabVal
    );
    queryParams.set(SUB_TAB_PARAM, newTab?.tabName ?? '');
    const newQueryString = queryParams.toString();
    const newUrl = `${window.location.origin}${window.location.pathname}?${newQueryString}`;
    window.history.replaceState({ path: newUrl }, '', newUrl);
  };

  useEffect(() => {
    setCurrentSubTab(getSubTabIndexByName(tabURLParam, displayedTabs));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [displayedTabs?.length]);

  if (
    haveAPISettings() &&
    (isAPISettingsBasicAuthenVisible ||
      isAPISettingsHMACAuthenVisible ||
      isAPISettingsLegacyApiVisible)
  )
    return (
      <div className={styles.wrapper}>
        <div className={styles.tabContent}>
          <Tab
            currentTab={currentSubTab}
            tabs={displayedTabs}
            tabContent={tabContent({
              vendorId,
              accountId,
              isAPISettingsBasicAuthenEditable,
              isAPISettingsHMACAuthenEditable,
              isAPISettingsLegacyApiEditable,
              isApiAssignmentVisible,
              isApiAssignmentEditable,
              isWebhookEditable,
              isWebhookVisible,
            })}
            onChangeTab={onChangeTab}
          />
        </div>
      </div>
    );
  return <>No permissions</>;
}

export default APISettings;
