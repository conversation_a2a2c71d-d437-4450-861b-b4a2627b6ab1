import { Button, Dialog, Input, Select, Toast } from 'nectary';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { LOADING_STATUS } from '../../../../../../constants';
import {
  createLegacyCredentials,
  loadLegacyCredentials,
  updateLegacyCredentials,
} from '../../../../../../redux/api-settings/legacy-authentication/legacy-authentication-slice';
import { LegacyCredentialsItem } from '../../../../../../types/api-settings/legacy';
import { RootState } from '../../../../../../types/store';
import { ToastItem } from '../../../../../../types';
import styles from './legacy-api-form-modal.module.less';

type LegacyApiFormModalProps = {
  isVisible: boolean;
  setIsVisible: (value: boolean) => void;
  formValues: LegacyCredentialsItem | null;
  setFormValues: (value: LegacyCredentialsItem | null) => void;
  accountId: string;
  vendorId: string;
};

const DEFAULT_PAGE_SIZE = 10;

const LegacyApiFormModal: React.FC<LegacyApiFormModalProps> = ({
  isVisible,
  setIsVisible,
  formValues,
  setFormValues,
  accountId,
  vendorId,
}) => {
  const dispatch = useDispatch();
  const rootState = useSelector((state: RootState) => state || {});
  const { legacyAuthentication } = rootState;
  const { createLoading, updateLoading } = legacyAuthentication || {};

  const [username, setUsername] = useState(formValues?.username || '');
  const [password, setPassword] = useState(formValues?.password || '');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [toasts, setToasts] = useState<ToastItem[]>([]);
  const [resetPasswordVisibility, setResetPasswordVisibility] = useState(false);
  const [errors, setErrors] = useState<{
    username?: string;
    password?: string;
    confirmPassword?: string;
  }>({});

  useEffect(() => {
    if (formValues) {
      setUsername(formValues.username || '');
      setPassword('');
      setConfirmPassword('');
      setErrors({});
    }
  }, [formValues]);

  useEffect(() => {
    if (updateLoading === LOADING_STATUS.SUCCEEDED || updateLoading === LOADING_STATUS.FAILED) {
      const text = `${updateLoading === LOADING_STATUS.SUCCEEDED ? 'Successfully' : 'Failed to'} updated Legacy API Key ${username}`;
      setToasts(prev => [...prev, {
        id: formValues?.id || username,
        text,
        type: updateLoading === LOADING_STATUS.SUCCEEDED ? 'success' : 'error',
      }]);

      if (updateLoading === LOADING_STATUS.SUCCEEDED) {
        handleClose();

        setTimeout(() => {
          dispatch(
            loadLegacyCredentials({
              vendorId,
              accountId,
              size: DEFAULT_PAGE_SIZE,
            })
          );
        }, 500);
      }
    }
  }, [updateLoading]);

  useEffect(() => {
    if (createLoading === LOADING_STATUS.SUCCEEDED || createLoading === LOADING_STATUS.FAILED) {
      const text = `${createLoading === LOADING_STATUS.SUCCEEDED ? 'Successfully' : 'Failed to'} created Legacy API Key ${username}`;
      setToasts(prev => [...prev, {
        id: username,
        text,
        type: createLoading === LOADING_STATUS.SUCCEEDED ? 'success' : 'error',
      }]);

      if (createLoading === LOADING_STATUS.SUCCEEDED) {
        handleClose();

        setTimeout(() => {
          dispatch(
            loadLegacyCredentials({
              vendorId,
              accountId,
              size: DEFAULT_PAGE_SIZE,
            })
          );
        }, 500);
      }
    }
  }, [createLoading]);

  const validateForm = () => {
    const newErrors: {
      username?: string;
      password?: string;
      confirmPassword?: string;
    } = {};

    if (!username) {
      newErrors.username = 'Username is required';
    } else if (username.length < 5) {
      newErrors.username = 'Username must be at least 5 characters';
    }

    if (!password) {
      newErrors.password = 'Password is required';
    } else if (password.length < 6 || password.length > 50) {
      newErrors.password = 'Password must be between 6 and 50 characters';
    }

    if (!confirmPassword && formValues?.id) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (password !== confirmPassword && formValues?.id) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    const payload = {
      vendorId,
      accountId,
      username,
      password,
    };

    if (formValues?.id) {
      dispatch(
        updateLegacyCredentials({
          ...payload,
          credentialId: formValues.id,
        })
      );
    } else {
      dispatch(createLegacyCredentials(payload));
    }

    handleClose();
  };

  const handleClose = () => {
    setIsVisible(false);
    setFormValues(null);
    setUsername('');
    setPassword('');
    setConfirmPassword('');
    setErrors({});
    setResetPasswordVisibility(true);
    // Reset the flag after a short delay to allow for future resets
    setTimeout(() => setResetPasswordVisibility(false), 100);
  };

  return (
    <>
      <Dialog
        isOpen={isVisible}
        onClose={handleClose}
        caption={formValues?.id ? 'Edit Legacy API Key' : 'Add Legacy API Key'}
      >
        <form onSubmit={handleSubmit}>
          <div className={styles.formItem}>
            <label htmlFor="username">Username</label>
            <Input
              id="username"
              value={username}
              placeholder="Enter username"
              required
              disabled={!!formValues?.id}
              fieldStyles={{ width: '100%' }}
              onChange={(value: string) => setUsername(value)}
              errorText={errors.username}
            />
          </div>
          <div className={styles.formItem}>
            <label htmlFor="password">Password</label>
            <Input
              id="password"
              type="password"
              value={password}
              placeholder="Enter password"
              required
              fieldStyles={{ width: '100%' }}
              onChange={(value: string) => setPassword(value)}
              errorText={errors.password}
              resetPasswordVisibility={resetPasswordVisibility}
            />
          </div>
          {formValues?.id && (
            <div className={styles.formItem}>
              <label htmlFor="confirmPassword">Confirm Password</label>
              <Input
                id="confirmPassword"
                type="password"
                value={confirmPassword}
                placeholder="Re-enter password"
                required
                fieldStyles={{ width: '100%' }}
                onChange={(value: string) => setConfirmPassword(value)}
                errorText={errors.confirmPassword}
                resetPasswordVisibility={resetPasswordVisibility}
              />
            </div>
          )}
          {!formValues?.id && (
            <div className={styles.formItem}>
              <label htmlFor="hashType">Hash Type</label>
              <Select
                id="hashType"
                value="bcrypt"
                options={[{ label: 'bcrypt', value: 'bcrypt' }]}
                disabled
                customStyles={{ width: '100%' }}
              />
            </div>
          )}
          <div className={styles.actions}>
            <Button
              type="primary"
              htmlType="submit"
              label={formValues?.id ? 'Update' : 'Create'}
              onClick={handleSubmit}
            />
            <Button
              type="default"
              htmlType="button"
              label="Cancel"
              onClick={handleClose}
            />
          </div>
        </form>
      </Dialog>
      <Toast toasts={toasts} setToasts={setToasts} />
    </>
  );
};

export default LegacyApiFormModal;
