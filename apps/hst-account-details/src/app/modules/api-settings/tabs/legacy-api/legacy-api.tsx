import '@nectary/assets/icons/add';
import _isEmpty from 'lodash/isEmpty';
import {
  Button,
  Link as NectaryLink,
  SearchInput,
  Table,
  Text,
  Toast,
} from 'nectary';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { LOADING_STATUS } from '../../../../constants';
import {
  deleteLegacyCredentials,
  loadLegacyCredentials,
} from '../../../../redux/api-settings/legacy-authentication/legacy-authentication-slice';
import { ToastItem } from '../../../../types';
import { LegacyCredentialsItem } from '../../../../types/api-settings/legacy';
import { RootState } from '../../../../types/store';
import DeleteModal from '../components/delete-modal';
import LegacyApiFormModal from './components/form-modal/legacy-api-form-modal';
import styles from './legacy-api.module.less';

type LegacyApiProps = {
  vendorId: string;
  accountId: string;
  isEditable: boolean;
};

const DEFAULT_PAGE_SIZE = 10;

const tableColumns = ({
  handleEdit,
  handleDelete,
  handleCopy,
  isEditable,
}: {
  handleEdit: (item: LegacyCredentialsItem) => void;
  handleDelete: (item: LegacyCredentialsItem) => void;
  handleCopy: (value: string) => void;
  isEditable: boolean;
}) => [
  {
    title: 'Legacy Key',
    index: 'username',
    key: 'username',
    sort: false,
    align: 'left',
    customStyles: {
      width: '50vw',
    },
    render: (value: LegacyCredentialsItem) => (
      <NectaryLink
        href="#"
        preventDefault
        onClick={() => handleCopy(value.username)}
        text={value.username}
      />
    ),
  },
  {
    title: 'Actions',
    index: 'actions',
    sort: false,
    key: 'actions',
    align: 'left',
    render: (value: LegacyCredentialsItem) => (
      <div className={styles.btnGroup}>
        <div className={styles.editBtn}>
          <Button
            label="Edit"
            type="secondary"
            onClick={() => handleEdit(value)}
            disabled={!isEditable}
          />
        </div>
        <Button
          label="Delete"
          type="destructive"
          onClick={() => handleDelete(value)}
          disabled={!isEditable}
        />
      </div>
    ),
  },
];

const LegacyApi: React.FC<LegacyApiProps> = ({
  vendorId,
  accountId,
  isEditable,
}) => {
  const dispatch = useDispatch();
  const rootState = useSelector((state: RootState) => state || {});
  const { legacyAuthentication } = rootState;
  const {
    data,
    loading,
    deleteLoading,
  } = legacyAuthentication || {};

  const [searchValue, setSearchValue] = useState('');
  const [tokenQueue, setTokenQueue] = useState<string[]>([]);
  const [toasts, setToasts] = useState<ToastItem[]>([]);
  const [pageSize, setPageSize] = useState<number>(DEFAULT_PAGE_SIZE);

  // Modal states
  const [isLegacyApiModalOpen, setLegacyApiModalOpen] = useState(false);
  const [isDeleteModalOpen, setDeleteModalOpen] = useState(false);
  const [formValues, setFormValues] = useState<LegacyCredentialsItem | null>(
    null
  );
  const [deleteItem, setDeleteItem] = useState<LegacyCredentialsItem | null>(
    null
  );

  useEffect(() => {
    dispatch(
      loadLegacyCredentials({
        vendorId,
        accountId,
        size: pageSize,
        next: '',
      })
    );
  }, []);

  useEffect(() => {
    if (_isEmpty(deleteItem)) return;

    if (deleteLoading === LOADING_STATUS.SUCCEEDED || deleteLoading === LOADING_STATUS.FAILED) {
      const text = `${deleteLoading === LOADING_STATUS.SUCCEEDED ? 'Successfully' : 'Failed to'} delete Legacy API Key ${deleteItem.username}`;

      setToasts(prev => [...prev, {
        text,
        type: deleteLoading === LOADING_STATUS.SUCCEEDED ? 'success' : 'error',
      }]);

      if (deleteLoading === LOADING_STATUS.SUCCEEDED) {
        setDeleteItem(null);
        setDeleteModalOpen(false);

        setTimeout(() => {
          dispatch(
            loadLegacyCredentials({
              vendorId,
              accountId,
              size: pageSize,
            })
          );
        }, 500);
      }
    }
  }, [deleteLoading]);

  const handleInputSearch = (value: string) => {
    setSearchValue(value);
  };

  useEffect(() => {
    const debounce = setTimeout(() => {
      dispatch(
        loadLegacyCredentials({
          vendorId,
          accountId,
          size: pageSize,
          username: searchValue,
        })
      );
    }, 500);
    return () => clearTimeout(debounce);
  }, [searchValue]);

  const handleAdd = () => {
    setFormValues({
      id: '',
      username: '',
      password: '',
    });
    setLegacyApiModalOpen(true);
  };

  const handleEdit = (item: LegacyCredentialsItem) => {
    setFormValues(item);
    setLegacyApiModalOpen(true);
  };

  const handleDelete = (item: LegacyCredentialsItem) => {
    setDeleteModalOpen(true);
    setDeleteItem(item);
  };

  const handleCopy = (value?: string) => {
    if (!value) return;

    setToasts(prev => [...prev, {
      id: value,
      text: `Successfully copied ${value} to clipboard`,
      type: 'success',
    }]);

    navigator.clipboard.writeText(value);
  };

  const handlePrevious = () => {
    tokenQueue.pop();
    setTokenQueue(tokenQueue);
    const tokensLen = tokenQueue.length;

    dispatch(
      loadLegacyCredentials({
        vendorId,
        accountId,
        size: pageSize,
        next: tokensLen ? tokenQueue[tokensLen - 1] : '',
      })
    );
  };

  const handleNext = (token: string) => {
    tokenQueue.push(token);
    setTokenQueue(tokenQueue);

    dispatch(
      loadLegacyCredentials({
        vendorId,
        accountId,
        size: pageSize,
        next: token,
      })
    );
  };

  const handleChangePageSize = (newSize: number) => {
    setPageSize(newSize);
    dispatch(
      loadLegacyCredentials({
        vendorId,
        accountId,
        size: newSize,
      })
    );
  };

  const { resources, pagination } = data || {};

  return (
    <div className={styles.wrapper}>
      <div className={styles.card}>
        <div className={styles.title}>
          <Text type="l" inline emphasized>
            Legacy API Key Details
          </Text>
        </div>
        <div className={styles.actionWrapper}>
          <div>
            <SearchInput
              defaultValue={searchValue}
              onChange={handleInputSearch}
              width={{ minWidth: 300 }}
            />
          </div>
          <div>
            <Button
              label="Add Legacy API Key"
              type="secondary"
              onClick={handleAdd}
              disabled={!isEditable}
            >
              <sinch-icon-add slot="icon" />
            </Button>
          </div>
        </div>
        <Table
          hasCheckbox={false}
          tableColumns={tableColumns({
            handleEdit,
            handleDelete,
            handleCopy,
            isEditable,
          })}
          tableData={resources || []}
          loading={loading === LOADING_STATUS.LOADING}
          next={pagination?.next}
          previous={tokenQueue.length}
          handlePreviousPage={handlePrevious}
          handleNextPage={handleNext}
          pageSize={pageSize}
          handleChangePageSize={handleChangePageSize}
        />

        <LegacyApiFormModal
          isVisible={isLegacyApiModalOpen}
          setIsVisible={setLegacyApiModalOpen}
          formValues={formValues}
          setFormValues={setFormValues}
          accountId={accountId}
          vendorId={vendorId}
        />

        <DeleteModal
          isVisible={isDeleteModalOpen}
          setIsVisible={setDeleteModalOpen}
          id={deleteItem?.id}
          accountId={accountId}
          vendorId={vendorId}
          type="Legacy API Key"
          label={deleteItem?.username}
          authenType={undefined}
          deleteAction={(params) =>
            deleteLegacyCredentials({
              vendorId,
              accountId,
              credentialId: deleteItem?.id || '',
            })
          }
        />
        <Toast toasts={toasts} setToasts={setToasts} />
      </div>
    </div>
  );
};

export default LegacyApi;
