import { Dialog, Text, Button } from 'nectary';
import { BasicAuthenticationItem } from 'apps/hst-account-details/src/app/types/api-settings';
import { HmacAuthenticationItem } from 'apps/hst-account-details/src/app/types/api-settings/hmac';

import styles from './created-modal.module.less';

const CreatedModal = ({
  isVisible,
  setIsVisible,
  value,
  handleCopy
}: {
  isVisible: boolean,
  setIsVisible: (isVisible: boolean) => void,
  value: BasicAuthenticationItem | HmacAuthenticationItem | null,
  handleCopy: (value: string | undefined) => void
}) => {

  const handleClose = () => {
    setIsVisible(false)
  }

  return (
    <Dialog
      isOpen={isVisible}
      caption="New API Credentials Created"
      onClose={handleClose}
    >
      <form>
        <div className={styles.formItem}>
          <Text>
            Please record these details in a safe place.
            Once this popup is closed you will no longer be able to retrieve your API secret.
          </Text>
          <div className={styles.card}>
            <Text>
              [{value?.label}]
            </Text>
            <Text>
              api_key = {value?.key}
            </Text>
            <Text>
              api_secret =  {value?.secret}
            </Text>
          </div>
          <div>
            <Button type="secondary" label="Copy to clipboard" onClick={() => handleCopy(value?.secret)} />
          </div>
        </div>
        <div className={styles.btnGroup}>
          <Button label="Ok" onClick={handleClose} />
        </div>
      </form>
    </Dialog>
  );
};

export default CreatedModal;
