import { Text, Button, Table, Toast, Link as NectaryLink, SearchInput } from 'nectary';
import { RootState } from 'apps/hst-account-details/src/app/types/store';
import { useDispatch, useSelector } from 'react-redux';
import _isEmpty from 'lodash/isEmpty';
import '@nectary/components/chip'
import '@nectary/components/skeleton';
import '@nectary/components/skeleton-item';
import '@nectary/assets/icons/add';
import {
  isNonEmptyString,
  customDateTimeFormatReadable,
} from 'helpers';
import { useEffect, useState } from 'react';
import { ToastItem } from 'apps/hst-account-details/src/app/types';
import { BasicAuthenticationItem } from 'apps/hst-account-details/src/app/types/api-settings';
import { deleteBasicAuthentication, fetchBasicAuthentication } from 'apps/hst-account-details/src/app/redux/api-settings/basic-authentication/basic-authentication-slice';
import { LOADING_STATUS } from 'apps/hst-account-details/src/app/constants';
import BasicAuthenticationFormModal from './components/form-modal/basic-authentication-form-modal';
import DeleteModal from '../components/delete-modal';

import styles from "./basic-authentication.module.less"
import CreatedModal from './components/created-modal/created-form-modal';

const DEFAULT_PAGE_SIZE = 10;

type BasicAuthenticationProps = {
  vendorId: string,
  accountId: string,
  isEditable: boolean
}

const tableColumns = ({
  handleEdit,
  handleDelete,
  handleCopy,
  isEditable,
}: {
  handleEdit: (item: BasicAuthenticationItem) => void,
  handleDelete: (item: BasicAuthenticationItem) => void,
  handleCopy: (value: string) => void,
  isEditable: boolean
}) => [
    {
      title: 'API Key	',
      index: 'apiKey',
      sort: false,
      align: 'left',
      render: (value: BasicAuthenticationItem) => (
        <NectaryLink href="#" preventDefault onClick={() => handleCopy(value.key)} text={value.label} />
      ),
    },
    {
      title: 'Date Created',
      index: 'createdAt',
      sort: false,
      align: 'left',
      render: (value: BasicAuthenticationItem) => (
        <Text type="s">
          {isNonEmptyString(value.createdAt)
            ? customDateTimeFormatReadable({
              datetime: value.createdAt,
              showTime: true,
            })
            : '-'}
        </Text>
      ),
    },
    {
      title: 'Actions',
      index: 'actions',
      sort: false,
      align: 'left',
      render: (value: BasicAuthenticationItem) => (
        <div className={styles.btnGroup}>
          <div className={styles.editBtn}>
            <Button
              label="Edit"
              type="secondary"
              onClick={() => handleEdit(value)}
              disabled={!isEditable}
            />
          </div>
          <Button
            label="Delete"
            type="destructive"
            onClick={() => handleDelete(value)}
            disabled={!isEditable}
          />
        </div>
      )
    },
  ]

function BasicAuthencation({ vendorId, accountId, isEditable }: BasicAuthenticationProps) {
  const rootState = useSelector((state: RootState) => state || {});
  const [isbasicAuthenticationModalOpen, setBasicAuthenticationModalOpen] = useState<boolean>(false)
  const [isDeleteModalOpen, setDeleteModalOpen] = useState<boolean>(false)
  const [deleteItem, setDeleteItem] = useState<BasicAuthenticationItem | null>(null)
  const [formValues, setFormValues] = useState<BasicAuthenticationItem | null>(null)
  const [tokenQueue, setTokenQueue] = useState<string[]>([]);
  const [toasts, setToasts] = useState<ToastItem[]>([]);
  const [pageSize, setPageSize] = useState<number>(DEFAULT_PAGE_SIZE);
  const { basicAuthentication } = rootState;
  const { data, createdData, loading, updateLoading, createLoading, deleteLoading } = basicAuthentication || {};
  const [searchValue, setSearchValue] = useState('');
  const [isCreatedModalOpen, setIsCreatedModalOpen] = useState<boolean>(false)
  const [createdItem, setIsCreatedItem] = useState<BasicAuthenticationItem | null>(null)

  const dispatch = useDispatch()

  useEffect(() => {
    if (_isEmpty(formValues)) return;
    let newToasts = [];
    if (
      updateLoading === LOADING_STATUS.SUCCEEDED ||
      updateLoading === LOADING_STATUS.FAILED
    ) {
      const text =
        updateLoading === LOADING_STATUS.SUCCEEDED
          ? `Successfully updated Basic Authentication ${formValues.label}`
          : `Failed to update Basic Authentication ${formValues.label}`;
      newToasts = toasts.concat({
        id: formValues.id,
        text,
        type: updateLoading === LOADING_STATUS.SUCCEEDED ? 'success' : 'error',
      });
      setToasts(newToasts);
      setFormValues(null);
      setBasicAuthenticationModalOpen(false)
      setTimeout(() => {
        dispatch(
          fetchBasicAuthentication({
            vendorId,
            accountId,
            size: pageSize,
            type: 'basic',
            label: searchValue
          })
        );
      }, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [updateLoading])

  useEffect(() => {
    if (!formValues) return;
    let newToasts = [];
    if (
      createLoading === LOADING_STATUS.SUCCEEDED ||
      createLoading === LOADING_STATUS.FAILED
    ) {
      const text =
        createLoading === LOADING_STATUS.SUCCEEDED
          ? `Successfully created Basic Authentication ${formValues.label}`
          : `Failed to create Basic Authentication ${formValues.label}`;
      newToasts = toasts.concat({
        id: formValues.label,
        text,
        type: createLoading === LOADING_STATUS.SUCCEEDED ? 'success' : 'error',
      });
      setToasts(newToasts);
      setFormValues(null);
      setBasicAuthenticationModalOpen(false)
      setTimeout(() => {
        dispatch(
          fetchBasicAuthentication({
            vendorId,
            accountId,
            size: pageSize,
            type: 'basic',
            label: searchValue
          })
        );
      }, 500);
      setIsCreatedItem(createdData)
      setIsCreatedModalOpen(true)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [createLoading])

  useEffect(() => {
    if (_isEmpty(deleteItem)) return;
    let newToasts = [];
    if (
      deleteLoading === LOADING_STATUS.SUCCEEDED ||
      deleteLoading === LOADING_STATUS.FAILED
    ) {
      const text =
        deleteLoading === LOADING_STATUS.SUCCEEDED
          ? `Successfully deleted Basic Authentication ${deleteItem.label}`
          : `Failed to delete Basic Authentication ${deleteItem.label}`;
      newToasts = toasts.concat({
        id: deleteItem.label,
        text,
        type: deleteLoading === LOADING_STATUS.SUCCEEDED ? 'success' : 'error',
      });
      setToasts(newToasts);
      setFormValues(null);
      setDeleteModalOpen(false)
      setTimeout(() => {
        dispatch(
          fetchBasicAuthentication({
            vendorId,
            accountId,
            size: pageSize,
            type: 'basic',
            label: searchValue
          })
        );
      }, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [deleteLoading])

  const handleEdit = (item: BasicAuthenticationItem) => {
    setBasicAuthenticationModalOpen(true)
    setFormValues(item)
  }

  const handleAdd = () => {
    setBasicAuthenticationModalOpen(true)
    setFormValues({
      id: '',
      key: '',
      label: '',
      createdAt: '',
      lastModifiedAt: '',
      role: '',
      type: 'basic',
      secret: '',
    })
  }

  const handleDelete = (item: BasicAuthenticationItem) => {
    setDeleteModalOpen(true)
    setDeleteItem(item)
  }

  const handlePrevious = () => {
    tokenQueue.pop();
    setTokenQueue(tokenQueue);
    const tokensLen = tokenQueue.length;

    setTimeout(() => {
      dispatch(
        fetchBasicAuthentication({
          vendorId,
          accountId,
          size: pageSize,
          next: tokensLen ? tokenQueue[tokensLen - 1] : '',
          type: 'basic',
          label: searchValue
        })
      );
    });
  };

  const handleNext = (token: string) => {
    tokenQueue.push(token);
    setTokenQueue(tokenQueue);

    setTimeout(() => {
      dispatch(
        fetchBasicAuthentication({
          vendorId,
          accountId,
          size: pageSize,
          next: token,
          type: 'basic',
          label: searchValue
        })
      );
    });
  };

  const handleCopy = (value?: string) => {
    if (!value) return
    let newToasts = [];
    navigator.clipboard.writeText(value)
    newToasts = toasts.concat({
      id: value,
      text: `Successfully copied ${value} to clipboard`,
      type: 'success',
    });
    setToasts(newToasts);
  }

  const handleInputSearch = (value: string) => {
    setSearchValue(value);
  };

  useEffect(() => {
    const debounce = setTimeout(() => {
      dispatch(
        fetchBasicAuthentication({
          vendorId,
          accountId,
          size: pageSize,
          type: 'basic',
          label: searchValue
        })
      );
    }, 500)
    return () => clearTimeout(debounce)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchValue]);

  const { resources, pagination } = data || {}

  const handleChangePageSize = (newSize: number) => {
    setPageSize(newSize);
    setTimeout(() => {
      dispatch(
        fetchBasicAuthentication({
          vendorId,
          accountId,
          size: newSize,
          type: 'basic',
          label: searchValue
        })
      );
    });
  };

  return (
    <div className={styles.wrapper}>
      <div className={styles.card}>
        <div className={styles.title}>
          <Text type="l" inline emphasized>
            Basic Authentication Details
          </Text>
        </div>
        <div className={styles.actionWrapper}>
          <div >
            <SearchInput
              defaultValue={searchValue}
              onChange={handleInputSearch}
              width={{ minWidth: 300 }}
            />
          </div>
          <div >
            <Button
              label="Add Basic Authentication"
              type="secondary"
              onClick={handleAdd}
              disabled={!isEditable}
            >
              <sinch-icon-add slot="icon" />
            </Button>
          </div>
        </div>
        <Table
          hasCheckbox={false}
          tableColumns={tableColumns({
            handleEdit,
            handleDelete,
            handleCopy,
            isEditable
          })}
          tableData={resources || []}
          loading={loading}
          next={pagination?.next}
          previous={tokenQueue.length}
          handlePreviousPage={handlePrevious}
          handleNextPage={handleNext}
          pageSize={pageSize}
          handleChangePageSize={handleChangePageSize}
        />
        <BasicAuthenticationFormModal
          isVisible={isbasicAuthenticationModalOpen}
          setIsVisible={setBasicAuthenticationModalOpen}
          formValues={formValues}
          setFormValues={setFormValues}
          accountId={accountId}
          vendorId={vendorId}
        />
        <DeleteModal
          isVisible={isDeleteModalOpen}
          setIsVisible={setDeleteModalOpen}
          id={deleteItem?.id}
          accountId={accountId}
          vendorId={vendorId}
          type="Basic Authentication"
          label={deleteItem?.label}
          authenType={deleteItem?.type}
          deleteAction={deleteBasicAuthentication}
        />
        <CreatedModal
          isVisible={isCreatedModalOpen}
          value={createdItem}
          handleCopy={handleCopy}
          setIsVisible={setIsCreatedModalOpen}
        />
        <Toast toasts={toasts} setToasts={setToasts} />
      </div>
    </div>
  )
}

export default BasicAuthencation
