import { useDispatch } from 'react-redux';

import { Dialog, Input, Button } from 'nectary';
import { BasicAuthenticationItem } from 'apps/hst-account-details/src/app/types/api-settings';
import { createBasicAuthentication, updateBasicAuthentication } from 'apps/hst-account-details/src/app/redux/api-settings/basic-authentication/basic-authentication-slice';

import styles from './basic-authentication-form-modal.module.less';

const BasicAuthenticationFormModal = ({
  accountId,
  vendorId,
  isVisible,
  setIsVisible,
  formValues,
  setFormValues
}: {
  accountId: string,
  vendorId: string,
  isVisible: boolean,
  setIsVisible: (isVisible: boolean) => void,
  formValues: BasicAuthenticationItem | null,
  setFormValues: (formValues: BasicAuthenticationItem | null) => void,
}) => {
  const dispatch = useDispatch()
  const { id, label, type } = formValues || {
    id: '',
    label: '',
    type: 'basic'
  };

  const onChangeLabel = (val: string) => {
    if (!formValues) return
    setFormValues({
      ...formValues,
      label: val,
    })
  };

  const handleClose = () => {
    setIsVisible(false)
  }

  const handleSubmit = () => {
    if (!formValues) {
      return null
    }

    if (id) {
      return dispatch(updateBasicAuthentication({
        accountId,
        vendorId,
        body: formValues,
        type
      }))
    }
    return dispatch(createBasicAuthentication({
      accountId,
      vendorId,
      body: formValues,
      type
    }))
  }

  return (
    <Dialog
      isOpen={isVisible}
      caption="Basic Authentication"
      onClose={handleClose}
    >
      <form>
        <div className={styles.formItem}>
          <Input
            label="Label"
            defaultValue={label}
            value={label}
            onChange={onChangeLabel}
            testId="label"
            fieldStyles={{ width: '100%' }}
          />
        </div>
        <div className={styles.btnGroup}>
          <Button label="Submit" onClick={handleSubmit} />
          <Button label="Cancel" onClick={handleClose} />
        </div>
      </form>
    </Dialog>
  );
};

export default BasicAuthenticationFormModal;
