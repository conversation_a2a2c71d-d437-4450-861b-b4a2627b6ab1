import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import _isEmpty from 'lodash/isEmpty';
import '@nectary/components/chip';
import '@nectary/components/skeleton';
import '@nectary/components/skeleton-item';
import '@nectary/assets/icons/add';
import '@nectary/components/link';

import {
  Text,
  Button,
  Table,
  Toast,
  SearchInput,
  Link as NectaryLink,
} from 'nectary';
import { RootState } from 'apps/hst-account-details/src/app/types/store';
import { isNonEmptyString, customDateTimeFormatReadable } from 'helpers';

import { ToastItem } from 'apps/hst-account-details/src/app/types';
import { HmacAuthenticationItem } from 'apps/hst-account-details/src/app/types/api-settings/hmac';
import {
  deleteHmacAuthentication,
  fetchHmacAuthentication,
} from 'apps/hst-account-details/src/app/redux/api-settings/hmac-authentication/hmac-authentication-slice';
import { LOADING_STATUS } from 'apps/hst-account-details/src/app/constants';

import HmacAuthenticationFormModal from './components/hmac-authentication-form-modal';
import DeleteModal from '../components/delete-modal';

import styles from './hmac-authentication.module.less';
import CreatedModal from '../basic-authentication/components/created-modal/created-form-modal';

const DEFAULT_PAGE_SIZE = 10;

type HmacAuthenticationProps = {
  vendorId: string;
  accountId: string;
  isEditable: boolean;
};

const tableColumns = ({
  handleEdit,
  handleDelete,
  isEditable,
  handleCopy,
}: {
  handleEdit: (item: HmacAuthenticationItem) => void;
  handleDelete: (item: HmacAuthenticationItem) => void;
  isEditable: boolean;
  handleCopy: (value: string) => void;
}) => [
  {
    title: 'HMAC Key',
    index: 'key',
    sort: false,
    align: 'left',
    render: (value: HmacAuthenticationItem) => (
      <NectaryLink
        href="#"
        preventDefault
        onClick={() => handleCopy(value.key)}
        text={value.label}
      />
    ),
  },
  {
    title: 'Date Created',
    index: 'createdAt',
    sort: false,
    align: 'left',
    render: (value: HmacAuthenticationItem) => {
      const formatedDate = isNonEmptyString(value.createdAt)
        ? customDateTimeFormatReadable({
            datetime: value.createdAt,
            showTime: false,
          })
        : '-';
      return <sinch-text type="s">{formatedDate}</sinch-text>;
    },
  },
  {
    title: 'Action',
    index: 'action',
    sort: false,
    align: 'left',
    render: (value: HmacAuthenticationItem) => (
      <div className={styles.btnGroup}>
        <div className={styles.editBtn}>
          <Button
            label="Edit"
            type="secondary"
            onClick={() => handleEdit(value)}
            disabled={!isEditable}
          />
        </div>
        <Button
          label="Delete"
          type="destructive"
          onClick={() => handleDelete(value)}
          disabled={!isEditable}
        />
      </div>
    ),
  },
];

function HmacAuthencation({
  vendorId,
  accountId,
  isEditable,
}: HmacAuthenticationProps) {
  const rootState = useSelector((state: RootState) => state || {});
  const [ishmacAuthenticationModalOpen, setHmacAuthenticationModalOpen] =
    useState<boolean>(false);
  const [isDeleteModalOpen, setDeleteModalOpen] = useState<boolean>(false);
  const [deleteItem, setDeleteItem] = useState<HmacAuthenticationItem | null>(
    null
  );
  const [formValues, setFormValues] = useState<HmacAuthenticationItem | null>(
    null
  );
  const [tokenQueue, setTokenQueue] = useState<string[]>([]);
  const [toasts, setToasts] = useState<ToastItem[]>([]);
  const [isCreatedModalOpen, setIsCreatedModalOpen] = useState<boolean>(false);
  const [searchValue, setSearchValue] = useState('');
  const [createdItem, setIsCreatedItem] =
    useState<HmacAuthenticationItem | null>(null);
  const [pageSize, setPageSize] = useState<number>(DEFAULT_PAGE_SIZE);

  const { hmacAuthentication } = rootState;
  const {
    data,
    loading,
    updateLoading,
    createLoading,
    deleteLoading,
    createdData,
  } = hmacAuthentication || {};
  const dispatch = useDispatch();

  useEffect(() => {
    if (_isEmpty(formValues)) return;
    let newToasts = [];
    if (
      updateLoading === LOADING_STATUS.SUCCEEDED ||
      updateLoading === LOADING_STATUS.FAILED
    ) {
      const text =
        updateLoading === LOADING_STATUS.SUCCEEDED
          ? `Successfully updated HMAC Authentication ${formValues.label}`
          : `Failed to update HMAC Authentication ${formValues.label}`;
      newToasts = toasts.concat({
        id: formValues.id,
        text,
        type: updateLoading === LOADING_STATUS.SUCCEEDED ? 'success' : 'error',
      });
      setToasts(newToasts);
      setFormValues(null);
      setHmacAuthenticationModalOpen(false);
      setTimeout(() => {
        dispatch(
          fetchHmacAuthentication({
            vendorId,
            accountId,
            size: pageSize,
            type: 'hmac',
            label: searchValue,
          })
        );
      }, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [updateLoading]);

  useEffect(() => {
    if (!formValues) return;
    let newToasts = [];
    if (
      createLoading === LOADING_STATUS.SUCCEEDED ||
      createLoading === LOADING_STATUS.FAILED
    ) {
      const text =
        createLoading === LOADING_STATUS.SUCCEEDED
          ? `Successfully created HMAC Authentication ${formValues.label}`
          : `Failed to create HMAC Authentication ${formValues.label}`;
      newToasts = toasts.concat({
        id: formValues.label,
        text,
        type: createLoading === LOADING_STATUS.SUCCEEDED ? 'success' : 'error',
      });
      setToasts(newToasts);
      setFormValues(null);
      setHmacAuthenticationModalOpen(false);
      setTimeout(() => {
        dispatch(
          fetchHmacAuthentication({
            vendorId,
            accountId,
            size: pageSize,
            type: 'hmac',
            label: searchValue,
          })
        );
        setIsCreatedItem(createdData);
        setIsCreatedModalOpen(true);
      }, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [createLoading]);

  useEffect(() => {
    if (_isEmpty(deleteItem)) return;
    let newToasts = [];
    if (
      deleteLoading === LOADING_STATUS.SUCCEEDED ||
      deleteLoading === LOADING_STATUS.FAILED
    ) {
      const text =
        deleteLoading === LOADING_STATUS.SUCCEEDED
          ? `Successfully deleted HMAC Authentication ${deleteItem.label}`
          : `Failed to delete HMAC Authentication ${deleteItem.label}`;
      newToasts = toasts.concat({
        id: deleteItem.label,
        text,
        type: deleteLoading === LOADING_STATUS.SUCCEEDED ? 'success' : 'error',
      });
      setToasts(newToasts);
      setFormValues(null);
      setDeleteModalOpen(false);
      setTimeout(() => {
        dispatch(
          fetchHmacAuthentication({
            vendorId,
            accountId,
            size: pageSize,
            type: 'hmac',
            label: searchValue,
          })
        );
      }, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [deleteLoading]);

  const handleEdit = (item: HmacAuthenticationItem) => {
    setHmacAuthenticationModalOpen(true);
    setFormValues(item);
  };

  const handleAdd = () => {
    setHmacAuthenticationModalOpen(true);
    setFormValues({
      id: '',
      key: '',
      label: '',
      createdAt: '',
      lastModifiedAt: '',
      role: '',
      type: 'hmac',
    });
  };

  const handleDelete = (item: HmacAuthenticationItem) => {
    setDeleteModalOpen(true);
    setDeleteItem(item);
  };

  const handlePrevious = () => {
    tokenQueue.pop();
    setTokenQueue(tokenQueue);
    const tokensLen = tokenQueue.length;

    setTimeout(() => {
      dispatch(
        fetchHmacAuthentication({
          vendorId,
          accountId,
          size: pageSize,
          ...(tokensLen && { next: tokenQueue[tokensLen - 1] }),
          type: 'hmac',
          label: searchValue,
        })
      );
    });
  };

  const handleNext = (token: string) => {
    tokenQueue.push(token);
    setTokenQueue(tokenQueue);

    setTimeout(() => {
      dispatch(
        fetchHmacAuthentication({
          vendorId,
          accountId,
          size: pageSize,
          next: token,
          type: 'hmac',
          label: searchValue,
        })
      );
    });
  };

  const handleChangePageSize = (newSize: number) => {
    setPageSize(newSize);
    dispatch(
      fetchHmacAuthentication({
        vendorId,
        accountId,
        size: newSize,
        type: 'hmac',
        label: searchValue,
      })
    );
  };

  useEffect(() => {
    dispatch(
      fetchHmacAuthentication({
        vendorId,
        accountId,
        size: pageSize,
        type: 'hmac',
        label: searchValue,
      })
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const debounce = setTimeout(() => {
      dispatch(
        fetchHmacAuthentication({
          vendorId,
          accountId,
          size: pageSize,
          type: 'hmac',
          label: searchValue,
        })
      );
    }, 500);
    return () => clearTimeout(debounce);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchValue]);

  const handleCopy = (value: string | undefined) => {
    if (!value) return;
    let newToasts = [];
    navigator.clipboard.writeText(value);
    newToasts = toasts.concat({
      id: value,
      text: `Successfully copied ${value} to clipboard`,
      type: 'success',
    });
    setToasts(newToasts);
  };

  const handleInputSearch = (value: string) => {
    setSearchValue(value);
  };
  const { resources, pagination } = data || {};

  return (
    <div className={styles.wrapper}>
      <div className={styles.card}>
        <div className={styles.title}>
          <Text type="l" inline emphasized>
            HMAC Authentication Details
          </Text>
        </div>
        <div className={styles.actionWrapper}>
          <div>
            <SearchInput
              defaultValue={searchValue}
              onChange={handleInputSearch}
              width={{ minWidth: 300 }}
            />
          </div>
          <div>
            <Button
              label="Add HMAC Authentication"
              type="secondary"
              onClick={handleAdd}
              disabled={!isEditable}
            >
              <sinch-icon-add slot="icon" />
            </Button>
          </div>
        </div>
        <Table
          hasCheckbox={false}
          tableColumns={tableColumns({
            handleEdit,
            handleDelete,
            isEditable,
            handleCopy,
          })}
          tableData={resources || []}
          loading={loading}
          next={pagination?.next}
          previous={tokenQueue.length}
          handlePreviousPage={handlePrevious}
          handleNextPage={handleNext}
          pageSize={pageSize}
          handleChangePageSize={handleChangePageSize}
        />
        <HmacAuthenticationFormModal
          isVisible={ishmacAuthenticationModalOpen}
          setIsVisible={setHmacAuthenticationModalOpen}
          formValues={formValues}
          setFormValues={setFormValues}
          accountId={accountId}
          vendorId={vendorId}
        />
        <DeleteModal
          isVisible={isDeleteModalOpen}
          setIsVisible={setDeleteModalOpen}
          id={deleteItem?.id}
          accountId={accountId}
          vendorId={vendorId}
          type="Hmac Authentication"
          label={deleteItem?.label}
          authenType={deleteItem?.type}
          deleteAction={deleteHmacAuthentication}
        />
        <CreatedModal
          isVisible={isCreatedModalOpen}
          value={createdItem}
          handleCopy={handleCopy}
          setIsVisible={setIsCreatedModalOpen}
        />
        <Toast toasts={toasts} setToasts={setToasts} />
      </div>
    </div>
  );
}

export default HmacAuthencation;
