import { useState } from 'react';
import { useDispatch } from 'react-redux';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { Dialog, Input, Button } from 'nectary';
import { HmacAuthenticationItem } from 'apps/hst-account-details/src/app/types/api-settings/hmac';
import { createHmacAuthentication, updateHmacAuthentication } from 'apps/hst-account-details/src/app/redux/api-settings/hmac-authentication/hmac-authentication-slice';

import styles from './hmac-authentication-form-modal.module.less';


const HmacAuthenticationFormModal = ({
  accountId,
  vendorId,
  isVisible,
  setIsVisible,
  formValues,
  setFormValues
}: {
  accountId: string,
  vendorId: string,
  isVisible: boolean,
  setIsVisible: (isVisible: boolean) => void,
  formValues: HmacAuthenticationItem | null,
  setFormValues: (formValues: HmacAuthenticationItem | null) => void,
}) => {
  const dispatch = useDispatch()
  const [validating, setValidating] = useState(false)
  const { id, label, type } = formValues || {
    id: '',
    label: '',
    type: 'hmac'
  };

  const onChangeLabel = (val: string) => {
    if (!formValues) return
    setFormValues({
      ...formValues,
      label: val,
    })
  };

  const handleClose = () => {
    setIsVisible(false)
  }

  const handleSubmit = () => {
    setValidating(true);
    if (!label || !formValues) {
      return null
    }

    if (id) {
      return dispatch(updateHmacAuthentication({
        accountId,
        vendorId,
        body: formValues,
        type
      }))
    }
    return dispatch(createHmacAuthentication({
      accountId,
      vendorId,
      body: formValues,
      type
    }))
  }

  return (
    <Dialog
      isOpen={isVisible}
      caption="Hmac Authentication"
      onClose={handleClose}
    >
      <form>
        <div className={styles.formItem}>
          <Input
            label="Label"
            defaultValue={label}
            value={label}
            onChange={onChangeLabel}
            testId="label"
            errorText={validating && !label ? 'label is required' : ''}
            fieldStyles={{ width: '100%' }}
          />
        </div>
        <div className={styles.btnGroup}>
          <Button label="Submit" onClick={handleSubmit} />
          <Button label="Cancel" onClick={handleClose} />
        </div>
      </form>
    </Dialog>
  );
};

export default HmacAuthenticationFormModal;
