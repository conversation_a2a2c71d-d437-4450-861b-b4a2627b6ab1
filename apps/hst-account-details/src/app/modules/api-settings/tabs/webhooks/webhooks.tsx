import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import '@nectary/components/skeleton';
import '@nectary/components/skeleton-item';
import '@nectary/assets/icons/add';

import { Text, Table, Button, Dialog, Input, Toast, Tooltip } from 'nectary';
import { RootState } from 'apps/hst-account-details/src/app/types/store';
import { WebhookItem } from 'apps/hst-account-details/src/app/types/api-settings/webhooks';
import {
  fetchWebhooks,
  deleteWebhooks,
} from 'apps/hst-account-details/src/app/redux/api-settings/webhooks/webhooks-slice';
import { ToastItem } from 'apps/hst-account-details/src/app/types';
import { LOADING_STATUS } from 'apps/hst-account-details/src/app/constants';

import styles from './webhooks.module.less';
import WebhookModal from './webhook-modal';

const DEFAULT_PAGE_SIZE = 10;

type WebhooksProps = {
  vendorId: string;
  accountId: string;
  isEditable: boolean;
};

const tableColumns = ({
  handleEdit,
  handleDelete,
  handleViewRequestBody,
  isEditable,
}: {
  handleEdit: (item: WebhookItem) => void;
  handleDelete: (item: WebhookItem) => void;
  handleViewRequestBody: (item: WebhookItem) => void;
  isEditable: boolean;
}) => [
  {
    title: 'Webhook ID',
    index: 'id',
    sort: false,
    align: 'left',
    render: (value: WebhookItem) => <Text>{value.id}</Text>,
  },
  {
    title: 'URL',
    index: 'url',
    sort: false,
    align: 'left',
    render: (value: WebhookItem) => (
      <div className={styles.shortColumn}>
        <Text>{value.url}</Text>
      </div>
    ),
  },
  {
    title: 'Event',
    index: 'notificationEvents',
    sort: false,
    align: 'left',
    render: (value: WebhookItem) => {
      if (value.notificationEvents.length === 1) {
        return <Text>{value.notificationEvents[0]}</Text>;
      }
      if (value.notificationEvents.length < 1) {
        return '-';
      }
      return (
        <div className={styles.eventColumn}>
          <Tooltip
            type="fast"
            orientation="top"
            text={value.notificationEvents.join(', ')}
          >
            <Text>{`${value.notificationEvents.length} events`}</Text>
          </Tooltip>
        </div>
      );
    },
  },
  {
    title: 'HTTP Method',
    index: 'method',
    sort: false,
    align: 'left',
    render: (value: WebhookItem) => <Text>{value.method}</Text>,
  },
  {
    title: 'Request body',
    index: 'requestBody',
    sort: false,
    align: 'left',
    render: (value: WebhookItem) => {
      let displayBodyText = value.requestBody;
      if (!displayBodyText) return '-';
      if (displayBodyText.length < 80) {
        return (
          <div className={styles.shortColumn}>
            <Text>{displayBodyText}</Text>
          </div>
        );
      }
      displayBodyText = displayBodyText.substring(0, 30);
      return (
        <div className={styles.shortColumn}>
          <Text>{`${displayBodyText}...`}</Text>
          <div className={styles.btnView}>
            <Button
              label="View Request Body"
              type="secondary"
              onClick={() => handleViewRequestBody(value)}
            />
          </div>
        </div>
      );
    },
  },
  {
    title: 'Action',
    index: 'action',
    sort: false,
    align: 'left',
    render: (value: WebhookItem) => (
      <div className={styles.btnGroup}>
        <div className={styles.editBtn}>
          <Button
            label="Edit"
            type="secondary"
            onClick={() => handleEdit(value)}
            disabled={!isEditable}
          />
        </div>
        <Button
          label="Delete"
          type="destructive"
          onClick={() => handleDelete(value)}
          disabled={!isEditable}
        />
      </div>
    ),
  },
];

function Webhooks({ vendorId, accountId, isEditable }: WebhooksProps) {
  const rootState: RootState = useSelector((state) => state || {});
  const [tokenQueue, setTokenQueue] = useState<string[]>([]);
  const [isDeleteModalVisible, setDeleteModalVisible] =
    useState<boolean>(false);
  const [deleteItem, setDeleteItem] = useState<WebhookItem | null>(null);
  const [formValues, setFormValues] = useState<WebhookItem>({});
  const [webhookModalVisible, setWebhookModalVisible] =
    useState<boolean>(false);
  const [deleteText, setDeleteText] = useState('');
  const [toasts, setToasts] = useState<ToastItem[]>([]);
  const [pageSize, setPageSize] = useState<number>(DEFAULT_PAGE_SIZE);

  const { accountWebhooks } = rootState;
  const { data, loading, deleteLoading, createLoading, updateLoading } =
    accountWebhooks || {};

  const [isPayloadModalVisible, setPayloadModalVisible] = useState(false);
  const [payloadContent, setPayloadContent] = useState('');

  const dispatch = useDispatch();

  const handlePrevious = () => {
    tokenQueue.pop();
    setTokenQueue(tokenQueue);
    const tokensLen = tokenQueue.length;

    setTimeout(() => {
      dispatch(
        fetchWebhooks({
          vendorId,
          accountId,
          size: pageSize,
          ...(tokensLen && { next: tokenQueue[tokensLen - 1] }),
        })
      );
    });
  };

  const handleNext = (token: string) => {
    tokenQueue.push(token);
    setTokenQueue(tokenQueue);

    setTimeout(() => {
      dispatch(
        fetchWebhooks({
          vendorId,
          accountId,
          size: pageSize,
          next: token,
        })
      );
    });
  };

  const handleChangePageSize = (newSize: number) => {
    setPageSize(newSize);
    setTimeout(() => {
      dispatch(
        fetchWebhooks({
          vendorId,
          accountId,
          size: newSize,
        })
      );
    });
  };

  useEffect(() => {
    dispatch(
      fetchWebhooks({
        vendorId,
        accountId,
        size: pageSize,
      })
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (!deleteItem) return;
    let newToasts = [];
    if (
      deleteLoading === LOADING_STATUS.SUCCEEDED ||
      deleteLoading === LOADING_STATUS.FAILED
    ) {
      const text =
        deleteLoading === LOADING_STATUS.SUCCEEDED
          ? `Successfully deleted webhook ${deleteItem.id}`
          : `Failed to delete webhook ${deleteItem.id}`;
      newToasts = toasts.concat({
        id: deleteItem.id,
        text,
        type: deleteLoading === LOADING_STATUS.SUCCEEDED ? 'success' : 'error',
      });
      setToasts(newToasts);
      setFormValues(null);
      setDeleteModalVisible(false);
      setTimeout(() => {
        dispatch(
          fetchWebhooks({
            vendorId,
            accountId,
            size: pageSize,
          })
        );
      }, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [deleteLoading]);

  useEffect(() => {
    let newToasts = [];
    if (
      createLoading === LOADING_STATUS.SUCCEEDED ||
      createLoading === LOADING_STATUS.FAILED
    ) {
      const text =
        createLoading === LOADING_STATUS.SUCCEEDED
          ? `Successfully created new webhook`
          : `Failed to create new webhook`;
      newToasts = toasts.concat({
        id: formValues?.url,
        text,
        type: createLoading === LOADING_STATUS.SUCCEEDED ? 'success' : 'error',
      });
      setToasts(newToasts);
      setFormValues({});
      setWebhookModalVisible(false);
      setTimeout(() => {
        dispatch(
          fetchWebhooks({
            vendorId,
            accountId,
            size: pageSize,
          })
        );
      }, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [createLoading]);

  useEffect(() => {
    let newToasts = [];
    if (
      updateLoading === LOADING_STATUS.SUCCEEDED ||
      updateLoading === LOADING_STATUS.FAILED
    ) {
      const text =
        updateLoading === LOADING_STATUS.SUCCEEDED
          ? `Successfully updated webhook`
          : `Failed to update webhook`;
      newToasts = toasts.concat({
        id: formValues?.url,
        text,
        type: updateLoading === LOADING_STATUS.SUCCEEDED ? 'success' : 'error',
      });
      setToasts(newToasts);
      setFormValues({});
      setWebhookModalVisible(false);
      setTimeout(() => {
        dispatch(
          fetchWebhooks({
            vendorId,
            accountId,
            size: pageSize,
          })
        );
      }, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [updateLoading]);

  const handleEdit = (item: WebhookItem) => {
    setWebhookModalVisible(true);
    setFormValues(item);
  };

  const handleAdd = () => {
    setWebhookModalVisible(true);
    setFormValues({});
  };

  const handleDelete = (item: WebhookItem) => {
    setDeleteModalVisible(true);
    setDeleteItem(item);
  };

  const confirmDelete = () => {
    if (!deleteItem) return;
    dispatch(
      deleteWebhooks({
        vendorId,
        accountId,
        webhookId: deleteItem.id || '',
      })
    );
    setDeleteModalVisible(false);
    setDeleteText('');
  };

  const handleViewRequestBody = (value: WebhookItem) => {
    setPayloadModalVisible(true);
    setPayloadContent(value?.requestBody || '');
  };

  const { resources, pagination } = data || {};

  let payloadContentDisplayed = {};
  let isStringPayload = true;
  try {
    payloadContentDisplayed = JSON.parse(payloadContent);
    isStringPayload = false;
  } catch (err) {
    payloadContentDisplayed = payloadContent;
    isStringPayload = true;
  }

  return (
    <div className={styles.wrapper}>
      <div className={styles.card}>
        <div className={styles.title}>
          <Text type="l" inline emphasized>
            Webhooks (HTTP notifications)
          </Text>
        </div>
        <div className={styles.actionWrapper}>
          <Button
            label="Add Webhook"
            type="secondary"
            onClick={handleAdd}
            disabled={!isEditable}
          >
            <sinch-icon-add slot="icon" />
          </Button>
        </div>
        <Table
          hasCheckbox={false}
          tableColumns={tableColumns({
            handleEdit,
            handleDelete,
            handleViewRequestBody,
            isEditable,
          })}
          tableData={resources || []}
          loading={loading}
          next={pagination?.next}
          previous={tokenQueue.length}
          handlePreviousPage={handlePrevious}
          handleNextPage={handleNext}
          pageSize={pageSize}
          handleChangePageSize={handleChangePageSize}
          scrollX
        />
      </div>
      <Dialog
        isOpen={isDeleteModalVisible}
        caption="Delete"
        onClose={() => {
          setDeleteModalVisible(false);
          setDeleteText('');
        }}
      >
        <div className={styles.formItem}>
          <Text>{`Are you sure you want to delete webhook ${deleteItem?.id}`}</Text>
        </div>
        <div className={styles.formItem}>
          <Input
            label="Please type delete"
            value={deleteText}
            onChange={(val: string) => setDeleteText(val)}
          />
        </div>
        <div className={styles.formItem}>
          <Button
            label="Confirm"
            disabled={deleteText.toLowerCase() !== 'delete'}
            onClick={confirmDelete}
          />
        </div>
      </Dialog>
      <WebhookModal
        isVisible={webhookModalVisible}
        setIsVisible={setWebhookModalVisible}
        vendorId={vendorId}
        accountId={accountId}
        formValues={formValues || {}}
        setFormValues={setFormValues}
      />
      <Toast toasts={toasts} setToasts={setToasts} />
      {payloadContent && (
        <div className={styles.dialogWrap}>
          <Dialog
            isOpen={isPayloadModalVisible}
            caption="Request Body"
            onClose={() => setPayloadModalVisible(false)}
          >
            <div
              className={styles.jsonView}
              style={!isStringPayload ? { whiteSpace: 'pre' } : {}}
            >
              {isStringPayload
                ? payloadContentDisplayed
                : JSON.stringify(payloadContentDisplayed, null, 4)}
            </div>
          </Dialog>
        </div>
      )}
    </div>
  );
}

export default Webhooks;
