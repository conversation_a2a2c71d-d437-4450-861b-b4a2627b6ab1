export const NOTIFICATION_EVENT_OPTIONS = [
  {
    label: 'MO',
    value: 'MO',
  },
  {
    label: 'MO_MMS',
    value: 'MO_MMS',
  },
  {
    label: 'DR_ENROUTE',
    value: 'DR_ENROUTE',
  },
  {
    label: 'DR_HELD',
    value: 'DR_HELD',
  },
  {
    label: 'DR_SUBMITTED',
    value: 'DR_SUBMITTED',
  },
  {
    label: 'DR_DELIVERED',
    value: 'DR_DELIVERED',
  },
  {
    label: 'DR_EXPIRED',
    value: 'DR_EXPIRED',
  },
  {
    label: 'DR_REJECTED',
    value: 'DR_REJECTED',
  },
  {
    label: 'DR_FAILED',
    value: 'DR_FAILED',
  },
  {
    label: 'DR_PENDING',
    value: 'DR_PENDING',
  },
  {
    label: 'OPTOUT_PERFORMED',
    value: 'OPTOUT_PERFORMED',
  },
  {
    label: 'DR_READ',
    value: 'DR_READ',
  },
  {
    label: 'MO_RCS',
    value: 'MO_RCS',
  },
];

export const HTTP_METHOD_OPTIONS = [
  {
    label: 'GET',
    value: 'GET',
  },
  {
    label: 'POST',
    value: 'POST',
  },
  {
    label: 'PUT',
    value: 'PUT',
  },
  {
    label: 'DELETE',
    value: 'DELETE',
  },
  {
    label: 'PATCH',
    value: 'PATCH',
  },
];

export const TLS_OPTIONS = [
  {
    label: 'NO',
    value: 'NO',
  },
  {
    label: 'YES_SYSTEM',
    value: 'YES_SYSTEM',
  },
  {
    label: 'YES_CUSTOM',
    value: 'YES_CUSTOM',
  },
];

export const FIELD_NAMES = {
  notificationEvents: 'notificationEvents',
  method: 'method',
  url: 'url',
  connectionTimeout: 'connectionTimeout',
  readTimeout: 'readTimeout',
  maximumRedirects: 'maximumRedirects',
  tlsCertificateVerification: 'tlsCertificateVerification',
  ttl: 'ttl',
};

export const URL_REGEX_STR =
  '(?:(?!(?:10|127)(?:\\.\\d{1,3}){3})(?!(?:169\\.254|192\\.168)(?:\\.\\d{1,3}){2})(?!172\\.(?:1[6-9]|2\\d|3[0-1])(?:\\.\\d{1,3}){2})(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})))(?::\\d{2,5})?(?:(\\/|\\?)\\S*)?';

// loosen url are those not contain http or www, are still considered as url
export const LOOSEN_URL_REGEX_STR = `(?:(?:https?):\\/\\/)?${URL_REGEX_STR}`;
export const loosenUrlRegex = new RegExp(`^${LOOSEN_URL_REGEX_STR}$`, 'i');

export const isUrlValid = (value) => loosenUrlRegex.test(value);
