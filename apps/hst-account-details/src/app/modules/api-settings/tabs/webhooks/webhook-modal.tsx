import { useState, useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import '@nectary/assets/icons/close';
import _isEmpty from 'lodash/isEmpty';

import {
  Dialog,
  Text,
  Select,
  MultiSelect,
  Input,
  Button,
  TextArea,
} from 'nectary';
import { WebhookItem } from '../../../../types/api-settings/webhooks';
import {
  updateWebhooks,
  createWebhooks,
} from '../../../../redux/api-settings/webhooks/webhooks-slice';
import { LOADING_STATUS } from '../../../../constants';
import { RootState } from '../../../../types/store';

import {
  FIELD_NAMES,
  NOTIFICATION_EVENT_OPTIONS,
  HTTP_METHOD_OPTIONS,
  TLS_OPTIONS,
  isUrlValid,
} from './constants';
import styles from './webhook-modal.module.less';

const isFieldValid = (field: string, value: any) => {
  switch (field) {
    case FIELD_NAMES.notificationEvents: {
      return !_isEmpty(value);
    }
    case FIELD_NAMES.method: {
      return !!value;
    }
    case FIELD_NAMES.url: {
      return !!value && isUrlValid(value) && value.length <= 1000;
    }
    case FIELD_NAMES.connectionTimeout: {
      return !isNaN(value) && Number(value) > 0 && Number(value) < 9999999999;
    }
    case FIELD_NAMES.readTimeout: {
      return !isNaN(value) && Number(value) > 0 && Number(value) < 9999999999;
    }
    case FIELD_NAMES.maximumRedirects: {
      return !isNaN(value) && Number(value) >= 0 && Number(value) < 999;
    }
    case FIELD_NAMES.tlsCertificateVerification: {
      return !!value;
    }
    case FIELD_NAMES.ttl: {
      return (
        !value || (!isNaN(value) && Number(value) > 0 && Number(value) < 604800)
      );
    }

    default: {
      return true;
    }
  }
};

const isFormValid = (payload: any) => {
  let isValid = true;
  for (const [key] of Object.entries(FIELD_NAMES)) {
    if (!isFieldValid(key, payload?.[key])) {
      isValid = false;
    }
  }
  return isValid;
};

const isKeyInvalid = (key: string, value: string) => !key && value;
const isValueInvalid = (key: string, value: string) => key && !value;

const isAttemptsInvalid = (attempts: string, delay: string) =>
  (!attempts && delay) ||
  (attempts &&
    (isNaN(Number(attempts)) ||
      Number(attempts) < 1 ||
      Number(attempts) > 672));

const isDelayInvalid = (attempts: string, delay: string) =>
  (attempts && !delay) ||
  (delay && (isNaN(Number(delay)) || Number(delay) < 5 || Number(delay) > 900));

const isKeyValueValid = (
  keyValueArr: Array<{ key: string; value: string }>
) => {
  let isValid = true;
  keyValueArr?.forEach(({ key, value }) => {
    if (isKeyInvalid(key, value) || isValueInvalid(key, value)) {
      isValid = false;
    }
  });
  return isValid;
};

const isRetryArrValid = (
  retryArr: Array<{ attempts: string; delay: string }>
) => {
  let isValid = true;
  retryArr?.forEach(({ attempts, delay }) => {
    if (isAttemptsInvalid(attempts, delay) || isDelayInvalid(attempts, delay)) {
      isValid = false;
    }
  });
  return isValid;
};

const DEFAULT_DR_BODY = `{
  "delivery_report_id": "$drId",
  "source_number": "$sourceAddress",
  "date_received": "$receivedTimestamp",
  "status": "$status",
  "delay": "0",
  "submitted_date": "$submittedTimestamp",
  "message_id": "$messageId",
  "original_text": "$esc.json($!mtContent)",
  "vendor_account_id": {
    "vendor_id": "$vendorId",
    "account_id": "$accountId"
  },
  "error_code": "$statusCode",
  "metadata": {
    #foreach($key in $metadata.keySet())
    "$key": "$esc.json($metadata.get($key))"#if( $velocityHasNext ),#end
    #end
  }
}`;

const DEFAULT_MO_BODY = `{
  "account_id": "$accountId",
  "reply_id": "$replyId",
  "destination_number": "$destinationAddress",
  "source_number": "$sourceAddress",
  "date_received": "$receivedTimestamp",
  "message_id": "$messageId",
  #if ($attachments)
  "attachments": [
    #foreach ($entry in $attachments)
    {
      "content_type": "$entry.contentType",
      "content": "$entry.base64",
      "original_name": "$entry.originalName"
    }#if( $foreach.hasNext ),#end
    #end
  ],
  #else
  "content": "$esc.json($moContent)",
  #end
  "metadata": {
    #foreach ($entry in $metadata.entrySet())
    "$entry.key": "$entry.value"#if( $foreach.hasNext ),#end
    #end
  }
}`;

const WebhookModal = ({
  vendorId,
  accountId,
  isVisible,
  setIsVisible,
  formValues,
}: {
  vendorId: string;
  accountId: string;
  isVisible: boolean;
  setIsVisible: (isVisible: boolean) => void;
  formValues: WebhookItem;
}) => {
  const {
    id,
    headers: headersObj,
    requestBody: requestBodyJson,
    retries: retriesObj,
  } = formValues || {};
  const dispatch = useDispatch();
  const rootState = useSelector((state: RootState) => state || {});
  const { accountWebhooks } = rootState;
  const { createLoading, updateLoading } = accountWebhooks || {};

  const [notificationEvents, setNotificationEvents] = useState(
    formValues.notificationEvents || []
  );
  const [url, setUrl] = useState(formValues.url || 'https://');
  const [method, setMethod] = useState(formValues.method || '');
  const [responseBody, setResponseBody] = useState(
    formValues.responseBody || '.*'
  );
  const [responseCode, setResponseCode] = useState(
    formValues.responseCode || '.*'
  );
  const [connectionTimeout, setConnectionTimeout] = useState(
    formValues.connectionTimeout || ''
  );
  const [readTimeout, setReadTimeout] = useState(formValues.readTimeout || '');
  const [maximumRedirects, setMaximumRedirects] = useState(
    formValues.maximumRedirects || ''
  );
  const [ttl, setTtl] = useState(formValues.ttl || '');
  const [tlsCertificateVerification, setTlsCertificateVerification] = useState(
    formValues.tlsCertificateVerification || TLS_OPTIONS[1].value
  );

  const initialHeaders = headersObj
    ? Object.entries(headersObj).map(([key, value]) => ({
        key,
        value,
      }))
    : null;
  const [headerArr, setHeaderArr] = useState(
    initialHeaders || [{ key: '', value: '' }]
  );

  let requestBodyObj = {};
  try {
    requestBodyObj = requestBodyJson ? JSON.parse(requestBodyJson) : {};
  } catch (err) {
    console.log('requestBodyJson parsing error: ', err);
  }
  const initialContents = Object.entries(requestBodyObj).map(
    ([key, value]) => ({
      key,
      value,
    })
  );
  const [contentArr, setContentArr] = useState(
    initialContents || [{ key: '', value: '' }]
  );

  const initialRetries = retriesObj
    ? Object.entries(retriesObj).map(([attempts, delay]) => ({
        attempts,
        delay,
      }))
    : null;
  const [retryArr, setRetryArr] = useState(
    initialRetries || [{ attempts: '', delay: '' }]
  );

  const [useFreeformBody, setUseFreeformBody] = useState(false);
  const [freeformBody, setFreeformBody] = useState(
    formValues.requestBody || ''
  );

  const [isValidated, setIsValidated] = useState(false);

  const resetFormToInitialValues = useCallback(() => {
    setNotificationEvents(formValues.notificationEvents || []);
    setUrl(formValues.url || 'https://');
    setMethod(formValues.method || '');
    setResponseBody(formValues.responseBody || '.*');
    setResponseCode(formValues.responseCode || '.*');
    setConnectionTimeout(formValues.connectionTimeout || '');
    setReadTimeout(formValues.readTimeout || '');
    setMaximumRedirects(formValues.maximumRedirects || '');
    setTtl(formValues.ttl || '');
    setTlsCertificateVerification(
      formValues.tlsCertificateVerification || TLS_OPTIONS[1].value
    );

    const nextHeaders = formValues.headers
      ? Object.entries(formValues.headers).map(([key, value]) => ({
          key,
          value,
        }))
      : null;
    setHeaderArr(nextHeaders || [{ key: '', value: '' }]);

    let nextRequestBodyObj = {};
    try {
      nextRequestBodyObj = formValues.requestBody
        ? JSON.parse(formValues.requestBody)
        : {};
    } catch (err) {
      // Ignore parsing errors, use empty object
    }
    const nextContents = Object.entries(nextRequestBodyObj).map(
      ([key, value]) => ({
        key,
        value,
      })
    );
    setContentArr(nextContents || [{ key: '', value: '' }]);

    const nextRetries = formValues.retries
      ? Object.entries(formValues.retries).map(([attempts, delay]) => ({
          attempts,
          delay,
        }))
      : null;
    setRetryArr(nextRetries || [{ attempts: '', delay: '' }]);

    setFreeformBody(formValues.requestBody || '');

    setUseFreeformBody(false);
    if (formValues.requestBody && !formValues.requestBody.startsWith('{')) {
      setUseFreeformBody(true);
    } else if (formValues.requestBody) {
      try {
        const parsed = JSON.parse(formValues.requestBody);
        const keys = Object.keys(parsed);
        if (keys.length > 5 || JSON.stringify(parsed).length > 200) {
          setUseFreeformBody(true);
        }
      } catch (err) {
        setUseFreeformBody(true);
      }
    }
  }, [formValues]);

  useEffect(() => {
    if (isVisible) {
      resetFormToInitialValues();
    }
  }, [formValues, id, isVisible, resetFormToInitialValues]);

  useEffect(() => {
    if (!isVisible) {
      setIsValidated(false);
    }
  }, [isVisible]);

  const handleAddHeader = () => {
    const newHeaderArr = [...headerArr];
    newHeaderArr.push({ key: '', value: '' });
    setHeaderArr(newHeaderArr);
  };

  const handleAddContent = () => {
    const newContentArr = [...contentArr];
    newContentArr.push({ key: '', value: '' });
    setContentArr(newContentArr);
  };

  const handleAddRetry = () => {
    const newRetryArr = [...retryArr];
    newRetryArr.push({ attempts: '', delay: '' });
    setRetryArr(newRetryArr);
  };

  const handleSubmit = () => {
    setIsValidated(true);

    const isHeadersValid = isKeyValueValid(
      headerArr as Array<{ key: string; value: string }>
    );
    const isRequestBodyValid = useFreeformBody
      ? true
      : isKeyValueValid(contentArr as Array<{ key: string; value: string }>);
    const isRetriesValid = isRetryArrValid(
      retryArr as Array<{ attempts: string; delay: string }>
    );

    if (!isHeadersValid || !isRequestBodyValid || !isRetriesValid) {
      return;
    }

    const headers: Record<string, any> = {};
    const validHeaders = headerArr.filter(({ key }) => !!key);
    validHeaders.forEach(({ key, value }) => {
      headers[key] = value;
    });

    const retries: Record<string, any> = {};
    const validRetries = retryArr.filter(({ attempts }) => !!attempts);
    validRetries.forEach(({ attempts, delay }) => {
      retries[attempts] = delay;
    });

    let requestBody: string;
    if (useFreeformBody) {
      requestBody = freeformBody;
    } else {
      const validContent = contentArr.filter(({ key }) => !!key);
      const contentObj: Record<string, any> = {};
      validContent.forEach(({ key, value }) => {
        contentObj[key] = value;
      });
      requestBody = JSON.stringify(contentObj);
    }

    const payload: WebhookItem = {
      ...formValues,
      notificationEvents,
      method,
      url,
      headers,
      requestBody,
      tlsCertificateVerification,
      responseBody,
      responseCode,
      maximumRedirects: Number(maximumRedirects) || 0,
      connectionTimeout: Number(connectionTimeout) || 0,
      readTimeout: Number(readTimeout) || 0,
      ttl: Number(ttl) || 0,
      retries,
    };

    const isPayloadValid = isFormValid(payload);

    if (!isPayloadValid) {
      return;
    }

    if (id) {
      dispatch(
        updateWebhooks({
          vendorId,
          accountId,
          payload,
        })
      );
    } else {
      dispatch(
        createWebhooks({
          vendorId,
          accountId,
          payload,
        })
      );
    }
  };

  const loading =
    createLoading === LOADING_STATUS.LOADING ||
    updateLoading === LOADING_STATUS.LOADING;

  return (
    <div className={styles.dialogWrap}>
      <Dialog
        isOpen={isVisible}
        caption="Webhooks"
        onClose={() => setIsVisible(false)}
      >
        <form className={styles.formWrap}>
          <div className={styles.formSegment}>
            <div className={styles.heading}>
              <Text>Webhook Configuration</Text>
            </div>
            <div className={styles.row}>
              <div className={styles.left}>
                <MultiSelect
                  label="Event (Required)"
                  value={notificationEvents}
                  onSelect={setNotificationEvents}
                  options={NOTIFICATION_EVENT_OPTIONS}
                  rows={7}
                  testId="notification-event-select"
                  fieldStyles={{ width: '100%' }}
                  errorText={
                    isValidated &&
                    !isFieldValid(
                      FIELD_NAMES.notificationEvents,
                      notificationEvents
                    )
                      ? 'Event is required'
                      : ''
                  }
                />
              </div>
              <div className={styles.right}>
                <Select
                  label="Method (Required)"
                  value={method}
                  options={HTTP_METHOD_OPTIONS}
                  onSelect={setMethod}
                  menuStyles={{ height: 350, overflow: 'scroll' }}
                  errorText={
                    isValidated && !isFieldValid(FIELD_NAMES.method, method)
                      ? 'Method is required'
                      : ''
                  }
                />
              </div>
            </div>
            <div className={styles.row}>
              <Input
                label="URL (Required)"
                defaultValue={url}
                value={url}
                onChange={setUrl}
                testId="url-input"
                fieldStyles={{ width: '100%' }}
                errorText={
                  isValidated && !isFieldValid(FIELD_NAMES.url, url)
                    ? 'You must enter a valid URL (up to 1000 characters)'
                    : ''
                }
              />
            </div>
            <div className={styles.row}>
              <Input
                label="Response Body Regex"
                value={responseBody}
                onChange={setResponseBody}
                testId="response-body-regex-input"
                fieldStyles={{ width: '100%' }}
              />
            </div>
            <div className={styles.row}>
              <Input
                label="Response Body Code"
                value={responseCode}
                onChange={setResponseCode}
                testId="response-body-code-input"
                fieldStyles={{ width: '100%' }}
              />
            </div>
            <div className={styles.row}>
              <Input
                label="Connection Timeout (Required)"
                value={connectionTimeout}
                onChange={setConnectionTimeout}
                testId="connection-timeout-input"
                fieldStyles={{ width: '100%' }}
                errorText={
                  isValidated &&
                  !isFieldValid(
                    FIELD_NAMES.connectionTimeout,
                    connectionTimeout
                  )
                    ? 'You must enter a valid connection timeout'
                    : ''
                }
              />
            </div>
            <div className={styles.row}>
              <Input
                label="Read Timeout (Required)"
                value={readTimeout}
                onChange={setReadTimeout}
                testId="read-timeout-input"
                fieldStyles={{ width: '100%' }}
                errorText={
                  isValidated &&
                  !isFieldValid(FIELD_NAMES.readTimeout, readTimeout)
                    ? 'You must enter a valid read timeout'
                    : ''
                }
              />
            </div>
            <div className={styles.row}>
              <Input
                label="Maximum Redirects"
                value={maximumRedirects}
                onChange={setMaximumRedirects}
                testId="maximum-redirects-input"
                fieldStyles={{ width: '100%' }}
                errorText={
                  isValidated &&
                  !isFieldValid(FIELD_NAMES.maximumRedirects, maximumRedirects)
                    ? 'You must enter a valid maximum redirects value between 0 and 999'
                    : ''
                }
              />
            </div>
            <div className={styles.row}>
              <div>
                <Select
                  label="TLS Certificate Verification (Required)"
                  value={tlsCertificateVerification}
                  options={TLS_OPTIONS}
                  onSelect={setTlsCertificateVerification}
                  errorText={
                    isValidated &&
                    !isFieldValid(
                      FIELD_NAMES.tlsCertificateVerification,
                      tlsCertificateVerification
                    )
                      ? 'TLS Certificate Verification is required'
                      : ''
                  }
                />
              </div>
            </div>
            <div className={styles.row}>
              <Input
                label="TTL"
                value={ttl}
                onChange={setTtl}
                testId="ttl-input"
                fieldStyles={{ width: '100%' }}
                errorText={
                  isValidated && !isFieldValid(FIELD_NAMES.ttl, ttl)
                    ? 'You must enter a valid maximum redirects value between 0 and 999'
                    : ''
                }
              />
            </div>
          </div>
          <div className={styles.formSegment}>
            <div className={styles.heading}>
              <Text>Headers</Text>
            </div>
            {headerArr.map(({ key, value }, index) => (
              // eslint-disable-next-line react/no-array-index-key
              <div key={index} className={styles.row}>
                <div className={styles.left}>
                  <Input
                    label="Header key"
                    value={key as string}
                    onChange={(newKey: string) => {
                      const newHeaderArr = [...headerArr];
                      newHeaderArr[index].key = newKey;
                      setHeaderArr(newHeaderArr);
                    }}
                    testId={`header-key-${index}`}
                    fieldStyles={{ width: '90%' }}
                    errorText={
                      isValidated &&
                      isKeyInvalid(key as string, value as string)
                        ? 'Header key is required'
                        : ''
                    }
                  />
                </div>
                <div className={styles.right}>
                  <Input
                    label="Header value"
                    value={value as string}
                    onChange={(newVal: string) => {
                      const newHeaderArr = [...headerArr];
                      newHeaderArr[index].value = newVal;
                      setHeaderArr(newHeaderArr);
                    }}
                    testId={`header-value-${index}`}
                    fieldStyles={{ width: '90%' }}
                    errorText={
                      isValidated &&
                      isValueInvalid(key as string, value as string)
                        ? 'Header value is required'
                        : ''
                    }
                  />
                </div>
                <div>
                  <Button
                    type="secondary"
                    icon={<sinch-icon-close slot="icon" />}
                    onClick={() => {
                      const newHeaderArr = [...headerArr];
                      newHeaderArr.splice(index, 1);
                      setHeaderArr(newHeaderArr);
                    }}
                  />
                </div>
              </div>
            ))}
            <div className={styles.row}>
              <Button
                label="Add header"
                type="secondary"
                onClick={handleAddHeader}
              />
            </div>
          </div>
          <div className={styles.formSegment}>
            <div className={styles.heading}>
              <Text>Request Body (POST, PUT, PATCH methods)</Text>
            </div>
            <div className={styles.row}>
              <div
                style={{
                  display: 'flex',
                  gap: '10px',
                  alignItems: 'center',
                  marginBottom: '15px',
                }}
              >
                <Button
                  label="Key-Value Pairs"
                  type={!useFreeformBody ? 'primary' : 'secondary'}
                  onClick={() => setUseFreeformBody(false)}
                />
                <Button
                  label="Freeform JSON"
                  type={useFreeformBody ? 'primary' : 'secondary'}
                  onClick={() => setUseFreeformBody(true)}
                />
              </div>
            </div>

            {useFreeformBody ? (
              <>
                <div className={styles.row}>
                  <div style={{ marginBottom: '15px' }}>
                    <Text
                      style={{
                        fontSize: '14px',
                        fontWeight: '500',
                        marginBottom: '8px',
                        display: 'block',
                      }}
                    >
                      Quick Templates:
                    </Text>
                    <div
                      style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}
                    >
                      <Button
                        label="DR (Delivery Report) Template"
                        type="secondary"
                        onClick={() => setFreeformBody(DEFAULT_DR_BODY)}
                        style={{ fontSize: '12px', padding: '6px 12px' }}
                      />
                      <Button
                        label="MO (Mobile Originated) Template"
                        type="secondary"
                        onClick={() => setFreeformBody(DEFAULT_MO_BODY)}
                        style={{ fontSize: '12px', padding: '6px 12px' }}
                      />
                      <Button
                        label="Format JSON"
                        type="secondary"
                        onClick={() => {
                          try {
                            const formatted = JSON.stringify(
                              JSON.parse(freeformBody),
                              null,
                              2
                            );
                            setFreeformBody(formatted);
                          } catch {
                            // If JSON is invalid, don't change anything
                          }
                        }}
                        style={{ fontSize: '12px', padding: '6px 12px' }}
                      />
                    </div>
                  </div>
                </div>
                <div className={styles.row}>
                  <TextArea
                    label="Request Body JSON"
                    value={freeformBody}
                    onChange={setFreeformBody}
                    resizable
                    minRows={10}
                    rows={15}
                    placeholder="Enter your JSON request body here... Use the templates above for quick setup."
                    fieldStyles={{
                      width: '100%',
                    }}
                  />
                </div>
              </>
            ) : (
              <>
                {contentArr.map(({ key, value }, index) => (
                  // eslint-disable-next-line react/no-array-index-key
                  <div key={index} className={styles.row}>
                    <div className={styles.left}>
                      <Input
                        label="Parameter key"
                        value={key as string}
                        onChange={(newKey: string) => {
                          const newContentArr = [...contentArr];
                          newContentArr[index].key = newKey;
                          setContentArr(newContentArr);
                        }}
                        testId={`param-key-${index}`}
                        fieldStyles={{ width: '90%' }}
                        errorText={
                          isValidated &&
                          isKeyInvalid(key as string, value as string)
                            ? 'Parameter key is required'
                            : ''
                        }
                      />
                    </div>
                    <div className={styles.right}>
                      <Input
                        label="Parameter value"
                        value={value as string}
                        onChange={(newVal: string) => {
                          const newContentArr = [...contentArr];
                          newContentArr[index].value = newVal;
                          setContentArr(newContentArr);
                        }}
                        testId={`param-value-${index}`}
                        fieldStyles={{ width: '90%' }}
                        errorText={
                          isValidated &&
                          isValueInvalid(key as string, value as string)
                            ? 'Parameter value is required'
                            : ''
                        }
                      />
                    </div>
                    <div>
                      <Button
                        type="secondary"
                        icon={<sinch-icon-close slot="icon" />}
                        onClick={() => {
                          const newContentArr = [...contentArr];
                          newContentArr.splice(index, 1);
                          setContentArr(newContentArr);
                        }}
                      />
                    </div>
                  </div>
                ))}
                <div className={styles.row}>
                  <Button
                    label="Add parameter"
                    type="secondary"
                    onClick={handleAddContent}
                  />
                </div>
              </>
            )}
          </div>

          <div className={styles.formSegment}>
            <div className={styles.heading}>
              <Text>Retries</Text>
            </div>
            {retryArr.map(({ attempts, delay }, index) => (
              // eslint-disable-next-line react/no-array-index-key
              <div key={index} className={styles.row}>
                <div className={styles.left}>
                  <Input
                    label="Attempts (between 1 and 672)"
                    value={attempts as string}
                    onChange={(newAttempts: string) => {
                      const newRetryArr = [...retryArr];
                      newRetryArr[index].attempts = newAttempts;
                      setRetryArr(newRetryArr);
                    }}
                    testId={`retry-attempts-${index}`}
                    fieldStyles={{ width: '90%' }}
                    errorText={
                      isValidated &&
                      isAttemptsInvalid(attempts as string, delay as string)
                        ? 'Attempts is invalid'
                        : ''
                    }
                  />
                </div>
                <div className={styles.right}>
                  <Input
                    label="Delay (between 5 and 900)"
                    value={delay as string}
                    onChange={(newDelay: string) => {
                      const newRetryArr = [...retryArr];
                      newRetryArr[index].delay = newDelay;
                      setRetryArr(newRetryArr);
                    }}
                    testId={`retry-delay-${index}`}
                    fieldStyles={{ width: '90%' }}
                    errorText={
                      isValidated &&
                      isDelayInvalid(attempts as string, delay as string)
                        ? 'Delay is invalid'
                        : ''
                    }
                  />
                </div>
                <div>
                  <Button
                    type="secondary"
                    icon={<sinch-icon-close slot="icon" />}
                    onClick={() => {
                      const newRetryArr = [...retryArr];
                      newRetryArr.splice(index, 1);
                      setRetryArr(newRetryArr);
                    }}
                  />
                </div>
              </div>
            ))}
            <div className={styles.row}>
              <Button
                label="Add Retry"
                type="secondary"
                onClick={handleAddRetry}
              />
            </div>
          </div>
          <div className={styles.actionGroup}>
            <Button
              label={id ? 'Update' : 'Create'}
              onClick={handleSubmit}
              icon={loading ? <sinch-spinner slot="icon" /> : null}
              disabled={loading}
            />
            <Button label="Cancel" onClick={() => setIsVisible(false)} />
          </div>
        </form>
      </Dialog>
    </div>
  );
};

export default WebhookModal;
