.dialogWrap {
  sinch-dialog {
    --sinch-comp-dialog-max-width: 900px;
    --sinch-comp-dialog-max-height: 800px;
  }
}

.formWrap {
  .formSegment {
    padding: 24px;
    border: 1px solid var(--sinch-comp-card-color-white-default-border-initial);;
    border-radius: 8px;
    box-shadow: var(--sinch-comp-card-shadow-initial);
    margin-bottom: 24px;

    .heading {
      margin-bottom: 8px;
    }

    .row {
      display: flex;
      flex-direction: row;
      align-items: baseline;
      margin-bottom: 24px;
    }

    .left, .right {
      width: 50%;
    }
  }
}

.actionGroup {
  display: flex;
  justify-content: space-evenly;
  padding-bottom: 36px;
}

.spinnerWrap {
  width: 100%;
  height: 100%;
  position: absolute;
  opacity: 0.3;
  display: flex;
  align-items: center;
  z-index: 1000;
}

