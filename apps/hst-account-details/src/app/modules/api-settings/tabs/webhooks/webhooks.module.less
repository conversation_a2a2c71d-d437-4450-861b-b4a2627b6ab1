.wrapper {
  margin: 15px;
}

.title {
  margin-bottom: 32px;
  display: flex;
  justify-content: space-between;
}

.actionWrapper {
  margin-bottom: 10px;
  display: flex;
  justify-content: flex-end;
}

.row {
  display: flex;
  flex-direction: row;
  margin-bottom: 16px;
  .left {
    width: 20%;
  }
  .right {
    width: 80%;
  }
}

.btnGroup {
  display: flex;
  flex-direction: row;
}

.editBtn {
  margin-right: 4px;
}

.key {
  cursor: pointer;
}

.card {
  padding: 24px;
  margin: 20px 0;
  border-radius: var(--sinch-comp-card-shape-radius);
  border: 1px solid var(--sinch-comp-card-color-white-default-border-initial);
  background-color: var(
    --sinch-comp-card-color-white-default-background-initial
  );
  box-shadow: var(--sinch-comp-card-shadow-initial);
  overflow: hidden;
}

.formItem {
  margin-bottom: 16px;
}

.shortColumn {
  width: 300px;
  word-break: break-all;
  display: inline-table;
}

.eventColumn {
  width: 100px;
}

.btnView {
  margin-top: 8px;
}

.jsonView {
  word-break: break-all;
}

.dialogWrap {
  sinch-dialog {
    --sinch-comp-dialog-max-width: 800px;
    --sinch-comp-dialog-max-height: 600px;
  }
}
