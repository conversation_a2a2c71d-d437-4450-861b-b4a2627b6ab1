.wrapper {
  padding: 24px;
}

.title {
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
}

.actionWrapper {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  align-items: center;
}

.btnGroup {
  display: flex;
  gap: 8px;
}

.editBtn {
  margin-right: 8px;
}

.card {
  background: var(--color-white);
  border-radius: 8px;
  padding: 24px;
  border: 1px solid var(--sinch-comp-card-color-white-default-border-initial);
  box-shadow: var(--sinch-comp-card-shadow-initial);
  overflow: hidden;
}

.apiKeyCell {
  max-width: 300px;
  word-break: break-all;
}

.modalContent {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px;
}

.modalActions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 16px;
}
