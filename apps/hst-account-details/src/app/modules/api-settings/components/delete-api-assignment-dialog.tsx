import { Button, Dialog } from 'nectary';
import React from 'react';
import styles from './delete-api-assignment-dialog.module.less';

interface DeleteApiAssignmentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  apiKeyLabel?: string;
}

export const DeleteApiAssignmentDialog: React.FC<
  DeleteApiAssignmentDialogProps
> = ({ isOpen, onClose, onConfirm, apiKeyLabel }) => {
  const safeApiKeyLabel = apiKeyLabel?.trim() || 'N/A';

  return (
    <Dialog
      isOpen={isOpen}
      onClose={onClose}
      caption="Delete API Key Assignment"
      disableCloseViaBackdrop
    >
      <div className={styles.content}>
        <p>
          Are you sure you wish to delete assignment for API Key &quot;
          {safeApiKeyLabel}&quot;?
        </p>
        <div className={styles.buttonGroup}>
          <Button label="Cancel" type="secondary" onClick={onClose} />
          <Button
            label="Delete"
            type="destructive"
            onClick={() => {
              onConfirm();
            }}
          />
        </div>
      </div>
    </Dialog>
  );
};

export default DeleteApiAssignmentDialog;
