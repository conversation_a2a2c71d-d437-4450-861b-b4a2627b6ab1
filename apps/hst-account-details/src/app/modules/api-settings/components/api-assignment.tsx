import { Button, SearchInput, Table, Text, Toast } from 'nectary';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { LOADING_STATUS } from '../../../constants';
import {
  createApiAssignment,
  deleteApiAssignment,
  fetchApiAssignments,
  updateApiAssignment,
  resetLoadingStates,
} from '../../../redux/api-settings/api-assignment/api-assignment-slice';
import { ToastItem } from '../../../types';
import {
  ApiAssignmentPayload,
  ApiAssignmentProps,
  ApiKeyAssignment,
} from '../../../types/api-settings/assignments';
import { RootState } from '../../../types/store';
import { ApiAssignmentModal } from './api-assignment-modal';
import styles from './api-assignment.module.less';
import { DeleteApiAssignmentDialog } from './delete-api-assignment-dialog';

interface TableColumnProps {
  handleEdit: (item: ApiKeyAssignment) => void;
  handleDelete: (item: ApiKeyAssignment) => void;
  isEditable: boolean;
}

const tableColumns = ({
  handleEdit,
  handleDelete,
  isEditable,
}: TableColumnProps) => [
  {
    title: 'Role',
    index: 'role',
    key: 'role',
    sort: false,
    align: 'left',
    render: (item: ApiKeyAssignment) => <Text>{item.role?.name || 'N/A'}</Text>,
  },
  {
    title: 'API Key',
    index: 'apiKey',
    key: 'apiKey',
    sort: false,
    align: 'left',
    render: (item: ApiKeyAssignment) => (
      <div className={styles.apiKeyCell}>
        <Text>{item.apiKey?.label || 'N/A'}</Text>
      </div>
    ),
  },
  {
    title: 'Actions',
    index: 'actions',
    sort: false,
    key: 'actions',
    align: 'left',
    render: (item: ApiKeyAssignment) => (
      <div className={styles.btnGroup}>
        <div className={styles.editBtn}>
          <Button
            label="Edit"
            type="secondary"
            onClick={() => handleEdit(item)}
            disabled={!isEditable}
          />
        </div>
        <Button
          label="Delete"
          type="destructive"
          onClick={() => handleDelete(item)}
          disabled={!isEditable}
        />
      </div>
    ),
  },
];

const ApiAssignment: React.FC<ApiAssignmentProps> = ({
  vendorId,
  accountId,
  isEditable,
}) => {
  const dispatch = useDispatch<any>();
  const rootState = useSelector((state: RootState) => state || {});
  const { apiAssignment } = rootState;
  const { data, loading, deleteLoading, updateLoading, createLoading } =
    apiAssignment || {};
  const [toasts, setToasts] = useState<ToastItem[]>([]);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [deleteItem, setDeleteItem] = useState<ApiKeyAssignment | null>(null);
  const [formValues, setFormValues] =
    useState<Partial<ApiKeyAssignment> | null>(null);
  const [isApiAssignmentModalOpen, setApiAssignmentModalOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredData, setFilteredData] = useState<ApiKeyAssignment[]>([]);

  const filterAssignments = (
    assignments: ApiKeyAssignment[],
    query: string
  ): ApiKeyAssignment[] => {
    const trimmedQuery = query.trim();
    if (trimmedQuery === '') {
      return assignments;
    }
    const lowercaseQuery = trimmedQuery.toLowerCase();
    return assignments.filter(
      (item) =>
        item.apiKey?.label?.toLowerCase().includes(lowercaseQuery) ||
        item.role?.name?.toLowerCase().includes(lowercaseQuery)
    );
  };

  async function refreshApiAssignments() {
    try {
      await dispatch(
        fetchApiAssignments({
          accountId,
          vendorId,
        })
      ).unwrap();
    } catch (error: any) {
      handleToast(
        `Failed to refresh API assignments: ${error.message}`,
        'error'
      );
    }
  }

  useEffect(() => {
    if (vendorId && accountId) {
      dispatch(
        fetchApiAssignments({
          accountId,
          vendorId,
        })
      ).catch((error: Error) => {
        handleToast(
          `Failed to fetch API assignments: ${error.message}`,
          'error'
        );
      });
    }
  }, [vendorId, accountId, dispatch]);

  useEffect(() => {
    if (data?.resources) {
      setFilteredData(filterAssignments(data.resources, searchQuery));
    }
  }, [data?.resources, searchQuery]);

  const handleToast = (message: string, type: 'success' | 'error') => {
    const newToast: ToastItem = {
      id: Date.now().toString(),
      text: message,
      type,
    };
    setToasts((prev) => [...prev, newToast]);
  };

  useEffect(() => {
    if (!deleteItem || deleteLoading === LOADING_STATUS.LOADING) return;

    const success = deleteLoading === LOADING_STATUS.SUCCEEDED;
    const apiKeyLabel = deleteItem.apiKey?.label || 'API Assignment';

    handleToast(
      success
        ? `Successfully deleted ${apiKeyLabel}`
        : `Failed to delete ${apiKeyLabel}. Please try again.`,
      success ? 'success' : 'error'
    );

    if (success) {
      setDeleteModalOpen(false);
      setDeleteItem(null);
      refreshApiAssignments();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [deleteLoading, accountId, vendorId, dispatch]);

  useEffect(() => {
    if (updateLoading === LOADING_STATUS.LOADING) return;

    if (updateLoading === LOADING_STATUS.SUCCEEDED || updateLoading === LOADING_STATUS.FAILED) {
      const success = updateLoading === LOADING_STATUS.SUCCEEDED;

      handleToast(
        success
          ? 'Successfully updated API Assignment'
          : 'Failed to update API Assignment. Please try again.',
        success ? 'success' : 'error'
      );

      if (success) {
        setApiAssignmentModalOpen(false);
        setFormValues(null);
        refreshApiAssignments();
      }
    }
  }, [updateLoading]);

  useEffect(() => {
    if (createLoading === LOADING_STATUS.LOADING) return;

    if (createLoading === LOADING_STATUS.SUCCEEDED || createLoading === LOADING_STATUS.FAILED) {
      const success = createLoading === LOADING_STATUS.SUCCEEDED;

      handleToast(
        success
          ? 'Successfully created API Assignment'
          : 'Failed to create API Assignment. Please try again.',
        success ? 'success' : 'error'
      );

      if (success) {
        setApiAssignmentModalOpen(false);
        setFormValues(null);
        refreshApiAssignments();
      }
    }
  }, [createLoading]);

  const handleAdd = () => {
    setFormValues(null);
    setApiAssignmentModalOpen(true);
  };

  const handleEdit = (item: ApiKeyAssignment) => {
    setFormValues(item);
    setApiAssignmentModalOpen(true);
  };

  const handleDelete = (item: ApiKeyAssignment) => {
    setDeleteModalOpen(true);
    setDeleteItem(item);
  };

  const handleDeleteConfirm = () => {
    if (!deleteItem) return;
    dispatch(
      deleteApiAssignment({
        vendorId,
        accountId,
        id: deleteItem.id || '',
      })
    );
  };

  const handleModalClose = () => {
    setApiAssignmentModalOpen(false);
    setFormValues(null);
  };

  const handleModalSubmit = (payload: ApiAssignmentPayload) => {
    if (!payload) return;
    if ('id' in payload) {
      dispatch(
        updateApiAssignment({
          vendorId,
          accountId,
          payload,
        })
      );
    } else {
      dispatch(
        createApiAssignment({
          vendorId,
          accountId,
          payload,
        })
      );
    }
    handleModalClose();
  };

  const handleSearch = (value: string) => {
    setSearchQuery(value);
  };

  useEffect(() => () => dispatch(resetLoadingStates()), [dispatch]);

  return (
    <div className={styles.wrapper}>
      <div className={styles.card}>
        <div className={styles.title}>
          <Text type="l" inline emphasized>
            API Assignment Details
          </Text>
        </div>
        <div className={styles.actionWrapper}>
          <SearchInput
            defaultValue=""
            hasLabel={false}
            onChange={handleSearch}
            width={{ width: 300 }}
          />
          <div>
            <Button
              label="Add Assignment"
              type="secondary"
              onClick={handleAdd}
              disabled={!isEditable || loading === LOADING_STATUS.LOADING}
            >
              <sinch-icon-add slot="icon" />
            </Button>
          </div>
        </div>
        <Table
          hasCheckbox={false}
          tableColumns={tableColumns({
            handleEdit,
            handleDelete,
            isEditable,
          })}
          tableData={filteredData || []}
          loading={loading === LOADING_STATUS.LOADING}
          previous={false}
        />
        <DeleteApiAssignmentDialog
          isOpen={deleteModalOpen}
          onClose={() => setDeleteModalOpen(false)}
          onConfirm={handleDeleteConfirm}
          apiKeyLabel={deleteItem?.apiKey?.label}
        />
        <ApiAssignmentModal
          isOpen={isApiAssignmentModalOpen}
          onClose={handleModalClose}
          formValues={formValues}
          onSubmit={handleModalSubmit}
          isLoading={updateLoading === LOADING_STATUS.LOADING}
          vendorId={vendorId}
          accountId={accountId}
        />
        <Toast toasts={toasts} setToasts={setToasts} />
      </div>
    </div>
  );
};

export default ApiAssignment;
