import debounce from 'lodash/debounce';
import { <PERSON>Com<PERSON><PERSON>, Button, Dialog, Select } from 'nectary';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { LOADING_STATUS } from '../../../constants';
import { searchApiAssignment } from '../../../redux/api-settings/api-assignment/api-assignment-slice';
import {
  ApiAssignmentPayload,
  ApiKeyAssignment,
  ROLES_KEY,
  UpdateApiKeyAssignmentsPayload,
} from '../../../types/api-settings/assignments';
import { RootState } from '../../../types/store';
import styles from './api-assignment.module.less';

interface ApiAssignmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  formValues: Partial<ApiKeyAssignment> | null;
  onSubmit: (
    payload: ApiAssignmentPayload | UpdateApiKeyAssignmentsPayload
  ) => void;
  isLoading: boolean;
  vendorId: string;
  accountId: string;
}

const roleOptions = [
  { label: 'USER', value: 'USER' },
  { label: 'ADMIN', value: 'ADMIN' },
];

function isUpdateApiKeyAssignmentsPayload(
  payload: any
): payload is UpdateApiKeyAssignmentsPayload {
  return 'id' in payload;
}

function isCreateApiAssignmentPayload(
  payload: any
): payload is ApiAssignmentPayload {
  return !('id' in payload);
}

export const ApiAssignmentModal: React.FC<ApiAssignmentModalProps> = ({
  isOpen,
  onClose,
  formValues,
  onSubmit,
  isLoading,
  vendorId,
  accountId,
}) => {
  const dispatch = useDispatch<any>();
  const { apiAssignment } = useSelector((state: RootState) => state);
  const { searchData, searchLoading } = apiAssignment || {};
  const { resources: apiAssignmentData } = searchData || {};

  const isEdit = Boolean(formValues?.id);
  const [role, setRole] = useState<ROLES_KEY | ''>('');
  const [apiKey, setApiKey] = useState('');
  const [searchValue, setSearchValue] = useState('');
  const [apiKeyOptions, setApiKeyOptions] = useState<
    Array<{ label: string; value: string }>
  >([]);

  useEffect(() => {
    if (!isOpen) {
      setRole('');
      setApiKey('');
      setSearchValue('');
      setApiKeyOptions([]);
    } else if (formValues) {
      setRole((formValues.role?.name as ROLES_KEY) || '');
      setApiKey(formValues.apiKey?.id || '');
      setSearchValue(formValues.apiKey?.label || '');
    }
  }, [formValues, isOpen]);

  useEffect(() => {
    if (searchLoading === LOADING_STATUS.SUCCEEDED && apiAssignmentData) {
      const options = apiAssignmentData.map(
        (item: { id: string; label?: string }) => ({
          label: item.label || item.id,
          value: item.id,
        })
      );
      setApiKeyOptions(options);
    }
  }, [searchLoading, apiAssignmentData]);

  const debouncedApiKeySearch = debounce((value: string) => {
    if (value.trim().length > 0) {
      dispatch(
        searchApiAssignment({
          vendorId,
          accountId,
          label: value.trim(),
        })
      );
    }
  }, 300);

  const handleApiKeySearch = (value: string) => {
    setSearchValue(value);
    debouncedApiKeySearch(value);
  };

  const handleSubmit = () => {
    if (!role || !apiKey) return;

    const payload = isEdit
      ? { id: formValues?.id || '', role, apiKey }
      : { name: '', role, apiKey };

    if (isUpdateApiKeyAssignmentsPayload(payload)) {
      onSubmit(payload);
    } else if (isCreateApiAssignmentPayload(payload)) {
      onSubmit(payload);
    }
  };

  const title = isEdit
    ? 'Edit API Key Assignment'
    : 'Create API Key Assignment';
  const submitDisabled = !role || !apiKey || isLoading;

  return (
    <Dialog
      isOpen={isOpen}
      onClose={onClose}
      caption={title}
      disableCloseViaBackdrop={isLoading}
    >
      <div className={styles.modalContent}>
        <Select
          label="Role"
          options={roleOptions}
          value={role}
          onSelect={(value: ROLES_KEY) => setRole(value)}
          placeholder="Select a role"
          disabled={isLoading}
          required
        />
        <AutoComplete
          label="API Key"
          placeholder="Search API Key"
          searchValue={searchValue}
          options={apiKeyOptions}
          handleSearch={handleApiKeySearch}
          handleSelect={(option: { label: string; value: string }) => {
            setApiKey(option.value);
            setSearchValue(option.label);
          }}
          disabled={isEdit || isLoading}
          loading={searchLoading === LOADING_STATUS.LOADING}
          required
        />
        <div className={styles.modalActions}>
          <Button
            label="Cancel"
            type="secondary"
            onClick={onClose}
            disabled={isLoading}
          />
          <Button
            label={isLoading ? 'Submitting...' : 'Submit'}
            type="primary"
            onClick={handleSubmit}
            disabled={submitDisabled}
          />
        </div>
      </div>
    </Dialog>
  );
};

export default ApiAssignmentModal;
