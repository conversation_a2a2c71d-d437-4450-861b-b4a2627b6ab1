import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { ROLES_KEY, getPermissionWithKey } from 'helpers';
import _get from 'lodash/get';
import { Tab } from 'nectary';

import HubActivity from './tabs/hub-activity/hub-activity';
import SupportLog from './tabs/support-log/support-log';
import ContactSync from './tabs/contact-sync/contact-sync';
import { RootState } from '../../types/store';

import styles from './activity-log.module.less';

type ActivityLogProp = {
  vendorId: string;
  accountId: string;
  roles: Array<string>;
};

type TabItem = {
  value: number;
  tabName: string;
  text: string;
};

const tabContent = ({
  vendorId,
  accountId,
  isActivityLogVisible,
  isSupportLogVisible,
  isContactSyncVisible,
  hasContactSyncFlag,
}: {
  vendorId: string;
  accountId: string;
  isActivityLogVisible: boolean;
  isSupportLogVisible: boolean;
  isContactSyncVisible: boolean;
  hasContactSyncFlag: boolean;
}) => {
  const tabList = [];
  let tabValue = 1;
  if (isActivityLogVisible) {
    tabList.push({
      value: tabValue,
      content: (
        <HubActivity
          vendorId={vendorId}
          accountId={accountId}
        />
      ),
    });
  }

  if (isSupportLogVisible) {
    tabValue += 1;
    tabList.push({
      value: tabValue,
      content: (
        <SupportLog
          vendorId={vendorId}
          accountId={accountId}
        />
      ),
    });
  }

  if (isContactSyncVisible && hasContactSyncFlag) {
    tabValue += 1;
    tabList.push({
      value: tabValue,
      content: (
        <ContactSync
          vendorId={vendorId}
          accountId={accountId}
        />
      ),
    });
  }

  return tabList;
};

const TABS_INFO = ({
  isActivityLogVisible,
  isSupportLogVisible,
  isContactSyncVisible,
  hasContactSyncFlag,
}: {
  isActivityLogVisible: boolean;
  isSupportLogVisible: boolean;
  isContactSyncVisible: boolean;
  hasContactSyncFlag: boolean;
}) => {
  const tabs = [];
  let index = 1;
  if (isActivityLogVisible) {
    tabs.push({
      value: index,
      tabName: 'activityLog',
      text: 'Hub Activity',
    });
  }

  if (isSupportLogVisible) {
    index += 1;
    tabs.push({
      value: index,
      tabName: 'supportLog',
      text: 'Support Log',
    });
  }

  if (isContactSyncVisible && hasContactSyncFlag) {
    index += 1;
    tabs.push({
      value: index,
      tabName: 'contactSync',
      text: 'Contact Sync',
    });
  }

  return tabs;
};

const SUB_TAB_PARAM = 'subTab';

const getSubTabIndexByName = ({
  subTabName,
  displayedTabs,
}: {
  subTabName: string | null;
  displayedTabs: TabItem[];
}): string => {
  const currentTab = displayedTabs.find(
    (tab: TabItem) => tab.tabName === subTabName
  );
  return currentTab ? currentTab.value.toString() : '1';
};

export function ActivityLog(props: ActivityLogProp) {
  const { vendorId, accountId, roles } = props;
  const rootState = useSelector((state: RootState) => state);
  const features = _get(rootState, 'accountDetailFeatures.data.features', []);
  const hasContactSyncFlag = features.some(
    (feature: any) => feature.name === 'CONTACT_SYNC'
  );

  const [currentSubTab, setCurrentSubTab] = useState('1');

  const queryParams = new URLSearchParams(window.location.search);
  const tabURLParam = queryParams.get(SUB_TAB_PARAM);
  const { isVisible: isActivityLogVisible } = getPermissionWithKey(
    ROLES_KEY.ACTIVITY_LOG,
    roles
  );
  const { isVisible: isContactSyncVisible } = getPermissionWithKey(
    ROLES_KEY.CONTACT_SYNC,
    roles
  );
  const { isVisible: isSupportLogVisible } = getPermissionWithKey(
    ROLES_KEY.SUPPORT_LOG,
    roles
  );

  const displayedTabs = TABS_INFO({
    isActivityLogVisible,
    isSupportLogVisible,
    isContactSyncVisible,
    hasContactSyncFlag,
  });

  const onChangeTab = (newTabVal: string) => {
    setCurrentSubTab(newTabVal);
    const newTab = displayedTabs.find(
      (tab: TabItem) => `${tab.value}` === newTabVal
    );
    if (newTab?.tabName) {
      queryParams.set(SUB_TAB_PARAM, newTab.tabName);
    }
    const newQueryString = queryParams.toString();
    const newUrl = `${window.location.origin}${window.location.pathname}?${newQueryString}`;
    window.history.replaceState({ path: newUrl }, '', newUrl);
  };

  useEffect(() => {
    setCurrentSubTab(
      getSubTabIndexByName({
        displayedTabs,
        subTabName: tabURLParam,
      })
    );
  }, [displayedTabs.length]);

  if (isActivityLogVisible || isContactSyncVisible || isSupportLogVisible) {
    return (
      <div className={styles.wrapper}>
        <div className={styles.leftColumn}>
          <Tab
            currentTab={currentSubTab}
            tabs={displayedTabs}
            tabContent={tabContent({
              vendorId,
              accountId,
              isActivityLogVisible,
              isSupportLogVisible,
              isContactSyncVisible,
              hasContactSyncFlag,
            })}
            onChangeTab={onChangeTab}
          />
        </div>
      </div>
    );
  }
  return <>No permissions</>;
}

export default ActivityLog;
