import _get from 'lodash/get'
import _isString from 'lodash/isString'
import _trim from 'lodash/trim'


const objectMap = {
  file: 'a file',
  url: 'a link for uploaded file',
  'auto payment': 'the Automatic Payment configuration',
  'default payment method': 'the default payment method',
  'payment method': 'a payment method',
  'billing contact': 'the billing information',
  invoice: 'the invoice',
  message: 'a message',
  contacts: 'the contacts',
  broadcast: 'a broadcast',
  user: 'a user',
  'SMS Limit': 'a SMS Limit',
  webhook: 'a webhook',
  feature: 'the account setting',
  'auto top-up': 'the automatic top-up setting',
  'api key': 'an API key',
  'email to sms': 'the email',
  'email domain to sms': 'the domain',
  template: 'a template',
  contact: 'a contact',
  'contact group': 'a contact group',
  'account user': 'the account user',
  'account name': 'the account name',
  'account timezone': 'the account timezone',
  'sub-account': 'a sub account',
  'sub-account name': 'the sub account name',
  numbers: 'a phone number',
}

export const changeObjectTypeIntoGenericPhrase = (objectType) => objectMap[objectType] || objectType

export const absNumberWithCommas = (x) => {
  if (!x) return ''
  return Math.abs(x).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

const PERIOD = { HOURLY: 'hour', DAILY: 'day', MONTHLY: 'month' }

export const map = [
  // integrations log
  {
    predicate: (log) => log?.activity?.objectType === 'modify integration' && log?.activity?.action,
    description: (log) => {
      const platform = (log?.activity?.newValue)?.integration
      const userName = log?.requester?.userName || 'A user'
      if (log.activity.action === 'uninstall integration') {
        return `${userName} has uninstalled ${platform} integration`
      }
      if (log.activity.action === 'update integration') {
        return `${userName} has updated connection details with ${platform} integration`
      }
      return `${userName} has installed ${platform} integration`
    }
  },
  // contact sync log
  {
    predicate: (log) => log?.activity?.objectType === 'contact sync' && log?.activity?.action,
    description: (log) => {
      const platform = (log?.activity?.newValue)?.platform
      const noOfContact = (log?.activity?.newValue)?.noOfContact
      if (log.activity.action === 'started contact sync') {
        return `Started contact sync for ${platform}`
      }
      if (log.activity.action === 'finished contact sync') {
        return `Synced ${noOfContact} contacts from ${platform}`
      }
      return log.activity.action
    },
  },
  // top up log
  {
    predicate: (log) => log?.activity?.objectType === 'credit' && log?.activity?.action === 'requested topup',
    description: (log) => {
      const unit = log?.activity?.newValue?.details?.unit
      const topUpCredits = log?.activity?.newValue?.details?.topUpCredits
      const userName = log?.requester?.userName || 'A user'
      if (unit?.toUpperCase() === 'NUMBER') {
        return `${userName} requested to top up ${absNumberWithCommas(topUpCredits)} credits`
      }
      return `${userName} requested to top up $${absNumberWithCommas(topUpCredits)}`
    },
  },
  {
    predicate: (log) => log?.activity?.objectType === 'credit' && log?.activity?.action === 'requested allocation',
    description: (log) => {
      const topUpCredits = log?.activity?.newValue?.details?.topUpCredits
      const userName = log?.requester?.userName || 'A user'
      const accountName = log?.activity?.newValue?.accountName
      if (!topUpCredits) return ''
      if (topUpCredits > 0) {
        return `${userName} requested ${absNumberWithCommas(topUpCredits)} credits to be allocated to ${accountName}`
      }
      return `${userName} requested ${absNumberWithCommas(topUpCredits)} credits to be deducted from ${accountName}`
    },
  },
  {
    predicate: (log) => log?.activity?.objectType === 'credit' && log?.activity?.action === 'requested transfer',
    description: (log) => {
      const topUpCredits = log?.activity?.newValue?.details?.topUpCredits
      const userName = log?.requester?.userName || 'A user'
      const accountName = log?.activity?.newValue?.accountName
      if (!topUpCredits) return ''
      if (topUpCredits > 0) {
        return `${userName} requested ${absNumberWithCommas(topUpCredits)} credits to be transferred to ${accountName}`
      }
      return `${userName} requested to get ${absNumberWithCommas(topUpCredits)} credits back from ${accountName}`
    },
  },
  {
    predicate: (log) => log?.activity?.objectType === 'credit' && log?.activity?.action === 'topped up',
    description: (log) => {
      const topUpCredits = log?.activity?.newValue?.details?.topUpCredits
      const unit = log?.activity?.newValue?.details?.unit
      const accountId = log?.activity?.newValue?.accountId
      const userId = log?.requester?.userId
      if (!topUpCredits) return ''
      if (accountId === userId && unit?.toUpperCase() === 'NUMBER' && topUpCredits > 0) {
        return `${absNumberWithCommas(topUpCredits)} credits was allocated`
      }
      if (accountId === userId && unit?.toUpperCase() === 'MONEY' && topUpCredits > 0) {
        return `$${absNumberWithCommas(topUpCredits)} was allocated`
      }
      if (accountId === userId && unit?.toUpperCase() === 'NUMBER' && topUpCredits < 0) {
        return `${absNumberWithCommas(topUpCredits)} credits was deducted`
      }
      if (accountId === userId && unit?.toUpperCase() === 'MONEY' && topUpCredits < 0) {
        return `$${absNumberWithCommas(topUpCredits)} was deducted`
      }
      if (accountId !== userId && unit?.toUpperCase() === 'NUMBER' && topUpCredits > 0) {
        const accountName = log?.activity?.newValue?.accountName
        return `${absNumberWithCommas(topUpCredits)} credits allocated to ${accountName}`
      }
      if (accountId !== userId && unit?.toUpperCase() === 'MONEY' && topUpCredits > 0) {
        const accountName = log?.activity?.newValue?.accountName
        return `$${absNumberWithCommas(topUpCredits)} allocated to ${accountName}`
      }
      if (accountId !== userId && unit?.toUpperCase() === 'NUMBER' && topUpCredits < 0) {
        const accountName = log?.activity?.newValue?.accountName
        return `${absNumberWithCommas(topUpCredits)} credits deducted from ${accountName}`
      }
      const accountName = log?.activity?.newValue?.accountName
      return `$${absNumberWithCommas(topUpCredits)} deducted from ${accountName}`
    },
  },
  // Expired event log
  {
    predicate: (log) => log?.activity?.objectType === 'credit' && log?.activity?.action === 'credit expired',
    description: (log) => {
      const expiredCredits = log?.activity?.newValue?.details?.expiredCredits?.MT_SMS
      const unit = log?.activity?.newValue?.details?.unit

      if (!expiredCredits) return ''
      if (unit?.toUpperCase() === 'NUMBER' && expiredCredits > 0) {
        return `${absNumberWithCommas(expiredCredits)} credits expired`

      }

      if (unit?.toUpperCase() === 'MONEY' && expiredCredits > 0) {
        return `$${absNumberWithCommas(expiredCredits)} expired`
      }

      return ''
    },
  },
  // User management
  {
    predicate: (log) => log?.activity?.objectType === 'user' && log?.activity?.action === 'logged in',
    description: (log) => {
      const idp = log?.activity?.newValue?.idp || ''
      const method = log?.activity?.newValue?.method
      if (method) {
        switch (method.toLowerCase()) {
          case 'mfa':
            return `${log?.requester?.userName} logged in with MFA`
          case 'reset_password':
            return `${log?.requester?.userName} reset password`
          case 'switch_account':
            return `${log?.requester?.userName} switch account to ${_get(log, 'activity.newValue.accountName')}`
        }
      }
      // @TODO: missing data for logged in SSO
      return `${log?.requester?.userName} logged in via ${idp ? `SSO (idp: ${idp})` : 'password'}`
    },
  },
  {
    predicate: (log) => log?.activity?.objectType === 'user' && log?.activity?.action === 'invited',
    description: (log) => `${log?.requester?.userName} invited the user ${log?.activity?.objectIdentifier}`,
  },
  {
    predicate: (log) => log?.activity?.objectType === 'account user' && log?.activity?.action === 'deleted',
    description: (log) => `${log?.requester?.userName} deleted the user ${log?.activity?.objectIdentifier} from the account`,
  },
  {
    predicate: (log) => log?.activity?.objectType === 'user' && log?.activity?.action === 'accepted invitation',
    description: (log) => `${log?.requester?.userName} accepted invitation sent to ${log?.activity?.objectIdentifier}`,
  },
  // Account management
  {
    predicate: (log) => log?.activity?.objectType === 'feature' && ['enabled', 'disabled'].includes(log?.activity?.action) && log?.activity?.objectIdentifier === 'social sending',
    description: (log) => `${log?.requester?.userName} ${log?.activity?.action} the social aware sending in account setting`,
  },
  {
    predicate: (log) => log?.activity?.objectType === 'SMS Limit',
    description: (log) => {
      const defaultValue = { threshold: '', period: '' }
      const value = log?.activity?.action === 'deleted' ? _get(log, 'activity.oldValue', defaultValue) : _get(log, 'activity.newValue', defaultValue)

      return `${log?.requester?.userName} ${log?.activity?.action} ${changeObjectTypeIntoGenericPhrase(log?.activity?.objectType)} at ${_get(value, 'threshold', '')} per ${_get(PERIOD, value.period, '')}`
    },
  },
  // Billing log
  {
    predicate: (log) => log?.activity?.objectType === 'default payment method' && log?.activity?.action === 'updated',
    description: (log) => {
      const newValue = _get(log, 'activity.newValue.value', '')

      if (newValue) {
        return `${log?.requester?.userName} set the default payment method to ${newValue}`
      }
      return `${log?.requester?.userName} unset the default payment method`
    },
  },
  {
    predicate: (log) => log?.activity?.objectType === 'payment method' && log?.activity?.action === 'deleted',
    description: (log) => `${log?.requester?.userName} ${log?.activity?.action} ${changeObjectTypeIntoGenericPhrase(log?.activity?.objectType)} ${_get(log, 'activity.oldValue.value', '')}`,
  },
  {
    predicate: (log) => log?.activity?.objectType === 'invoice' && log?.activity?.action === 'paid',
    description: (log) => `${log?.requester?.userName} ${log?.activity?.action} ${changeObjectTypeIntoGenericPhrase(log?.activity?.objectType)} ${log?.activity?.objectIdentifier}`,
  },
  // Contact import
  {
    predicate: (log) => log?.activity?.objectType === 'contacts' && log?.activity?.action === 'imported',
    description: (log) => {
      const subStr = _get(log, 'activity.newValue.unsubscribed') ? ' as unsubscribed' : ''

      return `${log?.requester?.userName} imported ${_get(log, 'activity.newValue.estimatedSize', '')} contacts${subStr}`
    },
  },
  // Contact delete
  {
    predicate: (log) => log?.activity?.objectType === 'contacts' && log?.activity?.action === 'deleted',
    description: (log) => {
      const subStr = _get(log, 'activity.oldValue.params.unsubscribed') ? 'unsubscribed' : 'subscribed'
      const groupName = _get(log, 'activity.oldValue.params.groups[0].name')

      return `${log?.requester?.userName} deleted ${subStr} contacts${groupName ? ` group ${groupName}` : ''}. Job Id: ${_get(log, 'activity.oldValue.jobId', '')}`
    },
  },
  // Contact management log
  {
    predicate: (log) => log?.activity?.objectType === 'contact' && ['deleted', 'updated', 'created'].includes(log?.activity?.action),
    description: (log) => {
      let { action } = log?.activity

      const oldSubscription = _get(log, 'activity.oldValue.unsubscribed')
      const newSubscription = _get(log, 'activity.newValue.unsubscribed')

      if (log?.activity?.action === 'updated' && oldSubscription !== newSubscription) {
        action = newSubscription ? 'unsubscribed' : 'resubscribed'
      }

      const value = `activity.${action === 'deleted' ? 'old' : 'new'}Value`

      const fullName = _trim(`${_get(log, `${value}.firstName`, '')} ${_get(log, `${value}.lastName`, '')}`)

      return `${log?.requester?.userName} ${action} the contact ${fullName || _get(log, `${value}.phoneNumber`, '')}`
    },
  },
  // Contact Group management log
  {
    predicate: (log) => log?.activity?.objectType === 'contact group' && ['created', 'updated', 'deleted', 'shared', 'unshared'].includes(log?.activity?.action),
    description: (log) => {
      if (['created', 'updated', 'deleted'].includes(log?.activity?.action)) {
        const value = `activity.${log?.activity?.action === 'deleted' ? 'old' : 'new'}Value`

        return `${log?.requester?.userName} ${log?.activity?.action} the contact group ${_get(log, `${value}.name`, '')}`
      }

      const value = `activity.${log?.activity?.action === 'unshared' ? 'old' : 'new'}Value`
      const prep = log?.activity?.action === 'unshared' ? 'from' : 'to'

      return `${log?.requester?.userName} ${log?.activity?.action} the contact group ${_get(log, `${value}.contactGroup.name`, '')} ${prep} the account ${_get(log, `${value}.account.label`, '')}`
    },
  },
  // Broadcast log
  {
    predicate: (log) => log?.activity?.objectType === 'broadcast' && ['created', 'updated'].includes(log?.activity?.action),
    description: (log) => {
      if (log?.activity?.action === 'updated') {
        if (_get(log, 'activity.oldValue.status') !== 'CANCELLED' && _get(log, 'activity.newValue.status') === 'CANCELLED') {
          return `${log?.requester?.userName} cancelled the broadcast ${_get(log, 'activity.newValue.name', '')}`
        }

        if (_get(log, 'activity.oldValue.status') !== 'CANCELLED') {
          if (_get(log, 'activity.oldValue.name', '') !== _get(log, 'activity.newValue.name', '')) {
            return `${log?.requester?.userName} updated broadcast "${_get(log, 'activity.oldValue.name', '')}" with a new name "${_get(log, 'activity.newValue.name', '')}"`
          }
          return `${log?.requester?.userName} updated the broadcast ${_get(log, 'activity.oldValue.name', '')}`
        }
      } else {
        return `${log?.requester?.userName} created the broadcast ${_get(log, 'activity.newValue.name', '')}`
      }

      return ''
    },
  },
  // Broadcast file log
  {
    predicate: (log) => log?.activity?.objectType === 'file' && log?.activity?.action === 'uploaded',
    description: (log) => `${log?.requester?.userName} uploaded the file ${_get(log, 'activity.newValue.filename', '')}`,
  },
  // API Management
  {
    predicate: (log) => log?.activity?.objectType === 'api key' && ['created', 'updated', 'deleted'].includes(log?.activity?.action),
    description: (log) => {
      const value = log?.activity?.action === 'deleted' ? 'activity.oldValue' : 'activity.newValue'

      return `${log?.requester?.userName} ${log?.activity?.action} the ${_get(log, `${value}.type`, '')} API key ${_trim(`${_get(log, `${value}.label`, '')} (${_get(log, `${value}.key`, '')}`)})`
    },
  },
  // Template
  {
    predicate: (log) => log?.activity?.objectType === 'template' && ['created', 'updated', 'deleted'].includes(log?.activity?.action),
    description: (log) => {
      const value = log?.activity?.action === 'deleted' ? 'activity.oldValue' : 'activity.newValue'

      return `${log?.requester?.userName} ${log?.activity?.action} the template ${_get(log, `${value}.name`, '')}`
    },
  },
  {
    predicate: (log) => log?.activity?.objectType === 'default template' && ['set', 'unset'].includes(log?.activity?.action),
    description: (log) => {
      if (log?.activity?.action === 'set') {
        return `${log?.requester?.userName} set the template ${_get(log, 'activity.newValue.name', '')} as default`
      }

      return `${log?.requester?.userName} unset the default template ${_get(log, 'activity.oldValue.name', '')}`
    },
  },
  // Email2SMS
  {
    predicate: (log) => ['email to sms', 'email domain to sms'].includes(log?.activity?.objectType) && ['registered', 'revoked'].includes(log?.activity?.action),
    description: (log) => {
      const value = log?.activity?.action === 'registered' ? 'activity.newValue.value' : 'activity.oldValue.value'

      return `${log?.requester?.userName} ${log?.activity?.action} ${changeObjectTypeIntoGenericPhrase(log?.activity?.objectType)} ${_get(log, value, '')} in the Email to SMS settings`
    },
  },
  {
    predicate: (log) => ['email to sms'].includes(log?.activity?.objectType) && ['added domain'].includes(log?.activity?.action),
    description: (log) => {
      if (log?.requester?.userName) {
        return `${log?.requester?.userName} ${log?.activity?.action} "${_get(log, 'activity.newValue.domain', '')}" in the Email to SMS settings`
      }
      return `Support user ${log?.activity?.action} "${_get(log, 'activity.newValue.domain', '')}" in the Email to SMS settings`
    },
  },
  {
    predicate: (log) => ['email to sms'].includes(log?.activity?.objectType) && ['added email'].includes(log?.activity?.action),
    description: (log) => {
      if (log?.requester?.userName) {
        return `${log?.requester?.userName} ${log?.activity?.action} "${_get(log, 'activity.newValue.email', '')}" in the Email to SMS settings`
      }
      return `Support user ${log?.activity?.action} "${_get(log, 'activity.newValue.email', '')}" in the Email to SMS settings`
    },
  },
  {
    predicate: (log) => ['email to sms'].includes(log?.activity?.objectType) && ['removed domain'].includes(log?.activity?.action),
    description: (log) => {
      if (log?.requester?.userName) {
        return `${log?.requester?.userName} ${log?.activity?.action} "${_get(log, 'activity.oldValue.domain.domain', '')}" in the Email to SMS settings`
      }
      return `Support user ${log?.activity?.action} "${_get(log, 'activity.oldValue.domain.domain', '')}" in the Email to SMS settings`
    },
  },
  {
    predicate: (log) => ['email to sms'].includes(log?.activity?.objectType) && ['removed email'].includes(log?.activity?.action),
    description: (log) => {
      if (log?.requester?.userName) {
        return `${log?.requester?.userName} ${log?.activity?.action} "${_get(log, 'activity.oldValue.email.address', '')}" in the Email to SMS settings`
      }
      return `Support user ${log?.activity?.action} "${_get(log, 'activity.oldValue.email.address', '')}" in the Email to SMS settings`
    },
  },
  // Sub-account log
  {
    predicate: (log) => log?.activity?.objectType?.includes('sub-account'),
    description: (log) => {
      const oldValue = _get(log, 'activity.oldValue.name')
      const newValue = _get(log, 'activity.newValue.label')
      const username = _get(log, 'activity.newValue.username')
      const value = _get(log, 'activity.newValue.value')
      if (log?.activity?.action === 'updated') {
        return `${log?.requester?.userName} ${log?.activity?.action} the sub account name from ${oldValue} to ${newValue}`
      }

      return `${log?.requester?.userName} ${log?.activity?.action} the sub account ${newValue || username || value}`
    },
  },
  // Sender IDs
  {
    predicate: (log) => log?.activity?.objectType?.includes('sender'),
    description: (log) => {
      const newValue = _get(log, 'activity.newValue')
      const oldValue = _get(log, 'activity.oldValue')

      const senderType = {
        ALPHANUMERIC: 'business name',
        INTERNATIONAL: 'phone number',
      }

      if (log?.activity?.objectType === 'default sender') {
        const number = _get(log, 'activity.newValue.number', null)
        if (number === null) {
          return `${log?.requester?.userName} set Shared numbers pool as the default sender ID`
        }
        return `${log?.requester?.userName} set ${_get(log, 'activity.newValue.number', '')} as the default sender ID`
      }

      if (log?.activity?.action === 'updated') {
        if (oldValue?.label && !newValue?.label) {
          return `${log?.requester?.userName} updated the phone number ${newValue?.number} and removed the name`
        }
        return `${log?.requester?.userName} updated the phone number ${newValue?.number}, with a new name "${newValue?.label}"`
      } if (log?.activity?.action === 'deleted') {
        return `${log?.requester?.userName} deleted the ${_get(senderType, oldValue?.type, '')} ${oldValue?.number}`
      }
      return `${log?.requester?.userName} added a new ${_get(senderType, newValue?.type, '')} ${_get(log, 'activity.newValue.number')}`
    },
  },
  {
    predicate: (log) => log?.activity?.objectType === 'numbers' && log?.activity?.action === 'assigned number',
    description: (log) => {
      const phoneNumber = _get(log, 'activity.newValue.number')
      const businessName = _get(log, 'activity.newValue.businessName')
      const countries = _get(log, 'activity.newValue.countries', [])
      const useCase = _get(log, 'activity.newValue.useCase')

      if (businessName) {
        const countryText = countries.length > 0 ? ` for ${countries.join(', ')}` : ''
        const useCaseText = useCase ? ` (${useCase})` : ''
        return `${log?.requester?.userName} assigned the business name ${businessName}${countryText}${useCaseText}`
      }

      const fallbackNumber = phoneNumber || _get(log, 'activity.objectIdentifier')
      return `${log?.requester?.userName} assigned the phone number ${fallbackNumber}`
    },
  },
  {
    predicate: (log) => log?.activity?.objectType === 'numbers' && log?.activity?.action === 'verified number',
    description: (log) => {
      const phoneNumber = _get(log, 'activity.newValue.number') || _get(log, 'activity.oldValue.number') || _get(log, 'activity.objectIdentifier')
      return `${log?.requester?.userName} verified the phone number ${phoneNumber}`
    },
  },
  {
    predicate: (log) => log?.activity?.objectType === 'numbers' && log?.activity?.action === 'released number',
    description: (log) => {
      const phoneNumber = _get(log, 'activity.oldValue.number') || _get(log, 'activity.newValue.number') || _get(log, 'activity.objectIdentifier')
      return `${log?.requester?.userName} released the phone number ${phoneNumber}`
    },
  },
  // Multi channel configuration
  {
    predicate: (log) => log?.activity?.objectType === 'multi-channel',
    description: (log) => {
      if (log?.activity?.action?.includes('disconnected')) {
        return `${log?.requester?.userName} disconnected ${log?.activity?.newValue?.channel} from ${log?.activity?.newValue?.accountName}`
      }

      return `${log?.requester?.userName} connected ${log?.activity?.newValue?.channel} to ${log?.activity?.newValue?.accountName}`
    },
  },
  // Mobile Landing Page
  {
    predicate: (log) => log?.activity?.objectType === 'mlp template',
    description: (log) => {
      let actionName = 'created'
      let pageName = log?.activity?.newValue?.landingPageTemplateName
      if (log?.activity?.action === 'updated mlp template') {
        actionName = 'updated'
        pageName = log?.activity?.newValue?.landingPageTemplateName
      } else if (log?.activity?.action === 'removed mlp template') {
        actionName = 'deleted'
        pageName = log?.activity?.oldValue?.name
      }

      return `${log?.requester?.userName} ${actionName} the Mobile Landing Page template: ${pageName}`
    },
  },
  // Generic
  {
    predicate: (log) => log?.requester?.userName && log?.activity?.action && log?.activity?.objectType,
    description: (log) => {
      const oldValue = _get(log, 'activity.oldValue.value')
      const newValue = _get(log, 'activity.newValue.value')

      let value = ''

      if (_isString(oldValue)) { value = oldValue }

      if (!value && _isString(newValue)) {
        value = newValue
      }

      if (_isString(oldValue) && _isString(newValue)) {
        value = `from ${oldValue} to ${newValue}`
      }

      return `${log?.requester?.userName} ${log?.activity?.action} ${changeObjectTypeIntoGenericPhrase(log?.activity?.objectType)} ${_trim(value)}`
    },
  },
]

export const generateActivityLogDescription = (log) => {
  const { description } = map.find((value) => value.predicate(log)) || {}

  return description ? _trim(description(log)) : ''
}

export default generateActivityLogDescription
