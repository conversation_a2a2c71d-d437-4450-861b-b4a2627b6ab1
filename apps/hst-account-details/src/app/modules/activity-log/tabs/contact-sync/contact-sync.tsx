import moment from 'moment';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Button, DateRange, Select, Table, Text } from 'nectary';

import { customDateTimeFormatReadable, isNonEmptyString } from 'helpers';

import { fetchEcosystemHistory } from '../../../../redux/activity-log/activity-log-slice';
import { ContactSyncItem } from '../../../../types/activity-log';
import { RootState } from '../../../../types/store';

import styles from './contact-sync.module.less';

const DEFAULT_PAGE_SIZE = 10;

const tableColumns = () => [
  {
    title: 'Report Name',
    index: 'reportName',
    sort: false,
    align: 'left',
    render: (value: ContactSyncItem) => (
      <a role="button" className={styles.reportDowloadLink} href={value.reportUrl} download>{value.reportName}</a>
    ),
  },
  {
    title: 'Platform',
    index: 'platform',
    sort: false,
    align: 'left',
    render: (value: ContactSyncItem) => <Text type="s">{value.platform}</Text>,
  },
  {
    title: 'Date Created',
    index: 'platform',
    sort: false,
    align: 'left',
    render: (value: ContactSyncItem) => {
      const formatedDate = isNonEmptyString(value.syncAt)
        ? customDateTimeFormatReadable({
            datetime: value.syncAt,
            showTime: false,
          })
        : '-';
      return <Text type="s">{formatedDate}</Text>;
    },
  },
];

export const ALL = 'ALL';

const PLATFORM_OPTIONS = [
  {
    label: ALL,
    value: undefined,
  },
  {
    label: 'BIGCOMMERCE',
    value: 'bigcommerce',
  },
  {
    label: 'HUBSPOT',
    value: 'hubspot',
  },
  {
    label: 'KLAVIYO',
    value: 'klaviyo',
  },
  {
    label: 'MAILCHIMP',
    value: 'mailchimp',
  },
  {
    label: 'MAILJET',
    value: 'mailjet',
  },
  {
    label: 'SHOPIFY',
    value: 'shopify',
  },
];

const ContactSync = ({
  accountId,
  vendorId,
}: {
  accountId: string;
  vendorId: string;
}) => {
  const dispatch = useDispatch();
  const rootState = useSelector((state: RootState) => state);
  const { accountActivityLog } = rootState;
  const timezone = moment.tz.guess()
  const { ecosystemHistory, ecosystemHistoryLoading } =
    accountActivityLog || {};
  const { resources, pagination } = ecosystemHistory || {};
  const [tokenQueue, setTokenQueue] = useState<string[]>([]);
  const [dateRange, setDateRange] = useState([]);
  const [platform, setPlatform] = useState(undefined);
  const [pageSize, setPageSize] = useState<number>(DEFAULT_PAGE_SIZE);
  const { next } = pagination || {};

  const getStartDateStr = (momentVal: string) => {
    if (!momentVal) return undefined;
    return moment(momentVal, 'DD-MM-YYYY').startOf('day').tz(timezone, true).format();
  };

  const getEndDateStr = (momentVal: string) => {
    if (!momentVal) return undefined;
    return moment(momentVal, 'DD-MM-YYYY').endOf('day').tz(timezone, true).format();
  };

  useEffect(() => {
    dispatch(
      fetchEcosystemHistory({
        vendorId,
        accountId,
        size: pageSize,
      })
    );
  }, []);

  const handlePrevious = () => {
    tokenQueue.pop();
    setTokenQueue(tokenQueue);
    const tokensLen = tokenQueue.length;
    const fromDate = getStartDateStr(dateRange?.[0]);
    const toDate = getEndDateStr(dateRange?.[1]);

    setTimeout(() => {
      dispatch(
        fetchEcosystemHistory({
          accountId,
          vendorId,
          size: pageSize,
          fromDate,
          toDate,
          next: tokensLen ? tokenQueue[tokensLen - 1] : undefined,
          platform: platform || undefined,
        })
      );
    });
  };

  const handleNext = (token: string) => {
    tokenQueue.push(token);
    setTokenQueue(tokenQueue);
    const fromDate = getStartDateStr(dateRange?.[0]);
    const toDate = getEndDateStr(dateRange?.[1]);

    setTimeout(() => {
      dispatch(
        fetchEcosystemHistory({
          accountId,
          vendorId,
          size: pageSize,
          fromDate,
          toDate,
          next: token,
          platform: platform || undefined,
        })
      );
    });
  };

  const handleFilter = () => {
    const fromDate = getStartDateStr(dateRange?.[0]);
    const toDate = getEndDateStr(dateRange?.[1]);

    dispatch(
      fetchEcosystemHistory({
        accountId,
        vendorId,
        size: pageSize,
        fromDate,
        toDate,
        platform: platform || undefined,
      })
    );
  };

  const handleResetFilter = () => {
    setPlatform(undefined);
    setDateRange([]);
  };

  const handleChangePageSize = (newSize: number) => {
    setPageSize(newSize);
    dispatch(
      fetchEcosystemHistory({
        accountId,
        vendorId,
        size: newSize,
        fromDate: getStartDateStr(dateRange?.[0]),
        toDate: getEndDateStr(dateRange?.[1]),
        platform: platform || undefined,
      })
    );
  };

  return (
    <div>
      <form className={styles.filterWrapper}>
        <div className={styles.formItem}>
          <DateRange
            value={dateRange}
            label="Date"
            onChange={setDateRange}
            min={moment().subtract('1', 'years').format('YYYY-MM-DD')}
            max={moment().endOf('day').format('YYYY-MM-DD')}
            testId="date-range"
            disabled
          />
        </div>
        <div className={styles.formItem}>
          <Select
            label="Platform"
            value={platform}
            placeholder="All"
            options={PLATFORM_OPTIONS}
            onSelect={setPlatform}
            testId="user-select"
          />
        </div>
        <div className={styles.formItem}>
          <Button label="Apply Filters" onClick={handleFilter} />
        </div>
        <div className={styles.formItem}>
          <Button
            type="secondary"
            label="Clear Filters"
            onClick={handleResetFilter}
          />
        </div>
      </form>
      <div>
        <Table
          hasCheckbox={false}
          tableColumns={tableColumns()}
          tableData={resources || []}
          loading={ecosystemHistoryLoading}
          next={next}
          previous={tokenQueue.length}
          handlePreviousPage={handlePrevious}
          handleNextPage={handleNext}
          pageSize={pageSize}
          handleChangePageSize={handleChangePageSize}
        />
      </div>
    </div>
  );
};

export default ContactSync;
