import _reduce from 'lodash/reduce'
import moment from 'moment'

export const FORMAT = 'YYYY-MM-DD HH:mm:ss'

const INITIAL_PAGE_SIZE = 10
export const ALL = 'All'

export const convertParamsToQueryObject = (params) => _reduce(
  params,
  (obj, value, key) => {
    if (key === 'dateRange') {
      return {
        ...obj,
        startDate: value?.[0],
        endDate: value?.[1],
      }
    }

    if (key === 'activitySelect' && typeof value === 'string') {
      return {
        ...obj,
        objectTypes: value?.split(','),
      }
    }

    if (key === 'userSelect') {
      return {
        ...obj,
        user: value,
      }
    }

    return obj
  },
  {},
)


export const getRequestParams = (values) => {
  if (!values) {
    return {
      size: INITIAL_PAGE_SIZE,
    }
  }

  let params = { ...values }

  if (values.userSelect === ALL) {
    const { userSelect, ...remains } = params

    params = remains
  }

  if (values.activitySelect === ALL) {
    const { activitySelect, ...remains } = params

    params = remains
  }

  const { size = INITIAL_PAGE_SIZE, next, ...remains } = params

  return {
    ...convertParamsToQueryObject(remains),
    size,
    ...(next && { next }),
  }
}


export function getDateTimeParams({
  date,
  timezone,
  isEndDate = false,
}) {
  let dateStringUserSees

  if (isEndDate) {
    dateStringUserSees = date.format('YYYY-MM-DD 23:59:59')
  } else {
    dateStringUserSees = date.format('YYYY-MM-DD 00:00:00')
  }

  const timestampToSubmitInUtc = moment.tz(dateStringUserSees, timezone).utc()
  const now = moment(new Date())

  if (timestampToSubmitInUtc.diff(now) > 0) {
    return now.subtract(2, 'minutes').utc().format(FORMAT)
  }

  return timestampToSubmitInUtc.format(FORMAT)
}


export const formatParams = (timezone, params) => {
  const { dateRange, ...rest } = params
  if (Array.isArray(dateRange)) {
    return {
      dateRange: dateRange.map(
        (d, index) => (typeof d === 'string' ? d : getDateTimeParams({ date: d, timezone, isEndDate: index === 1 })),
      ),
      ...rest,
    }
  }

  return params
}

export const ACTIVITY_TYPES = [{
  name: 'all',
  label: 'All',
  value: ALL,
}, {
  name: 'accountManagement',
  label: 'Account Management',
  value: ['feature', 'SMS Limit', 'account name', 'account timezone', 'sub-account', 'sub-account name'],
}, {
  name: 'userManagement',
  label: 'User Management',
  value: ['user', 'account user'],
}, {
  name: 'messagingBroadcasts',
  label: 'Messaging Broadcasts',
  value: ['broadcast', 'template', 'default template', 'message'],
}, {
  name: 'contactManagement',
  label: 'Contact Management',
  value: ['contact', 'contacts', 'contact group'],
}, {
  name: 'integration',
  label: 'Integration',
  value: ['contact sync', 'modify integration'],
}, {
  name: 'billing',
  label: 'Billing',
  value: ['auto top-up', 'invoice', 'billing contact', 'payment method', 'default payment method', 'auto payment', 'prepaid account', 'credit'],
}, {
  name: 'apiManagement',
  label: 'API Management',
  value: ['api key'],
}, {
  name: 'emailToSMS',
  label: 'Email to SMS',
  value: ['email to sms', 'email domain to sms'],
}, {
  name: 'files',
  label: 'Files',
  value: ['file', 'url'],
}, {
  name: 'template',
  label: 'Template',
  value: ['mlp template']
}, {
  name: 'numbersManagement',
  label: 'Numbers Management',
  value: ['numbers']
}]
