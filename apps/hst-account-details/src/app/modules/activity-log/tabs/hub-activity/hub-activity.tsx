import _compact from 'lodash/compact';
import moment from 'moment';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { RootState } from 'apps/hst-account-details/src/app/types/store';
import { Button, DateRange, Select, Table } from 'nectary';

import {
  fetchHubActivityLog,
  fetchHubActivityLogUsers,
} from '../../../../redux/activity-log/activity-log-slice';

import { generateActivityLogDescription } from '../../helper';
import { ACTIVITY_TYPES, ALL, formatParams, getRequestParams } from './utils';

import styles from './hub-activity.module.less';

const tableColumns = ({ timezone }) => [
  {
    title: 'When',
    index: 'id',
    sort: false,
    align: 'left',
    render: (value) => {
      const timeDisplay = moment(value.timestamp)
        .tz(timezone)
        .format('DD/MM/YYYY [at] hh.mma');
      return <sinch-text type="s">{timeDisplay}</sinch-text>;
    },
  },
  {
    title: 'What happened',
    index: 'id',
    sort: false,
    align: 'left',
    render: (value) => {
      const action = generateActivityLogDescription(value);
      return <sinch-text type="s">{action}</sinch-text>;
    },
  },
];

const DEFAULT_PAGE_SIZE = 10;

export const mapActivity = ({ value, label }) => ({
  value: Array.isArray(value) ? value.join(',') : value,
  key: Array.isArray(value) ? value.join(',') : value,
  label,
});

const HubActivity = ({
  accountId,
  vendorId,
}: {
  accountId: string;
  vendorId: string;
}) => {
  const dispatch = useDispatch();
  const rootState = useSelector((state: RootState) => state);
  const { accountActivityLog } = rootState;
  const timezone = moment.tz.guess();
  const { loading, hubActivityData, hubActivityUsers } =
    accountActivityLog || {};
  const { resources: userResources } = hubActivityUsers || {};
  const { resources, pagination } = hubActivityData || {};
  const { next } = pagination || {};
  const ACTIVITIES = ACTIVITY_TYPES.map(mapActivity).map((item) => item);

  const [dateRange, setDateRange] = useState([]);
  const [userSelect, setUserSelect] = useState(ALL);
  const [activitySelect, setActivitySelect] = useState(ALL);
  const [tokenQueue, setTokenQueue] = useState<string[]>([]);
  const [pageSize, setPageSize] = useState<number>(DEFAULT_PAGE_SIZE);

  const getStartDateStr = (momentVal: string) => {
    if (!momentVal) return undefined;
    return moment(momentVal, 'DD-MM-YYYY')
      ?.startOf('day')
      .format('YYYY-MM-DD 00:00:00');
  };
  const getEndDateStr = (momentVal: string) => {
    if (!momentVal) return undefined;
    const endDate = moment(momentVal, 'DD-MM-YYYY').endOf('day');
    const now = moment(new Date());
    if (endDate.diff(now) > 0) {
      return now.subtract(2, 'minutes').utc().format('YYYY-MM-DD HH:mm:ss');
    }
    return endDate.format('YYYY-MM-DD 23:59:59');
  };

  useEffect(() => {
    dispatch(
      fetchHubActivityLog({
        accountId,
        vendorId,
        size: DEFAULT_PAGE_SIZE,
      })
    );
    dispatch(
      fetchHubActivityLogUsers({
        accountId,
        vendorId,
        size: 100,
      })
    );
  }, []);

  const handleChangeDateRange = (val) => {
    setDateRange(val);
  };

  const handleSelectUser = (val) => {
    setUserSelect(val);
  };

  const handleSelectActivity = (val) => {
    setActivitySelect(val);
  };

  const handleFilter = () => {
    const from = getStartDateStr(dateRange?.[0]);
    const to = getEndDateStr(dateRange?.[1]);

    const params = {
      dateRange: _compact([from, to]),
      userSelect,
      activitySelect,
    };
    const requestParams = getRequestParams(formatParams(timezone, params));
    dispatch(
      fetchHubActivityLog({
        accountId,
        vendorId,
        size: DEFAULT_PAGE_SIZE,
        ...requestParams,
      })
    );
  };

  const handleResetFilter = () => {
    setUserSelect(ALL);
    setActivitySelect(ALL);
    setDateRange([]);
  };

  const handlePrevious = () => {
    tokenQueue.pop();
    setTokenQueue(tokenQueue);
    const tokensLen = tokenQueue.length;
    const from = getStartDateStr(dateRange?.[0]);
    const to = getEndDateStr(dateRange?.[1]);

    const params = {
      dateRange: _compact([from, to]),
      userSelect,
      activitySelect,
    };
    const requestParams = getRequestParams(formatParams(timezone, params));

    setTimeout(() => {
      dispatch(
        fetchHubActivityLog({
          accountId,
          vendorId,
          size: pageSize,
          ...requestParams,
          next: tokensLen ? tokenQueue[tokensLen - 1] : undefined,
        })
      );
    });
  };

  const handleNext = (token: string) => {
    tokenQueue.push(token);
    setTokenQueue(tokenQueue);
    const from = getStartDateStr(dateRange?.[0]);
    const to = getEndDateStr(dateRange?.[1]);

    const params = {
      dateRange: _compact([from, to]),
      userSelect,
      activitySelect,
    };

    const requestParams = getRequestParams(formatParams(timezone, params));

    setTimeout(() => {
      dispatch(
        fetchHubActivityLog({
          accountId,
          vendorId,
          size: pageSize,
          ...requestParams,
          next: token,
        })
      );
    });
  };

  const handleChangePageSize = (size: number) => {
    setPageSize(size);
    dispatch(
      fetchHubActivityLog({
        accountId,
        vendorId,
        size,
      })
    );
  };

  const USERS_OPTIONS = [{ id: ALL, name: ALL }, ...(userResources || [])];

  return (
    <div>
      <div>
        <form className={styles.filterWrapper}>
          <div className={styles.formItem}>
            <DateRange
              value={dateRange}
              label="Date"
              onChange={handleChangeDateRange}
              min={moment().subtract('1', 'years').format('YYYY-MM-DD')}
              max={moment().endOf('day').format('YYYY-MM-DD')}
              testId="date-range"
              disabled
            />
          </div>
          <div className={styles.formItem}>
            <Select
              label="User"
              value={userSelect}
              placeholder="All"
              options={USERS_OPTIONS.map((userItem) => ({
                value: userItem.id,
                key: userItem.id,
                label: userItem.name,
              }))}
              onSelect={handleSelectUser}
              testId="user-select"
            />
          </div>
          <div className={styles.formItem}>
            <Select
              label="Activity"
              value={activitySelect}
              placeholder="All"
              options={ACTIVITIES}
              onSelect={handleSelectActivity}
              testId="activity-select"
              searchable={false}
            />
          </div>
          <div className={styles.formItem}>
            <Button label="Filter Activity Log" onClick={handleFilter} />
          </div>
          <div className={styles.formItem}>
            <Button label="Reset All Filters" onClick={handleResetFilter} />
          </div>
        </form>
      </div>
      <div>
        <Table
          hasCheckbox={false}
          tableColumns={tableColumns({ timezone })}
          tableData={resources || []}
          loading={loading}
          next={next}
          previous={tokenQueue.length}
          handlePreviousPage={handlePrevious}
          handleNextPage={handleNext}
          pageSize={pageSize}
          handleChangePageSize={handleChangePageSize}
          currentPageSize={pageSize}
        />
      </div>
    </div>
  );
};

export default HubActivity;
