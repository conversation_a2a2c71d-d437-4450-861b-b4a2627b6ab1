import moment from 'moment';
import {
  <PERSON><PERSON>,
  <PERSON>box,
  DateRange,
  Dialog,
  MultiSelect,
  Table,
  Tag,
  Text
} from 'nectary';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { fetchSupportLogs } from '../../../../redux/activity-log/activity-log-slice';
import { SupportLogItem } from '../../../../types/activity-log';
import { RootState } from '../../../../types/store';
import { getLogDescription } from './helpers';

import styles from './support-log.module.less';

const HTTP_METHOD_OPTIONS = [
  { value: 'POST', label: 'POST' },
  { value: 'PATCH', label: 'PATCH' },
  { value: 'PUT', label: 'PUT' },
  { value: 'DELETE', label: 'DELETE' },
  { value: 'GET', label: 'GET' },
];

const DEFAULT_PAGE_SIZE = 10;

const tableColumns = ({
  timezone,
  showPayload,
}: {
  timezone: string;
  showPayload: (payload: string) => void;
}) => [
  {
    title: 'User',
    index: 'userName',
    sort: false,
    align: 'left',
    render: (value: SupportLogItem) => <Text type="s">{value.userName}</Text>,
  },
  {
    title: 'Time',
    index: 'logDate',
    sort: false,
    align: 'left',
    render: (value: SupportLogItem) => (
      <Text type="s">
        {moment(value.logDate).tz(timezone).format('DD/MM/YYYY [at] hh.mma')}
      </Text>
    ),
  },
  {
    title: 'Activity',
    index: 'httpMethod',
    sort: false,
    align: 'left',
    render: (value: SupportLogItem) => (
      <div className={styles.shortColumn}>
        <Text type="s">{getLogDescription(value)}</Text>
      </div>
    ),
  },
  {
    title: 'Endpoint',
    index: 'uriPath',
    sort: false,
    align: 'left',
    render: (value: SupportLogItem) => <Text type="s">{value.uriPath}</Text>,
  },
  {
    title: 'Action',
    index: 'httpMethod',
    sort: false,
    align: 'left',
    render: (value: SupportLogItem) => <Text type="s">{value.httpMethod}</Text>,
  },
  {
    title: 'Status',
    index: 'responseCode',
    sort: false,
    align: 'left',
    render: (value: SupportLogItem) => {
      const status = value.responseCode > 300 ? 'FAILED' : 'SUCCESS';
      return {
        FAILED: <Tag key={status} color="light-red" text={status} />,
        SUCCESS: <Tag key={status} color="light-green" text={status} />,
      }[status];
    },
  },
  {
    title: 'Action',
    index: 'httpMethod',
    sort: false,
    align: 'left',
    render: (value: SupportLogItem) =>
      (value.httpMethod === 'POST' ||
        value.httpMethod === 'PATCH' ||
        value.httpMethod === 'PUT') &&
      !!value.payload ? (
        <Button label="View" onClick={() => showPayload(value.payload)} />
      ) : null,
  },
];

const DEFAULT_DATE_RANGE = [
  moment().subtract('7', 'day').format('DD-MM-YYYY'),
  moment().format('DD-MM-YYYY'),
];

const SupportLog = ({
  accountId,
  vendorId,
}: {
  accountId: string;
  vendorId: string;
}) => {
  const dispatch = useDispatch();
  const rootState = useSelector((state: RootState) => state);
  const { accountActivityLog } = rootState;
  const timezone = moment.tz.guess();
  const { supportLogs, supportLogsLoading } = accountActivityLog || {};
  const { resources, pagination } = supportLogs || {};
  const { next } = pagination || {};

  const [dateRange, setDateRange] = useState(DEFAULT_DATE_RANGE);
  const [pageSize, setPageSize] = useState<number>(DEFAULT_PAGE_SIZE);
  const [httpMethod, setHttpMethod] = useState([
    HTTP_METHOD_OPTIONS[0].value,
    HTTP_METHOD_OPTIONS[1].value,
    HTTP_METHOD_OPTIONS[2].value,
    HTTP_METHOD_OPTIONS[3].value,
  ]);
  const [restrictResults, setRestrictResults] = useState(false);
  const [tokenQueue, setTokenQueue] = useState<string[]>([]);
  const [isPayloadModalVisible, setPayloadModalVisible] = useState(false);
  const [payloadContent, setPayloadContent] = useState('');

  const getStartDateStr = (momentVal: string) => {
    if (!momentVal) return moment().subtract('7', 'day').toISOString();
    return moment(momentVal, 'DD-MM-YYYY').startOf('day').toISOString();
  };

  const getEndDateStr = (momentVal: string) => {
    if (!momentVal) return moment().toISOString();
    return moment(momentVal, 'DD-MM-YYYY').endOf('day').toISOString();
  };

  useEffect(() => {
    const fromDate = getStartDateStr(dateRange?.[0]);
    const toDate = getEndDateStr(dateRange?.[1]);
    dispatch(
      fetchSupportLogs({
        accountId,
        vendorId,
        size: pageSize,
        fromDate,
        toDate,
        httpMethod,
      })
    );
  }, []);

  const handlePrevious = () => {
    tokenQueue.pop();
    setTokenQueue(tokenQueue);
    const tokensLen = tokenQueue.length;
    const fromDate = getStartDateStr(dateRange?.[0]);
    const toDate = getEndDateStr(dateRange?.[1]);

    setTimeout(() => {
      dispatch(
        fetchSupportLogs({
          accountId,
          vendorId,
          size: pageSize,
          fromDate,
          toDate,
          next: tokensLen ? tokenQueue[tokensLen - 1] : undefined,
          httpMethod,
          restrictResults
        })
      );
    });
  };

  const handleNext = (token: string) => {
    tokenQueue.push(token);
    setTokenQueue(tokenQueue);
    const fromDate = getStartDateStr(dateRange?.[0]);
    const toDate = getEndDateStr(dateRange?.[1]);

    setTimeout(() => {
      dispatch(
        fetchSupportLogs({
          accountId,
          vendorId,
          size: pageSize,
          fromDate,
          toDate,
          next: token,
          httpMethod,
          restrictResults
        })
      );
    });
  };

  const handleSearch = () => {
    const fromDate = getStartDateStr(dateRange?.[0]);
    const toDate = getEndDateStr(dateRange?.[1]);

    dispatch(
      fetchSupportLogs({
        accountId,
        vendorId,
        size: pageSize,
        fromDate,
        toDate,
        httpMethod,
        restrictResults,
      })
    );
  };

  const showPayload = (payload: string) => {
    setPayloadModalVisible(true);
    setPayloadContent(payload);
  };

  const handleChangePageSize = (newSize: number) => {
    setPageSize(newSize);
    const fromDate = getStartDateStr(dateRange?.[0]);
    const toDate = getEndDateStr(dateRange?.[1]);

    dispatch(
      fetchSupportLogs({
        accountId,
        vendorId,
        size: newSize,
        fromDate,
        toDate,
        httpMethod,
        restrictResults
      })
    );
  };

  return (
    <div>
      <form className={styles.filterWrapper}>
        <div className={styles.formItem}>
          <DateRange
            value={dateRange}
            label="Date"
            onChange={setDateRange}
            min={moment().subtract('1', 'years').format('YYYY-MM-DD')}
            max={moment().endOf('day').format('YYYY-MM-DD')}
            testId="date-range"
            disabled
          />
        </div>
        <div className={styles.formItem}>
          <MultiSelect
            label="Action"
            value={httpMethod}
            onSelect={setHttpMethod}
            options={HTTP_METHOD_OPTIONS}
            rows={7}
            testId="http-method-select"
          />
        </div>
        <div className={styles.formItem}>
          <Checkbox
            key="restrict-results"
            label="Hide Login to Hub"
            value={restrictResults}
            onChange={setRestrictResults}
          />
        </div>
        <div className={styles.formItem}>
          <Button label="Search" onClick={handleSearch} />
        </div>
      </form>
      <div>
        <Table
          hasCheckbox={false}
          tableColumns={tableColumns({ timezone, showPayload })}
          tableData={resources || []}
          loading={supportLogsLoading}
          next={next}
          previous={tokenQueue.length}
          handlePreviousPage={handlePrevious}
          handleNextPage={handleNext}
          pageSize={pageSize}
          handleChangePageSize={handleChangePageSize}
        />
      </div>
      {payloadContent && (
        <Dialog
          isOpen={isPayloadModalVisible}
          caption="API Payload"
          onClose={() => setPayloadModalVisible(false)}
        >
          <div className={styles.jsonView}>
            {JSON.stringify(JSON.parse(payloadContent), null, 4)}
          </div>
        </Dialog>
      )}
    </div>
  );
};

export default SupportLog;
