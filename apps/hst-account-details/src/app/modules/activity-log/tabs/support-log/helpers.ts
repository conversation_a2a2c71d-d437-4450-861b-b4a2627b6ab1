import _get from 'lodash/get';
import { SupportLogItem } from "../../../../types/activity-log";

const SUFFIX_LOG_MAPPING = {
  '/support/jwt/access-token': {
    'POST': 'Get the access-token from the Hub',
  },
  '/support/jwt/login': {
    'POST': 'Get the Hub URL and Login to Hub',
  },
  '/ten-dlc/brand/usecases':{
    'GET': 'Get the list of valid usecases',
  },
  '/ten-dlc/brand':{
    'GET': 'Get the details of a brand',
    'DELETE': 'Delete the brand'
  },
  '/ten-dlc/industries':{
    'GET': 'Get the list of valid industries',
  },
  '/keywords':{
    'GET': 'Get the list of opt-in keywords',
  },
  '/submit':{
    'POST': '10DLC Campaign Submission to TCR',
  },
  '/ten-dlc/campaigns':{
    'GET': 'Get the list of campaigns',
    'PUT': 'Assign the phone numbers for a campaign'
  },
  '/ten-dlc/phones':{
    'GET': 'Get the list of phones',
  },
  '/ten-dlc/application':{
    'GET': 'Get the list of 10DLC applications for the account',
    'PUT': 'Update an application',

  },
  '/ten-dlc/sync':{
    'POST': '10DLC Campaign sync with TCR',
  },
  '/ui-templates': {
    'GET': 'Get the appropriate UI template for the account',
  },
  '/templates': {
    'GET': 'Get the message templates for the account',
  },
  '/support/accounts/search/all': {
    'GET': 'Search the accounts filtered by the specific keyword',
  },
  '/support/accounts/search': {
    'GET': 'Search the accounts filtered by the specific keyword',
  },
  '/users': {
    'GET': 'List all users for the account',
    'PATCH': 'Edit or remove user phone',
  },
  '/users/': {
    'PATCH': 'Edit or remove user phone',
  },
  '/settings/account-statuses': {
    'GET': 'List the account’s status and state',
  },
  '/settings/bulk-update': {
    'POST': 'Bulk update status for an specific account and its sub-accounts',
  },
  '/status-profile': {
    'PATCH': 'Verify an account or Update the account status',
  },
  '/settings/states/reinstate': {
    'PATCH': 'Reinstate a suspended account',
  },
  '/settings/states/suspend': {
    'PATCH': 'Suspend an active account',
  },
  '/settings/detail': {
    'GET': 'List the details of the current account',
    'PATCH': 'Edit the account’s details',
  },
  '/settings/delivery-receipts/enable': {
    'POST': 'Enable the feature Delivery Receipts',
  },
  '/settings/delivery-receipts/disable': {
    'POST': 'Disable the feature Delivery Receipts',
  },
  '/settings/related-accounts':{
    'GET': 'Get the curren status of all the related-account features for an account',
    'PATCH': 'Update the related-account features for an account'
  },
  '/settings/related-accounts/contexts':{
    'GET': 'List all the related-account features'
  },
  '/usage-trackings': {
    'GET': 'List all the usage threshold for the account',
    'POST': 'Add UTT threshold for the account. \n Create an UTT to block sending of an account.',
    'PUT': 'Edit an existing UTT',
    'DELETE': 'Delete an existing UTT',
  },
  '/http-notifications': {
    'GET': 'List all the webhooks for the account',
    'POST': 'Add a webhook for the account',
    'PATCH': 'Edit an existing webhook',
    'DELETE': 'Delete an existing webhook',
  },
  '/provider-weights/': {
    'GET': 'Get the details of a specific provider weight',
    'PATCH': 'Edit the account’s provider weights',
    'DELETE': 'Delete an existing provider weight',
  },
  '/provider-weights': {
    'POST': 'Create new account’s provider weights',
    'GET': 'List all the provider weights for the account',
  },
  '/delivery-properties/': {
    'GET': 'Get the details of a specific delivery property',
    'PATCH': 'Edit a specific delivery property',
    'DELETE': 'Delete a specific delivery property',
  },
  '/delivery-properties': {
    'GET': 'List all the delivery properties for the account',
    'POST': 'Create new account’s delivery properties',
  },
  '/inbound-keywords': {
    'GET': 'List all the inbound keywords for the account',
    'POST': 'Add an inbound keyword for the account',
    'PUT': 'Edit an existing inbound keyword',
    'DELETE': 'Delete an existing inbound keyword',
  },
  '/settings/broadcast-hold-filters-settings': {
    'GET': 'List all the details of the auto-approval settings',
    'PATCH': 'Enable/Disable the settings of broadcast auto-approval',
  },
  '/account-detail/features': {
    'GET': 'List all features for the account',
  },
  '/settings/features': {
    'GET': 'List the valid features and their descriptions',
    'PUT': 'Edit the features for the account',
  },
  '/state-history': {
    'GET': 'List all historical entries of account state',
  },
  '/account-detail/message-usages': {
    'GET': 'Get the message usages of the account',
  },
  '/account-detail/messages': {
    'GET': 'Get the number of inbound/outbound messages for the account',
  },
  '/account-detail/billing-details/invoices': {
    'GET': 'List all the invoices for the account',
  },
  '/postpaid/balance': {
    'GET': 'Get the current balance for a postpaid account',
  },
  '/prepaid/balance': {
    'GET': 'Get the current balance for a prepaid account',
  },
  '/account-detail/billing-details/credits': {
    'POST': 'Add credits for the account',
  },
  '/account-detail/billing-details': {
    'GET': 'Get the billing details of the account',
  },
  '/account-detail/limits': {
    'GET': 'List all the messages limits for the account',
  },
  '/account-detail/sub-accounts': {
    'GET': 'List and filter the sub accounts for the account',
  },
  '/dedicated-numbers': {
    'GET': 'List and filter the dedicated numbers for the account',
  },
  '/channels/details': {
    'GET': 'Collect and list all available social channels for the account',
  },
  '/emails': {
    'GET': 'List all emails to SMS for the account',
  },
  '/email2sms': {
    'GET': 'Get the current Email2SMS settings for the account',
    'PUT': 'Edit the Email2SMS settings',
  },
  '/domains': {
    'GET': 'List all domains for the account',
  },
  '/messages/detail': {
    'GET': 'List the details of all messages for the account',
  },
  '/messages/insights': {
    'GET': 'List the summary of the messages in the specific period of time for the account',
  },
  '/messages/metakeys': {
    'GET': 'List the metakeys of the messages for the account',
  },
  '/messages/broadcasts': {
    'GET': 'List the campaigns for the account',
  },
  '/api-credentials/': {
    'GET': 'Get the details of a specific basic/hmac API authentication',
    'PATCH': 'Edit the existing basic/hmac API key',
    'DELETE': 'Delete the existing basic/hmac API key',
  },
  '/api-credentials': {
    'GET': 'List the basic/hmac API settings for the account',
    'POST': 'Create a basic/hmac API authentication for the account',
  },
  '/activity-log/logs': {
    'GET': 'List all the Hub Activity logs for the account',
  },
  '/support/logs': {
    'GET': 'List all the Support logs for the account',
  },
  '/history': {
    'GET': 'List all the Contact Sync logs for the account',
  },
  '/support/ecosystem/accounts/search': {
    'GET': 'List all the connected integrations for the account',
  },
  '/integrations/support/disconnect': {
    'DELETE': 'Disconnect a specific integration entry',
  },
  '/executions': {
    'GET': 'List all the executions of a specific workflow for the account',
  },
  '/workflows/': {
    'GET': 'Get the details of the workflow',
    'PUT': 'Edit the existing workflow'
  },
  '/workflows': {
    'GET': 'List all the available workflows for the account',
  },
  '/support/broadcast-hold-filters/': {
    'GET': 'List and filter the broadcasts',
    'PATCH': 'Approve/Reject a broadcast',
  },
  '/support/broadcast-hold-filters': {
    'GET': 'List and filter the broadcasts',
  },
  '/preview': {
    'GET': 'List the content for a specific broadcast',
  },
  '/toll-free-numbers/': {
    'GET': 'List all the toll free number requested or assigned',
    'PATCH': 'Update the status of an TFN request - Unverified, Pending or Verified',
  },
  '/support/toll-free-numbers': {
    'GET': 'List all the toll free number requested or assigned',
  },
  '/support/senders/requests/': {
    'GET': 'List all the requests for the sender addresses',
    'PATCH': 'Approve/Reject/Review a Sender ID request',
  },
  '/support/senders/requests': {
    'GET': 'List all the requests for the sender addresses',
  },
  '/support/senders/': {
    'GET': 'List and filter the trusted addresses',
    'DELETE': 'Delete a trusted address',
  },
  '/support/senders': {
    'GET': 'List and filter the trusted addresses',
  },
  '/messages/automated-broadcasts/': {
    'GET': 'Get the details of a specific automated broadcast',
    'PATCH': 'Edit the automated broadcast',
    'POST': 'Create a new automated broadcast',
    'DELETE': 'Delete an automated broadcast',
  },
  '/messages/automated-broadcasts': {
    'GET': 'List and filter the automated broadcasts',
    'POST': 'Create a new automated broadcast',
    'PATCH': 'Edit the automated broadcast',
    'DELETE': 'Delete an automated broadcast',
  },
  '/account-detail': {
    'GET': 'Get all the details about the current account',
  },
  '/settings/provider-weight-types': {
    'GET': 'List the available provider weight types',
  },
  '/settings/address-types': {
    'GET': 'List the available address types',
  },
  '/settings/providers': {
    'GET': 'List the available providers',
  },
  '/settings/carriers': {
    'GET': 'List the available carriers',
  },
  '/settings/countries': {
    'GET': 'List the available countries',
  },
  '/settings/delivery-property-types': {
    'GET': 'List the available delivery property types',
  },
  '/sub-accounts': {
    'GET': 'List and filter the sub accounts for the account',
  },
  '/settings/legacy-credentials': {
    'GET': 'List the legacy API keys for the account',
    'POST': 'Create a Legacy API key for the account',
    'PATCH': 'Edit an existing Legacy API key',
    'DELETE': 'Delete an existing Legacy API key',
  },
  '/settings/legacy-credentials/': {
    'GET': 'Get the details of a specific Legacy API key',
  },
  '/access-controls': {
    'GET': 'List the Access Control settings enabled',
    'PATCH': 'Edit an Access Control setting'
  },
  '/valid-actions': {
    'GET': 'Get the valid actions for a specific Access Control setting',
  },
  '/valid-properties': {
    'GET': 'Get the valid properties for a specific Access Control setting',
  },
  '/access-control-types': {
    'GET': 'List all valid Access Control settings',
  },
}

export const getLogDescription = (logItem: SupportLogItem) => {
  const SUFFIX_LOG_MAPPING_KEYS = Object.keys(SUFFIX_LOG_MAPPING)
  const descriptionExistedInMap = SUFFIX_LOG_MAPPING_KEYS.find((key: string) => logItem.uriPath.includes(key))
  if (descriptionExistedInMap) {
    return _get(SUFFIX_LOG_MAPPING, `${descriptionExistedInMap}.${logItem.httpMethod}`)
  }
  return ''
}
