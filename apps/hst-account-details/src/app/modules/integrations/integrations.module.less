.description {
  margin-top: 8px;
  margin-bottom: 24px;
}

.formWrap {
  min-height: 100px;
  display: flex;
  align-items: flex-start;

  .formItem {
    margin-right: 16px;
  }

  .buttonSearch {
    align-self: flex-start;
    margin-top: 28px;
  }
}

.active {
  align-items: flex-start;
  display: flex;
  justify-content: flex-start;
  sinch-text {
    color: var(--sinch-ref-color-complementary-olive-400);
  }
  --sinch-global-color-icon: var(--sinch-ref-color-complementary-olive-400);
}

.warning {
  align-items: flex-start;
  display: flex;
  justify-content: flex-start;
  sinch-text {
    color: var(--sinch-ref-color-main-honey-400);
  }
  --sinch-global-color-icon: var(--sinch-ref-color-main-honey-400);
}

.inactive {
  align-items: flex-start;
  display: flex;
  justify-content: flex-start;
  sinch-text {
    color: var(--sinch-ref-color-complementary-jasper-400);
  }
  --sinch-global-color-icon: var(--sinch-ref-color-complementary-jasper-400);
}

.modalFormItem {
  margin-bottom: 24px;
}

.link {
  cursor: pointer;
}
