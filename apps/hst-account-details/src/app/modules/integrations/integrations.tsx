import '@nectary/assets/icons/check-circle-outline';
import '@nectary/assets/icons/not-interested';
import '@nectary/assets/icons/warning';
import {
  ROLES_KEY,
  customDateTimeFormatReadable,
  getPermissionWithKey,
  isNonEmptyString,
} from 'helpers';
import _capitalize from 'lodash/capitalize';
import _get from 'lodash/get';
import { Button, Dialog, Input, Link, Table, Tag, Text, Toast } from 'nectary';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';

import {
  ACTIVE,
  INACTIVE,
  LOADING_STATUS,
  ONENZ,
  VODANZ,
} from '../../constants/index';
import {
  deleteIntegrationConnection,
  fetchIntegrations,
} from '../../redux/integrations/integrations-slice';
import { ToastItem } from '../../types';
import { IntegrationItem } from '../../types/integrations';
import { RootState } from '../../types/store';

import { CONNECTION_STATUS } from '../../constants/integration';
import styles from './integrations.module.less';

const tableColumns = ({
  handleShowDisconnectModal,
  isEditable,
  onViewAccount,
}: {
  handleShowDisconnectModal: (item: IntegrationItem) => void;
  isEditable: boolean;
  onViewAccount: (item: IntegrationItem) => void;
}) => [
  {
    title: 'Integration Name',
    index: 'integration',
    sort: false,
    align: 'left',
    render: (value: IntegrationItem) => (
      <Text type="s">{_capitalize(value.integration)}</Text>
    ),
  },
  {
    title: 'Integration ID',
    index: 'integrationId',
    sort: false,
    align: 'left',
    render: (value: IntegrationItem) => (
      <Text type="s">{value.integrationId}</Text>
    ),
  },
  {
    title: 'External ID',
    index: 'integrationId',
    sort: false,
    align: 'left',
    render: (value: IntegrationItem) => {
      if (value.integration === 'shopify') {
        return <Text type="s">{value.extras.storeId}</Text>;
      }
      if (value.integration === 'hubspot') {
        return <Text type="s">{value.extras.portalId}</Text>;
      }
      return null;
    },
  },
  {
    title: 'Created Date',
    index: 'createdAtTimestamp',
    sort: false,
    align: 'left',
    render: (values: IntegrationItem) => {
      const displayedTime = isNonEmptyString(values.createdAtTimestamp)
        ? customDateTimeFormatReadable({
            datetime: values.createdAtTimestamp,
            showTime: true,
          })
        : '-';
      return <Text type="s">{displayedTime}</Text>;
    },
  },
  {
    title: 'Updated Date',
    index: 'updatedAtTimestamp',
    sort: false,
    align: 'left',
    render: (values: IntegrationItem) => {
      const displayedTime = isNonEmptyString(values.updatedAtTimestamp)
        ? customDateTimeFormatReadable({
            datetime: values.updatedAtTimestamp,
            showTime: true,
          })
        : '-';
      return <Text type="s">{displayedTime}</Text>;
    },
  },
  {
    title: 'Connection Status',
    index: 'status',
    sort: false,
    align: 'left',
    render: (value: IntegrationItem) =>
      ({
        [ACTIVE]: (
          <div>
            {value.connectionStatus === CONNECTION_STATUS.PARTIAL_CONNECTED ? (
              <div className={styles.warning}>
                <sinch-icon-warning style={{ marginRight: 2 }} />
                <sinch-text type="s">Connected</sinch-text>
              </div>
            ) : (
              <div className={styles.active}>
                <sinch-icon-check-circle-outline style={{ marginRight: 2 }} />
                <sinch-text type="s">Connected</sinch-text>
              </div>
            )}
          </div>
        ),
        [INACTIVE]: (
          <div className={styles.inactive}>
            <sinch-icon-not-interested style={{ marginRight: 2 }} />
            <sinch-text type="s">Disconnected</sinch-text>
          </div>
        ),
      }[value.status]),
  },
  {
    title: 'Connection Type',
    index: 'connectionType',
    sort: false,
    align: 'left',
    render: (value: IntegrationItem) =>
      value.connectionType && (
        <Tag
          key={value.connectionType}
          color="light-violet"
          text={value.connectionType}
        />
      ),
  },
  {
    title: 'Primary Account',
    index: 'primaryAccount',
    sort: false,
    align: 'left',
    render: (value: IntegrationItem) => {
      if (!value.primaryAccount) return null;
      return (
        <span className={styles.link}>
          <Link
            text={value.primaryAccount}
            preventDefault
            onClick={() => onViewAccount(value)}
          />
        </span>
      );
    },
  },
  {
    title: 'Action',
    index: 'id',
    sort: false,
    align: 'left',
    render: (value: IntegrationItem) =>
      ({
        [ACTIVE]: (
          <span>
            <Button
              disabled={!isEditable || value.integration === 'shopify'}
              label="Disconnect"
              size="s"
              icon={<sinch-icon-not-interested slot="left-icon" />}
              type="destructive"
              onClick={() => handleShowDisconnectModal(value)}
            />
          </span>
        ),
        [INACTIVE]: null,
      }[value.status]),
  },
];

const Integrations = ({
  vendorId,
  accountId,
  roles,
}: {
  vendorId: string;
  accountId: string;
  roles: string[];
}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { accountIntegrations } = useSelector((state: RootState) => state);
  const { loading, data, deleteLoading } = accountIntegrations || {};
  const { resources } = data || {};

  const [deleteItem, setDeleteItem] = useState(null);
  const [isModalVisible, setModelVisible] = useState(false);
  const [deleteText, setDeleteText] = useState('');
  const [toasts, setToasts] = useState<ToastItem[]>([]);

  useEffect(() => {
    dispatch(
      fetchIntegrations({
        vendorId,
        accountId,
      })
    );
  }, []);

  useEffect(() => {
    if (!deleteItem) return;
    let newToasts = toasts;
    if (
      deleteLoading === LOADING_STATUS.SUCCEEDED ||
      deleteLoading === LOADING_STATUS.FAILED
    ) {
      const text =
        deleteLoading === LOADING_STATUS.SUCCEEDED
          ? `Successfully disconnect the integration ${deleteItem.integrationId}`
          : `Failed to disconnect the integration ${deleteItem.integrationId}`;
      newToasts = toasts.concat({
        id: deleteItem.integrationId,
        text,
        type: deleteLoading === LOADING_STATUS.SUCCEEDED ? 'success' : 'error',
      });
      setToasts(newToasts);
      setModelVisible(false);
      setDeleteText('');
      setTimeout(() => {
        dispatch(
          fetchIntegrations({
            accountId,
          })
        );
      }, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [deleteLoading]);

  const handleShowDisconnectModal = (item: IntegrationItem) => {
    setDeleteItem(item);
    setModelVisible(true);
  };

  const handleConfirmDisconnect = () => {
    dispatch(
      deleteIntegrationConnection({
        vendorId,
        accountId,
        integration: deleteItem.integration,
      })
    );
  };

  const { isVisible, isEditable } = getPermissionWithKey(
    ROLES_KEY.ECOSYSTEMS_SUPPORT,
    roles,
    true
  );

  if (!isVisible) {
    return (
      <div>
        <div className={styles.description}>
          <Text type="s">No Permissions</Text>
        </div>
      </div>
    );
  }

  const isDeleting = deleteLoading === LOADING_STATUS.LOADING;

  const onViewAccount = (value: IntegrationItem) => {
    let baseName = _get(value, 'vendorId', '').toLowerCase();
    if (baseName === VODANZ) baseName = ONENZ;
    navigate(`/accounts/${baseName}/${value.primaryAccount}`);
  };

  return (
    <div>
      <div className={styles.description}>
        <Text type="s">
          Here, you can find all ecosystem integrations that are connected to
          this account. Please direct the customer to the self-service
          integration pages to add new integrations.
        </Text>
      </div>
      <div>
        <Table
          hasCheckbox={false}
          tableColumns={tableColumns({
            handleShowDisconnectModal,
            isEditable,
            onViewAccount,
          })}
          tableData={resources || []}
          loading={loading}
          hidePagination
        />
      </div>
      <Dialog
        isOpen={isModalVisible}
        caption="Disconnect"
        onClose={() => {
          setModelVisible(false);
          setDeleteText('');
        }}
      >
        <div className={styles.modalFormItem}>
          <span>
            <Text>
              Are you sure you want to disconnect the integration{' '}
              <b>{deleteItem?.integrationId}</b>?
            </Text>
          </span>
        </div>
        <div className={styles.modalFormItem}>
          <Input
            label="Please type disconnect"
            value={deleteText}
            onChange={(val: string) => setDeleteText(val)}
          />
        </div>
        <div className={styles.modalFormItem}>
          <Button
            label="Confirm"
            disabled={deleteText.toLowerCase() !== 'disconnect'}
            onClick={handleConfirmDisconnect}
            icon={isDeleting ? <sinch-spinner slot="icon" /> : null}
          />
        </div>
      </Dialog>
      <Toast toasts={toasts} setToasts={setToasts} />
    </div>
  );
};

export default Integrations;
