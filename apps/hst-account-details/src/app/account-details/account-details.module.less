.wrapper {
  padding: 30px;
}

.info {
  margin: 20px 0;
  > span {
    margin-right: 20px;
  }
}

.header {
  display: flex;
  justify-content: space-between;
}

.backButton {
  margin-right: 15px;
}

.tag {
  display: inline-block;
  margin-left: 4px;
}

// .verified {
//   display: inline;
//   sinch-text {
//     color: var(--sinch-ref-color-complementary-olive-400);
//     display: inline;
//   }
//   --sinch-global-color-icon: var(--sinch-ref-color-complementary-olive-400);
// }

// .unverified {
//   display: inline;
//   sinch-text {
//     color: var(--sinch-ref-color-complementary-jasper-400);
//     display: inline;
//   }
//   --sinch-global-color-icon: var(--sinch-ref-color-complementary-jasper-400);
// }

.accessRow {
  display: inline-flex;
  flex-direction: row;
  align-items: center;
}

.unrestricted {
  align-items: center;
  display: flex;
  justify-content: flex-start;
  sinch-text {
    color: var(--sinch-ref-color-complementary-olive-400);
  }
  --sinch-global-color-icon: var(--sinch-ref-color-complementary-olive-400);
}

.restricted {
  align-items: center;
  display: flex;
  justify-content: flex-start;
  sinch-text {
    color: var(--sinch-ref-color-complementary-jasper-400);
  }
  --sinch-global-color-icon: var(--sinch-ref-color-complementary-jasper-400);
}

.link {
  cursor: pointer;
}
