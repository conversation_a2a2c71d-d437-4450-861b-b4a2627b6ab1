/* eslint-disable @nx/enforce-module-boundaries */
import '@nectary/assets/icons/content-copy';
import '@nectary/components/button';
import '@nectary/components/icon';
import { SupportSelectAccountModal } from 'components';
import { isFeatureBranch, msalInstance, haveImpersonationRole } from 'helpers';
import _get from 'lodash/get';
import { Button, Tab, Tag, Text, Title } from 'nectary';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';

import { getAuthAzure } from '../../../../hst-core/src/redux/auth-azure/auth-selectors';
import { TenDlcRegistration } from '../../../../hst-ten-dlc/src/app/registration/view/registration';
import {
  ACTIVE,
  CANCELLED,
  DORMANT,
  LOADING_STATUS,
  RESTRICTED,
  SUSPENDED,
  UNRESTRICTED,
  UNVERIFIED,
  VENDOR_ID_MAPPING,
  VERIFIED,
} from '../constants';
import { ACCOUNT_UI_TYPE } from '../constants/account-types';
import { TABS_INFO } from '../constants/tabs';
import ActivityLog from '../modules/activity-log/activity-log';
import APISettings from '../modules/api-settings/api-settings';
import Channels from '../modules/channels/channels';
import Features from '../modules/features/features';
import Integrations from '../modules/integrations/integrations';
import Messages from '../modules/messages/messages';
import Settings from '../modules/settings/settings';
import Summary from '../modules/summary/summary';
import Users from '../modules/users/users';
import {
  fetchAccountDetails,
  fetchAccountUITempate,
} from '../redux/summary/account-details-slice';
import { fetchFeatures } from '../redux/summary/features-slice';
import { RootState } from '../types/store';

import SubAccounts from '../modules/sub-accounts/sub-accounts';
import WorkflowsTable from '../modules/workflows/workflows';
import styles from './account-details.module.less';

type Props = {
  roles: Array<string>;
};

const tabContent = (
  vendorId: string,
  accountId: string,
  roles: Array<string>
) => [
  {
    value: 1,
    content: (
      <Summary vendorId={vendorId} accountId={accountId} roles={roles} />
    ),
  },
  {
    value: 2,
    content: (
      <Settings vendorId={vendorId} accountId={accountId} roles={roles} />
    ),
  },
  {
    value: 3,
    content: (
      <SubAccounts vendorId={vendorId} accountId={accountId} roles={roles} />
    ),
  },
  {
    value: 4,
    content: <Users accountId={accountId} vendorId={vendorId} roles={roles} />,
  },
  // {
  //   value: 5,
  //   content: 'Stay tuned for upcoming enhancements on this page',
  // },
  // {
  //   value: 6,
  //   content: 'Stay tuned for upcoming enhancements on this page',
  // },
  {
    value: 5,
    content: (
      <Features vendorId={vendorId} accountId={accountId} roles={roles} />
    ),
  },
  {
    value: 6,
    content: <TenDlcRegistration roles={roles} />,
  },
  {
    value: 7,
    content: (
      <Channels accountId={accountId} vendorId={vendorId} roles={roles} />
    ),
  },
  {
    value: 8,
    content: (
      <Messages accountId={accountId} vendorId={vendorId} roles={roles} />
    ),
  },
  {
    value: 9,
    content: (
      <APISettings vendorId={vendorId} accountId={accountId} roles={roles} />
    ),
  },
  {
    value: 10,
    content: (
      <ActivityLog vendorId={vendorId} accountId={accountId} roles={roles} />
    ),
  },
  {
    value: 11,
    content: (
      <Integrations vendorId={vendorId} accountId={accountId} roles={roles} />
    ),
  },
  {
    value: 12,
    content: (
      <WorkflowsTable vendorId={vendorId} accountId={accountId} roles={roles} />
    ),
  },
];

function AccountDetails(props: Props) {
  const { roles } = props;
  const navigate = useNavigate();
  const location = useLocation();
  const { pathname } = window.location;
  const { from } = location.state || {};
  const pathItems = pathname.split('/');
  const queryParams = new URLSearchParams(window.location.search);
  const TABS_INFO_ARRAY = Object.values(TABS_INFO);

  const getTabByName = (tabName: string | null) =>
    TABS_INFO_ARRAY.find((t) =>
      t.text
        .toLowerCase()
        .replace(/\s+/g, '-')
        .includes(tabName?.toLowerCase() || '')
    );

  const getTabIndexByName = (tabName: string | null): string => {
    const tab = getTabByName(tabName);
    return tab ? tab.value.toString() : TABS_INFO_ARRAY[0].value.toString();
  };

  const pathTabName = pathItems[pathItems.length - 1];
  const tabURLParam = queryParams.get('tab') || pathTabName;
  const [currentTab, setCurrentTab] = useState(getTabIndexByName(tabURLParam));

  const dispatch = useDispatch();
  const rootState: RootState = useSelector((state) => state || {});
  const { accountDetails } = rootState;
  const { details, template, detailsLoading } = accountDetails || {};
  const [loginAs, setLoginAs] = useState({
    account: {},
    isAccountSelectionModalOpen: false,
  });
  const authAzure = useSelector(getAuthAzure);
  const activeAccount = msalInstance.getActiveAccount();
  const username = authAzure ? `${_get(activeAccount, 'username')}` : '';

  if (detailsLoading === LOADING_STATUS.FAILED) {
    navigate('/404');
  }

  const handleUpdateURL = (queryString: string) => {
    const tabExists = getTabByName(pathTabName);
    const cleanPath = tabExists
      ? pathItems.slice(0, -1).join('/')
      : pathItems.join('/');
    const newurl = `${window.location.protocol}//${window.location.host}${cleanPath}?${queryString}`;
    window.history.replaceState({ path: newurl }, '', newurl);
  };

  const handleSetURLParams = (param: string, value: string) => {
    queryParams.set(param, value);
    queryParams.delete('subTab');
    handleUpdateURL(queryParams.toString());
  };

  const onChangeTab = (tab: string) => {
    const tabName = TABS_INFO_ARRAY.find((t) => t.value.toString() === tab)
      ?.text.toLowerCase()
      .replace(/\s+/g, '-');
    if (tabName) {
      handleSetURLParams('tab', tabName);
      setCurrentTab(tab);
    }
  };

  const vendorName = isFeatureBranch() ? pathItems?.[4] : pathItems?.[2];
  const vendorId = _get(VENDOR_ID_MAPPING, vendorName, null);
  const accountId = isFeatureBranch() ? pathItems?.[5] : pathItems?.[3];

  if (!vendorId) {
    navigate('/404');
  }

  useEffect(() => {
    dispatch(
      fetchAccountUITempate({
        vendorId,
        accountId,
      })
    );
    dispatch(
      fetchAccountDetails({
        vendorId,
        accountId,
      })
    );
    dispatch(
      fetchFeatures({
        vendorId,
        accountId,
      })
    );
    // Trigger a re-render
    setCurrentTab(getTabIndexByName(tabURLParam));
  }, [vendorId, accountId, dispatch]);

  useEffect(() => {
    const tab = getTabByName(pathTabName);
    if (!queryParams.get('tab') && !tab) {
      handleSetURLParams(
        'tab',
        TABS_INFO_ARRAY[0].text.toLowerCase().replace(/\s+/g, '-')
      );
    } else if (tab) {
      handleSetURLParams('tab', pathTabName);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const renderStatus = (status: string) =>
    ({
      [ACTIVE]: <Tag key={status} color="light-green" text={status} />,
      [SUSPENDED]: <Tag key={status} color="light-orange" text={status} />,
      [CANCELLED]: <Tag key={status} color="light-red" text={status} />,
      [DORMANT]: <Tag key={status} color="light-violet" text={status} />,
    }[status]);

  const renderVerificationStatus = (verificationStatus: string) =>
    ({
      [VERIFIED]: (
        <Tag
          key={verificationStatus}
          color="light-green"
          text={verificationStatus}
        />
      ),
      [UNVERIFIED]: (
        <Tag
          key={verificationStatus}
          color="light-red"
          text={verificationStatus}
        />
      ),
    }[verificationStatus]);

  if (template !== ACCOUNT_UI_TYPE.SinchEngage) {
    return (
      <div className={styles.wrapper}>
        <div className={styles.header}>
          <Title type="l" text="Account Details Page" />
        </div>
      </div>
    );
  }

  const renderAccess = (restrictedAccess: string | undefined) =>
    restrictedAccess === RESTRICTED ? (
      <div className={styles.restricted}>
        <sinch-icon-not-interested style={{ marginRight: 2 }} />
        <sinch-text type="s">{RESTRICTED}</sinch-text>
      </div>
    ) : (
      <div className={styles.unrestricted}>
        <sinch-icon-check-circle-outline style={{ marginRight: 2 }} />
        <sinch-text type="s">{UNRESTRICTED}</sinch-text>
      </div>
    );

  const renderSupportTier = (tierVal?: string) => {
    if (!tierVal) return '-';
    const tierText = tierVal.split(' ')[0];
    return (
      <Tag key={tierText} color="light-green" text={tierText.toUpperCase()} />
    );
  };

  const handleBackBtn = () => {
    const { state } = location;
    const { from } = state || {};
    if (from === 'support') navigate(-1);
    else if (from === 'subaccountsdetails') navigate(-1);
    else navigate('/accounts', { replace: true });
  };

  const handleLoginAs = () => {
    setLoginAs({
      account: { accountName: details?.label, accountId, vendorId },
      isAccountSelectionModalOpen: true,
    });
  };

  const closeLoginAsModal = () => {
    setLoginAs({ ...loginAs, isAccountSelectionModalOpen: false });
  };

  const openLoginAsModal = () => {
    setLoginAs({ ...loginAs, isAccountSelectionModalOpen: true });
  };

  const havingImpersonationRole = haveImpersonationRole(roles, true);

  return (
    <div className={styles.wrapper}>
      <div className={styles.header}>
        <Title type="l" text={details?.label} />
        <div>
          <span className={styles.backButton}>
            <Button
              type="subtle-primary"
              label={
                from === 'subaccountsdetails'
                  ? 'Back to Parent Account'
                  : 'Back to Account Search'
              }
              onClick={handleBackBtn}
            />
          </span>
          <span>
            <Button
              label="Login to the Hub"
              type="secondary"
              onClick={handleLoginAs}
              disabled={!havingImpersonationRole}
            />
          </span>
        </div>
      </div>
      <div className={styles.info}>
        <span>
          Brand:{' '}
          <Text type="s" inline emphasized>
            {details?.vendorId}
          </Text>
        </span>
        <span>
          Account ID:{' '}
          <Text type="s" inline emphasized>
            {details?.accountId}
          </Text>
          <sinch-button
            size="s"
            aria-label="copy-button"
            onClick={() => {
              navigator.clipboard.writeText(details?.accountId || '');
            }}
          >
            <sinch-icon-content-copy slot="icon" />
          </sinch-button>
        </span>
        <span>
          Support Tier:{' '}
          <span className={styles.tag}>
            {renderSupportTier(details?.resources?.supportTier)}
          </span>
        </span>
        <span>
          Status:{' '}
          <span className={styles.tag}>
            {renderStatus(details?.status || '')}
          </span>
        </span>
        <span>
          Verification:{' '}
          <span className={styles.tag}>
            {renderVerificationStatus(
              details?.resources?.verificationStatus || ''
            )}
          </span>
        </span>
        <span className={styles.accessRow}>
          Access:{' '}
          <span className={styles.tag}>
            {renderAccess(details?.resources?.restrictedAccess)}
          </span>
        </span>
      </div>
      <div className={styles.tab}>
        <Tab
          currentTab={currentTab}
          tabs={TABS_INFO_ARRAY}
          tabContent={tabContent(vendorId, accountId, roles)}
          onChangeTab={onChangeTab}
        />
      </div>
      <SupportSelectAccountModal
        isVisible={loginAs.isAccountSelectionModalOpen}
        openModal={openLoginAsModal}
        closeModal={closeLoginAsModal}
        account={loginAs.account}
        userEmail={username}
      />
    </div>
  );
}

export default AccountDetails;
