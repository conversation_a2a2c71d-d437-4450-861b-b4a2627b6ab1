import moment from 'moment-timezone';
import { Button, Input, Select } from 'nectary';
import React from 'react';
import COUNTRY_OPTIONS from '../../constants/country-options';
import styles from './editable-field.module.less';

export const FIELD_TYPE = {
  NAME: 'accountName',
  ACCOUNT_ID: 'accountId',
  COUNTRY: 'operatingCountry',
  TIMEZONE: 'timezone',
  STATUS: 'status',
  TEXT: 'text',
  DEFAULT_TRAFFIC_CLASS: 'defaultTrafficClass',
  TYPE: 'type',
  BILLING_TYPE: 'billingType',
} as const;

export type FieldType = (typeof FIELD_TYPE)[keyof typeof FIELD_TYPE];

interface SelectOption {
  value: string;
  label: string;
}

interface EditableFieldProps {
  type: FieldType;
  defaultValue: string | boolean;
  errorText?: string;
  loading: boolean;
  handleCancel: (type: FieldType, isEdit: boolean) => void;
  handleChangeValue: (type: FieldType, value: string | boolean) => void;
  handleOk: () => void;
  testId?: string;
}

const TIMEZONE_OPTIONS: SelectOption[] = moment.tz.names().map((timezone) => ({
  value: timezone,
  label: timezone,
}));

const STATUS_OPTIONS: SelectOption[] = [
  { value: 'ACTIVE', label: 'Active' },
  { value: 'SUSPENDED', label: 'Suspended' },
  { value: 'CANCELLED', label: 'Cancelled' },
];

const TYPE_OPTIONS: SelectOption[] = [
  { value: 'NORMAL', label: 'Normal' },
  { value: 'PARTNER', label: 'Partner' },
  { value: 'INTERNAL', label: 'Internal' },
  { value: 'SUB', label: 'Sub' },
];

const BILLING_TYPE_OPTIONS: SelectOption[] = [
  { value: 'PREPAID', label: 'Prepaid' },
  { value: 'POSTPAID', label: 'Postpaid' },
  { value: 'PARENT_ALLOCATED', label: 'Parent Allocated' },
  { value: 'PREPAID_MONEY', label: 'Prepaid Money' },
];

const TRAFFIC_CLASS_OPTIONS: SelectOption[] = [
  { value: 'HIGH', label: 'High' },
  { value: 'LOW', label: 'Low' },
  { value: 'NORMAL', label: 'Normal' },
  { value: 'VERY_HIGH', label: 'Very High' },
  { value: 'VERY_LOW', label: 'Very Low' },
];

const LABELS = {
  SAVE: 'Save',
  CANCEL: 'Cancel',
} as const;

function EditableField({
  type,
  defaultValue,
  loading = false,
  handleCancel,
  handleChangeValue,
  handleOk,
  errorText = '',
  testId,
}: EditableFieldProps): JSX.Element {
  const handleInputChange = (e: any) => {
    handleChangeValue(type, e);
  };

  const handleSelectChange = (value: string) => {
    handleChangeValue(type, value);
  };

  const renderSelect = (options: SelectOption[], menuHeight?: number) => (
    <Select
      value={defaultValue as string}
      defaultValue={defaultValue as string}
      options={options}
      onSelect={handleSelectChange}
      errorText={errorText}
      menuStyles={
        menuHeight ? { height: menuHeight, overflow: 'scroll' } : undefined
      }
    />
  );

  const renderInput = () => (
    <Input
      defaultValue={defaultValue as string}
      onChange={handleInputChange}
      errorText={errorText}
      testId={testId}
    />
  );

  const renderField = (): JSX.Element => {
    switch (type) {
      case FIELD_TYPE.COUNTRY:
        return renderSelect(COUNTRY_OPTIONS, 400);
      case FIELD_TYPE.TIMEZONE:
        return renderSelect(TIMEZONE_OPTIONS, 350);
      case FIELD_TYPE.STATUS:
        return renderSelect(STATUS_OPTIONS);
      case FIELD_TYPE.TYPE:
        return renderSelect(TYPE_OPTIONS);
      case FIELD_TYPE.BILLING_TYPE:
        return renderSelect(BILLING_TYPE_OPTIONS);
      case FIELD_TYPE.DEFAULT_TRAFFIC_CLASS:
        return renderSelect(TRAFFIC_CLASS_OPTIONS);
      default:
        return renderInput();
    }
  };

  return (
    <div className={styles.wrapper}>
      {renderField()}
      <div className={styles.buttonWrapper}>
        <Button
          label={LABELS.SAVE}
          type="primary"
          disabled={loading}
          onClick={handleOk}
          icon={loading ? <sinch-spinner slot="icon" /> : null}
        />
        <Button
          label={LABELS.CANCEL}
          type="secondary"
          disabled={loading}
          onClick={() => handleCancel(type, false)}
        />
      </div>
    </div>
  );
}

export default EditableField;
