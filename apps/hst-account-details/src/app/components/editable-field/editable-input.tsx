import { useState } from 'react';
import { Input, Button, Text } from 'nectary';

import styles from './editable-input.module.less';

type EditableInput = {
  title: string;
  value: string;
  errorText: string;
  loading: boolean;
  handleChangeValue: (value: string) => void;
  handleCancel: () => void;
  handleSave: () => void;
  testId?: string;
  isEditing: boolean;
  setIsEditing: (isEditing: boolean) => void;
};

export const EditableInput = ({
  title,
  value,
  loading,
  handleChangeValue,
  handleCancel,
  handleSave,
  errorText,
  testId,
  isEditing,
  setIsEditing,
}: EditableInput) => (
  <div className={styles.row}>
    <div className={styles.left}>
      <Text type="s" inline>
        {title}
      </Text>
    </div>
    <div className={styles.right}>
      {isEditing ? (
        <div className={styles.row}>
          <Input
            value={value}
            onChange={handleChangeValue}
            errorText={errorText}
            testId={testId}
          />
          <div className={styles.buttonWrapper}>
            <Button
              label="Save"
              type="primary"
              disabled={loading}
              onClick={handleSave}
              icon={loading ? <sinch-spinner slot="icon" /> : null}
            />
            <Button
              label="Cancel"
              type="secondary"
              disabled={loading}
              onClick={() => {
                setIsEditing(false);
                handleCancel();
              }}
            />
          </div>
        </div>
      ) : (
        <div>
          <span className={styles.value}>
            <Text type="s" inline emphasized>
              {value || '-'}
            </Text>
          </span>
          <Button
            className={styles.iconEdit}
            type="subtle-secondary"
            onClick={() => setIsEditing(true)}
            icon={<sinch-icon-edit slot="icon" />}
          />
        </div>
      )}
    </div>
  </div>
);

export default EditableInput;
