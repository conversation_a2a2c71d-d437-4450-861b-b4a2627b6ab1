import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { LOADING_STATUS } from '../../../constants/index';
import {
  WebhooksType,
  FetchWebhooksParams,
  DeleteWebhooksParams,
  UpdateWebhooksParams,
} from '../../../types/api-settings/webhooks';
import WebhooksApi from './webhooks';

export interface WebhooksState {
  data: WebhooksType | null;
  loading: string;
  deleteLoading: string;
  createLoading: string;
  updateLoading: string;
}

const initialState: WebhooksState = {
  data: null,
  loading: LOADING_STATUS.NOT_LOADED,
  deleteLoading: LOADING_STATUS.NOT_LOADED,
  createLoading: LOADING_STATUS.NOT_LOADED,
  updateLoading: LOADING_STATUS.NOT_LOADED,
};

export const fetchWebhooks = createAsyncThunk(
  'webhooks/fetchWebhooks',
  async (params: FetchWebhooksParams, thunkAPI) => {
    const response = await WebhooksApi.loadWebhooks(params);
    return response;
  }
);

export const deleteWebhooks = createAsyncThunk(
  'webhooks/deleteWebhooks',
  async (params: DeleteWebhooksParams, thunkAPI) => {
    const response = await WebhooksApi.deleteWebhooks(params);
    return response;
  }
);

export const updateWebhooks = createAsyncThunk(
  'webhooks/updateWebhooks',
  async (params: UpdateWebhooksParams, thunkAPI) => {
    const response = await WebhooksApi.patchWebhooks(params);
    return response;
  }
);

export const createWebhooks = createAsyncThunk(
  'webhooks/createWebhooks',
  async (params: UpdateWebhooksParams, thunkAPI) => {
    const response = await WebhooksApi.postWebhooks(params);
    return response;
  }
);

export const WebhooksSlice = createSlice({
  name: 'webhooks',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    // fetchWebhooks
    builder.addCase(
      fetchWebhooks.fulfilled,
      (state: WebhooksState, action) => ({
        ...state,
        data: action.payload,
        loading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(fetchWebhooks.pending, (state: WebhooksState) => ({
      ...state,
      loading: LOADING_STATUS.LOADING,
    }));
    builder.addCase(fetchWebhooks.rejected, (state: WebhooksState) => ({
      ...state,
      loading: LOADING_STATUS.FAILED,
    }));
    // deleteWebhooks
    builder.addCase(
      deleteWebhooks.fulfilled,
      (state: WebhooksState, action) => ({
        ...state,
        deleteLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(deleteWebhooks.pending, (state: WebhooksState) => ({
      ...state,
      deleteLoading: LOADING_STATUS.LOADING,
    }));
    builder.addCase(deleteWebhooks.rejected, (state: WebhooksState) => ({
      ...state,
      deleteLoading: LOADING_STATUS.FAILED,
    }));
    // createWebhooks
    builder.addCase(
      createWebhooks.fulfilled,
      (state: WebhooksState, action) => ({
        ...state,
        createLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(createWebhooks.pending, (state: WebhooksState) => ({
      ...state,
      createLoading: LOADING_STATUS.LOADING,
    }));
    builder.addCase(createWebhooks.rejected, (state: WebhooksState) => ({
      ...state,
      createLoading: LOADING_STATUS.FAILED,
    }));
    // updateWebhooks
    builder.addCase(
      updateWebhooks.fulfilled,
      (state: WebhooksState, action) => ({
        ...state,
        updateLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(updateWebhooks.pending, (state: WebhooksState) => ({
      ...state,
      updateLoading: LOADING_STATUS.LOADING,
    }));
    builder.addCase(updateWebhooks.rejected, (state: WebhooksState) => ({
      ...state,
      updateLoading: LOADING_STATUS.FAILED,
    }));
  },
});

export default WebhooksSlice.reducer;
