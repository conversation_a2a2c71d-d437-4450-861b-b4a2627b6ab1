import { fetchInstance, paramsToQueryString } from 'helpers';
import { handleAPI } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import { FetchWebhooksParams, DeleteWebhooksParams, UpdateWebhooksParams } from '../../../types/api-settings/webhooks';

export default {
  loadWebhooks: handleAPI(async (params: FetchWebhooksParams) => {
    const { vendorId, accountId, size, next } = params;
    const queryString = paramsToQueryString({ params: { size, next } });
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/http-notifications?${queryString}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
  deleteWebhooks: handleAPI(async (params: DeleteWebhooksParams) => {
    const { vendorId, accountId, webhookId } = params;
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/http-notifications/${webhookId}`,
      {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
  patchWebhooks: handleAPI(async (params: UpdateWebhooksParams) => {
    const { vendorId, accountId, payload } = params;
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/http-notifications/${payload?.id}`,
      {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(payload),
      }
    );
  }),
  postWebhooks: handleAPI(async (params: UpdateWebhooksParams) => {
    const { vendorId, accountId, payload } = params;
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/http-notifications`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(payload),
      }
    );
  }),
};
