import { fetchInstance, paramsToQueryString } from 'helpers';
import { handleAPI } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import {
  FetchBasicAuthenticationParams,
  UpdateBasicAuthenticationParams,
  DeleteBasicAuthenticationParams,
} from '../../../types/api-settings';

export default {
  loadBasicAuthentication: handleAPI(
    async (params: FetchBasicAuthenticationParams) => {
      const { vendorId, accountId, size, next, type, label } = params;
      const queryString = paramsToQueryString({
        params: { size, next, type, label },
      });
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/api-credentials?${queryString}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),
  updateBasicAuthentication: handleAPI(
    async (params: UpdateBasicAuthenticationParams) => {
      const { vendorId, accountId, body, type } = params;
      const queryString = paramsToQueryString({ params: { type } });
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/api-credentials/${body.id}?${queryString}`,
        {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify(body),
        }
      );
    }
  ),
  createBasicAuthentication: handleAPI(
    async (params: UpdateBasicAuthenticationParams) => {
      const { vendorId, accountId, body, type } = params;
      const { label } = body;
      const queryString = paramsToQueryString({ params: { type } });
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/api-credentials?${queryString}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({ label, type }),
        }
      );
    }
  ),
  deleteBasicAuthentication: handleAPI(
    async (params: DeleteBasicAuthenticationParams) => {
      const { vendorId, accountId, id, type } = params;
      const queryString = paramsToQueryString({ params: { type } });
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/api-credentials/${id}?${queryString}`,
        {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),
};
