import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { LOADING_STATUS } from '../../../constants/index';
import {
  BasicAuthenticationType,
  FetchBasicAuthenticationParams,
  UpdateBasicAuthenticationParams,
  DeleteBasicAuthenticationParams,
} from '../../../types/api-settings';
import BasicAuthenticationApi from './basic-authentication';

export interface BasicAuthenticationState {
  loading: string;
  updateLoading: string;
  createLoading: string;
  deleteLoading: string;
  data: BasicAuthenticationType | null;
  createdData: BasicAuthenticationType | null;
}

const initialState: BasicAuthenticationState = {
  loading: LOADING_STATUS.NOT_LOADED,
  updateLoading: LOADING_STATUS.NOT_LOADED,
  createLoading: LOADING_STATUS.NOT_LOADED,
  deleteLoading: LOADING_STATUS.NOT_LOADED,
  data: null,
  createdData: null,
};

export const fetchBasicAuthentication = createAsyncThunk(
  'basicAuthentication/fetchBasicAuthentication',
  async (params: FetchBasicAuthenticationParams, thunkAPI) => {
    const response = await BasicAuthenticationApi.loadBasicAuthentication(
      params
    );
    return response;
  }
);

export const updateBasicAuthentication = createAsyncThunk(
  'basicAuthentication/updateBasicAuthentication',
  async (params: UpdateBasicAuthenticationParams, thunkAPI) => {
    const response = await BasicAuthenticationApi.updateBasicAuthentication(
      params
    );
    return response;
  }
);

export const createBasicAuthentication = createAsyncThunk(
  'basicAuthentication/createBasicAuthentication',
  async (params: UpdateBasicAuthenticationParams, thunkAPI) => {
    const response = await BasicAuthenticationApi.createBasicAuthentication(
      params
    );
    return response;
  }
);

export const deleteBasicAuthentication = createAsyncThunk(
  'basicAuthentication/deleteBasicAuthentication',
  async (params: DeleteBasicAuthenticationParams, thunkAPI) => {
    const response = await BasicAuthenticationApi.deleteBasicAuthentication(
      params
    );
    return response;
  }
);

export const basicAuthenticationSlice = createSlice({
  name: 'basicAuthentication',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    // fetchBasicAuthentication
    builder.addCase(
      fetchBasicAuthentication.fulfilled,
      (state: BasicAuthenticationState, action) => ({
        ...state,
        data: action.payload,
        loading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchBasicAuthentication.pending,
      (state: BasicAuthenticationState) => ({
        ...state,
        loading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchBasicAuthentication.rejected,
      (state: BasicAuthenticationState) => ({
        ...state,
        loading: LOADING_STATUS.FAILED,
      })
    );
    // updateBasicAuthentication
    builder.addCase(
      updateBasicAuthentication.fulfilled,
      (state: BasicAuthenticationState, action) => ({
        ...state,
        updateLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      updateBasicAuthentication.pending,
      (state: BasicAuthenticationState) => ({
        ...state,
        updateLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      updateBasicAuthentication.rejected,
      (state: BasicAuthenticationState) => ({
        ...state,
        updateLoading: LOADING_STATUS.FAILED,
      })
    );
    // createBasicAuthentication
    builder.addCase(
      createBasicAuthentication.fulfilled,
      (state: BasicAuthenticationState, action) => ({
        ...state,
        createdData: action.payload,
        createLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      createBasicAuthentication.pending,
      (state: BasicAuthenticationState) => ({
        ...state,
        createLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      createBasicAuthentication.rejected,
      (state: BasicAuthenticationState) => ({
        ...state,
        createLoading: LOADING_STATUS.FAILED,
      })
    );
    // deleteBasicAuthentication
    builder.addCase(
      deleteBasicAuthentication.fulfilled,
      (state: BasicAuthenticationState, action) => ({
        ...state,
        deleteLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      deleteBasicAuthentication.pending,
      (state: BasicAuthenticationState) => ({
        ...state,
        deleteLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      deleteBasicAuthentication.rejected,
      (state: BasicAuthenticationState) => ({
        ...state,
        deleteLoading: LOADING_STATUS.FAILED,
      })
    );
  },
});

export default basicAuthenticationSlice.reducer;
