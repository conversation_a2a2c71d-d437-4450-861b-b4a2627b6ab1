import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { LOADING_STATUS } from '../../../constants/index';
import {
  HmacAuthenticationItem,
  HmacAuthenticationType,
  FetchHmacAuthenticationParams,
  UpdateHmacAuthenticationParams,
  DeleteHmacAuthenticationParams,
} from '../../../types/api-settings/hmac';
import HmacAuthentication<PERSON><PERSON> from './hmac-authentication';

export interface HmacAuthenticationState {
  loading: string;
  updateLoading: string;
  createLoading: string;
  deleteLoading: string;
  data: HmacAuthenticationType | null;
  createdData: HmacAuthenticationItem | null;
}

const initialState: HmacAuthenticationState = {
  loading: LOADING_STATUS.NOT_LOADED,
  updateLoading: LOADING_STATUS.NOT_LOADED,
  createLoading: LOADING_STATUS.NOT_LOADED,
  deleteLoading: LOADING_STATUS.NOT_LOADED,
  data: null,
  createdData: null,
};

export const fetchHmacAuthentication = createAsyncThunk(
  'hmacAuthentication/fetchHmacAuthentication',
  async (params: FetchHmacAuthenticationParams, thunkAPI) => {
    const response = await HmacAuthenticationApi.loadHmacAuthentication(
      params
    );
    return response;
  }
);

export const updateHmacAuthentication = createAsyncThunk(
  'hmacAuthentication/updateHmacAuthentication',
  async (params: UpdateHmacAuthenticationParams, thunkAPI) => {
    const response = await HmacAuthenticationApi.updateHmacAuthentication(
      params
    );
    return response;
  }
);

export const createHmacAuthentication = createAsyncThunk(
  'hmacAuthentication/createHmacAuthentication',
  async (params: UpdateHmacAuthenticationParams, thunkAPI) => {
    const response = await HmacAuthenticationApi.createHmacAuthentication(
      params
    );
    return response;
  }
);

export const deleteHmacAuthentication = createAsyncThunk(
  'hmacAuthentication/deleteHmacAuthentication',
  async (params: DeleteHmacAuthenticationParams, thunkAPI) => {
    const response = await HmacAuthenticationApi.deleteHmacAuthentication(
      params
    );
    return response;
  }
);

export const HmacAuthenticationSlice = createSlice({
  name: 'HmacAuthentication',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    // fetchHmacAuthentication
    builder.addCase(
      fetchHmacAuthentication.fulfilled,
      (state: HmacAuthenticationState, action) => ({
        ...state,
        data: action.payload,
        loading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchHmacAuthentication.pending,
      (state: HmacAuthenticationState) => ({
        ...state,
        loading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchHmacAuthentication.rejected,
      (state: HmacAuthenticationState) => ({
        ...state,
        loading: LOADING_STATUS.FAILED,
      })
    );
    // updateHmacAuthentication
    builder.addCase(
      updateHmacAuthentication.fulfilled,
      (state: HmacAuthenticationState, action) => ({
        ...state,
        updateLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      updateHmacAuthentication.pending,
      (state: HmacAuthenticationState) => ({
        ...state,
        updateLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      updateHmacAuthentication.rejected,
      (state: HmacAuthenticationState) => ({
        ...state,
        updateLoading: LOADING_STATUS.FAILED,
      })
    );
    // createHmacAuthentication
    builder.addCase(
      createHmacAuthentication.fulfilled,
      (state: HmacAuthenticationState, action) => ({
        ...state,
        createdData: action.payload,
        createLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      createHmacAuthentication.pending,
      (state: HmacAuthenticationState) => ({
        ...state,
        createLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      createHmacAuthentication.rejected,
      (state: HmacAuthenticationState) => ({
        ...state,
        createLoading: LOADING_STATUS.FAILED,
      })
    );
    // deleteHmacAuthentication
    builder.addCase(
      deleteHmacAuthentication.fulfilled,
      (state: HmacAuthenticationState, action) => ({
        ...state,
        deleteLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      deleteHmacAuthentication.pending,
      (state: HmacAuthenticationState) => ({
        ...state,
        deleteLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      deleteHmacAuthentication.rejected,
      (state: HmacAuthenticationState) => ({
        ...state,
        deleteLoading: LOADING_STATUS.FAILED,
      })
    );
  },
});

export default HmacAuthenticationSlice.reducer;
