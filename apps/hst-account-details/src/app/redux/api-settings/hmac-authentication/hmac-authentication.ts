import { fetchInstance, paramsToQueryString } from 'helpers';
import { handleAPI } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import {
  FetchHmacAuthenticationParams,
  UpdateHmacAuthenticationParams,
  DeleteHmacAuthenticationParams,
} from '../../../types/api-settings/hmac';

export default {
  loadHmacAuthentication: handleAPI(
    async (params: FetchHmacAuthenticationParams) => {
      const { vendorId, accountId, size, next, type, label } = params;
      const queryString = paramsToQueryString({ params: { size, next, type, label } });
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/api-credentials?${queryString}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),
  updateHmacAuthentication: handleAPI(
    async (params: UpdateHmacAuthenticationParams) => {
      const { vendorId, accountId, body, type } = params;
      const queryString = paramsToQueryString({ params: { type } });
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/api-credentials/${body.id}?${queryString}`,
        {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify(body),
        }
      );
    }
  ),
  createHmacAuthentication: handleAPI(
    async (params: UpdateHmacAuthenticationParams) => {
      const { vendorId, accountId, body, type } = params;
      const { label } = body;
      const queryString = paramsToQueryString({ params: { type } });
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/api-credentials?${queryString}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({ label, type }),
        }
      );
    }
  ),
  deleteHmacAuthentication: handleAPI(
    async (params: DeleteHmacAuthenticationParams) => {
      const { vendorId, accountId, id, type } = params;
      const queryString = paramsToQueryString({ params: { type } });
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/api-credentials/${id}?${queryString}`,
        {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),
};
