import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { LOADING_STATUS } from '../../../constants/index';
import {
  ApiAssignmentPayload,
  UpdateApiKeyAssignmentsPayload,
} from '../../../types/api-settings/assignments';
import ApiAssignmentApi from './api-assignment-api';

export interface ApiAssignmentState {
  loading: string;
  updateLoading: string;
  createLoading: string;
  deleteLoading: string;
  searchLoading: string;
  data: UpdateApiKeyAssignmentsPayload | null;
  searchData: any | null;
}

const initialState: ApiAssignmentState = {
  loading: LOADING_STATUS.NOT_LOADED,
  updateLoading: LOADING_STATUS.NOT_LOADED,
  createLoading: LOADING_STATUS.NOT_LOADED,
  deleteLoading: LOADING_STATUS.NOT_LOADED,
  searchLoading: LOADING_STATUS.NOT_LOADED,
  data: null,
  searchData: null,
};

export const fetchApiAssignments = createAsyncThunk(
  'apiAssignment/fetchApiAssignments',
  async (params: { vendorId: string; accountId: string }, thunkAPI) => {
    const response = await ApiAssignmentApi.loadAccountDetailsApiKeyAssignments(
      params
    );
    return response;
  }
);

export const createApiAssignment = createAsyncThunk(
  'apiAssignment/createApiAssignment',
  async (
    params: {
      vendorId: string;
      accountId: string;
      payload: ApiAssignmentPayload;
    },
    thunkAPI
  ) => {
    const response =
      await ApiAssignmentApi.createAccountDetailsApiKeyAssignment(params);
    return response;
  }
);

export const updateApiAssignment = createAsyncThunk(
  'apiAssignment/updateApiAssignment',
  async (
    params: {
      vendorId: string;
      accountId: string;
      payload: UpdateApiKeyAssignmentsPayload;
    },
    thunkAPI
  ) => {
    const response =
      await ApiAssignmentApi.updateAccountDetailsApiKeyAssignment(params);
    return response;
  }
);

export const deleteApiAssignment = createAsyncThunk(
  'apiAssignment/deleteApiAssignment',
  async (
    params: { vendorId: string; accountId: string; id: string },
    thunkAPI
  ) => {
    const response =
      await ApiAssignmentApi.deleteAccountDetailsApiKeyAssignment(params);
    return response;
  }
);

export const searchApiAssignment = createAsyncThunk<
  { data: Array<{ id: string; label?: string }> },
  { vendorId: string; accountId: string; label: string }
>('apiAssignment/searchApiAssignment', async (params, thunkAPI) => {
  const response = await ApiAssignmentApi.searchAccountDetailsApiKeys(params);
  return response;
});

export const ApiAssignmentSlice = createSlice({
  name: 'ApiAssignment',
  initialState,
  reducers: {
    resetLoadingStates: (state) => ({
      ...state,
      updateLoading: LOADING_STATUS.NOT_LOADED,
      createLoading: LOADING_STATUS.NOT_LOADED,
      deleteLoading: LOADING_STATUS.NOT_LOADED,
    }),
  },
  extraReducers: (builder) => {
    // fetchApiAssignments
    builder.addCase(
      fetchApiAssignments.fulfilled,
      (state: ApiAssignmentState, action) => ({
        ...state,
        data: action.payload,
        loading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchApiAssignments.pending,
      (state: ApiAssignmentState) => ({
        ...state,
        loading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchApiAssignments.rejected,
      (state: ApiAssignmentState) => ({
        ...state,
        loading: LOADING_STATUS.FAILED,
      })
    );
    // updateApiAssignment
    builder.addCase(
      updateApiAssignment.fulfilled,
      (state: ApiAssignmentState, action) => ({
        ...state,
        updateLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      updateApiAssignment.pending,
      (state: ApiAssignmentState) => ({
        ...state,
        updateLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      updateApiAssignment.rejected,
      (state: ApiAssignmentState) => ({
        ...state,
        updateLoading: LOADING_STATUS.FAILED,
      })
    );
    // createApiAssignment
    builder.addCase(
      createApiAssignment.fulfilled,
      (state: ApiAssignmentState, action) => ({
        ...state,
        createLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      createApiAssignment.pending,
      (state: ApiAssignmentState) => ({
        ...state,
        createLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      createApiAssignment.rejected,
      (state: ApiAssignmentState) => ({
        ...state,
        createLoading: LOADING_STATUS.FAILED,
      })
    );
    // deleteApiAssignment
    builder.addCase(
      deleteApiAssignment.fulfilled,
      (state: ApiAssignmentState, action) => ({
        ...state,
        deleteLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      deleteApiAssignment.pending,
      (state: ApiAssignmentState) => ({
        ...state,
        deleteLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      deleteApiAssignment.rejected,
      (state: ApiAssignmentState) => ({
        ...state,
        deleteLoading: LOADING_STATUS.FAILED,
      })
    );
    // searchApiAssignment
    builder.addCase(
      searchApiAssignment.fulfilled,
      (state: ApiAssignmentState, action) => ({
        ...state,
        searchData: action.payload,
        searchLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      searchApiAssignment.pending,
      (state: ApiAssignmentState) => ({
        ...state,
        searchLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      searchApiAssignment.rejected,
      (state: ApiAssignmentState) => ({
        ...state,
        searchLoading: LOADING_STATUS.FAILED,
      })
    );
  },
});

export const { resetLoadingStates } = ApiAssignmentSlice.actions;

export default ApiAssignmentSlice.reducer;
