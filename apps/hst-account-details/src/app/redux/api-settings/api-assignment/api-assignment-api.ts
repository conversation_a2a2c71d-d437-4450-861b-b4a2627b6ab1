import { handleAPI } from '@sinch-smb/dev-utils';
import paramsToQueryString from 'apps/hst-core/src/apis/helpers/params-to-querystring';
import fetchInstance from 'apps/hst-core/src/apis/utils/helpers';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import { UpdateApiKeyAssignmentsPayload, ApiAssignmentPayload } from '../../../types/api-settings/assignments';

export default {
  loadAccountDetailsApiKeyAssignments: handleAPI(
    async (params: { vendorId: string; accountId: string }) => {
      const { vendorId, accountId } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/api-keys/assignments`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),

  createAccountDetailsApiKeyAssignment: handleAPI(
    async (params: {
      vendorId: string;
      accountId: string;
      payload: ApiAssignmentPayload;
    }) => {
      const { vendorId, accountId, payload } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/api-keys/assignments`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify(payload),
        }
      );
    }
  ),

  updateAccountDetailsApiKeyAssignment: handleAPI(
    async (params: {
      vendorId: string;
      accountId: string;
      payload: UpdateApiKeyAssignmentsPayload;
    }) => {
      const { vendorId, accountId, payload } = params;
      const { id } = payload;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/api-keys/assignments/${id}`,
        {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify(payload),
        }
      );
    }
  ),

  deleteAccountDetailsApiKeyAssignment: handleAPI(
    async (params: { vendorId: string; accountId: string; id: string }) => {
      const { vendorId, accountId, id } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/api-keys/assignments/${id}`,
        {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),

  searchAccountDetailsApiKeys: handleAPI(
    async (params: { vendorId: string; accountId: string; label?: string }) => {
      const { vendorId, accountId, label } = params;
      const queryString = paramsToQueryString({ params: { label } });
      const url = `${
        Endpoint.SUPPORT_V2_API_URL
      }/v3/support/accounts/${vendorId}:${accountId}/settings/api-keys/search${
        queryString ? `?${queryString}` : ''
      }`;

      return fetchInstance()(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });
    }
  ),
};
