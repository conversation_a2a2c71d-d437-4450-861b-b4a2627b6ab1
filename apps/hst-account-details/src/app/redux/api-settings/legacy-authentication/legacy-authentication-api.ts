import { fetchInstance, paramsToQueryString } from 'helpers';
import { handleAPI } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import {
  CreateLegacyCredentialsParams,
  UpdateLegacyCredentialsParams,
  DeleteLegacyCredentialsParams,
  FetchLegacyCredentialsParams,
  FetchSingleLegacyCredentialParams
} from '../../../types/api-settings/legacy';

export default {
  loadLegacyCredentials: handleAPI(
    async (params: FetchLegacyCredentialsParams) => {
      const { vendorId, accountId, username, size, next } = params;
      const queryString = paramsToQueryString({ params: { size, next, username } });
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/legacy-credentials?${queryString}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),

  fetchSingleLegacyCredential: handleAPI(
    async (params: FetchSingleLegacyCredentialParams) => {
      const { vendorId, accountId, credentialId } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/legacy-credentials/${credentialId}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),

  createLegacyCredentials: handleAPI(
    async (params: CreateLegacyCredentialsParams) => {
      const { vendorId, accountId, username, password } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/legacy-credentials`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({ username, password }),
        }
      );
    }
  ),

  updateLegacyCredentials: handleAPI(
    async (params: UpdateLegacyCredentialsParams) => {
      const { vendorId, accountId, credentialId, password } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/legacy-credentials/${credentialId}`,
        {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({ password }),
        }
      );
    }
  ),

  deleteLegacyCredentials: handleAPI(
    async (params: DeleteLegacyCredentialsParams) => {
      const { vendorId, accountId, credentialId } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/legacy-credentials/${credentialId}`,
        {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),
};
