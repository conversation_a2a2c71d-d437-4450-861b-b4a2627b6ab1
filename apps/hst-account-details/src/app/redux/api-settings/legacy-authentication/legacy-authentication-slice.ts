import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { LOADING_STATUS } from '../../../constants/index';
import {
  LegacyCredentialsItem,
  LegacyCredentialsType,
  CreateLegacyCredentialsParams,
  UpdateLegacyCredentialsParams,
  DeleteLegacyCredentialsParams,
  FetchLegacyCredentialsParams,
  FetchSingleLegacyCredentialParams,
} from '../../../types/api-settings/legacy';
import LegacyAuthenticationApi from './legacy-authentication-api';

export interface LegacyAuthenticationState {
  loading: string;
  createLoading: string;
  updateLoading: string;
  deleteLoading: string;
  fetchSingleLoading: string;
  data: LegacyCredentialsType | null;
  createdData: LegacyCredentialsItem | null;
  singleData: LegacyCredentialsItem | null;
}

const initialState: LegacyAuthenticationState = {
  loading: LOADING_STATUS.NOT_LOADED,
  createLoading: LOADING_STATUS.NOT_LOADED,
  updateLoading: LOADING_STATUS.NOT_LOADED,
  deleteLoading: LOADING_STATUS.NOT_LOADED,
  fetchSingleLoading: LOADING_STATUS.NOT_LOADED,
  data: null,
  createdData: null,
  singleData: null,
};

export const loadLegacyCredentials = createAsyncThunk(
  'legacyAuthentication/loadLegacyCredentials',
  async (params: FetchLegacyCredentialsParams, thunkAPI) => {
    const response = await LegacyAuthenticationApi.loadLegacyCredentials(
      params
    );
    return response;
  }
);

export const fetchSingleLegacyCredential = createAsyncThunk(
  'legacyAuthentication/fetchSingleLegacyCredential',
  async (params: FetchSingleLegacyCredentialParams, thunkAPI) => {
    const response = await LegacyAuthenticationApi.fetchSingleLegacyCredential(
      params
    );
    return response;
  }
);

export const createLegacyCredentials = createAsyncThunk(
  'legacyAuthentication/createLegacyCredentials',
  async (params: CreateLegacyCredentialsParams, thunkAPI) => {
    const response = await LegacyAuthenticationApi.createLegacyCredentials(
      params
    );
    return response;
  }
);

export const updateLegacyCredentials = createAsyncThunk(
  'legacyAuthentication/updateLegacyCredentials',
  async (params: UpdateLegacyCredentialsParams, thunkAPI) => {
    const response = await LegacyAuthenticationApi.updateLegacyCredentials(
      params
    );
    return response;
  }
);

export const deleteLegacyCredentials = createAsyncThunk(
  'legacyAuthentication/deleteLegacyCredentials',
  async (params: DeleteLegacyCredentialsParams, thunkAPI) => {
    const response = await LegacyAuthenticationApi.deleteLegacyCredentials(
      params
    );
    return response;
  }
);

export const LegacyAuthenticationSlice = createSlice({
  name: 'LegacyAuthentication',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    // loadLegacyCredentials
    builder.addCase(
      loadLegacyCredentials.fulfilled,
      (state: LegacyAuthenticationState, action) => ({
        ...state,
        data: action.payload,
        loading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      loadLegacyCredentials.pending,
      (state: LegacyAuthenticationState) => ({
        ...state,
        loading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      loadLegacyCredentials.rejected,
      (state: LegacyAuthenticationState) => ({
        ...state,
        loading: LOADING_STATUS.FAILED,
      })
    );

    // fetchSingleLegacyCredential
    builder.addCase(
      fetchSingleLegacyCredential.fulfilled,
      (state: LegacyAuthenticationState, action) => ({
        ...state,
        singleData: action.payload,
        fetchSingleLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchSingleLegacyCredential.pending,
      (state: LegacyAuthenticationState) => ({
        ...state,
        fetchSingleLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchSingleLegacyCredential.rejected,
      (state: LegacyAuthenticationState) => ({
        ...state,
        fetchSingleLoading: LOADING_STATUS.FAILED,
      })
    );

    // createLegacyCredentials
    builder.addCase(
      createLegacyCredentials.fulfilled,
      (state: LegacyAuthenticationState, action) => ({
        ...state,
        createdData: action.payload,
        createLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      createLegacyCredentials.pending,
      (state: LegacyAuthenticationState) => ({
        ...state,
        createLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      createLegacyCredentials.rejected,
      (state: LegacyAuthenticationState) => ({
        ...state,
        createLoading: LOADING_STATUS.FAILED,
      })
    );

    // updateLegacyCredentials
    builder.addCase(
      updateLegacyCredentials.fulfilled,
      (state: LegacyAuthenticationState) => ({
        ...state,
        updateLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      updateLegacyCredentials.pending,
      (state: LegacyAuthenticationState) => ({
        ...state,
        updateLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      updateLegacyCredentials.rejected,
      (state: LegacyAuthenticationState) => ({
        ...state,
        updateLoading: LOADING_STATUS.FAILED,
      })
    );

    // deleteLegacyCredentials
    builder.addCase(
      deleteLegacyCredentials.fulfilled,
      (state: LegacyAuthenticationState) => ({
        ...state,
        deleteLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      deleteLegacyCredentials.pending,
      (state: LegacyAuthenticationState) => ({
        ...state,
        deleteLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      deleteLegacyCredentials.rejected,
      (state: LegacyAuthenticationState) => ({
        ...state,
        deleteLoading: LOADING_STATUS.FAILED,
      })
    );
  },
});

export default LegacyAuthenticationSlice.reducer;
