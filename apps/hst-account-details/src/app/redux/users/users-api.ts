import { handleAPI, paramsToQueryString } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import { fetchInstance } from 'helpers';
import {
  GetSubAccountsParamsType,
  GetUsersParamsType,
  LockUserParams,
  UpdateUserParams,
} from '../../types/users/users';

export default {
  getUsers: handleAPI(async ({ accountId, params }: GetUsersParamsType) => {
    const queryString = paramsToQueryString({ params });
    let encodedQueryString;
    if (typeof queryString === 'string')
      encodedQueryString = queryString
        .replaceAll('[', '%5B')
        .replaceAll(']', '%5D');

    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v2.1/support/accounts/${accountId}/users?${encodedQueryString}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
  getSubAccounts: handleAPI(
    async ({ vendorId, accountId, params }: GetSubAccountsParamsType) => {
      const queryString = paramsToQueryString({ params });

      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/sub-accounts?${queryString}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),
  updateUser: handleAPI(
    async ({ vendorId, accountId, userId, params }: UpdateUserParams) =>
      fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/users/${userId}`,
        {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify(params),
        }
      )
  ),
  lockUser: handleAPI(
    async ({ payload, userId, accountId, vendorId }: LockUserParams) =>
      fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/users/${userId}/lock`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify(payload),
        }
      )
  ),
};
