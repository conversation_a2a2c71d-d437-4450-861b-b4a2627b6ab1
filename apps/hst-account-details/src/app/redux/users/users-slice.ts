import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { LOADING_STATUS } from '../../constants/index';
import {
  GetSubAccountsParamsType,
  GetUsersParamsType,
  LockUserParams,
  SubAccountsResponseType,
  UpdateUserParams,
  UsersResponseType,
} from '../../types/users/users';
import UsersApi from './users-api';

export interface UsersState {
  data: UsersResponseType | null;
  subAccountsData: SubAccountsResponseType | null;
  loading: string;
  updateLoading: string;
  loadingSubAccounts: string;
  lockLoading?: string;
}

const initialState: UsersState = {
  data: null,
  loading: LOADING_STATUS.NOT_LOADED,
  subAccountsData: null,
  loadingSubAccounts: LOADING_STATUS.NOT_LOADED,
  updateLoading: LOADING_STATUS.NOT_LOADED,
};

export const fetchUsers = createAsyncThunk(
  'users/fetchUsers',
  async (params: GetUsersParamsType, thunkAPI) => {
    const response = await UsersApi.getUsers(params);
    return response;
  }
);

export const fetchSubAccounts = createAsyncThunk(
  'users/fetchSubAccounts',
  async (params: GetSubAccountsParamsType, thunkAPI) => {
    const response = await UsersApi.getSubAccounts(params);
    return response;
  }
);

export const updateUser = createAsyncThunk(
  'users/updateUser',
  async (params: UpdateUserParams, thunkAPI) => {
    const response = await UsersApi.updateUser(params);
    return response;
  }
);

export const lockUser = createAsyncThunk(
  'users/lockUser',
  async (params: LockUserParams, thunkAPI) => {
    const response = await UsersApi.lockUser(params);
    return response;
  }
);

export const UsersSlice = createSlice({
  name: 'users',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    // fetchUsers
    builder.addCase(fetchUsers.fulfilled, (state: UsersState, action) => ({
      ...state,
      data: action.payload,
      loading: LOADING_STATUS.SUCCEEDED,
    }));
    builder.addCase(fetchUsers.pending, (state: UsersState) => ({
      ...state,
      loading: LOADING_STATUS.LOADING,
    }));
    builder.addCase(fetchUsers.rejected, (state: UsersState) => ({
      ...state,
      data: null,
      loading: LOADING_STATUS.FAILED,
    }));
    // fetchSubAccounts
    builder.addCase(
      fetchSubAccounts.fulfilled,
      (state: UsersState, action) => ({
        ...state,
        subAccountsData: action.payload,
        loadingSubAccounts: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(fetchSubAccounts.pending, (state: UsersState) => ({
      ...state,
      loadingSubAccounts: LOADING_STATUS.LOADING,
    }));
    builder.addCase(fetchSubAccounts.rejected, (state: UsersState) => ({
      ...state,
      loadingSubAccounts: LOADING_STATUS.FAILED,
    }));
    // updateUser
    builder.addCase(updateUser.fulfilled, (state: UsersState, action) => ({
      ...state,
      updateLoading: LOADING_STATUS.SUCCEEDED,
    }));
    builder.addCase(updateUser.pending, (state: UsersState) => ({
      ...state,
      updateLoading: LOADING_STATUS.LOADING,
    }));
    builder.addCase(updateUser.rejected, (state: UsersState) => ({
      ...state,
      updateLoading: LOADING_STATUS.FAILED,
    }));
    // lockUser
    builder.addCase(lockUser.fulfilled, (state: UsersState) => ({
      ...state,
      lockLoading: LOADING_STATUS.SUCCEEDED,
    }));

    builder.addCase(lockUser.pending, (state: UsersState) => ({
      ...state,
      lockLoading: LOADING_STATUS.LOADING,
    }));

    builder.addCase(lockUser.rejected, (state: UsersState) => ({
      ...state,
      lockLoading: LOADING_STATUS.FAILED,
    }));
  },
});

export default UsersSlice.reducer;
