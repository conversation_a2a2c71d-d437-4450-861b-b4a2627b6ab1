import { fetchInstance } from 'helpers';
import { handleAPI } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';

export default {
  updateDeliveryReceipts: handleAPI(async ({
    vendorId,
    accountId,
    isEnabled
  }: {
    vendorId: string,
    accountId: string,
    isEnabled: boolean
  }) =>
    fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/delivery-receipts/${isEnabled ? 'enable': 'disable'}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
  ))
};
