import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { LOADING_STATUS } from '../../../constants/index';
import DeliveryReceiptsApi from './delivery-receipts-api';

export interface DeliveryReceiptsState {
  loading: string;
}

const initialState: DeliveryReceiptsState = {
  loading: LOADING_STATUS.NOT_LOADED,
};


export const updateDeliveryReceipts = createAsyncThunk(
  'deliveryReceipts/updateDeliveryReceipts',
  async (params: {
    vendorId: string,
    accountId: string,
    isEnabled: boolean
  }, thunkAPI) => {
    const response = await DeliveryReceiptsApi.updateDeliveryReceipts(params);
    return response;
  }
);

export const DeliveryReceiptsSlice = createSlice({
  name: 'deliveryReceipts',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    // updateDeliveryReceipts
    builder.addCase(
      updateDeliveryReceipts.fulfilled,
      (state: DeliveryReceiptsState, action) => ({
        ...state,
        loading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      updateDeliveryReceipts.pending,
      (state: DeliveryReceiptsState) => ({
        ...state,
        loading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      updateDeliveryReceipts.rejected,
      (state: DeliveryReceiptsState) => ({
        ...state,
        loading: LOADING_STATUS.FAILED,
      })
    );
  },
});

export default DeliveryReceiptsSlice.reducer;
