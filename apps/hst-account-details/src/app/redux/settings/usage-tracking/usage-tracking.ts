import { fetchInstance, paramsToQueryString } from 'helpers';
import { handleAPI } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import {
  FetchUsageTrackingParams,
  UpdateUTTParams,
  DeleteUTTParams,
} from '../../../types/settings';

export default {
  loadUsageTracking: handleAPI(async (params: FetchUsageTrackingParams) => {
    const { vendorId, accountId, size, next } = params;
    const queryString = paramsToQueryString({ params: { size, next } });
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/usage-trackings?${queryString}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
  updateUsageTracking: handleAPI(async (params: UpdateUTTParams) => {
    const { vendorId, accountId, body } = params;
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/usage-trackings/${body.id}`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(body),
      }
    );
  }),
  createUsageTracking: handleAPI(async (params: UpdateUTTParams) => {
    const { vendorId, accountId, body } = params;
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/usage-trackings`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(body),
      }
    );
  }),
  deleteUsageTracking: handleAPI(async (params: DeleteUTTParams) => {
    const { vendorId, accountId, id } = params;
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/usage-trackings/${id}`,
      {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
};
