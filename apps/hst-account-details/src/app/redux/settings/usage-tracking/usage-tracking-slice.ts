import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { LOADING_STATUS } from '../../../constants/index';
import {
  UsageTrackingType,
  FetchUsageTrackingParams,
  UpdateUTTParams,
  DeleteUTTParams,
} from '../../../types/settings/index';
import UsageTrackingApi from './usage-tracking';

export interface UsageTrackingState {
  loading: string;
  updateLoading: string;
  createLoading: string;
  deleteLoading: string;
  data: UsageTrackingType | null;
}

const initialState: UsageTrackingState = {
  loading: LOADING_STATUS.NOT_LOADED,
  updateLoading: LOADING_STATUS.NOT_LOADED,
  createLoading: LOADING_STATUS.NOT_LOADED,
  deleteLoading: LOADING_STATUS.NOT_LOADED,
  data: null,
};

export const fetchUsageTracking = createAsyncThunk(
  'usageTracking/fetchUsageTracking',
  async (params: FetchUsageTrackingParams, thunkAPI) => {
    const response = await UsageTrackingApi.loadUsageTracking(params);
    return response;
  }
);

export const updateUsageTracking = createAsyncThunk(
  'usageTracking/updateUsageTracking',
  async (params: UpdateUTTParams, thunkAPI) => {
    const response = await UsageTrackingApi.updateUsageTracking(params);
    return response;
  }
);

export const createUsageTracking = createAsyncThunk(
  'usageTracking/createUsageTracking',
  async (params: UpdateUTTParams, thunkAPI) => {
    const response = await UsageTrackingApi.createUsageTracking(params);
    return response;
  }
);

export const deleteUsageTracking = createAsyncThunk(
  'usageTracking/deleteUsageTracking',
  async (params: DeleteUTTParams, thunkAPI) => {
    const response = await UsageTrackingApi.deleteUsageTracking(params);
    return response;
  }
);

export const UsageTrackingSlice = createSlice({
  name: 'usageTracking',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    // fetchUsageTracking
    builder.addCase(
      fetchUsageTracking.fulfilled,
      (state: UsageTrackingState, action) => ({
        ...state,
        data: action.payload,
        loading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchUsageTracking.pending,
      (state: UsageTrackingState) => ({
        ...state,
        loading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchUsageTracking.rejected,
      (state: UsageTrackingState) => ({
        ...state,
        loading: LOADING_STATUS.FAILED,
      })
    );
    // updateUsageTracking
    builder.addCase(
      updateUsageTracking.fulfilled,
      (state: UsageTrackingState, action) => ({
        ...state,
        updateLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      updateUsageTracking.pending,
      (state: UsageTrackingState) => ({
        ...state,
        updateLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      updateUsageTracking.rejected,
      (state: UsageTrackingState) => ({
        ...state,
        updateLoading: LOADING_STATUS.FAILED,
      })
    );
    // createUsageTracking
    builder.addCase(
      createUsageTracking.fulfilled,
      (state: UsageTrackingState, action) => ({
        ...state,
        createLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      createUsageTracking.pending,
      (state: UsageTrackingState) => ({
        ...state,
        createLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      createUsageTracking.rejected,
      (state: UsageTrackingState) => ({
        ...state,
        createLoading: LOADING_STATUS.FAILED,
      })
    );
    // deleteUsageTracking
    builder.addCase(
      deleteUsageTracking.fulfilled,
      (state: UsageTrackingState, action) => ({
        ...state,
        deleteLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      deleteUsageTracking.pending,
      (state: UsageTrackingState) => ({
        ...state,
        deleteLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      deleteUsageTracking.rejected,
      (state: UsageTrackingState) => ({
        ...state,
        deleteLoading: LOADING_STATUS.FAILED,
      })
    );
  },
});

export default UsageTrackingSlice.reducer;
