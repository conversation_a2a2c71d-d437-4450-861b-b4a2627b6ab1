import { fetchInstance } from 'helpers';
import { handleAPI } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import {
  FetchAccountDetailsParams,
  UpdateAccountDetailsParams,
} from '../../../types/settings';

export default {
  loadAccountDetails: handleAPI(async (params: FetchAccountDetailsParams) => {
    const { vendorId, accountId } = params;
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/detail`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
  updateAccountDetails: handleAPI(
    async (params: UpdateAccountDetailsParams) => {
      const { vendorId, accountId, data } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/detail`,
        {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({ ...data }),
        }
      );
    }
  ),
};
