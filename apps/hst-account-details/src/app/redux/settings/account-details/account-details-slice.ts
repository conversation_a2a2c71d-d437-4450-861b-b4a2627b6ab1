import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { LOADING_STATUS } from '../../../constants/index';
import {
  AccountDetailsType,
  FetchAccountDetailsParams,
  UpdateAccountDetailsParams,
} from '../../../types/settings/index';
import AccountDetailsApi from './account-details';

export interface AccountDetailsState {
  loading: string;
  updateLoading: string;
  data: AccountDetailsType | null;
}

const initialState: AccountDetailsState = {
  loading: LOADING_STATUS.NOT_LOADED,
  updateLoading: LOADING_STATUS.NOT_LOADED,
  data: null,
};

export const fetchAccountDetails = createAsyncThunk(
  'accountSettingsDetails/fetchAccountSettingsDetails',
  async (params: FetchAccountDetailsParams, thunkAPI) => {
    const response = await AccountDetailsApi.loadAccountDetails(params);
    return response;
  }
);

export const updateAccountDetails = createAsyncThunk(
  'accountSettingsDetails/updatehAccountSettingsDetails',
  async (params: UpdateAccountDetailsParams, thunkAPI) => {
    const response = await AccountDetailsApi.updateAccountDetails(params);
    return response;
  }
);

export const AccountDetailsSlice = createSlice({
  name: 'accountSettingsDetails',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(
      fetchAccountDetails.fulfilled,
      (state: AccountDetailsState, action) => ({
        ...state,
        data: action.payload,
        loading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchAccountDetails.pending,
      (state: AccountDetailsState) => ({
        ...state,
        loading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchAccountDetails.rejected,
      (state: AccountDetailsState) => ({
        ...state,
        loading: LOADING_STATUS.FAILED,
      })
    );
    // update account details
    builder.addCase(
      updateAccountDetails.pending,
      (state: AccountDetailsState, action) => ({
        ...state,
        updateLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      updateAccountDetails.fulfilled,
      (state: AccountDetailsState, action) => ({
        ...state,
        updateLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      updateAccountDetails.rejected,
      (state: AccountDetailsState, action) => ({
        ...state,
        updateLoading: LOADING_STATUS.FAILED,
      })
    );
  },
});

export default AccountDetailsSlice.reducer;
