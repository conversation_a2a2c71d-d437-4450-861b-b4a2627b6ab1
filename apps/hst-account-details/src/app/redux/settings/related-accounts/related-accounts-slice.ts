import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { LOADING_STATUS } from '../../../constants';
import {
  fetchReloatedAccountsContextParams,
  updateRelatedAccountsParams,
} from '../../../types/settings/related-accounts';
import RelatedAccountsApi from './related-accounts-api';

export interface RelatedAccountsState {
  loading: string;
  contextLoading: string;
  updateLoading: string;
  relatedAccountsData: any | null;
  relatedAccountsContexts: any | null;
}

const initialState: RelatedAccountsState = {
  loading: LOADING_STATUS.NOT_LOADED,
  contextLoading: LOADING_STATUS.NOT_LOADED,
  updateLoading: LOADING_STATUS.NOT_LOADED,
  relatedAccountsData: null,
  relatedAccountsContexts: null,
};

export const fetchRelatedAccountsContext = createAsyncThunk(
  'relatedAccounts/fetchContext',
  async (params: fetchReloatedAccountsContextParams, thunkAPI) => {
    const response = await RelatedAccountsApi.loadRelatedAccountsContext(
      params
    );
    return response;
  }
);

export const fetchRelatedAccounts = createAsyncThunk(
  'relatedAccounts/fetchAccounts',
  async (params: fetchReloatedAccountsContextParams, thunkAPI) => {
    const response = await RelatedAccountsApi.loadRelativedAccounts(params);
    return response;
  }
);

export const updateRelatedAccounts = createAsyncThunk(
  'relatedAccounts/updateAccounts',
  async (params: updateRelatedAccountsParams, thunkAPI) => {
    const response = await RelatedAccountsApi.updateRelatedAccounts(params);
    return response;
  }
);

export const RelatedAccountsSlice = createSlice({
  name: 'relatedAccounts',
  initialState,
  reducers: {
    resetUpdateLoading: (state) => ({
      ...state,
      updateLoading: LOADING_STATUS.NOT_LOADED,
    }),
  },
  extraReducers: (builder) => {
    // fetchRelatedAccountsContext
    builder.addCase(
      fetchRelatedAccountsContext.fulfilled,
      (state: RelatedAccountsState, action) => ({
        ...state,
        relatedAccountsContexts: action.payload,
        contextLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchRelatedAccountsContext.pending,
      (state: RelatedAccountsState) => ({
        ...state,
        contextLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchRelatedAccountsContext.rejected,
      (state: RelatedAccountsState) => ({
        ...state,
        contextLoading: LOADING_STATUS.FAILED,
      })
    );

    // fetchRelatedAccounts
    builder.addCase(
      fetchRelatedAccounts.fulfilled,
      (state: RelatedAccountsState, action) => ({
        ...state,
        relatedAccountsData: action.payload,
        loading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchRelatedAccounts.pending,
      (state: RelatedAccountsState) => ({
        ...state,
        loading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchRelatedAccounts.rejected,
      (state: RelatedAccountsState) => ({
        ...state,
        loading: LOADING_STATUS.FAILED,
      })
    );

    // updateRelatedAccounts
    builder.addCase(
      updateRelatedAccounts.fulfilled,
      (state: RelatedAccountsState, action) => ({
        ...state,
        updateLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      updateRelatedAccounts.pending,
      (state: RelatedAccountsState) => ({
        ...state,
        updateLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      updateRelatedAccounts.rejected,
      (state: RelatedAccountsState) => ({
        ...state,
        updateLoading: LOADING_STATUS.FAILED,
      })
    );
  },
});

export const { resetUpdateLoading } = RelatedAccountsSlice.actions;

export default RelatedAccountsSlice.reducer;
