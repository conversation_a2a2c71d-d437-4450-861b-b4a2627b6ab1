import { handleAPI } from '@sinch-smb/dev-utils';
import fetchInstance from 'apps/hst-core/src/apis/utils/helpers';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import {
  fetchReloatedAccountsContextParams,
  updateRelatedAccountsParams,
} from '../../../types/settings/related-accounts';

export default {
  loadRelatedAccountsContext: handleAPI(
    async (params: fetchReloatedAccountsContextParams) => {
      const { vendorId, accountId } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/related-accounts/contexts`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),

  loadRelativedAccounts: handleAPI(
    async (params: fetchReloatedAccountsContextParams) => {
      const { vendorId, accountId } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/related-accounts`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),

  updateRelatedAccounts: handleAPI(
    async (params: updateRelatedAccountsParams) => {
      const { vendorId, accountId, relatedAccountsContexts } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/related-accounts`,
        {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({ relatedAccountsContexts })
        }
      );
    }
  ),
};
