import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { LOADING_STATUS } from '../../../constants';
import { CarriersType, CountriesType, FetchCarriersParams, FetchCountriesParams } from '../../../types/settings';
import {
  DeleteDeliveryPropertiesParams,
  DeliveryPropertiesType,
  DeliveryPropertyTypesType,
  FetchDeliveryPropertiesParams,
  FetchDeliveryPropertyTypesParams,
  PatchDeliveryPropertiesParams,
  PostDeliveryPropertiesParams
} from '../../../types/settings/delivery-properties';
import DeliveryPropertiesApi from './delivery-properties';

export interface DeliveryPropertiesState {
  loading: string;
  data: DeliveryPropertiesType | null;
  carriers: CarriersType | null;
  carriersLoading: string;
  deliveryPropertyTypes: DeliveryPropertyTypesType | null;
  deliveryPropertyTypesLoading: string;
  countries: CountriesType | null;
  countriesLoading: string;
  createDeliveryPropertiesLoading: string;
  updateDeliveryPropertiesLoading: string;
  deleteDeliveryPropertyLoading: string;
}

const initialState: DeliveryPropertiesState = {
  loading: LOADING_STATUS.NOT_LOADED,
  data: null,
  carriers: null,
  carriersLoading: LOADING_STATUS.NOT_LOADED,
  deliveryPropertyTypes: null,
  deliveryPropertyTypesLoading: LOADING_STATUS.NOT_LOADED,
  countries: null,
  countriesLoading: LOADING_STATUS.NOT_LOADED,
  createDeliveryPropertiesLoading: LOADING_STATUS.NOT_LOADED,
  updateDeliveryPropertiesLoading: LOADING_STATUS.NOT_LOADED,
  deleteDeliveryPropertyLoading: LOADING_STATUS.NOT_LOADED,
};

export const fetchDeliveryProperties = createAsyncThunk(
  'deliveryProperties/fetchDeliveryProperties',
  async (params: FetchDeliveryPropertiesParams, thunkAPI) => {
    const response = await DeliveryPropertiesApi.loadDeliveryProperties(params);
    return response;
  }
);

export const fetchCarriers = createAsyncThunk(
  'deliveryProperties/fetchCarriers',
  async (params: FetchCarriersParams, thunkAPI) => {
    const response = await DeliveryPropertiesApi.loadCarriers(params);
    return response;
  }
);

export const fetchDeliveryPropertyTypes = createAsyncThunk(
  'deliveryProperties/fetchDeliveryPropertyTypes',
  async (params: FetchDeliveryPropertyTypesParams, thunkAPI) => {
    const response = await DeliveryPropertiesApi.loadDeliveryPropertyTypes(params);
    return response;
  }
);

export const fetchCountries = createAsyncThunk(
  'deliveryProperties/fetchCountries',
  async (params: FetchCountriesParams, thunkAPI) => {
    const response = await DeliveryPropertiesApi.loadCountries(params);
    return response;
  }
);

export const createDeliveryProperties = createAsyncThunk(
  'deliveryProperties/createDeliveryProperties',
  async (params: PostDeliveryPropertiesParams, thunkAPI) => {
    const response = await DeliveryPropertiesApi.postDeliveryProperties(params);
    return response;
  }
);

export const updateDeliveryProperties = createAsyncThunk(
  'deliveryProperties/updateDeliveryProperties',
  async (params: PatchDeliveryPropertiesParams, thunkAPI) => {
    const response = await DeliveryPropertiesApi.patchDeliveryProperties(params);
    return response;
  }
);

export const deleteDeliveryProperty = createAsyncThunk(
  'deliveryProperties/deleteDeliveryProperty',
  async (params: DeleteDeliveryPropertiesParams, thunkAPI) => {
    const response = await DeliveryPropertiesApi.deleteDeliveryProperties(params);
    return response;
  }
);


export const deliveryPropertiesSlice = createSlice({
  name: 'deliveryProperties',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    // fetchDeliveryProperties
    builder.addCase(
      fetchDeliveryProperties.fulfilled,
      (state: DeliveryPropertiesState, action) => ({
        ...state,
        data: action.payload,
        loading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchDeliveryProperties.pending,
      (state: DeliveryPropertiesState) => ({
        ...state,
        loading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchDeliveryProperties.rejected,
      (state: DeliveryPropertiesState) => ({
        ...state,
        loading: LOADING_STATUS.FAILED,
      })
    );
    // fetchCarriers
    builder.addCase(
      fetchCarriers.fulfilled,
      (state: DeliveryPropertiesState, action) => ({
        ...state,
        carriers: action.payload,
        carriersLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchCarriers.pending,
      (state: DeliveryPropertiesState) => ({
        ...state,
        carriersLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchCarriers.rejected,
      (state: DeliveryPropertiesState) => ({
        ...state,
        carriersLoading: LOADING_STATUS.FAILED,
      })
    );
    // fetchDeliveryPropertyTypes
    builder.addCase(
      fetchDeliveryPropertyTypes.fulfilled,
      (state: DeliveryPropertiesState, action) => ({
        ...state,
        deliveryPropertyTypes: action.payload,
        deliveryPropertyTypesLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchDeliveryPropertyTypes.pending,
      (state: DeliveryPropertiesState) => ({
        ...state,
        deliveryPropertyTypesLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchDeliveryPropertyTypes.rejected,
      (state: DeliveryPropertiesState) => ({
        ...state,
        deliveryPropertyTypesLoading: LOADING_STATUS.FAILED,
      })
    );
    // fetchCountries
    builder.addCase(
      fetchCountries.fulfilled,
      (state: DeliveryPropertiesState, action) => ({
        ...state,
        countries: action.payload,
        countriesLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchCountries.pending,
      (state: DeliveryPropertiesState) => ({
        ...state,
        countriesLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchCountries.rejected,
      (state: DeliveryPropertiesState) => ({
        ...state,
        countriesLoading: LOADING_STATUS.FAILED,
      })
    );
    // createDeliveryProperties
    builder.addCase(
      createDeliveryProperties.fulfilled,
      (state: DeliveryPropertiesState, action) => ({
        ...state,
        createDeliveryPropertiesLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      createDeliveryProperties.pending,
      (state: DeliveryPropertiesState) => ({
        ...state,
        createDeliveryPropertiesLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      createDeliveryProperties.rejected,
      (state: DeliveryPropertiesState) => ({
        ...state,
        createDeliveryPropertiesLoading: LOADING_STATUS.FAILED,
      })
    );
    // updateDeliveryProperties
    builder.addCase(
      updateDeliveryProperties.fulfilled,
      (state: DeliveryPropertiesState, action) => ({
        ...state,
        updateDeliveryPropertiesLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      updateDeliveryProperties.pending,
      (state: DeliveryPropertiesState) => ({
        ...state,
        updateDeliveryPropertiesLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      updateDeliveryProperties.rejected,
      (state: DeliveryPropertiesState) => ({
        ...state,
        updateDeliveryPropertiesLoading: LOADING_STATUS.FAILED,
      })
    );
    // deleteDeliveryProperty
    builder.addCase(
      deleteDeliveryProperty.fulfilled,
      (state: DeliveryPropertiesState, action) => ({
        ...state,
        deleteDeliveryPropertyLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      deleteDeliveryProperty.pending,
      (state: DeliveryPropertiesState) => ({
        ...state,
        deleteDeliveryPropertyLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      deleteDeliveryProperty.rejected,
      (state: DeliveryPropertiesState) => ({
        ...state,
        deleteDeliveryPropertyLoading: LOADING_STATUS.FAILED,
      })
    );
  },
});

export default deliveryPropertiesSlice.reducer;
