import { handleAPI } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import { fetchInstance, paramsToQueryString } from 'helpers';
import { FetchCarriersParams } from '../../../types/settings';
import {
  DeleteDeliveryPropertiesParams,
  FetchDeliveryPropertiesParams,
  PatchDeliveryPropertiesParams,
  PostDeliveryPropertiesParams,
} from '../../../types/settings/delivery-properties';

export default {
  loadDeliveryProperties: handleAPI(
    async (params: FetchDeliveryPropertiesParams) => {
      const { vendorId, accountId, size, next } = params;
      const queryString = paramsToQueryString({ params: { size, next } });
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/delivery-properties?${queryString}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),
  postDeliveryProperties: handleAPI(
    async (params: PostDeliveryPropertiesParams) => {
      const { vendorId, accountId, payload } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/delivery-properties`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify(payload),
        }
      );
    }
  ),
  patchDeliveryProperties: handleAPI(
    async (params: PatchDeliveryPropertiesParams) => {
      const { vendorId, accountId, payload } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/delivery-properties/${payload.id}`,
        {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify(payload),
        }
      );
    }
  ),
  deleteDeliveryProperties: handleAPI(
    async (params: DeleteDeliveryPropertiesParams) => {
      const { vendorId, accountId, id } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/delivery-properties/${id}`,
        {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),
  loadCarriers: handleAPI(async (params: FetchCarriersParams) => {
    const { vendorId, accountId } = params;
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/carriers`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
  loadDeliveryPropertyTypes: handleAPI(async (params: FetchCarriersParams) => {
    const { vendorId, accountId } = params;
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/delivery-property-types`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
  loadCountries: handleAPI(async (params: FetchCarriersParams) => {
    const { vendorId, accountId } = params;
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/countries`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
};
