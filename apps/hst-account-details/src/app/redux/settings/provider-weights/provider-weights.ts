import { handleAPI } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import { fetchInstance, paramsToQueryString } from 'helpers';
import {
  DeleteProviderWeightParams,
  FetchAddressTypesParams,
  FetchProviderWeightTypesParams,
  FetchProviderWeightsParams,
  FetchProvidersParams,
  PatchProviderWeightParams,
  PostProviderWeightParams,
} from '../../../types/settings/provider-weights';

export default {
  loadProviderWeights: handleAPI(async (params: FetchProviderWeightsParams) => {
    const { vendorId, accountId, size, next } = params;
    const queryString = paramsToQueryString({ params: { size, next } });
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/provider-weights?${queryString}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
  postProviderWeight: handleAPI(async (params: PostProviderWeightParams) => {
    const { vendorId, accountId, payload } = params;
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/provider-weights`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(payload)
      },
    );
  }),
  patchProviderWeight: handleAPI(async (params: PatchProviderWeightParams) => {
    const { vendorId, accountId, payload } = params;
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/provider-weights/${payload.id}`,
      {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(payload)
      },
    );
  }),
  deleteProviderWeight: handleAPI(async (params: DeleteProviderWeightParams) => {
    const { vendorId, accountId, providerWeightId } = params;
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/provider-weights/${providerWeightId}`,
      {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      },
    );
  }),
  loadProviders: handleAPI(async (params: FetchProvidersParams) => {
    const { vendorId, accountId } = params;
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/providers`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
  loadAddressTypes: handleAPI(async (params: FetchAddressTypesParams) => {
    const { vendorId, accountId } = params;
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/address-types`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
  loadProviderWeightTypes: handleAPI(async (params: FetchProviderWeightTypesParams) => {
    const { vendorId, accountId } = params;
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/provider-weight-types`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
};
