import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { LOADING_STATUS } from '../../../constants';
import {
  AddressTypesType,
  DeleteProviderWeightParams,
  FetchAddressTypesParams,
  FetchProvidersParams,
  FetchProviderWeightsParams,
  FetchProviderWeightTypesParams,
  PatchProviderWeightParams,
  PostProviderWeightParams,
  ProvidersType,
  ProviderWeightsType,
  ProviderWeightTypesType
} from '../../../types/settings/provider-weights';
import ProviderWeightsApi from './provider-weights';


export interface ProviderWeightsState {
  loading: string;
  data: ProviderWeightsType | null;
  providers: ProvidersType | null;
  providersLoading: string;
  addressTypes: AddressTypesType | null;
  addressTypesLoading: string;
  providerWeightTypes: ProviderWeightTypesType | null;
  providerWeightTypesLoading: string;
  createProviderWeightLoading: string;
  updateProviderWeightLoading: string;
  deleteProviderWeightLoading: string;
}

const initialState: ProviderWeightsState = {
  loading: LOADING_STATUS.NOT_LOADED,
  data: null,
  providers: null,
  providersLoading: LOADING_STATUS.NOT_LOADED,
  addressTypes: null,
  addressTypesLoading: LOADING_STATUS.NOT_LOADED,
  providerWeightTypes: null,
  providerWeightTypesLoading: LOADING_STATUS.NOT_LOADED,
  createProviderWeightLoading: LOADING_STATUS.NOT_LOADED,
  updateProviderWeightLoading: LOADING_STATUS.NOT_LOADED,
  deleteProviderWeightLoading: LOADING_STATUS.NOT_LOADED,
};

export const fetchProviderWeights = createAsyncThunk(
  'providerWeights/fetchProviderWeights',
  async (params: FetchProviderWeightsParams, thunkAPI) => {
    const response = await ProviderWeightsApi.loadProviderWeights(params);
    return response;
  }
);

export const fetchProviders = createAsyncThunk(
  'providerWeights/fetchProviders',
  async (params: FetchProvidersParams, thunkAPI) => {
    const response = await ProviderWeightsApi.loadProviders(params);
    return response;
  }
);

export const fetchAddressTypes = createAsyncThunk(
  'providerWeights/fetchAddressTypes',
  async (params: FetchAddressTypesParams, thunkAPI) => {
    const response = await ProviderWeightsApi.loadAddressTypes(params);
    return response;
  }
);

export const fetchProviderWeightTypes = createAsyncThunk(
  'providerWeights/fetchProviderWeightTypes',
  async (params: FetchProviderWeightTypesParams, thunkAPI) => {
    const response = await ProviderWeightsApi.loadProviderWeightTypes(params);
    return response;
  }
);

export const createProviderWeight = createAsyncThunk(
  'providerWeights/createProviderWeight',
  async (params: PostProviderWeightParams, thunkAPI) => {
    const response = await ProviderWeightsApi.postProviderWeight(params);
    return response;
  }
);

export const updateProviderWeight = createAsyncThunk(
  'providerWeights/updateProviderWeight',
  async (params: PatchProviderWeightParams, thunkAPI) => {
    const response = await ProviderWeightsApi.patchProviderWeight(params);
    return response;
  }
);

export const deleteProviderWeight = createAsyncThunk(
  'providerWeights/deleteProviderWeight',
  async (params: DeleteProviderWeightParams, thunkAPI) => {
    const response = await ProviderWeightsApi.deleteProviderWeight(params);
    return response;
  }
);

export const ProviderWeightsSlice = createSlice({
  name: 'providerWeights',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    // fetchProviderWeights
    builder.addCase(
      fetchProviderWeights.fulfilled,
      (state: ProviderWeightsState, action) => ({
        ...state,
        data: action.payload,
        loading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchProviderWeights.pending,
      (state: ProviderWeightsState) => ({
        ...state,
        loading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchProviderWeights.rejected,
      (state: ProviderWeightsState) => ({
        ...state,
        loading: LOADING_STATUS.FAILED,
      })
    );
    // fetchProviders
    builder.addCase(
      fetchProviders.fulfilled,
      (state: ProviderWeightsState, action) => ({
        ...state,
        providers: action.payload,
        providersLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchProviders.pending,
      (state: ProviderWeightsState) => ({
        ...state,
        providersLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchProviders.rejected,
      (state: ProviderWeightsState) => ({
        ...state,
        providersLoading: LOADING_STATUS.FAILED,
      })
    );
    // fetchAddressTypes
    builder.addCase(
      fetchAddressTypes.fulfilled,
      (state: ProviderWeightsState, action) => ({
        ...state,
        addressTypes: action.payload,
        addressTypesLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchAddressTypes.pending,
      (state: ProviderWeightsState) => ({
        ...state,
        addressTypesLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchAddressTypes.rejected,
      (state: ProviderWeightsState) => ({
        ...state,
        addressTypesLoading: LOADING_STATUS.FAILED,
      })
    );
    // fetchProviderWeightTypes
    builder.addCase(
      fetchProviderWeightTypes.fulfilled,
      (state: ProviderWeightsState, action) => ({
        ...state,
        providerWeightTypes: action.payload,
        providerWeightTypesLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchProviderWeightTypes.pending,
      (state: ProviderWeightsState) => ({
        ...state,
        providerWeightTypesLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchProviderWeightTypes.rejected,
      (state: ProviderWeightsState) => ({
        ...state,
        providerWeightTypesLoading: LOADING_STATUS.FAILED,
      })
    );
    // createProviderWeight
    builder.addCase(
      createProviderWeight.fulfilled,
      (state: ProviderWeightsState, action) => ({
        ...state,
        createProviderWeightLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      createProviderWeight.pending,
      (state: ProviderWeightsState) => ({
        ...state,
        createProviderWeightLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      createProviderWeight.rejected,
      (state: ProviderWeightsState) => ({
        ...state,
        createProviderWeightLoading: LOADING_STATUS.FAILED,
      })
    );
    // updateProviderWeight
    builder.addCase(
      updateProviderWeight.fulfilled,
      (state: ProviderWeightsState, action) => ({
        ...state,
        updateProviderWeightLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      updateProviderWeight.pending,
      (state: ProviderWeightsState) => ({
        ...state,
        updateProviderWeightLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      updateProviderWeight.rejected,
      (state: ProviderWeightsState) => ({
        ...state,
        updateProviderWeightLoading: LOADING_STATUS.FAILED,
      })
    );
    // deleteProviderWeight
    builder.addCase(
      deleteProviderWeight.fulfilled,
      (state: ProviderWeightsState, action) => ({
        ...state,
        deleteProviderWeightLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      deleteProviderWeight.pending,
      (state: ProviderWeightsState) => ({
        ...state,
        deleteProviderWeightLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      deleteProviderWeight.rejected,
      (state: ProviderWeightsState) => ({
        ...state,
        deleteProviderWeightLoading: LOADING_STATUS.FAILED,
      })
    );
  },
});

export default ProviderWeightsSlice.reducer;
