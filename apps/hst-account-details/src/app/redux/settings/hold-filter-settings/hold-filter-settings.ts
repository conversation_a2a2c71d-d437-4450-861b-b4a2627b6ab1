import { fetchInstance } from 'helpers';
import { handleAPI } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import {
  FetchHoldFilterSettingsParams,
  UpdateHoldFilterSettingsParams,
} from '../../../types/settings';

export default {
  loadHoldFilterSettings: handleAPI(
    async (params: FetchHoldFilterSettingsParams) => {
      const { vendorId, accountId } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/broadcast-hold-filters-settings`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),
  updateHoldFilterSettings: handleAPI(
    async (params: UpdateHoldFilterSettingsParams) => {
      const { vendorId, accountId, autoApprove } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/broadcast-hold-filters-settings`,
        {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({ autoApprove }),
        }
      );
    }
  ),
};
