import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { LOADING_STATUS } from '../../../constants/index';
import {
  HoldFilterSettingsType,
  FetchHoldFilterSettingsParams,
  UpdateHoldFilterSettingsParams,
} from '../../../types/settings/index';
import HoldFilterSettingsApi from './hold-filter-settings';

export interface HoldFilterSettingsState {
  data: HoldFilterSettingsType | null;
  loading: string;
  updateLoading: string;
}

const initialState: HoldFilterSettingsState = {
  data: null,
  loading: LOADING_STATUS.NOT_LOADED,
  updateLoading: LOADING_STATUS.NOT_LOADED,
};

export const fetchHoldFilterSettings = createAsyncThunk(
  'holdFilterSettings/fetchHoldFilterSettings',
  async (params: FetchHoldFilterSettingsParams, thunkAPI) => {
    const response = await HoldFilterSettingsApi.loadHoldFilterSettings(params);
    return response;
  }
);

export const updateHoldFilters = createAsyncThunk(
  'holdFilterSettings/updatehHoldFilterSettings',
  async (params: UpdateHoldFilterSettingsParams, thunkAPI) => {
    const response = await HoldFilterSettingsApi.updateHoldFilterSettings(
      params
    );
    return response;
  }
);

export const HoldFilterSettingsSlice = createSlice({
  name: 'holdFilterSettings',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    // fetchHoldFilterSettings
    builder.addCase(
      fetchHoldFilterSettings.fulfilled,
      (state: HoldFilterSettingsState, action) => ({
        ...state,
        data: action.payload,
        loading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchHoldFilterSettings.pending,
      (state: HoldFilterSettingsState) => ({
        ...state,
        loading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchHoldFilterSettings.rejected,
      (state: HoldFilterSettingsState) => ({
        ...state,
        loading: LOADING_STATUS.FAILED,
      })
    );
    // update hold filter settings
    builder.addCase(
      updateHoldFilters.pending,
      (state: HoldFilterSettingsState, action) => ({
        ...state,
        updateLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      updateHoldFilters.fulfilled,
      (state: HoldFilterSettingsState, action) => ({
        ...state,
        updateLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      updateHoldFilters.rejected,
      (state: HoldFilterSettingsState, action) => ({
        ...state,
        updateLoading: LOADING_STATUS.FAILED,
      })
    );
  },
});

export default HoldFilterSettingsSlice.reducer;
