import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { LOADING_STATUS } from '../../../constants/index';
import {
  FetchAccessControlsParams,
  FetchAccessControlTypesParams,
  FetchValidActionsParams,
  FetchValidPropertiesParams,
  UpdateAccessControlsParams,
} from '../../../types/settings';
import AccessControlsApi from './access-controls-api';

export interface AccessControlsState {
  loading: string;
  updateLoading: string;
  typesLoading: string;
  validActionsLoading: string;
  validPropertiesLoading: string;
  data: { resources: any[] } | null;
  types: { resources: any[] } | null;
  validActions: { resources: any[] } | null;
  validProperties: { resources: any[] } | null;
}

export const fetchAccessControls = createAsyncThunk(
  'accessControls/fetchAccessControls',
  async (params: FetchAccessControlsParams) => {
    const response = await AccessControlsApi.loadAccessControls(params);
    return response;
  }
);

export const updateAccessControls = createAsyncThunk(
  'accessControls/updateAccessControls',
  async (params: UpdateAccessControlsParams) => {
    const response = await AccessControlsApi.updateAccessControls(params);
    return response;
  }
);

export const fetchAccessControlTypes = createAsyncThunk(
  'accessControls/fetchAccessControlTypes',
  async (params: FetchAccessControlTypesParams) => {
    const response = await AccessControlsApi.loadAccessControlTypes(params);
    return response;
  }
);

export const fetchValidActions = createAsyncThunk(
  'accessControls/fetchValidActions',
  async (params: FetchValidActionsParams) => {
    const response = await AccessControlsApi.fetchValidActions(params);
    return response;
  }
);

export const fetchValidProperties = createAsyncThunk(
  'accessControls/fetchValidProperties',
  async (params: FetchValidPropertiesParams) => {
    const response = await AccessControlsApi.fetchValidProperties(params);
    return response;
  }
);

const accessControlsSlice = createSlice({
  name: 'accessControls',
  initialState: {
    loading: LOADING_STATUS.NOT_LOADED,
    updateLoading: LOADING_STATUS.NOT_LOADED,
    typesLoading: LOADING_STATUS.NOT_LOADED,
    validActionsLoading: LOADING_STATUS.NOT_LOADED,
    validPropertiesLoading: LOADING_STATUS.NOT_LOADED,
    data: null,
    types: null,
    validActions: null,
    validProperties: null,
  } as AccessControlsState,
  reducers: {
    resetUpdateStatus: (state) => ({
      ...state,
      updateLoading: LOADING_STATUS.NOT_LOADED,
    }),
  },
  extraReducers: (builder) => {
    // Fetch Access Controls
    builder.addCase(fetchAccessControls.pending, (state) => ({
      ...state,
      loading: LOADING_STATUS.LOADING,
    }));
    builder.addCase(fetchAccessControls.fulfilled, (state, action) => ({
      ...state,
      data: action.payload,
      loading: LOADING_STATUS.SUCCEEDED,
    }));
    builder.addCase(fetchAccessControls.rejected, (state) => ({
      ...state,
      loading: LOADING_STATUS.FAILED,
    }));

    // Update Access Controls
    builder.addCase(updateAccessControls.pending, (state) => ({
      ...state,
      updateLoading: LOADING_STATUS.LOADING,
    }));
    builder.addCase(updateAccessControls.fulfilled, (state, action) => ({
      ...state,
      updateLoading: LOADING_STATUS.SUCCEEDED,
    }));
    builder.addCase(updateAccessControls.rejected, (state) => ({
      ...state,
      updateLoading: LOADING_STATUS.FAILED,
    }));

    // Fetch Access Control Types
    builder.addCase(fetchAccessControlTypes.pending, (state) => ({
      ...state,
      typesLoading: LOADING_STATUS.LOADING,
    }));
    builder.addCase(fetchAccessControlTypes.fulfilled, (state, action) => ({
      ...state,
      types: action.payload,
      typesLoading: LOADING_STATUS.SUCCEEDED,
    }));
    builder.addCase(fetchAccessControlTypes.rejected, (state) => ({
      ...state,
      typesLoading: LOADING_STATUS.FAILED,
    }));

    // Fetch Valid Actions
    builder
      .addCase(fetchValidActions.pending, (state) => ({
        ...state,
        validActionsLoading: LOADING_STATUS.LOADING,
      }))
      .addCase(fetchValidActions.fulfilled, (state, action) => ({
        ...state,
        validActionsLoading: LOADING_STATUS.SUCCEEDED,
        validActions: action.payload,
      }))
      .addCase(fetchValidActions.rejected, (state) => ({
        ...state,
        validActionsLoading: LOADING_STATUS.FAILED,
      }));

    // Fetch Valid Properties
    builder
      .addCase(fetchValidProperties.pending, (state) => ({
        ...state,
        validPropertiesLoading: LOADING_STATUS.LOADING,
      }))
      .addCase(fetchValidProperties.fulfilled, (state, action) => ({
        ...state,
        validPropertiesLoading: LOADING_STATUS.SUCCEEDED,
        validProperties: action.payload,
      }))
      .addCase(fetchValidProperties.rejected, (state) => ({
        ...state,
        validPropertiesLoading: LOADING_STATUS.FAILED,
      }));
  },
});

export const { resetUpdateStatus } = accessControlsSlice.actions;
export default accessControlsSlice.reducer;
