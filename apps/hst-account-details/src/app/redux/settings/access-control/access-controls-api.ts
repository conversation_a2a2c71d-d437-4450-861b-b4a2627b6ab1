import { handleAPI } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import { fetchInstance } from 'helpers';
import {
  FetchAccessControlsParams,
  FetchAccessControlTypesParams,
  FetchValidActionsParams,
  FetchValidPropertiesParams,
  UpdateAccessControlsParams,
} from '../../../types/settings';

export default {
  loadAccessControls: handleAPI(async (params: FetchAccessControlsParams) => {
    const { vendorId, accountId } = params;
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/access-controls`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),

  updateAccessControls: handleAPI(
    async (params: UpdateAccessControlsParams) => {
      const { vendorId, accountId, data } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/access-controls`,
        {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({ ...data }),
        }
      );
    }
  ),

  loadAccessControlTypes: handleAPI(
    async (params: FetchAccessControlTypesParams) => {
      const { vendorId, accountId } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/access-control-types`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),

  fetchValidActions: handleAPI(
    async (params: FetchValidActionsParams) => {
      const { vendorId, accountId, accessControlTypeId } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/access-control-types/${accessControlTypeId}/valid-actions`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),

  fetchValidProperties: handleAPI(
    async (params: FetchValidPropertiesParams) => {
      const { vendorId, accountId, accessControlTypeId } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/access-control-types/${accessControlTypeId}/valid-properties`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),
};
