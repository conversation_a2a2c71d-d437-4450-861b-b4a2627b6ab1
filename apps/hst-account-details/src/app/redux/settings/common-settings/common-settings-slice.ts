import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { LOADING_STATUS } from '../../../constants';
import {
  CarriersType,
  CountriesType,
  FetchCarriersParams,
  FetchCountriesParams,
} from '../../../types/settings';
import CommonApi from './common-settings-api';

export interface CommonSettingsState {
  carriers: CarriersType | null;
  carriersLoading: string;
  countries: CountriesType | null;
  countriesLoading: string;
}

const initialState: CommonSettingsState = {
  carriers: null,
  carriersLoading: LOADING_STATUS.NOT_LOADED,
  countries: null,
  countriesLoading: LOADING_STATUS.NOT_LOADED,
};


export const fetchCarriers = createAsyncThunk(
  'commonSettings/fetchCarriers',
  async (params: FetchCarriersParams, thunkAPI) => {
    const response = await CommonApi.loadCarriers(params);
    return response;
  }
);

export const fetchCountries = createAsyncThunk(
  'commonSettings/fetchCountries',
  async (params: FetchCountriesParams, thunkAPI) => {
    const response = await CommonApi.loadCountries(params);
    return response;
  }
);


export const commonSettingsSlice = createSlice({
  name: 'commonSettings',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    // fetchCarriers
    builder.addCase(
      fetchCarriers.fulfilled,
      (state: CommonSettingsState, action) => ({
        ...state,
        carriers: action.payload,
        carriersLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchCarriers.pending,
      (state: CommonSettingsState) => ({
        ...state,
        carriersLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchCarriers.rejected,
      (state: CommonSettingsState) => ({
        ...state,
        carriersLoading: LOADING_STATUS.FAILED,
      })
    );
    // fetchCountries
    builder.addCase(
      fetchCountries.fulfilled,
      (state: CommonSettingsState, action) => ({
        ...state,
        countries: action.payload,
        countriesLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchCountries.pending,
      (state: CommonSettingsState) => ({
        ...state,
        countriesLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchCountries.rejected,
      (state: CommonSettingsState) => ({
        ...state,
        countriesLoading: LOADING_STATUS.FAILED,
      })
    );
  },
});

export default commonSettingsSlice.reducer;
