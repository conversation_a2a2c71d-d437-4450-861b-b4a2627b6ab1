import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { LOADING_STATUS } from '../../../constants/index';
import {
  AccountStatusType,
  FetchAccountStatusParams,
  UpdateAccountStatusParams,
} from '../../../types/settings/index';
import AccountStatusApi from './account-status';

export interface AccountStatusState {
  data: AccountStatusType | null;
  loading: string;
  updateLoading: string;
}

const initialState: AccountStatusState = {
  data: null,
  loading: LOADING_STATUS.NOT_LOADED,
  updateLoading: LOADING_STATUS.NOT_LOADED,
};

export const fetchAccountStatus = createAsyncThunk(
  'accountStatus/fetchAccountStatus',
  async (params: FetchAccountStatusParams, thunkAPI) => {
    const response = await AccountStatusApi.loadAccountStatus(params);
    return response;
  }
);

export const updateAccountStatus = createAsyncThunk(
  'accountStatus/updateAccountStatus',
  async (params: UpdateAccountStatusParams, thunkAPI) => {
    const response = await AccountStatusApi.updateAccountStatus(params);
    return response;
  }
);

export const AccountStatusSlice = createSlice({
  name: 'accountStatus',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    // fetchAccountStatus
    builder.addCase(
      fetchAccountStatus.fulfilled,
      (state: AccountStatusState, action) => ({
        ...state,
        data: action.payload,
        loading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchAccountStatus.pending,
      (state: AccountStatusState) => ({
        ...state,
        loading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchAccountStatus.rejected,
      (state: AccountStatusState) => ({
        ...state,
        loading: LOADING_STATUS.FAILED,
      })
    );
    // updateAccountStatus
    builder.addCase(
      updateAccountStatus.fulfilled,
      (state: AccountStatusState, action) => ({
        ...state,
        updateLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      updateAccountStatus.pending,
      (state: AccountStatusState) => ({
        ...state,
        updateLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      updateAccountStatus.rejected,
      (state: AccountStatusState) => ({
        ...state,
        updateLoading: LOADING_STATUS.FAILED,
      })
    );
  },
});

export default AccountStatusSlice.reducer;
