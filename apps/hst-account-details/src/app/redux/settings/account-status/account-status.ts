import { handleAPI } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import { fetchInstance } from 'helpers';
import { ACTIVE, REINSTATE, VERIFIED } from '../../../constants';
import { STATUS_MAPPING } from '../../../modules/settings/components/update-status-modal';
import { FetchAccountStatusParams, UpdateAccountStatusParams } from '../../../types/settings';

export default {
  loadAccountStatus: handleAPI(async (params: FetchAccountStatusParams) => {
    const { vendorId, accountId } = params;
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/account-statuses`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
  updateAccountStatus: handleAPI(async (params: UpdateAccountStatusParams) => {
    const { vendorId, accountId, status, applyToSubaccounts } = params;

    if (status === VERIFIED) {
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/status-profile`,
        {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({ status })
        }
      );
    }

    if (applyToSubaccounts) {
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/bulk-update`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            status: status === REINSTATE ? ACTIVE : status,
            applyToSubaccounts,
           })
        }
      );
    }


    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/states/${STATUS_MAPPING[status]}`,
      {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        // body: JSON.stringify({ applyToSubaccounts })
      }
    );
  })
};
