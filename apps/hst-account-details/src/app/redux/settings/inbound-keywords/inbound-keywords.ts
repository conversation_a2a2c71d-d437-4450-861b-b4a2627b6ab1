import { fetchInstance, paramsToQueryString } from 'helpers';
import { handleAPI } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import {
  FetchInboundKeywordsParams,
  UpdateInboundKeywordsParams,
  DeleteInboundKeywordsParams,
} from '../../../types/settings';

export default {
  loadInboundKeywords: handleAPI(async (params: FetchInboundKeywordsParams) => {
    const { vendorId, accountId, size, next } = params;
    const queryString = paramsToQueryString({ params: { size, next } });
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/inbound-keywords?${queryString}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
  updateInboundKeywords: handleAPI(
    async (params: UpdateInboundKeywordsParams) => {
      const { vendorId, accountId, body } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/inbound-keywords/${body.id}`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json; charset=utf-8',
          },
          credentials: 'include',
          body: JSON.stringify(body),
        }
      );
    }
  ),
  createInboundKeywords: handleAPI(
    async (params: UpdateInboundKeywordsParams) => {
      const { vendorId, accountId, body } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/inbound-keywords`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json; charset=utf-8',
          },
          credentials: 'include',
          body: JSON.stringify(body),
        }
      );
    }
  ),
  deleteInboundKeywords: handleAPI(
    async (params: DeleteInboundKeywordsParams) => {
      const { vendorId, accountId, id } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/inbound-keywords/${id}`,
        {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),
};
