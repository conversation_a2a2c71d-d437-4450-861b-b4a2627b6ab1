import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { LOADING_STATUS } from '../../../constants/index';
import {
  InboundKeywordsType,
  FetchInboundKeywordsParams,
  UpdateInboundKeywordsParams,
  DeleteInboundKeywordsParams,
} from '../../../types/settings/index';
import InboundKeywordsApi from './inbound-keywords';

export interface InboundKeywordsState {
  loading: string;
  updateLoading: string;
  createLoading: string;
  deleteLoading: string;
  data: InboundKeywordsType | null;
}

const initialState: InboundKeywordsState = {
  loading: LOADING_STATUS.NOT_LOADED,
  updateLoading: LOADING_STATUS.NOT_LOADED,
  createLoading: LOADING_STATUS.NOT_LOADED,
  deleteLoading: LOADING_STATUS.NOT_LOADED,
  data: null,
};

export const fetchInboundKeywords = createAsyncThunk(
  'inboundKeywords/fetchInboundKeywords',
  async (params: FetchInboundKeywordsParams, thunkAPI) => {
    const response = await InboundKeywordsApi.loadInboundKeywords(params);
    return response;
  }
);

export const updateInboundKeywords = createAsyncThunk(
  'inboundKeywords/updateInboundKeywords',
  async (params: UpdateInboundKeywordsParams, thunkAPI) => {
    const response = await InboundKeywordsApi.updateInboundKeywords(params);
    return response;
  }
);

export const createInboundKeywords = createAsyncThunk(
  'inboundKeywords/createInboundKeywords',
  async (params: UpdateInboundKeywordsParams, thunkAPI) => {
    const response = await InboundKeywordsApi.createInboundKeywords(params);
    return response;
  }
);

export const deleteInboundKeywords = createAsyncThunk(
  'inboundKeywords/deleteInboundKeywords',
  async (params: DeleteInboundKeywordsParams, thunkAPI) => {
    const response = await InboundKeywordsApi.deleteInboundKeywords(params);
    return response;
  }
);

export const inboundKeywordsSlice = createSlice({
  name: 'inboundKeywords',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    // fetchInboundKeywords
    builder.addCase(
      fetchInboundKeywords.fulfilled,
      (state: InboundKeywordsState, action) => ({
        ...state,
        data: action.payload,
        loading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchInboundKeywords.pending,
      (state: InboundKeywordsState) => ({
        ...state,
        loading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchInboundKeywords.rejected,
      (state: InboundKeywordsState) => ({
        ...state,
        loading: LOADING_STATUS.FAILED,
      })
    );
    // updateInboundKeywords
    builder.addCase(
      updateInboundKeywords.fulfilled,
      (state: InboundKeywordsState, action) => ({
        ...state,
        updateLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      updateInboundKeywords.pending,
      (state: InboundKeywordsState) => ({
        ...state,
        updateLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      updateInboundKeywords.rejected,
      (state: InboundKeywordsState) => ({
        ...state,
        updateLoading: LOADING_STATUS.FAILED,
      })
    );
    // createInboundKeywords
    builder.addCase(
      createInboundKeywords.fulfilled,
      (state: InboundKeywordsState, action) => ({
        ...state,
        createLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      createInboundKeywords.pending,
      (state: InboundKeywordsState) => ({
        ...state,
        createLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      createInboundKeywords.rejected,
      (state: InboundKeywordsState) => ({
        ...state,
        createLoading: LOADING_STATUS.FAILED,
      })
    );
    // deleteInboundKeywords
    builder.addCase(
      deleteInboundKeywords.fulfilled,
      (state: InboundKeywordsState, action) => ({
        ...state,
        deleteLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      deleteInboundKeywords.pending,
      (state: InboundKeywordsState) => ({
        ...state,
        deleteLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      deleteInboundKeywords.rejected,
      (state: InboundKeywordsState) => ({
        ...state,
        deleteLoading: LOADING_STATUS.FAILED,
      })
    );
  },
});

export default inboundKeywordsSlice.reducer;
