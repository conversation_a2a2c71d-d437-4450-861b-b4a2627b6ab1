import { handleAPI } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import { fetchInstance, paramsToQueryString } from 'helpers';
import { FetchBroadcastDetailsParams, FetchBroadcastInsightsParams, FetchBroadcastsMessagesParams } from "../../types/broadcast";
import { FetchMessageParams } from '../../types/messages';

export default {
  loadBroadcastsMessages: handleAPI(
    async (params: FetchBroadcastsMessagesParams) => {
      const {
        accountId,
        vendorId,
        name,
        size,
        next,
      } = params;

      const queryString = paramsToQueryString({
        params: {
          name,
          size,
          next,
        },
      });
      const formattedQueryString = queryString.replace(/\[.*?\]/g, '');

      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/messages/broadcasts?${formattedQueryString}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),
  loadBroadcastDetails: handleAPI(
    async (params: FetchBroadcastDetailsParams) => {
      const {
        accountId,
        vendorId,
        campaignId,
      } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/messages/broadcasts/${campaignId}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),
  loadBroadcastInsights: handleAPI(
    async (params: FetchBroadcastInsightsParams) => {
      const {
        accountId,
        vendorId,
        date,
        accounts,
        statuses,
        source,
        metadataKey,
        metadataValue,
        timezone,
        groupBy,
        direction,
        destination,
      } = params;
      const queryString = paramsToQueryString({
        params: {
          date,
          accounts,
          statuses,
          source,
          metadataKey,
          metadataValue,
          timezone,
          groupBy,
          direction,
          destination,
        },
      });
      const formattedQueryString = queryString.replace(/\[.*?\]/g, '');

      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/messages/insights?${formattedQueryString}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),

  loadMessagesDetail: handleAPI(async (params: FetchMessageParams) => {
    const {
      accountId,
      vendorId,
      ...rest
    } = params;
    const queryString = paramsToQueryString({
      params: rest,
    });
    const formattedQueryString = queryString.replace(/\[.*?\]/g, '');

    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/messages/detail?${formattedQueryString}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
}
