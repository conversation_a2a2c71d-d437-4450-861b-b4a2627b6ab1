import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { LOADING_STATUS } from '../../constants';
import {
  CreateAutomatedBroadcastParams,
  DeleteAutomatedBroadcastParams,
  FetchAutomatedBroadcastsParams,
  GetAutomatedBroadcastByIdParams,
  UpdateAutomatedBroadcastParams,
} from '../../types/automated-broadcast';
import AutomatedBroadcastApi from './automated-broadcast-api';

interface AutomatedBroadcastsState {
  loading: {
    load: string;
    create: string;
    update: string;
    delete: string;
    getById: string;
  };
  data: {
    all: any[];
    byId: any | null;
    created: any | null;
    updated: any | null;
    deleted: boolean;
  };
  error: {
    create?: string;
    update?: string;
  };
}

const initialState: AutomatedBroadcastsState = {
  loading: {
    load: LOADING_STATUS.NOT_LOADED,
    create: LOADING_STATUS.NOT_LOADED,
    update: LOADING_STATUS.NOT_LOADED,
    delete: LOADING_STATUS.NOT_LOADED,
    getById: LOADING_STATUS.NOT_LOADED,
  },
  data: {
    all: [],
    byId: null,
    created: null,
    updated: null,
    deleted: false,
  },
  error: {},
};

export const loadAutomatedBroadcasts = createAsyncThunk(
  'automatedBroadcast/loadAutomatedBroadcasts',
  async (params: FetchAutomatedBroadcastsParams, thunkAPI) => {
    const response = await AutomatedBroadcastApi.loadAutomatedBroadcasts(
      params
    );
    return response;
  }
);

export const createAutomatedBroadcast = createAsyncThunk(
  'automatedBroadcast/createAutomatedBroadcast',
  async (params: CreateAutomatedBroadcastParams, thunkAPI) => {
    const response = await AutomatedBroadcastApi.createAutomatedBroadcast(
      params
    );
    return response;
  }
);

export const updateAutomatedBroadcast = createAsyncThunk(
  'automatedBroadcast/updateAutomatedBroadcast',
  async (params: UpdateAutomatedBroadcastParams, thunkAPI) => {
    const response = await AutomatedBroadcastApi.updateAutomatedBroadcast(
      params
    );
    return response;
  }
);

export const deleteAutomatedBroadcast = createAsyncThunk(
  'automatedBroadcast/deleteAutomatedBroadcast',
  async (params: DeleteAutomatedBroadcastParams, thunkAPI) => {
    const response = await AutomatedBroadcastApi.deleteAutomatedBroadcast(
      params
    );
    return response;
  }
);

export const getAutomatedBroadcastById = createAsyncThunk(
  'automatedBroadcast/getAutomatedBroadcastById',
  async (params: GetAutomatedBroadcastByIdParams, thunkAPI) => {
    const response = await AutomatedBroadcastApi.getAutomatedBroadcastById(
      params
    );
    return response;
  }
);

const automatedBroadcastSlice = createSlice({
  name: 'automatedBroadcast',
  initialState,
  reducers: {
    resetStatus: (state) => ({
      ...state,
      loading: {
        ...state.loading,
        create: LOADING_STATUS.NOT_LOADED,
        update: LOADING_STATUS.NOT_LOADED
      },
      error: {}
    })
  },
  extraReducers: (builder) => {
    // Load automated broadcasts
    builder.addCase(loadAutomatedBroadcasts.pending, (state, action) => ({
      ...state,
      loading: { ...state.loading, load: LOADING_STATUS.LOADING },
    }));
    builder.addCase(loadAutomatedBroadcasts.fulfilled, (state, action) => ({
      ...state,
      loading: { ...state.loading, load: LOADING_STATUS.SUCCEEDED },
      data: { ...state.data, all: action.payload },
    }));
    builder.addCase(loadAutomatedBroadcasts.rejected, (state) => ({
      ...state,
      loading: { ...state.loading, load: LOADING_STATUS.FAILED },
    }));
    // Create automated broadcast
    builder.addCase(createAutomatedBroadcast.pending, (state) => ({
      ...state,
      loading: { ...state.loading, create: LOADING_STATUS.LOADING },
      error: { ...state.error, create: undefined },
    }));
    builder.addCase(createAutomatedBroadcast.fulfilled, (state, action) => ({
      ...state,
      loading: { ...state.loading, create: LOADING_STATUS.SUCCEEDED },
      data: { ...state.data, created: action.payload },
      error: { ...state.error, create: undefined },
    }));
    builder.addCase(createAutomatedBroadcast.rejected, (state, action) => ({
      ...state,
      loading: { ...state.loading, create: LOADING_STATUS.FAILED },
      error: { ...state.error, create: action.error.message },
    }));
    // Update automated broadcast
    builder.addCase(updateAutomatedBroadcast.pending, (state) => ({
      ...state,
      loading: { ...state.loading, update: LOADING_STATUS.LOADING },
      error: { ...state.error, update: undefined },
    }));
    builder.addCase(updateAutomatedBroadcast.fulfilled, (state, action) => ({
      ...state,
      loading: { ...state.loading, update: LOADING_STATUS.SUCCEEDED },
      data: { ...state.data, updated: action.payload },
      error: { ...state.error, update: undefined },
    }));
    builder.addCase(updateAutomatedBroadcast.rejected, (state, action) => ({
      ...state,
      loading: { ...state.loading, update: LOADING_STATUS.FAILED },
      error: { ...state.error, update: action.error.message },
    }));
    // Get automated broadcast by id
    builder.addCase(getAutomatedBroadcastById.pending, (state) => ({
      ...state,
      loading: { ...state.loading, getById: LOADING_STATUS.LOADING },
    }));
    builder.addCase(getAutomatedBroadcastById.fulfilled, (state, action) => ({
      ...state,
      loading: { ...state.loading, getById: LOADING_STATUS.SUCCEEDED },
      data: { ...state.data, byId: action.payload },
    }));
    builder.addCase(getAutomatedBroadcastById.rejected, (state) => ({
      ...state,
      loading: { ...state.loading, getById: LOADING_STATUS.FAILED },
    }));
    // Delete automated broadcast
    builder.addCase(deleteAutomatedBroadcast.pending, (state) => ({
      ...state,
      loading: { ...state.loading, delete: LOADING_STATUS.LOADING },
    }));
    builder.addCase(deleteAutomatedBroadcast.fulfilled, (state) => ({
      ...state,
      loading: { ...state.loading, delete: LOADING_STATUS.SUCCEEDED },
      data: { ...state.data, deleted: true },
    }));
    builder.addCase(deleteAutomatedBroadcast.rejected, (state) => ({
      ...state,
      loading: { ...state.loading, delete: LOADING_STATUS.FAILED },
    }));
  },
});

export const { resetStatus } = automatedBroadcastSlice.actions;
export default automatedBroadcastSlice.reducer;
