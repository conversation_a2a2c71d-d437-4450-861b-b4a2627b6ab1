import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import _concat from 'lodash/concat';
import _find from 'lodash/find';
import _reduce from 'lodash/reduce';

import { LOADING_STATUS } from '../../constants/index';
import {
  AutomatedBroadcastsType,
  DeleteAutomatedBroadcastParams,
  FetchAutomatedBroadcastsParams,
  FetchMessageParams,
  FetchMessagesInsightsParams,
  FetchMetakeysParams,
  MessagesInsightsType,
  MessagesType,
  MetakeysType,
  SummaryItemType,
} from '../../types/messages';
import messagesApi from './messages-api';

export interface messagesState {
  messagesInsightsLoading: string;
  messagesInsights: MessagesInsightsType | null;
  metakeys: MetakeysType | null;
  metakeysLoading: string;
  messagesDetailLoading: string;
  messagesDetail: MessagesType | null;
  automatedBroadcastsLoading: string;
  automatedBroadcasts: AutomatedBroadcastsType | null;
  deleteAutomatedBroadcastLoading: string;
  messageDetailByMessageIdLoading: string;
  messageDetailByMessageId: MessageDetailResponse | null;
}

const initialState: messagesState = {
  messagesInsightsLoading: LOADING_STATUS.NOT_LOADED,
  messagesInsights: null,
  metakeysLoading: LOADING_STATUS.NOT_LOADED,
  metakeys: null,
  messagesDetailLoading: LOADING_STATUS.NOT_LOADED,
  messagesDetail: null,
  automatedBroadcastsLoading: LOADING_STATUS.NOT_LOADED,
  automatedBroadcasts: null,
  deleteAutomatedBroadcastLoading: LOADING_STATUS.NOT_LOADED,
  messageDetailByMessageIdLoading: LOADING_STATUS.NOT_LOADED,
  messageDetailByMessageId: null,
};

interface MessageDeliveryResource {
  messageId: string;
  submissionId: string;
  submissionDate: string; // ISO 8601 date-time format
  provider: string;
  status: string;
  providerErrorCode: string;
}

interface MessageDetailResponse {
  resources: MessageDeliveryResource[];
}

export interface messageDetailState {
  messagesDetailLoading: string;
  messagesDetail: MessagesType | null;
  messageDetailByMessageIdLoading: string;
  messageDetailByMessageId: MessageDetailResponse | null;
}

export const fetchMessagesInsights = createAsyncThunk(
  'messages/fetchMessagesInsights',
  async (params: FetchMessagesInsightsParams, thunkAPI) => {
    if (!params.source) {
      const response = await messagesApi.loadMessagesInsights(params);
      return response;
    }
    const outboundResponse = await messagesApi.loadMessagesInsights({
      ...params,
      direction: 'OUTBOUND',
      destination: params.source,
      source: undefined,
    });

    const inboundResponse = await messagesApi.loadMessagesInsights({
      ...params,
      direction: 'INBOUND',
      source: params.source,
    });

    let combinedSummaries = _concat(
      outboundResponse.summaries,
      inboundResponse.summaries
    );

    combinedSummaries = _reduce(
      combinedSummaries,
      (result: SummaryItemType[], summaryItem: SummaryItemType) => {
        let newResult = [...result];
        const existedGroupItem = _find(
          result,
          (resultItem: SummaryItemType) =>
            resultItem.group === summaryItem.group
        );

        if (existedGroupItem) {
          const newItem = {
            group: summaryItem.group,
            date: summaryItem.date,
            totalBillingUnits:
              summaryItem.totalBillingUnits +
              existedGroupItem.totalBillingUnits,
            totalOptOut: summaryItem.totalOptOut + existedGroupItem.totalOptOut,
            totalReceived:
              summaryItem.totalReceived + existedGroupItem.totalReceived,
            totalSent: summaryItem.totalSent + existedGroupItem.totalSent,
          };
          newResult = result.filter((item) => item.group !== summaryItem.group);
          newResult.push(newItem);
        } else {
          newResult.push(summaryItem);
        }

        return newResult;
      },
      []
    );

    return {
      summaries: combinedSummaries,
      totalBillingUnits:
        outboundResponse.totalBillingUnits + inboundResponse.totalBillingUnits,
      totalOptOut: outboundResponse.totalOptOut + inboundResponse.totalOptOut,
      totalReceived:
        outboundResponse.totalReceived + inboundResponse.totalReceived,
      totalSent: outboundResponse.totalSent + inboundResponse.totalSent,
    };
  }
);

export const fetchMetakeys = createAsyncThunk(
  'messages/fetchMetakeys',
  async (params: FetchMetakeysParams, thunkAPI) => {
    const response = await messagesApi.loadMetakeys(params);
    return response;
  }
);

export const fetchMessagesDetail = createAsyncThunk(
  'messages/fetchMessagesDetail',
  async (params: FetchMessageParams, thunkAPI) => {
    const response = await messagesApi.loadMessagesDetail(params);
    return response;
  }
);

export const fetchMessageDetailByMessageId = createAsyncThunk(
  'messages/fetchMessageDetailByMessageId',
  async (params: { messageId: string, vendorId: string, accountId: string }, thunkAPI) => {
    const response = await messagesApi.loadMessageDetailByMessageId(params);
    return response;
  }
);

export const fetchAutomatedBroadcasts = createAsyncThunk(
  'messages/fetchAutomatedBroadcasts',
  async (params: FetchAutomatedBroadcastsParams, thunkAPI) => {
    const response = await messagesApi.loadAutomatedBroadcasts(params);
    return response;
  }
);
export const deleteAutomatedBroadcast = createAsyncThunk(
  'messages/deleteAutomatedBroadcast',
  async (params: DeleteAutomatedBroadcastParams, thunkAPI) => {
    const response = await messagesApi.deleteAutomatedBroadcast(params);
    return response;
  }
);

export const messagesSlice = createSlice({
  name: 'messages',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    // fetchMessagesInsights
    builder.addCase(
      fetchMessagesInsights.fulfilled,
      (state: messagesState, action) => ({
        ...state,
        messagesInsights: action.payload,
        messagesInsightsLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(fetchMessagesInsights.pending, (state: messagesState) => ({
      ...state,
      messagesInsightsLoading: LOADING_STATUS.LOADING,
    }));
    builder.addCase(fetchMessagesInsights.rejected, (state: messagesState) => ({
      ...state,
      messagesInsights: null,
      messagesInsightsLoading: LOADING_STATUS.FAILED,
    }));
    // fetchMetakeys
    builder.addCase(
      fetchMetakeys.fulfilled,
      (state: messagesState, action) => ({
        ...state,
        metakeys: action.payload,
        metakeysLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(fetchMetakeys.pending, (state: messagesState) => ({
      ...state,
      metakeysLoading: LOADING_STATUS.LOADING,
    }));
    builder.addCase(fetchMetakeys.rejected, (state: messagesState) => ({
      ...state,
      metakeys: null,
      metakeysLoading: LOADING_STATUS.FAILED,
    }));
    // fetchMessagesDetail
    builder.addCase(
      fetchMessagesDetail.fulfilled,
      (state: messagesState, action) => ({
        ...state,
        messagesDetail: action.payload,
        messagesDetailLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(fetchMessagesDetail.pending, (state: messagesState) => ({
      ...state,
      messagesDetailLoading: LOADING_STATUS.LOADING,
    }));
    builder.addCase(fetchMessagesDetail.rejected, (state: messagesState) => ({
      ...state,
      messagesDetail: null,
      messagesDetailLoading: LOADING_STATUS.FAILED,
    }));
    // fetchAutomatedBroadcasts
    builder.addCase(
      fetchAutomatedBroadcasts.fulfilled,
      (state: messagesState, action) => ({
        ...state,
        automatedBroadcasts: action.payload,
        automatedBroadcastsLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchAutomatedBroadcasts.pending,
      (state: messagesState) => ({
        ...state,
        automatedBroadcastsLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchAutomatedBroadcasts.rejected,
      (state: messagesState) => ({
        ...state,
        automatedBroadcasts: null,
        automatedBroadcastsLoading: LOADING_STATUS.FAILED,
      })
    );
    // deleteAutomatedBroadcast
    builder.addCase(
      deleteAutomatedBroadcast.fulfilled,
      (state: messagesState, action) => ({
        ...state,
        deleteAutomatedBroadcastLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      deleteAutomatedBroadcast.pending,
      (state: messagesState) => ({
        ...state,
        deleteAutomatedBroadcastLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      deleteAutomatedBroadcast.rejected,
      (state: messagesState) => ({
        ...state,
        deleteAutomatedBroadcastLoading: LOADING_STATUS.FAILED,
      })
    );
    // fetchMessageDetailByMessageId
    builder.addCase(
      fetchMessageDetailByMessageId.fulfilled,
      (state: messagesState, action) => ({
        ...state,
        messageDetailByMessageId: action.payload,
        messageDetailByMessageIdLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(fetchMessageDetailByMessageId.pending, (state: messagesState) => ({
      ...state,
      messageDetailByMessageIdLoading: LOADING_STATUS.LOADING,
    }));
    builder.addCase(fetchMessageDetailByMessageId.rejected, (state: messagesState) => ({
      ...state,
      messageDetailByMessageId: null,
      messageDetailByMessageIdLoading: LOADING_STATUS.FAILED,
    }));
  },
});

export default messagesSlice.reducer;
