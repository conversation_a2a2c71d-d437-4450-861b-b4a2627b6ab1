import { handleAPI } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import { fetchInstance } from 'helpers';
import {
  CreateAutomatedBroadcastParams,
  DeleteAutomatedBroadcastParams,
  FetchAutomatedBroadcastsParams,
  GetAutomatedBroadcastByIdParams,
  UpdateAutomatedBroadcastParams,
} from '../../types/automated-broadcast';

export default {
  loadAutomatedBroadcasts: handleAPI(
    async (params: FetchAutomatedBroadcastsParams) => {
      const { vendorId, accountId } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/messages/automated-broadcasts`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),
  createAutomatedBroadcast: handleAPI(
    async (params: CreateAutomatedBroadcastParams) => {
      const { vendorId, accountId, ...body } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/messages/automated-broadcasts`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify(body),
        }
      );
    }
  ),
  updateAutomatedBroadcast: handleAPI(
    async (params: UpdateAutomatedBroadcastParams) => {
      const { vendorId, accountId, id, ...body } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/messages/automated-broadcasts/${id}`,
        {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify(body),
        }
      );
    }
  ),
  deleteAutomatedBroadcast: handleAPI(
    async (params: DeleteAutomatedBroadcastParams) => {
      const { vendorId, accountId, id } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/messages/automated-broadcasts/${id}`,
        {
          method: 'DELETE',
        }
      );
    }
  ),
  getAutomatedBroadcastById: handleAPI(
    async (params: GetAutomatedBroadcastByIdParams) => {
      const { vendorId, accountId, id } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/messages/automated-broadcasts/${id}`,
        {
          method: 'GET',
        }
      );
    }
  ),
};
