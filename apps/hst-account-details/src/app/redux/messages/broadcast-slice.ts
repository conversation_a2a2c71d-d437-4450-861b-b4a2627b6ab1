import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { LOADING_STATUS } from '../../constants';
import {
  BroadcastDetailsTablesType,
  BroadcastDetailsType,
  BroadcastInsightsTablesType,
  BroadcastInsightsType,
  FetchBroadcastDetailsParams,
  FetchBroadcastInsightsParams,
  FetchBroadcastsMessagesParams,
  FetchBroadcastsMessagesResponse,
  FetchMessageDetailsParams,
} from '../../types/broadcast';
import BroadcastApi from './broadcast-api';

export interface BroadcastsState {
  loading: string;
  data: FetchBroadcastsMessagesResponse | null;
  detailsLoading: string;
  detailsData: BroadcastDetailsType | null;
  broadcastInsightsLoading: string;
  broadcastInsightsData: BroadcastInsightsType | null;
  broadcastDetailsTablesData: BroadcastDetailsTablesType | null;
  broadcastDetailsTablesLoading: string;
  broadcastInsightsTablesData: BroadcastInsightsTablesType | null;
  broadcastInsightsTablesLoading: string;
}

const initialState: BroadcastsState = {
  loading: LOADING_STATUS.NOT_LOADED,
  data: null,
  detailsLoading: LOADING_STATUS.NOT_LOADED,
  detailsData: null,
  broadcastInsightsLoading: LOADING_STATUS.NOT_LOADED,
  broadcastInsightsData: null,
  broadcastDetailsTablesData: null,
  broadcastDetailsTablesLoading: LOADING_STATUS.NOT_LOADED,
  broadcastInsightsTablesData: null,
  broadcastInsightsTablesLoading: LOADING_STATUS.NOT_LOADED,
};

export const fetchBroadcastsMessages = createAsyncThunk(
  'broadcasts/fetchBroadcastsMessages',
  async (params: FetchBroadcastsMessagesParams, thunkAPI) => {
    const response = await BroadcastApi.loadBroadcastsMessages(params);
    return response;
  }
);

export const fetchBroadcastDetails = createAsyncThunk(
  'broadcasts/fetchBroadcastDetails',
  async (params: FetchBroadcastDetailsParams, thunkAPI) => {
    const response = await BroadcastApi.loadBroadcastDetails(params);
    return response;
  }
);

export const fetchBroadcastInsights = createAsyncThunk(
  'broadcasts/fetchBroadcastInsights',
  async (params: FetchBroadcastInsightsParams, thunkAPI) => {
    const response = await BroadcastApi.loadBroadcastInsights(params);
    return response;
  }
);

export const fetchBroadcastInsightsForTables = createAsyncThunk(
  'broadcasts/fetchBroadcastInsightsForTables',
  async (params: FetchBroadcastInsightsParams, thunkAPI) => {
    const response = await BroadcastApi.loadBroadcastInsights(params);
    const { direction, statuses, optOut } = params || {};
    let tableType = '';
    if (direction === 'outbound') {
      tableType = (statuses?.length || 0) > 3 ? 'undelivered' : 'outbound';
    } else {
      tableType = optOut ? 'optOut' : 'inbound';
    }
    return {
      tableType,
      response,
    };
  }
);

export const fetchMessagesDetail = createAsyncThunk(
  'broadcasts/loadMessagesDetail',
  async (params: FetchMessageDetailsParams, thunkAPI) => {
    const response = await BroadcastApi.loadMessagesDetail(params);
    const { direction, statuses, optOut } = params || {};
    let tableType = '';
    if (direction === 'outbound') {
      tableType = (statuses?.length || 0) > 3 ? 'undelivered' : 'outbound';
    } else {
      tableType = optOut ? 'optOut' : 'inbound';
    }
    return {
      tableType,
      response,
    };
  }
);

export const broadcastsSlice = createSlice({
  name: 'broadcasts',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    // fetchBroadcastsMessages
    builder.addCase(
      fetchBroadcastsMessages.fulfilled,
      (state: BroadcastsState, action) => ({
        ...state,
        data: action.payload,
        loading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchBroadcastsMessages.pending,
      (state: BroadcastsState) => ({
        ...state,
        loading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchBroadcastsMessages.rejected,
      (state: BroadcastsState) => ({
        ...state,
        data: null,
        loading: LOADING_STATUS.FAILED,
      })
    );
    // fetchBroadcastDetails
    builder.addCase(
      fetchBroadcastDetails.fulfilled,
      (state: BroadcastsState, action) => ({
        ...state,
        detailsData: action.payload,
        detailsLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchBroadcastDetails.pending,
      (state: BroadcastsState) => ({
        ...state,
        detailsLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchBroadcastDetails.rejected,
      (state: BroadcastsState) => ({
        ...state,
        detailsLoading: LOADING_STATUS.FAILED,
      })
    );
    // fetchBroadcastInsights
    builder.addCase(
      fetchBroadcastInsights.fulfilled,
      (state: BroadcastsState, action) => ({
        ...state,
        broadcastInsightsData: action.payload,
        broadcastInsightsLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchBroadcastInsights.pending,
      (state: BroadcastsState) => ({
        ...state,
        broadcastInsightsLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchBroadcastInsights.rejected,
      (state: BroadcastsState) => ({
        ...state,
        broadcastInsightsLoading: LOADING_STATUS.FAILED,
      })
    );
    // fetchMessagesDetail
    builder.addCase(
      fetchMessagesDetail.fulfilled,
      (state: BroadcastsState, action) => ({
        ...state,
        broadcastDetailsTablesData: {
          ...state.broadcastDetailsTablesData,
          [action.payload.tableType]: action.payload.response,
        },
        broadcastDetailsTablesLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(fetchMessagesDetail.pending, (state: BroadcastsState) => ({
      ...state,
      broadcastDetailsTablesLoading: LOADING_STATUS.LOADING,
    }));
    builder.addCase(fetchMessagesDetail.rejected, (state: BroadcastsState) => ({
      ...state,
      broadcastDetailsTablesLoading: LOADING_STATUS.FAILED,
    }));
    // fetchBroadcastInsightsForTables
    builder.addCase(
      fetchBroadcastInsightsForTables.fulfilled,
      (state: BroadcastsState, action) => ({
        ...state,
        broadcastInsightsTablesData: {
          ...state.broadcastInsightsTablesData,
          [action.payload.tableType]: action.payload.response,
        },
        broadcastDetailsTablesLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchBroadcastInsightsForTables.pending,
      (state: BroadcastsState) => ({
        ...state,
        broadcastDetailsTablesLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchBroadcastInsightsForTables.rejected,
      (state: BroadcastsState) => ({
        ...state,
        broadcastDetailsTablesLoading: LOADING_STATUS.FAILED,
      })
    );
  },
});

export default broadcastsSlice.reducer;
