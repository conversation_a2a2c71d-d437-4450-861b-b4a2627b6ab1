import { handleAPI } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import { fetchInstance, paramsToQueryString } from 'helpers';
import {
  DeleteAutomatedBroadcastsParams,
  FetchAutomatedBroadcastsParams,
  FetchMessageParams,
  FetchMessagesInsightsParams,
  FetchMetakeysParams
} from '../../types/messages';

export default {
  loadMessagesInsights: handleAPI(
    async (params: FetchMessagesInsightsParams) => {
      const {
        accountId,
        vendorId,
        date,
        accounts,
        statuses,
        source,
        metadataKey,
        metadataValue,
        timezone,
        groupBy,
        direction,
        destination,
      } = params;
      const queryString = paramsToQueryString({
        params: {
          date,
          accounts,
          statuses,
          source,
          metadataKey,
          metadataValue,
          timezone,
          groupBy,
          direction,
          destination,
        },
      });
      const formattedQueryString = queryString.replace(/\[.*?\]/g, '');

      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/messages/insights?${formattedQueryString}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),
  loadMetakeys: handleAPI(async (params: FetchMetakeysParams) => {
    const { accountId, vendorId, date } = params;
    const queryString = paramsToQueryString({
      params: {
        date,
      },
    });
    const formattedQueryString = queryString.replace(/\[.*?\]/g, '');

    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/messages/metakeys?${formattedQueryString}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
  loadMessageDetailByMessageId: handleAPI(async (params: { messageId: string, vendorId: string, accountId: string }) => {
    const { messageId, vendorId, accountId } = params;
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/messages/detail/${messageId}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
  loadMessagesDetail: handleAPI(async (params: FetchMessageParams) => {
    const {
      accountId,
      vendorId,
      date,
      accounts,
      statuses,
      source,
      destination,
      metadataKey,
      metadataValue,
      timezone,
      groupBy,
      sortByType,
      sortDirection,
      direction,
      next,
      size,
    } = params;
    const queryString = paramsToQueryString({
      params: {
        date,
        accounts,
        statuses,
        source,
        destination,
        metadataKey,
        metadataValue,
        timezone,
        groupBy,
        sortByType,
        sortDirection,
        direction,
        next,
        size,
      },
    });
    const formattedQueryString = queryString.replace(/\[.*?\]/g, '');

    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/messages/detail?${formattedQueryString}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
  loadAutomatedBroadcasts: handleAPI(async (params: FetchAutomatedBroadcastsParams) => {
    const { accountId, vendorId, size, next } = params;
    const queries = paramsToQueryString({ params: { size, next } })

    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/messages/automated-broadcasts?${queries}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
  deleteAutomatedBroadcast: handleAPI(async (params: DeleteAutomatedBroadcastsParams) => {
    const { accountId, vendorId, broadcastId } = params;

    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/messages/automated-broadcasts/${broadcastId}`,
      {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
};
