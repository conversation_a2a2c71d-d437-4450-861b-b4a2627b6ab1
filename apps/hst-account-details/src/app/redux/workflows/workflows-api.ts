import { fetchInstance } from 'helpers';
import queryString from 'query-string';
import { handleAPI } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import {
  FetchWorkflows,
  FetchWorkflowsDetailParams,
  GetExecutionDetailsParams,
  GetWorkflowDetailsParams,
  TUpdateWorkflow,
} from '../../types/workflows/workflows';

export default {
  getWorkflows: handleAPI((body: FetchWorkflows) => {
    const queries = queryString.stringify({next: body.params.next, size: body.params.size});
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${body.vendorId}:${body.accountId}/workflows?${queries}`,
      {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
      credentials: 'include',
    })
}),
  getWorkflowDetails: handleAPI(async (params: GetWorkflowDetailsParams) => {
    const {workflowID, vendorId, accountId} = params
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/workflows/${workflowID}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json;charset=UTF-8',
        },
        credentials: 'include',
      })
  }),
  getWorkflowExecutions: handleAPI(async (params: FetchWorkflowsDetailParams) => {
    const {workflowID, next, size, vendorId, accountId, status} = params;
    const queries = queryString.stringify({next, size, status});
    return fetchInstance()(`${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/workflows/${workflowID}/executions?${queries}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json;charset=UTF-8',
        },
        credentials: 'include',
      }
    );
  }),
  getExecutionDetails: handleAPI(async (params: GetExecutionDetailsParams) => {
    const {workflowId, vendorId, accountId, executionId} = params
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/workflows/${workflowId}/executions/${executionId}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json;charset=UTF-8',
        },
        credentials: 'include',
      })
  }),
  updateWorkflow: handleAPI(async ({
    vendorId, accountId, workflowId, body,
  }: TUpdateWorkflow) => (
    fetchInstance()(`${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/workflows/${workflowId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
      body: JSON.stringify(body),
      credentials: 'include',
    })
  )),
};
