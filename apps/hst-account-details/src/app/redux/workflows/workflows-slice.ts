import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import workflowsApi from './workflows-api';
import { LOADING_STATUS } from '../../constants/index';

import {
  Executions,
  FetchWorkflows,
  FetchWorkflowsDetailParams,
  GetExecutionDetailsParams,
  GetWorkflowDetailsParams,
  TWorkflows,
} from '../../types/workflows/workflows';

export interface WorkflowsState {
  loading: string;
  data: TWorkflows | null;
  executionsLoading: string;
  executionsData: Executions | null;
  executionLoading: string;
  executionData: Executions | null;
}

const initialState: WorkflowsState = {
  loading: LOADING_STATUS.NOT_LOADED,
  data: null,
  executionsLoading: LOADING_STATUS.NOT_LOADED,
  executionsData: null,
  executionData: null,
  executionLoading: LOADING_STATUS.NOT_LOADED,
};

export const getWorkflows = createAsyncThunk(
  'workflows/getWorkflows',
  async (params: FetchWorkflows, thunkAPI) => {
    const response = await workflowsApi.getWorkflows(params);
    return response;
  }
);

export const getWorkflowDetails = createAsyncThunk(
  'workflows/getWorkflowDetails',
  async (params: GetWorkflowDetailsParams, thunkAPI) => {
    const response = await workflowsApi.getWorkflowDetails(params);
    return response;
  }
);


export const getWorkflowExecutions = createAsyncThunk(
  'workflows/getWorkflowExecutions',
  async (params: FetchWorkflowsDetailParams, thunkAPI) => {
    const response = await workflowsApi.getWorkflowExecutions(params);
    return response;
  }
);

export const getExecutionDetails = createAsyncThunk(
  'workflows/getExecutionDetails',
  async (params: GetExecutionDetailsParams, thunkAPI) => {
    const res = await workflowsApi.getExecutionDetails(params);
    return res;
  }
)

export const workflowsSilce = createSlice({
  name: 'workflows',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(
      getWorkflows.fulfilled,
      (state: WorkflowsState, action) => ({
        ...state,
        data: action.payload,
        loading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      getWorkflows.pending,
      (state: WorkflowsState) => ({
        ...state,
        loading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      getWorkflows.rejected,
      (state: WorkflowsState) => ({
        ...state,
        data: null,
        loading: LOADING_STATUS.FAILED,
      })
    );
    builder.addCase(
      getWorkflowDetails.fulfilled,
      (state: WorkflowsState, action) => ({
        ...state,
        workflow: action.payload,
        loading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      getWorkflowDetails.pending,
      (state: WorkflowsState) => ({
        ...state,
        loading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      getWorkflowDetails.rejected,
      (state: WorkflowsState) => ({
        ...state,
        workflowDetail: null,
        loading: LOADING_STATUS.FAILED,
      })
    );
    builder.addCase(
      getWorkflowExecutions.fulfilled,
      (state: WorkflowsState, action) => ({
        ...state,
        executionsData: action.payload,
        executionsLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      getWorkflowExecutions.pending,
      (state: WorkflowsState) => ({
        ...state,
        executionsData: null,
        executionsLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      getWorkflowExecutions.rejected,
      (state: WorkflowsState) => ({
        ...state,
        executionsData: null,
        executionsLoading: LOADING_STATUS.FAILED,
      })
    );
    builder.addCase(
      getExecutionDetails.fulfilled,
      (state: WorkflowsState, action) => ({
        ...state,
        executionData: action.payload,
        executionLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      getExecutionDetails.pending,
      (state: WorkflowsState) => ({
        ...state,
        executionData: null,
        executionLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      getExecutionDetails.rejected,
      (state: WorkflowsState) => ({
        ...state,
        executionData: null,
        executionLoading: LOADING_STATUS.FAILED,
      })
    );
  },
});

export default workflowsSilce.reducer;
