import { fetchInstance, paramsToQueryString } from 'helpers';
import { handleAPI } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import {
  FetchFeaturesParams,
  UpdateFeatureParams,
} from '../../types/api-features';

export default {
  loadActiveFeatures: handleAPI(async (params: FetchFeaturesParams) => {
    const { vendorId, accountId } = params;
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/account-detail/features`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),

  loadAllFeatures: handleAPI(
    async (params: { vendorId: string; accountId: string }) => {
      const { vendorId, accountId } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/features`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),

  updateActieFeatures: handleAPI(async (params: UpdateFeatureParams) => {
    const { vendorId, accountId, data } = params;
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/features`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ data }),
      }
    );
  }),
};
