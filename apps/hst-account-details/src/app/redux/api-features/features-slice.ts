/* eslint-disable no-param-reassign */
import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { LOADING_STATUS } from '../../constants';
import {
  FeaturesResponse,
  FetchFeaturesParams,
} from '../../types/api-features';
import FeaturesApi from './features-api';

export interface FeaturesState {
  allFeaturesLoading: string;
  allFeaturesData: FeaturesResponse | null;
  activeFeaturesLoading: string;
  activeFeaturesData: FeaturesResponse | null;
  updateActiveFeaturesLoading: string;
}

const initialState: FeaturesState = {
  allFeaturesLoading: LOADING_STATUS.NOT_LOADED,
  allFeaturesData: null,
  activeFeaturesLoading: LOADING_STATUS.NOT_LOADED,
  activeFeaturesData: null,
  updateActiveFeaturesLoading: LOADING_STATUS.NOT_LOADED,
};

export const loadAllFeatures = createAsyncThunk(
  'features/loadAllFeatures',
  async (params: FetchFeaturesParams, thunkAPI) => {
    const response = await FeaturesApi.loadAllFeatures(params);
    return response;
  }
);

export const loadActiveFeatures = createAsyncThunk(
  'features/loadActiveFeatures',
  async (params: FetchFeaturesParams, thunkAPI) => {
    const response = await FeaturesApi.loadActiveFeatures(params);
    return response;
  }
);

export const updateActiveFeatures = createAsyncThunk(
  'features/updateActiveFeatures',
  async (
    params: Parameters<typeof FeaturesApi.updateActieFeatures>[0],
    thunkAPI
  ) => {
    const response = await FeaturesApi.updateActieFeatures(params);
    return response;
  }
);

export const FeaturesSlice = createSlice({
  name: 'Features',
  initialState,
  reducers: {
    resetUpdateLoading: (state): void => {
      state.updateActiveFeaturesLoading = LOADING_STATUS.NOT_LOADED;
    },
  },
  extraReducers: (builder) => {
    builder
      // All Features cases
      .addCase(loadAllFeatures.pending, (state: FeaturesState) => ({
        ...state,
        allFeaturesLoading: LOADING_STATUS.LOADING,
      }))
      .addCase(loadAllFeatures.fulfilled, (state: FeaturesState, action) => ({
        ...state,
        allFeaturesLoading: LOADING_STATUS.SUCCEEDED,
        allFeaturesData: action.payload,
      }))
      .addCase(loadAllFeatures.rejected, (state: FeaturesState) => ({
        ...state,
        allFeaturesLoading: LOADING_STATUS.FAILED,
      }))
      // Active Features cases
      .addCase(loadActiveFeatures.pending, (state: FeaturesState) => ({
        ...state,
        activeFeaturesLoading: LOADING_STATUS.LOADING,
      }))
      .addCase(
        loadActiveFeatures.fulfilled,
        (state: FeaturesState, action) => ({
          ...state,
          activeFeaturesLoading: LOADING_STATUS.SUCCEEDED,
          activeFeaturesData: action.payload,
        })
      )
      .addCase(loadActiveFeatures.rejected, (state: FeaturesState) => ({
        ...state,
        activeFeaturesLoading: LOADING_STATUS.FAILED,
      }))
      // Update Features cases
      .addCase(updateActiveFeatures.pending, (state: FeaturesState) => ({
        ...state,
        updateActiveFeaturesLoading: LOADING_STATUS.LOADING,
      }))
      .addCase(updateActiveFeatures.fulfilled, (state: FeaturesState) => ({
        ...state,
        updateActiveFeaturesLoading: LOADING_STATUS.SUCCEEDED,
      }))
      .addCase(updateActiveFeatures.rejected, (state: FeaturesState) => ({
        ...state,
        updateActiveFeaturesLoading: LOADING_STATUS.FAILED,
      }));
  },
});

export default FeaturesSlice.reducer;
