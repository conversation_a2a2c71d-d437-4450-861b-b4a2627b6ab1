
import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import channelsApi from './channels-api';

import { LOADING_STATUS } from '../../constants/index';
import {
  RequestsType,
  FetchRequestsParams,
  FetchTrustedAddressesParams,
  TrustedAddressesType,
  FetchEmailsParams,
  EmailsType,
  FetchDomainsParams,
  DomainsType,
  FetchSocialChannelsParams,
  SocialChannelsType,
  FetchEmail2SMSSettingsParams,
  Email2SMSSettingsType,
} from '../../types/channels';

export interface ChannelsState {
  requests: RequestsType | null
  requestsLoading: string;
  trustedAddresses: TrustedAddressesType | null
  trustedAddressesLoading: string;
  emails: EmailsType | null
  emailsLoading: string;
  domains: DomainsType | null
  domainsLoading: string;
  socialChannels: SocialChannelsType | null
  socialChannelsLoading: string;
  email2SMSSettings: Email2SMSSettingsType | null
  email2SMSSettingsLoading: string;
  updateEmail2SMSSettingsLoading: string;
}

const initialState: ChannelsState = {
  requests: null,
  requestsLoading: LOADING_STATUS.NOT_LOADED,
  trustedAddresses: null,
  trustedAddressesLoading: LOADING_STATUS.NOT_LOADED,
  emails: null,
  emailsLoading: LOADING_STATUS.NOT_LOADED,
  domains: null,
  domainsLoading: LOADING_STATUS.NOT_LOADED,
  socialChannels: null,
  socialChannelsLoading: LOADING_STATUS.NOT_LOADED,
  email2SMSSettings: null,
  email2SMSSettingsLoading: LOADING_STATUS.NOT_LOADED,
  updateEmail2SMSSettingsLoading: LOADING_STATUS.NOT_LOADED,
};

export const fetchRequests = createAsyncThunk(
  'channels/fetchRequests',
  async (params: FetchRequestsParams, thunkAPI) => {
    const response = await channelsApi.loadRequests(params);
    return response;
  }
);

export const fetchTrustedAddresses = createAsyncThunk(
  'channels/fetchTrustedAddresses',
  async (params: FetchTrustedAddressesParams, thunkAPI) => {
    const response = await channelsApi.loadTrustedAddresses(params);
    return response;
  }
);

export const fetchEmails = createAsyncThunk(
  'channels/fetchEmails',
  async (params: FetchEmailsParams, thunkAPI) => {
    const response = await channelsApi.loadEmails(params);
    return response;
  }
);

export const fetchDomains = createAsyncThunk(
  'channels/fetchDomains',
  async (params: FetchDomainsParams, thunkAPI) => {
    const response = await channelsApi.loadDomains(params);
    return response;
  }
);

export const fetchSocialChannels = createAsyncThunk(
  'channels/fetchSocialChannels',
  async (params: FetchSocialChannelsParams, thunkAPI) => {
    const response = await channelsApi.loadSocialChannels(params);
    return response;
  }
);

export const fetchEmail2SMSSettings = createAsyncThunk(
  'channels/fetchEmail2SMSSettings',
  async (params: FetchEmail2SMSSettingsParams, thunkAPI) => {
    const response = await channelsApi.loadEmail2SMSSettings(params);
    return response;
  }
);

export const updateEmail2SMSSettings = createAsyncThunk(
  'channels/updateEmail2SMSSettings',
  async (params: UpdateEmail2SMSSettingsParams, thunkAPI) => {
    const response = await channelsApi.putEmail2SMSSettings(params);
    return response;
  }
);

export const channelsSlice = createSlice({
  name: 'channels',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    // fetchRequests
    builder.addCase(
      fetchRequests.fulfilled,
      (state: ChannelsState, action) => ({
        ...state,
        requests: action.payload,
        requestsLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchRequests.pending,
      (state: ChannelsState) => ({
        ...state,
        requestsLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchRequests.rejected,
      (state: ChannelsState) => ({
        ...state,
        requestsLoading: LOADING_STATUS.FAILED,
      })
    );
    // fetchTrustedAddresses
    builder.addCase(
      fetchTrustedAddresses.fulfilled,
      (state: ChannelsState, action) => ({
        ...state,
        trustedAddresses: action.payload,
        trustedAddressesLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchTrustedAddresses.pending,
      (state: ChannelsState) => ({
        ...state,
        trustedAddressesLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchTrustedAddresses.rejected,
      (state: ChannelsState) => ({
        ...state,
        trustedAddressesLoading: LOADING_STATUS.FAILED,
      })
    );
    // fetchEmails
    builder.addCase(
      fetchEmails.fulfilled,
      (state: ChannelsState, action) => ({
        ...state,
        emails: action.payload,
        emailsLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchEmails.pending,
      (state: ChannelsState) => ({
        ...state,
        emailsLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchEmails.rejected,
      (state: ChannelsState) => ({
        ...state,
        emailsLoading: LOADING_STATUS.FAILED,
      })
    );
    // fetchDomains
    builder.addCase(
      fetchDomains.fulfilled,
      (state: ChannelsState, action) => ({
        ...state,
        domains: action.payload,
        domainsLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchDomains.pending,
      (state: ChannelsState) => ({
        ...state,
        domainsLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchDomains.rejected,
      (state: ChannelsState) => ({
        ...state,
        domainsLoading: LOADING_STATUS.FAILED,
      })
    );
    // fetchSocialChannels
    builder.addCase(
      fetchSocialChannels.fulfilled,
      (state: ChannelsState, action) => ({
        ...state,
        socialChannels: action.payload,
        socialChannelsLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchSocialChannels.pending,
      (state: ChannelsState) => ({
        ...state,
        socialChannelsLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchSocialChannels.rejected,
      (state: ChannelsState) => ({
        ...state,
        socialChannelsLoading: LOADING_STATUS.FAILED,
      })
    );
    // fetchEmail2SMSSettings
    builder.addCase(
      fetchEmail2SMSSettings.fulfilled,
      (state: ChannelsState, action) => ({
        ...state,
        email2SMSSettings: action.payload,
        email2SMSSettingsLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchEmail2SMSSettings.pending,
      (state: ChannelsState) => ({
        ...state,
        email2SMSSettingsLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchEmail2SMSSettings.rejected,
      (state: ChannelsState) => ({
        ...state,
        email2SMSSettingsLoading: LOADING_STATUS.FAILED,
      })
    );
    // updateEmail2SMSSettings
    builder.addCase(
      updateEmail2SMSSettings.fulfilled,
      (state: ChannelsState, action) => ({
        ...state,
        updateEmail2SMSSettingsLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      updateEmail2SMSSettings.pending,
      (state: ChannelsState) => ({
        ...state,
        updateEmail2SMSSettingsLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      updateEmail2SMSSettings.rejected,
      (state: ChannelsState) => ({
        ...state,
        updateEmail2SMSSettingsLoading: LOADING_STATUS.FAILED,
      })
    );
  },
});

export default channelsSlice.reducer;
