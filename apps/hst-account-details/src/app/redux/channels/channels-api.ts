import queryString from 'query-string';
import { fetchInstance, paramsToQueryString } from 'helpers';
import { handleAPI } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import {
  FetchRequestsParams,
  FetchTrustedAddressesParams,
  FetchEmailsParams,
  FetchDomainsParams,
  FetchSocialChannelsParams,
  UpdateEmail2SMSSettingsParams
} from '../../types/channels';

export default {
  loadRequests: handleAPI(async (params: FetchRequestsParams) => {
    const queries = queryString.stringify(params);

    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/senders/requests?${queries}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
  loadTrustedAddresses: handleAPI(async (params: FetchTrustedAddressesParams) => {
    const queries = queryString.stringify(params);

    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/senders?${queries}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
  loadEmails: handleAPI(async (params: FetchEmailsParams) => {
    const { vendorId, accountId, size, next, filter } = params;
    const queries = paramsToQueryString({ params: { size, next, filter } })
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/emails?${queries}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
  loadDomains: handleAPI(async (params: FetchDomainsParams) => {
    const { vendorId, accountId, size, next, filter } = params;
    const queries = paramsToQueryString({ params: { size, next, filter } })
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/domains?${queries}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
  loadSocialChannels: handleAPI(async (params: FetchSocialChannelsParams) => {
    const { vendorId, accountId } = params;
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/channels/details`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
  loadEmail2SMSSettings: handleAPI(async (params: FetchEmail2SMSSettingsParams) => {
    const { vendorId, accountId } = params;
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/email2sms`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
  putEmail2SMSSettings: handleAPI(async (params: UpdateEmail2SMSSettingsParams) => {
    const { vendorId, accountId, payload } = params;
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/settings/email2sms`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(payload),
      }
    );
  }),
};
