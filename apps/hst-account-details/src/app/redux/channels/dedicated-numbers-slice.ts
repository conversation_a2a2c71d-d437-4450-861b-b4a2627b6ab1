import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { LOADING_STATUS } from '../../constants';
import { DedicatedNumbersType, FetchDedicatedNumbersParams } from '../../types/channels';
import dedicatedNumbersApi from './dedicated-numbers-api';

export interface DedicatedNumbersState {
  data: DedicatedNumbersType | null;
  requestsLoading: string;
}

const initialState: DedicatedNumbersState = {
  data: null,
  requestsLoading: LOADING_STATUS.NOT_LOADED,
};

export const fetchDedicatedNumbers = createAsyncThunk(
  'dedicatedNumbers/fetchDedicatedNumbers',
  async (params: FetchDedicatedNumbersParams, thunkAPI) => {
    const response = await dedicatedNumbersApi.loadDedicatedNumbers(params);
    return response;
  }
);

export const dedicatedNumbersSlice = createSlice({
  name: 'dedicatedNumbers',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    // fetchDedicatedNumbers
    builder.addCase(
      fetchDedicatedNumbers.fulfilled,
      (state: DedicatedNumbersState, action) => ({
          ...state,
          data: action.payload,
          requestsLoading: LOADING_STATUS.SUCCEEDED,
        })
    );
    builder.addCase(
      fetchDedicatedNumbers.pending,
      (state: DedicatedNumbersState) => ({
          ...state,
          requestsLoading: LOADING_STATUS.LOADING,
        })
    );
    builder.addCase(
      fetchDedicatedNumbers.rejected,
      (state: DedicatedNumbersState) => ({
          ...state,
          requestsLoading: LOADING_STATUS.FAILED,
        })
    );
  },
});

export default dedicatedNumbersSlice.reducer;
