import { handleAPI } from '@sinch-smb/dev-utils';
import paramsToQueryString from 'apps/hst-core/src/apis/helpers/params-to-querystring';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import { fetchInstance } from 'helpers';
import { FetchDedicatedNumbersParams } from '../../types/channels';

export default {
  loadDedicatedNumbers: handleAPI(
    async (params: FetchDedicatedNumbersParams) => {
      // extract accountId from params
      const { accountId, ...restParams } = params;
      const queryString = paramsToQueryString({ params: restParams });
      const encodedQueryString = queryString
        .replaceAll('[', '%5B')
        .replaceAll(']', '%5D');

      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v2.1/support/accounts/${accountId}/dedicated-numbers?${encodedQueryString}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),
};
