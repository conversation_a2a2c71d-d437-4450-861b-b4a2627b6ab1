/* eslint-disable no-param-reassign */
import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { LOADING_STATUS } from '../../constants';
import AccountsSearchApi from './accounts-search-api';

export interface User {
  id: string;
  username: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
}

export interface AccountBilling {
  billingAccountId: string;
  billingType: string;
}

export interface AccountContent {
  id: string;
  accountId: string;
  vendorId: string;
  label: string;
  createdAt: string;
  status: string;
  verificationStatus: string;
  type: string;
  billing: AccountBilling;
  country: string;
  users: User[];
}

export interface AccountResource {
  index: string;
  id: string;
  score: number;
  sortValues: (string | number)[];
  content: AccountContent;
}

export interface AccountsSearchResponse {
  pagination: Record<string, any>;
  resources: AccountResource[];
}

export interface AccountsSearchCriteria {
  apiKey?: string;
  apiKeysLabel?: string;
  e2sEmail?: string;
  integrationsId?: string;
  integrationsType?: string;
  integrationsExternalPlatformId?: string;
  billingAccountId?: string;
  usersPhone?: string;
  usersEmail?: string;
  accountId?: string;
  vendorId?: string;
  label?: string;
  carrierBillingNumber?: string;
  dedicatedNumber?: string;
  senderAddress?: string;
  legacyCredentialUser?: string;
  next?: string;
  size?: number;
  exact?: boolean;
}

export interface AccountsFilterCriteria {
  billingType?: string;
  status?: string;
  vendorId?: string;
  country?: string[];
  verificationStatus?: string;
  createdAt?: string;
}

export interface AccountsSearchState {
  accountsLoading: string;
  accountsData: AccountsSearchResponse | null;
}

const initialState: AccountsSearchState = {
  accountsLoading: LOADING_STATUS.NOT_LOADED,
  accountsData: null,
};

export const loadAccounts = createAsyncThunk(
  'accounts/loadAccounts',
  async (params: { searchCriteria?: AccountsSearchCriteria; filterCriteria?: AccountsFilterCriteria } = {}, thunkAPI) => {
    const response = await AccountsSearchApi.loadAccounts(params);
    return response;
  }
);

export const AccountsSearchSlice = createSlice({
  name: 'AccountsSearch',
  initialState,
  reducers: {
    resetAccountsSearch: (state): void => {
      state.accountsLoading = LOADING_STATUS.NOT_LOADED;
      state.accountsData = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(loadAccounts.pending, (state: AccountsSearchState) => ({
        ...state,
        accountsLoading: LOADING_STATUS.LOADING,
      }))
      .addCase(loadAccounts.fulfilled, (state: AccountsSearchState, action) => ({
        ...state,
        accountsLoading: LOADING_STATUS.SUCCEEDED,
        accountsData: action.payload,
      }))
      .addCase(loadAccounts.rejected, (state: AccountsSearchState) => ({
        ...state,
        accountsLoading: LOADING_STATUS.FAILED,
      }));
  },
});

export const { resetAccountsSearch } = AccountsSearchSlice.actions;

export default AccountsSearchSlice.reducer;
