import { handleAPI } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import { fetchInstance, paramsToQueryString } from 'helpers';

export default {
  loadAccounts: handleAPI(
    async (
      params = {
        searchCriteria: {
          accountId: '',
          exact: false,
          size: 10,
        },
      }
    ) => {
      const { searchCriteria } = params;
      const { accountId, exact } = searchCriteria;
      const queryString = paramsToQueryString({
        params: { accountId, exact, size: 10 },
      });
      const formattedQueryString = queryString.replace(/\[.*?\]/g, '');
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/search?${formattedQueryString}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),
};
