import { createApi, BaseQueryFn } from '@reduxjs/toolkit/query/react';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import { fetchInstance, paramsToQueryString } from 'helpers';

// Define a custom base query function that uses fetchInstance and handles params properly
type CustomQueryArgs = {
  url: string;
  method?: string;
  body?: any;
  params?: Record<string, any>;
  baseUrl?: string;
};

/**
 * Custom base query function that integrates with existing fetchInstance helper
 * and properly handles query parameters and different HTTP methods
 */
export const customBaseQuery: BaseQueryFn<
  CustomQueryArgs,
  unknown,
  { status: number; data: string }
> = async (args, api) => {
  const { url, method = 'GET', body, params, baseUrl } = args;
  try {
    // Handle query params if provided
    let queryString = '';
    if (params) {
      queryString = paramsToQueryString({
        params,
        prefix: ''
      }).replace(/\[.*?\]/g, '');
      
      if (queryString) {
        queryString = `?${queryString}`;
      }
    }
    const baseApiUrl = baseUrl || `${Endpoint.SUPPORT_V2_API_URL}/v3/support`;
    // Construct the full URL with query params if needed
    let fullUrl = `${baseApiUrl}${url}`;
    
    // Only append query params if not already part of the URL
    if (params && queryString && !url.includes('?')) {
      fullUrl += queryString;
    }
        
    // Construct request options
    const requestOptions: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      signal: api.signal
    };
    
    // Only add body for non-GET requests if body is provided
    if (method !== 'GET' && body) {
      requestOptions.body = JSON.stringify(body);
    }
    const response = await fetchInstance()(fullUrl, requestOptions);
    
    if (!response.ok) {
      const errorText = await response.text();
      return {
        error: { 
          status: response.status, 
          data: errorText 
        }
      };
    }
    
    // Special handling for DELETE requests that might not return JSON
    if (method === 'DELETE' && response.status === 204) {
      return { data: { success: true } };
    }
    
    // Parse response as JSON
    const data = await response.json();    
    return { data };
  } catch (error: any) {
    return {
      error: {
        status: 500,
        data: error.message || 'Unknown error occurred'
      }
    };
  }
};

/**
 * Base API service that can be extended by other API services
 * Provides the common baseQuery and error handling
 */
export const baseApiService = createApi({
  reducerPath: 'api',
  baseQuery: customBaseQuery,
  endpoints: () => ({}),
  // Make sure to include keepUnusedDataFor to control cache lifetime
  keepUnusedDataFor: 300, // 5 minutes in seconds
});
