import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import _get from 'lodash/get';

import { LOADING_STATUS } from '../../constants/index';
import { CONNECTION_STATUS } from '../../constants/integration';
import {
  DeleteIntegrationConnectionParams,
  FetchIntegrationsParams,
  IntegrationItem,
  IntegrationsType,
} from '../../types/integrations';
import integrationsApi from './integrations-api';

export interface IntegrationsState {
  loading: string;
  deleteLoading: string;
  data: IntegrationsType | null;
}

const initialState: IntegrationsState = {
  loading: LOADING_STATUS.NOT_LOADED,
  deleteLoading: LOADING_STATUS.NOT_LOADED,
  data: null,
};

const HUBSPOT = 'hubspot';

export const fetchIntegrations = createAsyncThunk(
  'integrations/fetchIntegrations',
  async (params: FetchIntegrationsParams, thunkAPI) => {
    const response = await integrationsApi.loadIntegrations(params);
    const hubspotIntegrations =
      response?.resources?.filter(
        (item: IntegrationItem) => item.integration === HUBSPOT
      ) || [];

    if (!hubspotIntegrations.length) return response;

    const { vendorId, accountId } = params;

    let newResources = response.resources;
    newResources = await Promise.all(
      newResources.map(async (integrationItem: IntegrationItem) => {
        if (
          integrationItem.integration === HUBSPOT &&
          integrationItem.extras.portalId
        ) {
          let connectionStatus = CONNECTION_STATUS.CONNECTED;
          try {
            const hubspotDetailsResponse =
              await integrationsApi.loadHubspotIntegration({
                vendorId,
                accountId,
              });

            const accountExists = _get(
              hubspotDetailsResponse,
              'accountExists',
              []
            );

            const fullPrimaryHubspotTables = [
              `int-hub-bff-integrations-${BUILD_ENV === 'production' ? 'pro' : 'dev'}`,
              `int-hubspot-${BUILD_ENV === 'production' ? 'pro' : 'dev'}`,
              'int-auth-settings',
              'int-app-settings',
              `int-hubspot-sub-accounts-${BUILD_ENV === 'production' ? 'pro' : 'dev'}`,
            ];

            const fullSecondaryHubspotTables = [
              `int-hub-bff-integrations-${BUILD_ENV === 'production' ? 'pro' : 'dev'}`,
              'int-auth-settings',
              `int-hubspot-sub-accounts-${BUILD_ENV === 'production' ? 'pro' : 'dev'}`,
            ];

            const isFullPrimaryConnectedHubspot = accountExists.length > 0 &&
              accountExists.filter((existedIn: string) => fullPrimaryHubspotTables.includes(existedIn)).length === fullPrimaryHubspotTables.length
            const isFullSecondaryConnectedHubspot = accountExists.length > 0 &&
              accountExists.filter((existedIn: string) => fullSecondaryHubspotTables.includes(existedIn)).length === fullSecondaryHubspotTables.length


            const connectionType = hubspotDetailsResponse.primaryAccount
              ? 'Secondary'
              : 'Primary';

            const isFullConnectedHubspot =
              (hubspotDetailsResponse.primaryAccount &&
                isFullSecondaryConnectedHubspot) ||
              (!hubspotDetailsResponse.primaryAccount &&
                isFullPrimaryConnectedHubspot);

            if (isFullConnectedHubspot) {
              connectionStatus = CONNECTION_STATUS.CONNECTED;
            } else if (accountExists.length > 0) {
              connectionStatus = CONNECTION_STATUS.PARTIAL_CONNECTED;
            } else {
              connectionStatus = CONNECTION_STATUS.DISCONNECTED;
            }

            return {
              ...integrationItem,
              connectionType,
              primaryAccount: hubspotDetailsResponse.primaryAccount,
              connectionStatus,
            };
          } catch (err) {
            return {
              ...integrationItem,
              connectionStatus,
            };
          }
        }

        return {
          ...integrationItem,
          connectionStatus: CONNECTION_STATUS.CONNECTED,
        };
      })
    );

    return {
      resources: newResources,
    };
  }
);

export const deleteIntegrationConnection = createAsyncThunk(
  'integrations/deleteIntegrationConnection',
  async (params: DeleteIntegrationConnectionParams, thunkAPI) => {
    const response = await integrationsApi.deleteIntegrationConnection(params);
    return response;
  }
);

export const integrationsSlice = createSlice({
  name: 'integrations',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    // fetchIntegrations
    builder.addCase(
      fetchIntegrations.fulfilled,
      (state: IntegrationsState, action) => ({
        ...state,
        data: action.payload,
        loading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(fetchIntegrations.pending, (state: IntegrationsState) => ({
      ...state,
      loading: LOADING_STATUS.LOADING,
    }));
    builder.addCase(fetchIntegrations.rejected, (state: IntegrationsState) => ({
      ...state,
      data: null,
      loading: LOADING_STATUS.FAILED,
    }));
    builder.addCase(
      deleteIntegrationConnection.fulfilled,
      (state: IntegrationsState, action) => ({
        ...state,
        deleteLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      deleteIntegrationConnection.pending,
      (state: IntegrationsState) => ({
        ...state,
        deleteLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      deleteIntegrationConnection.rejected,
      (state: IntegrationsState) => ({
        ...state,
        deleteLoading: LOADING_STATUS.FAILED,
      })
    );
  },
});

export default integrationsSlice.reducer;
