import { handleAPI } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import { fetchInstance } from 'helpers';
import queryString from 'query-string';
import {
  DeleteIntegrationConnectionParams,
  FetchHubspotIntegrationParams,
  FetchIntegrationsParams,
} from '../../types/integrations';

export default {
  loadIntegrations: handleAPI(async (params: FetchIntegrationsParams) => {
    const { accountId, platform } = params;
    const queries = queryString.stringify({ accountId, platform });

    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/ecosystem/accounts/search?${queries}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
  loadHubspotIntegration: handleAPI(async (params: FetchHubspotIntegrationParams) => {
    const { vendorId, accountId } = params;

    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/ecosystem/accounts/${vendorId}:${accountId}/integrations/support/hubspot`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
  deleteIntegrationConnection: handleAPI(
    async (params: DeleteIntegrationConnectionParams) => {
      const { accountId, vendorId, integration } = params;

      return fetchInstance()(
        `${
          Endpoint.SUPPORT_V2_API_URL
        }/v3/support/ecosystem/accounts/${vendorId}:${accountId}/integrations/support/disconnect?integration=${integration.toUpperCase()}`,
        {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),
};
