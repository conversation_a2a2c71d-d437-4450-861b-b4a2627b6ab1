import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import activityLogApi from './activity-log-api';

import { LOADING_STATUS } from '../../constants/index';
import {
  EcosystemHistoryType,
  FetchEcosystemHistoryParams,
  FetchHubActivityLogParams,
  FetchHubActivityLogUsersParams,
  FetchSupportLogsParams,
  HubActivityLogType,
  HubActivityLogUsersType,
  SupportLogsType,
} from '../../types/activity-log';

export interface ActivityLogState {
  loading: string;
  hubActivityData: HubActivityLogType | null;
  filterLoading: string;
  hubActivityUsers: HubActivityLogUsersType | null;
  ecosystemHistoryLoading: string;
  ecosystemHistory: EcosystemHistoryType | null;
  supportLogsLoading: string;
  supportLogs: SupportLogsType | null;
}

const initialState: ActivityLogState = {
  loading: LOADING_STATUS.NOT_LOADED,
  hubActivityData: null,
  filterLoading: LOADING_STATUS.NOT_LOADED,
  hubActivityUsers: null,
  ecosystemHistory: null,
  ecosystemHistoryLoading: LOADING_STATUS.NOT_LOADED,
  supportLogs: null,
  supportLogsLoading: LOADING_STATUS.NOT_LOADED,
};

export const fetchHubActivityLog = createAsyncThunk(
  'activityLog/fetchHubActivityLog',
  async (params: FetchHubActivityLogParams, thunkAPI) => {
    const response = await activityLogApi.loadHubActivity(params);
    return response;
  }
);

export const fetchHubActivityLogUsers = createAsyncThunk(
  'activityLog/fetchHubActivityLogUsers',
  async (params: FetchHubActivityLogUsersParams, thunkAPI) => {
    const response = await activityLogApi.loadHubActivityUsers(params);
    return response;
  }
);

export const fetchEcosystemHistory = createAsyncThunk(
  'activityLog/fetchEcosystemHistory',
  async (params: FetchEcosystemHistoryParams, thunkAPI) => {
    const response = await activityLogApi.loadEcosystemHistory(params);
    return response;
  }
);

export const fetchSupportLogs = createAsyncThunk(
  'activityLog/fetchSupportLogs',
  async (params: FetchSupportLogsParams, thunkAPI) => {
    const response = await activityLogApi.loadSupportLogs(params);
    return response;
  }
);

export const activityLogSlice = createSlice({
  name: 'activityLog',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    // fetchHubActivityLog
    builder.addCase(
      fetchHubActivityLog.fulfilled,
      (state: ActivityLogState, action) => ({
        ...state,
        hubActivityData: action.payload,
        loading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(fetchHubActivityLog.pending, (state: ActivityLogState) => ({
      ...state,
      loading: LOADING_STATUS.LOADING,
    }));
    builder.addCase(
      fetchHubActivityLog.rejected,
      (state: ActivityLogState) => ({
        ...state,
        hubActivityData: null,
        loading: LOADING_STATUS.FAILED,
      })
    );
    // fetchHubActivityLogUsers
    builder.addCase(
      fetchHubActivityLogUsers.fulfilled,
      (state: ActivityLogState, action) => ({
        ...state,
        hubActivityUsers: action.payload,
        filterLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchHubActivityLogUsers.pending,
      (state: ActivityLogState) => ({
        ...state,
        filterLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchHubActivityLogUsers.rejected,
      (state: ActivityLogState) => ({
        ...state,
        hubActivityUsers: null,
        filterLoading: LOADING_STATUS.FAILED,
      })
    );
    // fetchEcosystemHistory
    builder.addCase(
      fetchEcosystemHistory.fulfilled,
      (state: ActivityLogState, action) => ({
        ...state,
        ecosystemHistory: action.payload,
        ecosystemHistoryLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchEcosystemHistory.pending,
      (state: ActivityLogState) => ({
        ...state,
        ecosystemHistoryLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchEcosystemHistory.rejected,
      (state: ActivityLogState) => ({
        ...state,
        ecosystemHistory: null,
        ecosystemHistoryLoading: LOADING_STATUS.FAILED,
      })
    );
    // fetchSupportLogs
    builder.addCase(
      fetchSupportLogs.fulfilled,
      (state: ActivityLogState, action) => ({
        ...state,
        supportLogs: action.payload,
        supportLogsLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(fetchSupportLogs.pending, (state: ActivityLogState) => ({
      ...state,
      supportLogsLoading: LOADING_STATUS.LOADING,
    }));
    builder.addCase(fetchSupportLogs.rejected, (state: ActivityLogState) => ({
      ...state,
      supportLogs: null,
      supportLogsLoading: LOADING_STATUS.FAILED,
    }));
  },
});

export default activityLogSlice.reducer;
