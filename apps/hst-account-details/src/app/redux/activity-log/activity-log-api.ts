import { handleAPI } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import { fetchInstance, paramsToQueryString } from 'helpers';
import queryString from 'query-string';
import {
  FetchEcosystemHistoryParams,
  FetchHubActivityLogParams,
  FetchHubActivityLogUsersParams,
  FetchSupportLogsParams,
} from '../../types/activity-log';

export default {
  loadHubActivity: handleAPI(async (params: FetchHubActivityLogParams) => {
    const {
      vendorId,
      accountId,
      endDate,
      objectTypes,
      size,
      startDate,
      user,
      next,
    } = params;
    const queries = queryString.stringify({
      endDate,
      objectTypes,
      size,
      startDate,
      user,
      next,
    });

    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/activity-log/logs?${queries}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
  loadHubActivityUsers: handleAPI(
    async (params: FetchHubActivityLogUsersParams) => {
      const { vendorId, accountId, size } = params;
      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/activity-log/users?size=${size}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),
  loadEcosystemHistory: handleAPI(
    async (params: FetchEcosystemHistoryParams) => {
      const { vendorId, accountId, size, fromDate, toDate, platform, next } =
        params;
      const queryParams = { size, fromDate, toDate, platform, next };
      const queries = paramsToQueryString({ params: queryParams });

      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/ecosystem/${vendorId}:${accountId}/history?${queries}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),
  loadSupportLogs: handleAPI(async (params: FetchSupportLogsParams) => {
    const { vendorId, accountId, size, fromDate, toDate, next, httpMethod, restrictResults } = params;
    const queries = queryString.stringify({
      size,
      fromDate,
      toDate,
      next,
      httpMethod,
      restrictResults
    });

    const encodedQueryString = queries.replaceAll(
      'httpMethod',
      'httpMethod%5B%5D'
    );

    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/support/logs?${encodedQueryString}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
};
