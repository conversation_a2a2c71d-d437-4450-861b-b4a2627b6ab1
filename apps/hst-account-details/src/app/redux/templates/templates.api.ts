import { fetchInstance } from 'helpers';
import { handleAPI, paramsToQueryString } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import { GetAccountTemplatesParams } from '../../types/templates/templates';

export default {
  getTemplates: handleAPI(async (params: GetAccountTemplatesParams) => {
    const { vendorId, accountId, ...rest } = params;
    const queries = paramsToQueryString({ params: rest });

    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/templates?${queries}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
};
