import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { LOADING_STATUS } from '../../constants';
import {
  GetAccountTemplatesParams,
  TemplateResponse,
} from '../../types/templates/templates';
import TemplatesApi from './templates.api';

export interface TemplatesState {
  loading: string;
  data: TemplateResponse | null;
}

const initialState: TemplatesState = {
  loading: LOADING_STATUS.NOT_LOADED,
  data: null,
};

export const fetchTemplates = createAsyncThunk(
  'templates/fetchTemplates',
  async (params: GetAccountTemplatesParams, thunkAPI) => {
    const response = await TemplatesApi.getTemplates(params);
    return response;
  }
);


export const TemplatesSlice = createSlice({
  name: 'templates',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(fetchTemplates.fulfilled, (state, action) => ({
      ...state,
      data: action.payload,
      loading: LOADING_STATUS.SUCCEEDED,
    }));
    builder.addCase(fetchTemplates.pending, (state) => ({
      ...state,
      loading: LOADING_STATUS.LOADING,
    }));
    builder.addCase(fetchTemplates.rejected, (state) => ({
      ...state,
      loading: LOADING_STATUS.FAILED,
    }));
  },
});

export default TemplatesSlice.reducer;
