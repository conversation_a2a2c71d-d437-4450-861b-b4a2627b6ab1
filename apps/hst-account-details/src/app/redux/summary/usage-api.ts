import { fetchInstance, paramsToQueryString } from 'helpers';
import { handleAPI } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import { FetchUsageParams } from '../../types/summary';

export default {
  loadUsage: handleAPI(async (params: FetchUsageParams) => {
    const { vendorId, accountId, date, timezone } = params;
    const queryString = paramsToQueryString({ params: { date, timezone } });
    const formattedQueryString = queryString.replace(/\[.*?\]/g, '');
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/account-detail/message-usages?${formattedQueryString}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
};
