import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { LOADING_STATUS } from '../../constants/index';
import {
  BillingDetailsInvoiceType,
  BillingDetailsType,
  FetchBillingDetailsParams,
  PostAccountCreditParams,
  FetchBillingBalanceParams,
  BillingBalanceType,
} from '../../types/summary';
import billingDetailsApi from './billing-details-api';

export interface BillingDetailsState {
  loading: string;
  details: BillingDetailsType | null;
  loadingInvoice: string;
  invoice: BillingDetailsInvoiceType | null;
  createAcccountCreditLoading: string;
  billingBalance: BillingBalanceType | null;
  fetchBillingBalanceLoading: string;
}

const initialState: BillingDetailsState = {
  loading: LOADING_STATUS.NOT_LOADED,
  details: null,
  loadingInvoice: LOADING_STATUS.NOT_LOADED,
  invoice: null,
  createAcccountCreditLoading: LOADING_STATUS.NOT_LOADED,
  billingBalance: null,
  fetchBillingBalanceLoading: LOADING_STATUS.NOT_LOADED,
};

export const fetchBillingDetails = createAsyncThunk(
  'billingDetails/fetchBillingDetails',
  async (params: FetchBillingDetailsParams, thunkAPI) => {
    const response = await billingDetailsApi.loadBillingDetails(params);
    return response;
  }
);

export const fetchBillingDetailsInvoice = createAsyncThunk(
  'billingDetails/fetchBillingDetailsInvoice',
  async (params: FetchBillingDetailsParams, thunkAPI) => {
    const response = await billingDetailsApi.loadBillingDetailsInvoice(params);
    return response;
  }
);

export const createAccountCredit = createAsyncThunk(
  'billingDetails/createAccountCredit',
  async (params: PostAccountCreditParams, thunkAPI) => {
    const response = await billingDetailsApi.postAccountCredit(params);
    return response;
  }
);

export const fetchBillingBalance = createAsyncThunk(
  'billingDetails/fetchBillingBalance',
  async (params: FetchBillingBalanceParams, thunkAPI) => {
    const response = await billingDetailsApi.loadBillingBalance(params);
    return response;
  }
);

export const billingDetailsSlice = createSlice({
  name: 'billingDetails',
  initialState,
  reducers: {
    clearBillingDetailsState: () => initialState,
  },
  extraReducers: (builder) => {
    builder.addCase(
      fetchBillingDetails.fulfilled,
      (state: BillingDetailsState, action) => ({
        ...state,
        details: action.payload,
        loading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchBillingDetails.pending,
      (state: BillingDetailsState) => ({
        ...state,
        details: null,
        loading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchBillingDetails.rejected,
      (state: BillingDetailsState) => ({
        ...state,
        loading: LOADING_STATUS.FAILED,
      })
    );
    // fetchBillingDetailsInvoice
    builder.addCase(
      fetchBillingDetailsInvoice.fulfilled,
      (state: BillingDetailsState, action) => ({
        ...state,
        invoice: action.payload,
        loadingInvoice: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchBillingDetailsInvoice.pending,
      (state: BillingDetailsState) => ({
        ...state,
        invoice: null,
        loadingInvoice: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchBillingDetailsInvoice.rejected,
      (state: BillingDetailsState) => ({
        ...state,
        loadingInvoice: LOADING_STATUS.FAILED,
      })
    );
    // createAccountCredit
    builder.addCase(
      createAccountCredit.fulfilled,
      (state: BillingDetailsState, action) => ({
        ...state,
        createAcccountCreditLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      createAccountCredit.pending,
      (state: BillingDetailsState) => ({
        ...state,
        createAcccountCreditLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      createAccountCredit.rejected,
      (state: BillingDetailsState) => ({
        ...state,
        createAcccountCreditLoading: LOADING_STATUS.FAILED,
      })
    );
    // fetchBillingBalance
    builder.addCase(
      fetchBillingBalance.fulfilled,
      (state: BillingDetailsState, action) => ({
        ...state,
        billingBalance: action.payload,
        fetchBillingBalanceLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchBillingBalance.pending,
      (state: BillingDetailsState) => ({
        ...state,
        billingBalance: null,
        fetchBillingBalanceLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchBillingBalance.rejected,
      (state: BillingDetailsState) => ({
        ...state,
        fetchBillingBalanceLoading: LOADING_STATUS.FAILED,
      })
    );
  },
});

export const { clearBillingDetailsState } = billingDetailsSlice.actions;

export default billingDetailsSlice.reducer;
