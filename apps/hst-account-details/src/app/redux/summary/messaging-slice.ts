import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { LOADING_STATUS } from '../../constants/index';
import { UsageType, FetchMessagesParams } from '../../types/summary';
import MessagingApi from './messaging-api';

export interface MessagingState {
  loading: string;
  data: UsageType | null;
}

const initialState: MessagingState = {
  loading: LOADING_STATUS.NOT_LOADED,
  data: null,
};

export const fetchMessages = createAsyncThunk(
  'usage/fetchMessages',
  async (params: FetchMessagesParams, thunkAPI) => {
    const response = await MessagingApi.loadMessages(params);
    return response;
  }
);

export const MessagesSlice = createSlice({
  name: 'Usage',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(
      fetchMessages.fulfilled,
      (state: MessagingState, action) => ({
        ...state,
        data: action.payload,
        loading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(fetchMessages.pending, (state: MessagingState) => ({
      ...state,
      loading: LOADING_STATUS.LOADING,
    }));
    builder.addCase(fetchMessages.rejected, (state: MessagingState) => ({
      ...state,
      loading: LOADING_STATUS.FAILED,
    }));
  },
});

export default MessagesSlice.reducer;
