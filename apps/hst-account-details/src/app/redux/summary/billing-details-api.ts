import { fetchInstance, paramsToQueryString } from 'helpers';
import { handleAPI } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import { FetchBillingDetailsParams, PostAccountCreditParams, FetchBillingBalanceParams } from '../../types/summary';

export default {
  loadBillingDetails: handleAPI(async (params: FetchBillingDetailsParams) => {
    const { vendorId, accountId } = params;
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/account-detail/billing-details`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
  loadBillingDetailsInvoice: handleAPI(
    async (params: FetchBillingDetailsParams) => {
      const { vendorId, accountId, size, next } = params;
      const queryString = paramsToQueryString({ params: { size, next } });

      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/account-detail/billing-details/invoices?${queryString}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),
  postAccountCredit: handleAPI(
    async (params: PostAccountCreditParams) => {
      const { vendorId, accountId, payload} = params;

      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/account-detail/billing-details/credits`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify(payload),
        }
      );
    }
  ),
  loadBillingBalance: handleAPI(
    async (params: FetchBillingBalanceParams) => {
      const { vendorId, accountId, billingType } = params;

      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/${billingType}/balance`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),
};
