import { fetchInstance } from 'helpers';
import { handleAPI } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import { FetchAccountStateHistoryParams } from '../../types/account-state-history';

export default {
  loadAccountStateHistory: handleAPI(async (params: FetchAccountStateHistoryParams) => {
    const { vendorId, accountId} = params;
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/state-history`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
};
