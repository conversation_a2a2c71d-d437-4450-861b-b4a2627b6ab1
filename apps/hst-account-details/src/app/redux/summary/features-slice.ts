import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { LOADING_STATUS } from '../../constants/index';
import { FeaturesType, FetchFeaturesParams } from '../../types/summary';
import FeaturesApi from './features-api';

export interface FeaturesState {
  loading: string;
  data: FeaturesType | null;
}

const initialState: FeaturesState = {
  loading: LOADING_STATUS.NOT_LOADED,
  data: null,
};

export const fetchFeatures = createAsyncThunk(
  'features/fetchFeatures',
  async (params: FetchFeaturesParams, thunkAPI) => {
    const response = await FeaturesApi.loadFeatures(params);
    return response;
  }
);

export const FeaturesSlice = createSlice({
  name: 'AccountDetailFeatures',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(
      fetchFeatures.fulfilled,
      (state: FeaturesState, action) => ({
        ...state,
        data: action.payload,
        loading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(fetchFeatures.pending, (state: FeaturesState) => ({
      ...state,
      loading: LOADING_STATUS.LOADING,
    }));
    builder.addCase(fetchFeatures.rejected, (state: FeaturesState) => ({
      ...state,
      loading: LOADING_STATUS.FAILED,
    }));
  },
});

export default FeaturesSlice.reducer;
