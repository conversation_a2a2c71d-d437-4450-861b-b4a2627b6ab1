import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { FetchSubAccountsDetailsParams, SubAccountsResponse } from '../../types/sub-accounts';
import subAccountsApi from './sub-accounts-api';
import { LOADING_STATUS } from '../../constants';

export interface SubAccountsState {
  data: SubAccountsResponse | null;
  loading: string;
}

const initialState: SubAccountsState = {
  data: null,
  loading: LOADING_STATUS.NOT_LOADED,
};

export const fetchSubAccountsDetails = createAsyncThunk(
  'subAccounts/fetchSubAccountsDetails',
  async (params: FetchSubAccountsDetailsParams) => {
    const response = await subAccountsApi.loadSubAccountsDetails(params);
    return response;
  }
);

export const subAccountsSlice = createSlice({
  name: 'SubAccounts',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(
      fetchSubAccountsDetails.fulfilled,
      (state: SubAccountsState, action) => ({
        ...state,
        data: action.payload,
        loading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchSubAccountsDetails.pending,
      (state: SubAccountsState) => ({
        ...state,
        loading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchSubAccountsDetails.rejected,
      (state: SubAccountsState) => ({
        ...state,
          loading: LOADING_STATUS.FAILED,
      })
    );
  },
});

export default subAccountsSlice.reducer;
