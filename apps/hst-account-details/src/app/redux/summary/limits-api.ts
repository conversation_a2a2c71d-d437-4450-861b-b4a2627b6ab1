import { fetchInstance } from 'helpers';
import { handleAPI } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import { FetchLimitsParams } from '../../types/summary';

export default {
  loadMessages: handleAPI(async (params: FetchLimitsParams) => {
    const { vendorId, accountId } = params;
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/account-detail/limits`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
};
