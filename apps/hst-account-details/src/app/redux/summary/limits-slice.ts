import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { LOADING_STATUS } from '../../constants/index';
import { LimitsType, FetchLimitsParams } from '../../types/summary';
import LimitsApi from './limits-api';

export interface LimitsState {
  loading: string;
  data: LimitsType | null;
}

const initialState: LimitsState = {
  loading: LOADING_STATUS.NOT_LOADED,
  data: null,
};

export const fetchLimits = createAsyncThunk(
  'usage/fetchLimits',
  async (params: FetchLimitsParams, thunkAPI) => {
    const response = await LimitsApi.loadMessages(params);
    return response;
  }
);

export const LimitsSlice = createSlice({
  name: 'Limits',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(fetchLimits.fulfilled, (state: LimitsState, action) => ({
      ...state,
      data: action.payload,
      loading: LOADING_STATUS.SUCCEEDED,
    }));
    builder.addCase(fetchLimits.pending, (state: LimitsState) => ({
      ...state,
      loading: LOADING_STATUS.LOADING,
    }));
    builder.addCase(fetchLimits.rejected, (state: LimitsState) => ({
      ...state,
      loading: LOADING_STATUS.FAILED,
    }));
  },
});

export default LimitsSlice.reducer;
