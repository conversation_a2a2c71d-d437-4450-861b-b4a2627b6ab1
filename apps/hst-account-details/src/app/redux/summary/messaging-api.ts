import { fetchInstance, paramsToQueryString } from 'helpers';
import { handleAPI } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import { FetchMessagesParams } from '../../types/summary';

export default {
  loadMessages: handleAPI(async (params: FetchMessagesParams) => {
    const { vendorId, accountId, date, timezone } = params;
    const queryString = paramsToQueryString({ params: { date, timezone } });
    const formattedQueryString = queryString.replace(/\[.*?\]/g, '');
    return fetchInstance()(
      `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/account-detail/messages?${formattedQueryString}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );
  }),
};
