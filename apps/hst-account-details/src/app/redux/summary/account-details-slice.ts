import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import accountDetailsApi from './account-details-api';

import { LOADING_STATUS } from '../../constants/index';
import {
  AccountDetailsType,
  FetchAccountDetailsParams,
  FetchUITempateParams,
} from '../../types/summary';

export interface AccountDetailsState {
  loading: string;
  template: string;
  detailsLoading: string;
  details: AccountDetailsType | null;
}

const initialState: AccountDetailsState = {
  loading: LOADING_STATUS.NOT_LOADED,
  template: '',
  detailsLoading: LOADING_STATUS.NOT_LOADED,
  details: null,
};

export const fetchAccountUITempate = createAsyncThunk(
  'accountDetails/fetchAccountUITempate',
  async (params: FetchUITempateParams, thunkAPI) => {
    const response = await accountDetailsApi.loadUITemplate(params);
    return response;
  }
);

export const fetchAccountDetails = createAsyncThunk(
  'accountDetails/fetchAccountDetails',
  async (params: FetchAccountDetailsParams, thunkAPI) => {
    const response = await accountDetailsApi.loadAccountDetails(params);
    return response;
  }
);

export const accountDetailsSlice = createSlice({
  name: 'accountDetails',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    // fetchAccountUITempate
    builder.addCase(
      fetchAccountUITempate.fulfilled,
      (state: AccountDetailsState, action) => ({
        ...state,
        template: action.payload.template,
        loading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchAccountUITempate.pending,
      (state: AccountDetailsState) => ({
        ...state,
        loading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchAccountUITempate.rejected,
      (state: AccountDetailsState) => ({
        ...state,
        loading: LOADING_STATUS.FAILED,
      })
    );
    // fetchAccountDetails
    builder.addCase(
      fetchAccountDetails.fulfilled,
      (state: AccountDetailsState, action) => ({
        ...state,
        details: action.payload,
        detailsLoading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchAccountDetails.pending,
      (state: AccountDetailsState) => ({
        ...state,
        detailsLoading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchAccountDetails.rejected,
      (state: AccountDetailsState) => ({
        ...state,
        detailsLoading: LOADING_STATUS.FAILED,
      })
    );
  },
});

export default accountDetailsSlice.reducer;
