import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { LOADING_STATUS } from '../../constants/index';
import { UsageType, FetchUsageParams } from '../../types/summary';
import UsageApi from './usage-api';

export interface UsageState {
  loading: string;
  data: UsageType | null;
}

const initialState: UsageState = {
  loading: LOADING_STATUS.NOT_LOADED,
  data: null,
};

export const fetchUsage = createAsyncThunk(
  'usage/fetchUsage',
  async (params: FetchUsageParams, thunkAPI) => {
    const response = await UsageApi.loadUsage(params);
    return response;
  }
);

export const UsageSlice = createSlice({
  name: 'Usage',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(fetchUsage.fulfilled, (state: UsageState, action) => ({
      ...state,
      data: action.payload,
      loading: LOADING_STATUS.SUCCEEDED,
    }));
    builder.addCase(fetchUsage.pending, (state: UsageState) => ({
      ...state,
      loading: LOADING_STATUS.LOADING,
    }));
    builder.addCase(fetchUsage.rejected, (state: UsageState) => ({
      ...state,
      loading: LOADING_STATUS.FAILED,
    }));
  },
});

export default UsageSlice.reducer;
