import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { LOADING_STATUS } from '../../constants/index';
import {
  AccountStateHistoryType,
  FetchAccountStateHistoryParams,
} from '../../types/account-state-history';
import AccountStateHistoryApi from './account-state-history-api';

export interface AccountStateHistoryState {
  loading: string;
  data: AccountStateHistoryType | null;
}

const initialState: AccountStateHistoryState = {
  loading: LOADING_STATUS.NOT_LOADED,
  data: null,
};

export const fetchAccountStateHistory = createAsyncThunk(
  'usage/fetchAccountStateHistory',
  async (params: FetchAccountStateHistoryParams, thunkAPI) => {
    const response = await AccountStateHistoryApi.loadAccountStateHistory(params);
    return response;
  }
);


export const AccountStateHistorySlice = createSlice({
  name: 'Usage',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(
      fetchAccountStateHistory.fulfilled,
      (state: AccountStateHistoryState, action) => ({
        ...state,
        data: action.payload,
        loading: LOADING_STATUS.SUCCEEDED,
      })
    );
    builder.addCase(
      fetchAccountStateHistory.pending,
      (state: AccountStateHistoryState) => ({
        ...state,
        loading: LOADING_STATUS.LOADING,
      })
    );
    builder.addCase(
      fetchAccountStateHistory.rejected,
      (state: AccountStateHistoryState) => ({
        ...state,
        loading: LOADING_STATUS.FAILED,
      })
    );
  },
});

export default AccountStateHistorySlice.reducer;
