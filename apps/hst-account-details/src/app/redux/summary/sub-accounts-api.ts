import { handleAPI } from '@sinch-smb/dev-utils';
import Endpoint from 'apps/hst-core/src/helpers/endpoint';
import { fetchInstance, paramsToQueryString } from 'helpers';
import { FetchSubAccountsDetailsParams } from '../../types/sub-accounts';

export default {
  loadSubAccountsDetails: handleAPI(
    async (params: FetchSubAccountsDetailsParams) => {
      const {
        vendorId,
        accountId,
        term,
        status,
        verificationStatus,
        billingType,
        next,
        size,
        createdAt,
      } = params;

      const filteredParams = Object.fromEntries(
        Object.entries({
          term,
          status,
          verificationStatus,
          billingType,
          next,
          size: size || 10,
          createdAt,
        }).filter(([_, value]) =>
          value != null &&
          value !== '' &&
          value !== 'All' &&
          value !== 'ALL' &&
          !(Array.isArray(value) && value.length === 0)
        )
      );

      const queryString = paramsToQueryString({ params: filteredParams });

      const formattedQueryString = queryString.replace(/\[.*?\]/g, '');

      return fetchInstance()(
        `${Endpoint.SUPPORT_V2_API_URL}/v3/support/accounts/${vendorId}:${accountId}/account-detail/sub-accounts?${formattedQueryString}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );
    }
  ),
};
