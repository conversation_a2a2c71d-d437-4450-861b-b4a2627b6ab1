export type FetchNumbersParams = {
  vendorId?: string;
  accounts?: string;
  size?: number;
  next?: string;
  country?: string;
  status?: string;
  providerId?: string;
  matching?: string;
  types?: string[];
  assigned?: boolean;
  accountId?: string;
};

export type FetchAssignmentsParams = {
  vendorId?: string;
  status?: string[] | string;
  accountId?: string;
  accounts?: string;
  matching?: string;
  types?: string[];
  size?: number;
  next?: string;
};

export type ListResponseType<T> = {
  resources: T[];
  pagination: {
    next?: string;
  };
};

export type AssignToType = {
  id: string;
  numberId: string;
  label?: string;
  vendorId: string;
  accountId: string;
  metadata: {
    Key2: string;
    Key1: string;
  };
  created: string;
};

export type NumberVerificationStatus = 'ASSIGNED' | 'PENDING' | 'UNVERIFIED'
export type NumberType = 'MOBILE' | 'LANDLINE' | 'TOLL_FREE' | 'HOSTED_TEN_DLC' | 'TEN_DLC' | 'SHORT_CODE'
// Response from api
export type NumberItem = {
  number: {
    id: string;
    providerId: string;
    phoneNumber: string;
    country: string;
    type: NumberType;
    classification: string;
    created: string;
    updated: string;
    availableAfter: string | null;
    capabilities: string[];
    dedicatedReceiver: false;
    status: NumberVerificationStatus;
  };
  assignment?: AssignToType;
};
export type NumberItemType = {
  id: string;
  phoneNumber: string;
  providerId: string;
  country: string;
  type: string;
  classification: string;
  capabilities?: string[];
  created: string;
  updated: string;
  status?: string;
  assignedTo?: AssignToType;
  availableAfter?: string;
};

export type UpdateNumberParams = {
  numberId: string;
  shouldInvalidate?: boolean;
  payload: {
    classification?: string;
    capabilities?: string[];
    availableAfter?: {
      value?: string | null;
      explicitNull: boolean;
    };
    status?: string | null;
    providerId?: string;
    dedicatedReceiver?: boolean;
  };
};
export type AddNumberParams = {
  phoneNumber: string;
  capabilities: string[];
  country: string;
  providerId: string;
  type: string;
  classification: string;
  shouldInvalidate?: boolean;
};

export type UnassignNumberParams = {
  numberId: string;
  release?: boolean;
};

export type AssignNumberParams = {
  numberId: string;
  payload: {
    vendorId: string;
    accountId: string;
    label?: string;
    billable?: boolean;
  };
};

export type DeleteNumberParams = {
  numberId: string;
};

export type FetchProvidersParams = {
  vendorId: string;
  accountId: string;
};

export type ProviderItem = {
  id: string;
  name: string;
  providerId: string;
  new: boolean;
  _links: {
    self: {
      href: string;
    };
    provider: {
      href: string;
    };
  };
};

export type ProvidersType = {
  resources: ProviderItem[];
};

export type FetchCountriesParams = {
  vendorId: string;
  accountId: string;
};

export type CountryItem = {
  id: string;
  countryCode: string;
  countryName: string;
  new: boolean;
  _links: {
    self: {
      href: string;
    };
    country: {
      href: string;
    };
  };
};

export type CountriesType = {
  resources: CountryItem[];
};

export type ReassignNumberParams = {
  numberId: string;
  payload: {
    vendorId: string;
    accountId: string;
    label?: string;
    metadata?: Record<string, any>;
    callbackUrl?: string;
  };
}