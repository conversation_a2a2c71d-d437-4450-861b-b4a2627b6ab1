export type FetchBroadcastsMessagesParams = {
  vendorId: string;
  accountId: string;
  name?: string;
  size?: number;
  next?: string;
};

export type FetchBroadcastsMessagesResponse = {
  resources: [];
  pagination: {
    next: string;
  };
};

export type FetchBroadcastsMessagesResource = {
  id: string;
  name: string;
  recipientsValue: string;
  recipientsLabel: string;
  estimatedRecipientCount: string;
  content: string;
  sendDate: string;
  sentFrom: string;
  status: string;
};

export type FetchBroadcastDetailsParams = {
  vendorId: string;
  accountId: string;
  campaignId: string;
};

export type BroadcastDetailsType = {
  status: string;
  id: string;
  name: string;
  enableDeliveryReceipts: boolean;
  recipientCount: number;
  estimatedRecipientCount: number;
  recipientErrorCount: number;
  processedCount: number;
  processedErrorCount: number;
  estimatedBillingUnits: number | null;
  messagesQueued: number;
  queueFailures: number | null;
  format: string;
  createdAt: string;
  updatedAt: string;
  staggerWindow: string | null;
  scheduledDisplayTimezone: string;
  scheduled: string;
  metadata: {
    userName: string;
  };
  templateId: string;
  message: string;
  recipients: {
    contacts: string[];
    groups: string[];
    segments: string[];
  };
};

export type FetchBroadcastInsightsParams = {
  vendorId: string;
  accountId: string;
  date: string[];
  accounts?: string[];
  statuses?: MessageStatus[];
  source?: string;
  metadataKey?: string;
  metadataValue?: string;
  timezone?: string;
  groupBy?: string[]
  direction?: string;
  destination?: string;
  optOut?: boolean;
};

export type SummaryItemType = {
  date: string;
  group: string;
  totalBillingUnits: number;
  totalOptOut: number;
  totalReceived: number;
  totalSent: number;
}

export type BroadcastInsightsType = {
  summaries: SummaryItemType[];
  totalBillingUnits: number;
  totalOptOut: number;
  totalReceived: number;
  totalSent: number;
};

export type FetchMessageDetailsParams = {
  accountId: string;
  vendorId: string;
  size: number;
  date: string[];
  timezone?: string;
  accounts?: string[];
  direction?: string;
  destination?: string;
  source?: string;
  metadataKey?: string;
  metadataValue?: string;
  statuses?: MessageStatus[];
  type?: string;
  next?: string;
  sortByType?: string;
  sortDirection?: string;
  groupBy?: string[];
  optOut?: boolean;
}


export enum MessageStatus {
  DELIVERED = 'DELIVERED',
  SUBMITTED = 'SUBMITTED',
  FAILED = 'FAILED',
  PENDING = 'PENDING',
  REJECTED = 'REJECTED',
  EXPIRED = 'EXPIRED',
  UNDEFINED = 'UNDEFINED',
  HELD = 'HELD',
}

export interface Message {
  messageId: string;
  format: string;
  timestamp: string;
  deliveredTimestamp?: string;
  lastStatusUpdate: string;
  direction: 'inbound' | 'outbound';
  status: MessageStatus;
  inResponseTo?: string;
  mediaUrl?: string[];
  statusCode: number;
  statusDescription: string;
  sourceAddress: string;
  sourceAddressCountry: string;
  destinationAddress: string;
  destinationAddressCountry: string;
  content: string;
  accountId: string;
  action: string;
  units: number;
  metadata: Array<{ key: string; value: string }>;
}

export type BroadcastDetailsTablesType = {
  outbound?: {
    pagination: {
      next?: string;
      token?: string;
    };
    resources: Message[];
  };
  inbound?: {
    pagination: {
      next?: string;
      token?: string;
    };
    resources: Message[];
  };
  optOut?: {
    pagination: {
      next?: string;
      token?: string;
    };
    resources: Message[];
  };
  undelivered?: {
    pagination: {
      next?: string;
      token?: string;
    };
    resources: Message[];
  };
}

export type BroadcastInsightsTablesType = {
  outbound?: BroadcastInsightsType,
  inbound?: BroadcastInsightsType,
  optOut?: BroadcastInsightsType,
  undelivered?: BroadcastInsightsType,
}
