export type TWorkflowsData = {
  id: string;
  name: string;
  status: string;
  labels: string[]
};

export type WorkflowDetail = {
  id: string;
  name: string;
  status: string;
  labels: string[]
}

export type Executions = {
  pagination: {
    next?: string;
  };
  resources: Execution[];
}

export type Execution = {
  executionArn: string;
  stateMachineArn: string;
  name: string;
  status: string;
  startDate: string;
  stopDate: string;
  redriveCount: number;
  events: ExecutionEvent[];
}

export type ExecutionEvent = {
  id: string;
  stateExitedEventDetails: StateExitedEventDetails;
  lambdaFunctionFailedEventDetails: FailedEventDetails
  isExpanded: boolean;
  type: string;
  name: string;
}

export type FailedEventDetails = {
  cause: string;

}
export type StateExitedEventDetails = {
  name: string;
  output: string;
}

export type TWorkflows = {
  executions: TWorkflowsData[];
  nextToken: string;
}

export enum ExecutionStatus {
  RUNNING = 'RUNNING',
  SUCCEEDED = 'SUCCEEDED',
  FAILED = 'FAILED',
  TIMED_OUT = 'TIMED_OUT',
  ABORTED = 'ABORTED',
}


export type FetchWorkflowsDetailParams = {
  workflowID: string;
  next?: string;
  size: number;
  vendorId: string;
  accountId: string;
  status?: string
}

export type GetWorkflowDetailsParams = {
  workflowID: string;
  vendorId: string;
  accountId: string;
}

export type GetExecutionDetailsParams = {
  workflowId: string;
  executionId: string;
  vendorId: string;
  accountId: string;
}

export type FetchWorkflows = {
  vendorId: string;
  accountId: string;
  params: {
    next?: string;
    size: number;
  }
}

export type TUpdateWorkflow = {
  workflowId: string;
  vendorId: string;
  accountId: string;
  body: any
}
