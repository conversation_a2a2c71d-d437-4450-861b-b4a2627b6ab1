export type FetchSubAccountsDetailsParams = {
  vendorId: string;
  accountId: string;
  term?: string;
  status?: string;
  verificationStatus?: string;
  billingType?: string;
  next?: string;
  size?: number;
  createdAt?: string[];
};

export type SubAccount = {
  accountId: string;
  vendorId: string;
  status: string;
  verificationStatus: string;
  billingType: string;
  createdAt: string;
  id: string;
  accountLabel: string;
};

export type Pagination = {
  next: string;
};

export type SubAccountsResponse = {
  pagination: Pagination;
  resources: SubAccount[];
};
