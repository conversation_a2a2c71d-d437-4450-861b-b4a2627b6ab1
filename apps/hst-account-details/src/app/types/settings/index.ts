export const ACTIONS = {
  reinstate: 'reinstate',
  suspend: 'suspend',
  cancel: 'cancel',
  'extend-trial-duration': 'extend-trial-duration',
};

export type AccountStatusType = {
  status: string;
  state: string;
  verificationStatus: string;
  verificationReason: string;
  actions: (keyof typeof ACTIONS)[];
};

export type FetchAccountStatusParams = {
  vendorId: string;
  accountId: string;
};

export type UpdateAccountStatusParams = {
  vendorId: string;
  accountId: string;
  status: string;
  reason?: string;
  applyToSubaccounts?: boolean;
};

export type FetchUsageTrackingParams = {
  vendorId: string;
  accountId: string;
  next?: string;
  size?: number;
};

export type UsageTrackingItem = {
  id?: string;
  label: string;
  threshold: string;
  period: string;
  timezoneType: string;
  action: string;
  timezone: string;
  alerts: number[];
};

export type UsageTrackingType = {
  pagination: {
    next: string;
  };
  resources: UsageTrackingItem[];
};

export type UpdateUTTParams = {
  vendorId: string;
  accountId: string;
  body: UsageTrackingItem;
};

export type DeleteUTTParams = {
  vendorId: string;
  accountId: string;
  id: string;
};

export type AccountDetailsType = {
  accountName: string;
  accountId: string;
  operatingCountry: string;
  timezone: string;
  status: string;
  deliveryReceipts?: string;
  defaultTrafficClass?: string;
  type?: string;
  billingType?: string;
  deliveryReceiptEnabled?: boolean;
  delegatedCompliance?: boolean;
  ignoreClientSendingAccount?: boolean;
  saveDeliveryReportsInREST?: boolean;
  saveRepliesInREST?: boolean;
  externalContentSupported?: boolean;
};

export type FetchAccountDetailsParams = FetchAccountStatusParams;

export type UpdateAccountDetailsParams = {
  vendorId: string;
  accountId: string;
  data: AccountDetailsType;
};

export type FetchHoldFilterSettingsParams = {
  vendorId: string;
  accountId: string;
};

export type HoldFilterSettingsType = {
  accountId: string;
  autoApprove: boolean;
  createdAt: string;
  updatedAt: string;
  updatedBy: string;
  updatedByUsername: string;
};

export type UpdateHoldFilterSettingsParams = {
  vendorId: string;
  accountId: string;
  autoApprove: boolean;
};

export type FetchInboundKeywordsParams = {
  vendorId: string;
  accountId: string;
  next?: string;
  size?: number;
};

export type InboundKeywordsItem = {
  id?: string;
  keyword: string;
  action: string;
  requiredForCompliance: boolean;
  autoResponse?: string;
};

export type InboundKeywordsType = {
  pagination: {
    next: string;
  };
  resources: InboundKeywordsItem[];
};

export type UpdateInboundKeywordsParams = {
  vendorId: string;
  accountId: string;
  body: InboundKeywordsItem;
};

export type DeleteInboundKeywordsParams = {
  vendorId: string;
  accountId: string;
  id: string;
};

export type FetchCountriesParams = {
  vendorId: string;
  accountId: string;
};

export type CarrierItem = {
  id: string;
  carrierId: string;
  name: string;
  new: false;
  _links: {
    self: {
      href: string;
    };
    carrier: {
      href: string;
    };
  };
};

export type CarriersType = {
  resources: CarrierItem[];
};

export type CountryItem = {
  id: string;
  countryCode: string;
  countryName: string;
  new: boolean;
  _links: {
    self: {
      href: string;
    };
    country: {
      href: string;
    };
  };
};

export type CountriesType = {
  resources: CountryItem[];
};

export type FetchCarriersParams = {
  vendorId: string;
  accountId: string;
};

// Access Controls Types
export interface AccessControlAction {
  id: string;
  name: string;
}

export interface AccessControlType {
  id: string;
  name: string;
}

export interface AccessControlProperty {
  id: string;
  propertyKey: string;
  multiValue: boolean;
}

export interface AccessControl {
  id: string;
  action: AccessControlAction;
  type: AccessControlType;
  accessControlProperties: Record<string, any>[];
}

export interface AccessControlsResponse {
  resources: AccessControl[];
}

export interface AccessControlTypesResponse {
  resources: {
    id: string;
    name: string;
  }[];
}

export interface ValidActionsResponse {
  resources: AccessControlAction[];
}

export interface ValidPropertiesResponse {
  resources: AccessControlProperty[];
}

// Access Controls API Params Types
export interface FetchAccessControlsParams {
  vendorId: string;
  accountId: string;
}

export interface UpdateAccessControlsParams {
  vendorId: string;
  accountId: string;
  data: {
    accessControls: Record<string, any>;
  };
}

export interface FetchAccessControlTypesParams {
  vendorId: string;
  accountId: string;
}

export interface FetchValidActionsParams {
  vendorId: string;
  accountId: string;
  accessControlTypeId: string;
}

export interface FetchValidPropertiesParams {
  vendorId: string;
  accountId: string;
  accessControlTypeId: string;
}
