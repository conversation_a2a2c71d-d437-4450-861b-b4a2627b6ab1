export type FetchProvidersParams = {
  vendorId: string;
  accountId: string;
};

export type PostProviderWeightParams = {
  vendorId: string;
  accountId: string;
  payload: ProviderWeightsItem;
};

export type PatchProviderWeightParams = {
  vendorId: string;
  accountId: string;
  payload: ProviderWeightsItem;
};

export type FetchProviderWeightsParams = {
  vendorId: string;
  accountId: string;
  next?: string;
  size?: number;
};

export type ProviderWeightsItem = {
  id: string;
  type: string;
  presence: string;
  weight: string;
  carrier: string;
  provider: string;
  value: string;
  serviceType?: string;
  addressType: string;
  endpointType: string;
  country: string;
};

export type ProviderWeightsType = {
  pagination: {
    next: string;
  };
  resources: ProviderWeightsItem[];
};

export type ProviderItem = {
  id: string;
  name: string;
  providerId: string;
  new: boolean;
  _links: {
    self: {
      href: string;
    };
    provider: {
      href: string;
    };
  };
};

export type ProvidersType = {
  resources: ProviderItem[];
};

export type FetchAddressTypesParams = {
  vendorId: string;
  accountId: string;
};

export type AddressTypeItem = {
  id: string;
  name: string;
  providerId: string;
  new: boolean;
  _links: {
    self: {
      href: string;
    };
    provider: {
      href: string;
    };
  };
};

export type AddressTypesType = {
  resources: AddressTypeItem[];
};

export type FetchProviderWeightTypesParams = {
  vendorId: string;
  accountId: string;
};

export type DeleteProviderWeightParams = {
  vendorId: string;
  accountId: string;
  providerWeightId: string;
};

export type ProviderWeightTypeItem = {
  id: string;
  name: string;
  providerId: string;
  new: boolean;
  _links: {
    self: {
      href: string;
    };
    provider: {
      href: string;
    };
  };
};

export type ProviderWeightTypesType = {
  resources: ProviderWeightTypeItem[];
};

