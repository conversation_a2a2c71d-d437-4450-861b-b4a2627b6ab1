
export type FetchDeliveryPropertiesParams = {
  vendorId: string;
  accountId: string;
  next?: string;
  size?: number;
};

export type DeliveryPropertiesItem = {
  id: string;
  url?: string;
  type: string;
  presence: string;
  weight: string;
  carrier: string;
  provider: string;
  value: string;
  addressType: string;
  endpointType: string;
  country: string;
};

export type DeliveryPropertiesType = {
  pagination: {
    next: string;
  };
  resources: DeliveryPropertiesItem[];
};

export type FetchDeliveryPropertyTypesParams = {
  vendorId: string;
  accountId: string;
};

export type DeliveryPropertyTypeItem = {
  id: string;
  name: string;
  maximumRoutingAttempts: boolean;
  allocateSourceAddress: boolean;
  familiarSender: boolean;
  new: boolean;
  _links: {
    self: {
      href: string;
    };
    deliveryPropertyType: {
      href: string;
    };
  };
};

export type DeliveryPropertyTypesType = {
  resources: DeliveryPropertyTypeItem[];
};


export type PostDeliveryPropertiesParams = {
  vendorId: string;
  accountId: string;
  payload: DeliveryPropertiesItem;
};

export type PatchDeliveryPropertiesParams = {
  vendorId: string;
  accountId: string;
  payload: DeliveryPropertiesItem;
};

export type DeleteDeliveryPropertiesParams = {
  vendorId: string;
  accountId: string;
  id: string;
};
