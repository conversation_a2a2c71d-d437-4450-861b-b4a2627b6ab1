export type FetchHubActivityLogParams = {
  vendorId: string;
  accountId: string;
  size: number;
  startDate?: string;
  endDate?: string;
  objectTypes?: string[];
  user?: string;
  next?: string;
};

export type HubActivityLogItem = {
  id: string;
  requester: {
    userId: string;
    userName: string;
    ipAddress: string;
    userAgent: string;
  };
  timestamp: string;
  isSupport: boolean;
  activity: {
    objectType: string;
    objectIdentifier: string;
    action: string;
  };
};

export type HubActivityLogType = {
  pagination: {
    next?: string;
  };
  resources: HubActivityLogItem[];
};

export type FetchHubActivityLogUsersParams = {
  vendorId: string;
  accountId: string;
  size: number;
};

export type ActivityLogUserType = {
  id: string;
  name: string;
};

export type HubActivityLogUsersType = {
  pagination: {
    next: string;
  };
  resources: ActivityLogUserType[];
};

export type ContactSyncItem = {
  integrationId: string;
  batchId: string;
  platform: string;
  syncAt: string;
  reportName: string;
  reportUrl: string;
  numberOfSuccess: number;
  numberOfFailed: number;
};

export type EcosystemHistoryType = {
  pagination: {
    next: string;
  };
  resources: ContactSyncItem[];
};

export type FetchEcosystemHistoryParams = {
  vendorId: string;
  accountId: string;
  size: number;
  fromDate?: string;
  toDate?: string;
  platform?: string;
  next?: string;
};

export type FetchSupportLogsParams = {
  vendorId: string;
  accountId: string;
  size: number;
  fromDate?: string;
  toDate?: string;
  next?: string;
  httpMethod?: string[];
  restrictResults?: boolean;
};

export type SupportLogItem = {
  id: string;
  accountId: string;
  vendorId: string;
  uriPath: string;
  httpMethod: string;
  logDate: string;
  userName: string;
  responseCode: number;
  payload: string;
};

export type SupportLogsType = {
  pagination: {
    next: string;
  };
  resources: SupportLogItem[];
};
