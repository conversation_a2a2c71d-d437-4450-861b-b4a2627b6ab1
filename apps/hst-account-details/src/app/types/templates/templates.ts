export type TemplatesSortBy =
  | 'name.asc'
  | 'createdAt.asc'
  | 'updatedAt.asc'
  | 'name.desc'
  | 'createdAt.desc'
  | 'updatedAt.desc';

export type TemplateType = 'standard' | 'restricted';

export interface FilterCriteria {
  name: string;
}

export interface GetAccountTemplatesParams {
  accountId: string;
  vendorId: string;
  sortBy?: TemplatesSortBy;
  type?: TemplateType;
  size?: number;
  filterCriteria?: FilterCriteria;
}

export type GetAccountTemplatesQueryParams = Omit<GetAccountTemplatesParams, 'accountId' | 'vendorId'>;

interface SharedAccountAccess {
  [accountId: string]: string;
}

export interface BroadcastTemplateType {
  id: string;
  name: string;
  messageText: string;
  default: boolean;
  type: string;
  draftBroadcasts: number;
    scheduledBroadcasts: number;
    createdAt: string;
    updatedAt: string;
    sharedAccountIds: SharedAccountAccess;
  mode: string;
}

export interface TemplateResponse {
  pagination: {
    next: string;
  };
  resources: BroadcastTemplateType[];
}
