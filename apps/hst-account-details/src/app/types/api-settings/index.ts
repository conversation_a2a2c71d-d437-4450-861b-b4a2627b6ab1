export type FetchBasicAuthenticationParams = {
  vendorId: string;
  accountId: string;
  next?: string;
  size?: number;
  type: string;
  label: string;
};

export type BasicAuthenticationItem = {
  id?: string;
  createdAt: string;
  key: string;
  label: string;
  lastModifiedAt: string;
  role: string;
  type: string;
  secret: string;
};

export type BasicAuthenticationType = {
  pagination: {
    next: string;
  };
  resources: BasicAuthenticationItem[];
};

export type UpdateBasicAuthenticationParams = {
  vendorId: string;
  accountId: string;
  body: BasicAuthenticationItem;
  type: string;
};

export type DeleteBasicAuthenticationParams = {
  vendorId: string;
  accountId: string;
  id: string;
  type: string;
};
