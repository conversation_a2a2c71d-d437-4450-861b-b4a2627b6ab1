export interface ApiKey {
  id: string;
  label: string;
  keyHashed: string;
  generatedKey: string | null;
  generatedSecret: string | null;
  new: boolean;
}

export interface Role {
  id: string;
  name: string;
  new: boolean;
}

export interface ApiKeyAssignment {
  id: string;
  new: boolean;
  apiKey: ApiKey;
  role: Role;
}

export interface BaseApiAssignmentPayload {
  role: ROLES_KEY;
  apiKey: string;
  name: string;
}

export interface UpdateApiKeyAssignmentsPayload
  extends BaseApiAssignmentPayload {
  id: string;
}

export type ApiAssignmentPayload =
  | BaseApiAssignmentPayload
  | UpdateApiKeyAssignmentsPayload;

export interface ApiAssignmentProps {
  vendorId: string;
  accountId: string;
  isEditable: boolean;
}

export interface ApiAssignmentState {
  data?: {
    resources: ApiKeyAssignment[];
  };
  loading: string;
  deleteLoading: string;
}

export enum ROLES_KEY {
  ADMIN = 'ADMIN',
  USER = 'USER',
}
