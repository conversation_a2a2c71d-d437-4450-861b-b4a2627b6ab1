export type WebhookItem = {
  id?: string;
  url: string;
  requestBody?: string;
  method: string;
  headers?: any;
  connectionTimeout: number;
  readTimeout: number;
  maximumRedirects: number;
  tlsCertificateVerification: string;
  responseBody: string;
  responseCode: string;
  notificationEvents: string[],
  ttl: number;
  retries?: any;
};

export type WebhooksType = {
  pagination: {
    next: string;
  };
  resources: WebhookItem[];
};

export type FetchWebhooksParams = {
  vendorId: string;
  accountId: string;
  size: number;
  next?: string;
};

export type DeleteWebhooksParams = {
  vendorId: string;
  accountId: string;
  webhookId: string;
};

export type UpdateWebhooksParams = {
  vendorId: string;
  accountId: string;
  payload: WebhookItem,
};
