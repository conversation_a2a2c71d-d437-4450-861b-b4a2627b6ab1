export type FetchHmacAuthenticationParams = {
  vendorId: string;
  accountId: string;
  next?: string;
  size?: number;
  type: string;
  label: string;
};

export type HmacAuthenticationItem = {
  id?: string;
  createdAt: string;
  key: string;
  label: string;
  lastModifiedAt: string;
  role: string;
  type: string;
};

export type HmacAuthenticationType = {
  pagination: {
    next: string;
  };
  resources: HmacAuthenticationItem[];
};

export type UpdateHmacAuthenticationParams = {
  vendorId: string;
  accountId: string;
  body: HmacAuthenticationItem;
  type: string;
};

export type DeleteHmacAuthenticationParams = {
  vendorId: string;
  accountId: string;
  id: string;
  type: string;
};
