export interface LegacyCredentialsItem {
  id: string;
  username: string;
  password: string;
}

export interface LegacyCredentialsType {
  pagination?: {
    next?: string;
  };
  resources: LegacyCredentialsItem[];
}

export interface CreateLegacyCredentialsParams {
  vendorId: string;
  accountId: string;
  username: string;
  password: string;
}

export interface FetchLegacyCredentialsParams {
  vendorId: string;
  accountId: string;
  username?: string;
  size?: number;
  next?: string;
}

export interface FetchSingleLegacyCredentialParams {
  vendorId: string;
  accountId: string;
  credentialId: string;
}

export interface UpdateLegacyCredentialsParams {
  vendorId: string;
  accountId: string;
  credentialId: string;
  password: string;
}

export interface DeleteLegacyCredentialsParams {
  vendorId: string;
  accountId: string;
  credentialId: string;
}
