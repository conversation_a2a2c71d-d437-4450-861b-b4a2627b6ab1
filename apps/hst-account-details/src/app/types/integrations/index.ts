export type IntegrationItem = {
  accountId: string;
  extras: {
    enableEmailNotification: boolean;
    // eslint-disable-next-line camelcase
    response_type: string;
    integrationUrl?: string;
    storeId?: string;
  };
  vendorId: string;
  status: string;
  integrationId: string;
  integration: string;
  createdAtTimestamp: string;
  updatedAtTimestamp: string;
  connectionStatus?: string;
  connectionType?: string;
  primaryAccount?: string;
};

export type IntegrationsType = {
  resources: IntegrationItem[];
};

export type FetchIntegrationsParams = {
  accountId: string;
  platform?: string;
};

export type FetchHubspotIntegrationParams = {
  vendorId: string;
  accountId: string;
};

export type DeleteIntegrationConnectionParams = {
  vendorId: string;
  accountId: string;
  integration: string;
};
