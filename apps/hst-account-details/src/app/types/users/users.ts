export type AccountType = {
  accountId: string;
  accountName: string;
  invitationStatus: string;
  role: string;
};

export type UserType = {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  createdAt: string;
  locked: boolean;
  lastLoggedIn?: string;
  accounts: AccountType[];
  phone?: string;
};

export type GetUsersParamsType = {
  accountId: string;
  params: {
    filter: string;
    accountIds?: string[];
    roles: string[];
    sortBy?: string;
    sortDirection: string;
    page: number;
    pageSize: number;
    vendorId: string;
  };
};

export type UsersResponseType = {
  pagination: {
    totalPages: number;
    totalResults: number;
    next?: string;
    previous?: string;
  };
  resources: UserType[];
};

export type GetSubAccountsParamsType = {
  accountId: string;
  vendorId: string;
  params: {
    size: number;
  };
};

export type SubAccount = {
  accountId: string;
  vendorId: string;
  accountLabel: string;
  createdAt: string;
  status: string;
};

export type SubAccountsResponseType = {
  pagination: {
    next: string;
  };
  resources: SubAccount[];
};

export type UpdateUserParams = {
  accountId: string;
  vendorId: string;
  userId: string;
  params: {
    phone: string;
  };
};

export type LockUserParams = {
  userId: string;
  accountId: string;
  vendorId: string;
  payload: {
    reason: string;
    reasonDetails: string;
  };
};
