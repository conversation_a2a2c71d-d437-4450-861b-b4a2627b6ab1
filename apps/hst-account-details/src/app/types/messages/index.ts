export interface Message {
  messageId: string;
  format: string;
  timestamp: string;
  deliveredTimestamp?: string;
  lastStatusUpdate: string;
  direction: 'inbound' | 'outbound';
  status: MessageStatus;
  inResponseTo?: string;
  mediaUrl?: string[];
  statusCode: number;
  statusDescription: string;
  sourceAddress: string;
  sourceAddressCountry: string;
  destinationAddress: string;
  destinationAddressCountry: string;
  content: string;
  accountId: string;
  action: string;
  units: number;
  metadata: Array<{ key: string; value: string }>;
}

export interface MessagesType {
  pagination: {
    next?: string;
    token?: string;
  };
  resources: Message[];
}

export type FetchMessageParams = {
  accountId: string;
  vendorId: string;
  size: number;
  date: string[];
  timezone?: string;
  accounts?: string[];
  direction?: string;
  destination?: string;
  source?: string;
  metadataKey?: string;
  metadataValue?: string;
  statuses?: MessageStatus[];
  type?: string;
  next?: string;
  sortByType?: string;
  sortDirection?: string;
  groupBy?: string[];
  optOut?: boolean;
}

export enum MessageStatus {
  DELIVERED = 'DELIVERED',
  SUBMITTED = 'SUBMITTED',
  FAILED = 'FAILED',
  PENDING = 'PENDING',
  REJECTED = 'REJECTED',
  EXPIRED = 'EXPIRED'
}

export type SummaryItemType = {
  date: string;
  group: string;
  totalBillingUnits: number;
  totalOptOut: number;
  totalReceived: number;
  totalSent: number;
}

export type MessagesInsightsType = {
  summaries: SummaryItemType[];
  totalBillingUnits: number;
  totalOptOut: number;
  totalReceived: number;
  totalSent: number;
};

export type FetchMessagesInsightsParams = {
  vendorId: string;
  accountId: string;
  date: string[];
  accounts?: string[];
  statuses?: string[];
  source?: string;
  metadataKey?: string;
  metadataValue?: string;
  timezone?: string;
  groupBy?: string[]
  direction?: string;
  destination?: string;
};

export type FetchMetakeysParams = {
  vendorId: string;
  accountId: string;
  date: string[]
};

export type MetakeysType = {
  resources: string[];
}

export type FetchAutomatedBroadcastsParams = {
  vendorId: string;
  accountId: string;
  size: string | number;
  next?: string;
};

export type AutomatedBroadcastType = {
  id: string;
  name: string;
  recurrence: string;
  filePath: string;
  accountHeader: string;
  createdAt: string;
  accountId: string;
  host: string;
}

export type AutomatedBroadcastsType = {
  pagination: {
    next?: string
  },
  resources: AutomatedBroadcastType[]
}

export type DeleteAutomatedBroadcastParams = {
  vendorId: string;
  accountId: string;
  broadcastId: string;
}
