export interface FileSettings {
  filename: string;
  isCustomFilename: boolean;
  headers?: string[];
  phoneHeader: string;
  accountHeader: string;
  fileHasHeaderRow: boolean;
}

export interface ScheduleSettings {
  minute: number;
  hour: number;
  days: number[];
  staggerWindow?: number;
}

export interface SftpSettings {
  host: string;
  username: string;
  password: string;
  path: string;
}

export interface AutomatedBroadcastTemplate {
  templateId: string;
  templateName: string;
  customerEmails: string[];
  fileSettings: FileSettings;
  scheduleSettings: ScheduleSettings;
  sftpSettings: SftpSettings;
}

export interface CreateAutomatedBroadcastParams extends AutomatedBroadcastTemplate {
  vendorId: string;
  accountId: string;
}

export interface UpdateAutomatedBroadcastParams extends AutomatedBroadcastTemplate {
  vendorId: string;
  accountId: string;
  id: string;
}

export interface DeleteAutomatedBroadcastParams {
  vendorId: string;
  accountId: string;
  id: string;
}

export interface FetchAutomatedBroadcastsParams {
  vendorId: string;
  accountId: string;
}

export interface GetAutomatedBroadcastByIdParams {
  vendorId: string;
  accountId: string;
  id: string;
}
