import { COUNTRIES_MAPPING } from '../../constants/geoip';

export type FetchUITempateParams = {
  vendorId: string;
  accountId: string;
};

export type FetchAccountDetailsParams = {
  vendorId: string;
  accountId: string;
};

export type AccountDetailsType = {
  accountId: string;
  vendorId: string;
  createdAt: string;
  label: string;
  state: string;
  status: string;
  sinchAccountId: string;
  sinchAppId: string;
  sinchAppRegion: string;
  sinchProjectAppModel: string;
  sinchProjectId: string;
  resources: {
    parentAccountId: string;
    parentAccountName: string;
    timezone: string;
    successManagerName: string;
    ownerName: string;
    signupSource: string;
    restrictedAccess: string;
    verificationStatus: string;
    operatingCountry: keyof typeof COUNTRIES_MAPPING;
    supportTier?: string;
    carrierBillingNumber?: string;
    billing: {
      volume: number;
      balance: number;
      billingType: string;
      currency: string;
      zuoraId: string;
      billingAccountId: string;
    };
    featureFlags: [
      {
        scope: string;
        name: string;
        label: string;
      }
    ];
  };
};

export type BillingDetailsType = {
  volume: number;
  balance: number;
  billingType: string;
  currency: string;
  zuoraId: string;
  billingAccountId: string;
};

export type FetchBillingDetailsParams = {
  vendorId: string;
  accountId: string;
  next?: string;
  size?: number;
};

export type BillingDetailsInvoiceType = {
  resources: [
    {
      id: string;
      defaultTimezone: string;
      invoiceDate: string;
      invoiceNumber: string;
      amount: number;
      balance: number;
      status: string;
    }
  ];
  pagination: {
    next: string;
  };
};

export type SummaryType = {
  date: string;
  group: string;
  totalSent: number;
  totalReceived: number;
  totalBillingUnits: number;
  totalOptOut: number;
};

export type UsageType = {
  summaries: SummaryType[];
  totalBillingUnits: number;
  totalOptOut: number;
  totalReceived: number;
  totalSent: number;
};

export type FetchUsageParams = {
  vendorId: string;
  accountId: string;
  date: string[];
  timezone: string;
};

export type FetchMessagesParams = {
  vendorId: string;
  accountId: string;
  date: string[];
  timezone: string;
};

export type FetchLimitsParams = {
  vendorId: string;
  accountId: string;
};

export type LimitsType = {
  monthlyLimit: number;
  weeklyLimit: number;
  dailyLimit: number;
  hourlyLimit: number;
};

export type FetchFeaturesParams = {
  vendorId: string;
  accountId: string;
};

export type Feature = {
  name: string;
  type: string;
  label: string;
};

export type FeaturesType = {
  features: Feature[];
};

export type PostAccountCreditParams = {
  vendorId: string;
  accountId: string;
  payload: {
    quantity: number;
    reason: string;
    accountCountry: string;
  };
};

export type FetchBillingBalanceParams = {
  vendorId: string;
  accountId: string;
  billingType: string;
};

export type BillingBalanceType = {
  account: string;
  currentBalance: {
    allocationType: string;
    credits: {
      MT_SMS: number;
    };
    expiresAt: string;
  };
  recentExpiredBalance: {
    credits: {
      MT_SMS: number;
    };
    expiredAt: string;
    unit: string;
  };
};
