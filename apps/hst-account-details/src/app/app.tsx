/* eslint-disable @nx/enforce-module-boundaries */
import { ErrorBoundary } from 'components';
import { withPermissions } from 'hocs';

import AccountDetailsView from './account-details/account-details';
import styles from './app.module.less';

type Props = {
  roles: Array<string>
}

export const AccountDetails = (props: Props) => {
  const { roles } = props
  return (
    <ErrorBoundary componentName="AccountDetails" >
      <div className={styles.container}>
        <AccountDetailsView roles={roles} />
      </div>
    </ErrorBoundary>
  )
}
const AccountDetailsApp = withPermissions(AccountDetails);
export default AccountDetailsApp

