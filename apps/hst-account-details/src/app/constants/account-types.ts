export const ACCOUNT_UI_TYPE = {
  SinchEngage: 'sinchEngage',
  SinchVoice: 'sinchVoice',
  SinchCPaaS: 'sinchCPaaS',
  Mailgun: 'mailgun',
};

export const SINCH_PROJECT_APP_MODAL_ASP_DES =
  'ASP: Legacy model where all customer apps and projects are housed within a single Sinch account (representing MessageMedia/SinchEU vendor).\n\nUnified: Each customer receives their own dedicated Sinch account where all their apps and projects are created and managed.';

export const NO_SINCH_PROJECT_APP_MODAL_DES =
  'No Sinch project and app have been configured for the AMS account.';
export default ACCOUNT_UI_TYPE;
