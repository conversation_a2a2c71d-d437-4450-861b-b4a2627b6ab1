import { useFeatureFlag } from "configcat-react";
import { countries } from '@nectary/components/utils/countries';

export const SYD_VENDOR = {
  MessageMedia: 'MessageMedia',
  DirectSMS: 'DirectSMS',
  SMSBroadcast: 'SMSBroadcast',
  SMSCentral: 'SMSCentral',
  Streetdata: 'Streetdata',
  MessageNet: 'MessageNet',
  Mobipost: 'Mobipost',
  WholesaleSMS: 'WholesaleSMS',
  ClickSend: 'ClickSend',
  VodaNZ: 'VodaNZ',
  '2Degrees': '2Degrees',
  eTXT: 'eTXT',
  TPGTelecom: 'TPGTelecom',
};

export const NUMBER_VERIFICATION_STATUS = {
  UNVERIFIED: 'UNVERIFIED',
  PENDING: 'PENDING',
  ASSIGNED: 'ASSIGNED',
  VERIFIED: 'VERIFIED',
}

export const CLASSIFICATION = {
  BRONZE: 'BRONZE',
  SILVER: 'SILVER',
  GOLD: 'GOLD',
}

export const CLASSIFICATION_OPTIONS = [
  {
    label: CLASSIFICATION.BRONZE,
    value: CLASSIFICATION.BRONZE,
  },
  {
    label: CLASSIFICATION.SILVER,
    value: CLASSIFICATION.SILVER,
  },
  {
    label: CLASSIFICATION.GOLD,
    value: CLASSIFICATION.GOLD,
  },
]


export const CAPABILITIES = {
  SMS: 'SMS',
  TTS: 'TTS',
  MMS: 'MMS',
  CALL: 'CALL',
}

export const CAPABILITIES_OPTIONS = [
  {
    label: CAPABILITIES.SMS,
    value: CAPABILITIES.SMS,
  },
  {
    label: CAPABILITIES.TTS,
    value: CAPABILITIES.TTS,
  },
  {
    label: CAPABILITIES.MMS,
    value: CAPABILITIES.MMS,
  },
  {
    label: CAPABILITIES.CALL,
    value: CAPABILITIES.CALL,
  },
]
export const NUMBER_TYPE = {
  MOBILE: 'MOBILE',
  LANDLINE: 'LANDLINE',
  TOLL_FREE: 'TOLL_FREE',
  SHORT_CODE: 'SHORT_CODE',
  TEN_DLC: 'TEN_DLC',
  HOSTED_TEN_DLC: 'HOSTED_TEN_DLC'
}
export const US_NUMBER_TYPES = [NUMBER_TYPE.TEN_DLC, NUMBER_TYPE.HOSTED_TEN_DLC, NUMBER_TYPE.TOLL_FREE]

export const ASSIGNMENT_STATUS = {
  ASSIGNED: 'ASSIGNED',
  UNASSIGNED: 'UNASSIGNED',
}

export const ASSIGNMENT_STATUS_OPTIONS = [
  {
    label: 'All',
    value: '',
  },
  {
    label: ASSIGNMENT_STATUS.ASSIGNED,
    value: ASSIGNMENT_STATUS.ASSIGNED,
  },
  {
    label: ASSIGNMENT_STATUS.UNASSIGNED,
    value: ASSIGNMENT_STATUS.UNASSIGNED,
  },
]

export const VERIFICATION_STATUS = {
  UNVERIFIED: 'UNVERIFIED',
  PENDING: 'PENDING',
  ASSIGNED: 'ASSIGNED',
  VERIFIED: 'VERIFIED',
}

export const VERIFICATION_STATUS_OPTIONS = [
  {
    label: 'All',
    value: '',
  },
  {
    label: VERIFICATION_STATUS.UNVERIFIED,
    value: VERIFICATION_STATUS.UNVERIFIED,
  },
  {
    label: VERIFICATION_STATUS.PENDING,
    value: VERIFICATION_STATUS.PENDING,
  },
  {
    label: VERIFICATION_STATUS.VERIFIED,
    value: VERIFICATION_STATUS.ASSIGNED,
  },
]


export const useNumberTypeOptions = () => {
  const { value: isHostedNumberEnabled } = useFeatureFlag('hostedNumberOptions', false);

  return [
    {
      label: NUMBER_TYPE.MOBILE,
      value: NUMBER_TYPE.MOBILE,
    },
    {
      label: NUMBER_TYPE.LANDLINE,
      value: NUMBER_TYPE.LANDLINE,
    },
    {
      label: NUMBER_TYPE.TOLL_FREE,
      value: NUMBER_TYPE.TOLL_FREE,
    },
    {
      label: NUMBER_TYPE.SHORT_CODE,
      value: NUMBER_TYPE.SHORT_CODE,
    },
    {
      label: NUMBER_TYPE.TEN_DLC,
      value: NUMBER_TYPE.TEN_DLC,
    },
    ...(isHostedNumberEnabled ? [
      {
        label: NUMBER_TYPE.HOSTED_TEN_DLC,
        value: NUMBER_TYPE.HOSTED_TEN_DLC,
      },
    ] : [])
  ];
};

export const US_SUPPORT_COUNTRIES = [['us', countries.us], ['ca', countries.ca], ['pr', countries.pr]]
export const US_SUPPORT_COUNTRIES_OPTIONS = [{
  label: countries.us.name,
  value: 'us',
}, {
  label: countries.ca.name,
  value: 'ca',
}, {
  label: countries.pr.name,
  value: 'pr',
}]
