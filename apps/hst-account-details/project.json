{"name": "hst-account-details", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/hst-account-details/src", "projectType": "application", "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"compiler": "babel", "outputPath": "dist/apps/hst-account-details", "index": "apps/hst-account-details/src/index.html", "baseHref": "/", "main": "apps/hst-account-details/src/main.tsx", "tsConfig": "apps/hst-account-details/tsconfig.app.json", "assets": ["apps/hst-account-details/src/favicon.ico", "apps/hst-account-details/src/assets"], "styles": ["apps/hst-account-details/src/styles.less"], "scripts": [], "isolatedConfig": true, "webpackConfig": "apps/hst-account-details/webpack.config.js"}, "configurations": {"development": {"extractLicenses": false, "optimization": false, "sourceMap": true, "vendorChunk": true}, "production": {"fileReplacements": [{"replace": "apps/hst-account-details/src/environments/environment.ts", "with": "apps/hst-account-details/src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false}}}, "serve": {"executor": "@nx/webpack:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "hst-account-details:build", "hmr": true}, "configurations": {"development": {"buildTarget": "hst-account-details:build:development"}, "production": {"buildTarget": "hst-account-details:build:production", "hmr": false}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/hst-account-details/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/hst-account-details/jest.config.ts"}}}, "tags": []}