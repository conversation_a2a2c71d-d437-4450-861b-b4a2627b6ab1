#!/usr/bin/env bash

set -eu

npm install --legacy-peer-deps && cd /code && tar -czf node_modules.tar.gz node_modules

wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | tee /etc/apt/trusted.gpg.d/google.asc >/dev/null

apt-get update && apt-get -y install awscli

echo "--- Clear the bucket"
aws s3 rm s3://sinchsaas-${1}-oneportal-module-cache --recursive

echo "--- Upload to the bucket"
aws s3 cp "node_modules.tar.gz" "s3://sinchsaas-${1}-oneportal-module-cache"

filehash=`shasum -a 512 package-lock.json | cut -f1 -d' '`

aws s3 cp "package-lock.json" "s3://sinchsaas-${1}-oneportal-module-cache/package-lock.json$filehash"
