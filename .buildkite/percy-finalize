# #!/usr/bin/env bash

# set +e
# echo ${BUILDKITE_BUILD_ID}

# set -x

# if [[ ${BROWSER} == "CHROME" ]]; then
#   if [[ ${BUILDKITE_BRANCH} =~ ^(staging|master|hotfix|.*percy.*).* ]]; then
#       # Apply Percy Finalize after capturing snapshots in parallel agents
#       echo "Percy finalize for parallel test suites with BUILDKITE_BUILD_ID - "${BUILDKITE_BUILD_ID}
#       npx percy build:finalize
#       exitCode=$?
#   else
#       echo "No Percy finalization with BUILDKITE_BUILD_ID - "${BUILDKITE_BUILD_ID}
#       exitCode=0
#   fi
# fi

# exit $exitCode
