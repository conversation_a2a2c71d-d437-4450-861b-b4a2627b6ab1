#!/usr/bin/env bash

set -eu

while [[ $# -gt 0 ]]; do
  case "$1" in
    --env)
      shift
      env="$1"
      ;;
    --region)
      shift
      region="$1"
      ;;
    *)
      echo "Invalid option: $1"
      exit 1
      ;;
  esac
  shift
done

case "${region}" in
  au) airport="syd";;
  us) airport="pdx";;
esac

MFE_S3_LOCATION="s3://sinchsaas-${env}-${airport}-mmgp-support"

bucket="${MFE_S3_LOCATION}"
backupBucket="${MFE_S3_LOCATION}/backup"

echo "+++ :s3: Remove the old backup ${backupBucket}"
aws s3 rm "${backupBucket}" --recursive

echo "+++ :s3: Backup ${bucket} to ${backupBucket}"
aws s3 sync "${bucket}" "${backupBucket}" --delete
