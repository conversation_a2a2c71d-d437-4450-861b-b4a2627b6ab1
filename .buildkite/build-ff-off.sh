set -e

echo '--- Building artifact files to deploy into qa environment.'

docker run \
-v ~/.ssh:/root/.ssh \
-v ${SSH_AUTH_SOCK}:/ssh-agent \
-v ${PWD}:/code \
-e "SSH_AUTH_SOCK=/ssh-agent" \
-e "HUSKY" \
-w="/code" \
cypress/browsers:node-18.16.0-chrome-113.0.5672.92-1-ff-113.0-edge-113.0.1774.35-1 \
bash -c "npm install --legacy-peer-deps && nx build hst-core cross-env BUILD_PATH=dist-ffoff FEATURE_FLAGS=false"
