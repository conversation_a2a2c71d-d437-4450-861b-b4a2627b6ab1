set -e

while [[ $# -gt 0 ]]; do
  case "$1" in
    --env)
      shift
      env="$1"
      ;;
    --prefix)
      shift
      prefix="$1"
      ;;
    --region)
      shift
      region="$1"
      ;;
    *)
      echo "Invalid option: $1"
      exit 1
      ;;
  esac
  shift
done


rawurlencode() {
  local string="${1}"
  local strlen=${#string}
  local encoded=""
  local pos c o

  for (( pos=0 ; pos<strlen ; pos++ )); do
    c=${string:$pos:1}
    case "$c" in
        [-_.~a-zA-Z0-9] ) o="${c}" ;;
        * )               printf -v o '%%%02x' "'$c"
    esac
    encoded+="${o}"
  done
  echo "${encoded}"    # You can either set a return variable (FASTER)
  REPLY="${encoded}"   #+or echo the result (EASIER)... or both... :p
}

rm -rf build && mkdir build

ls -R build

ENVIRONMENT=`aws ssm get-parameter --name /sinch/environment --query Parameter.Value --output text`
AIRPORT=`aws ssm get-parameter --name /sinch/aws/airport --query Parameter.Value --output text`

buildkite-agent artifact download "build${env}${AIRPORT}/*" build

MFE_S3_LOCATION="s3://${prefix}-${ENVIRONMENT}-${AIRPORT}-support"
MFE_S3_LOCATION_BRANCH="s3://${prefix}-${ENVIRONMENT}-${AIRPORT}-support/feature-branch/${BUILDKITE_BRANCH}"
VERSION_ANNOTATION_CONFIG="beta-support=on&mfe-support-dashboard-version"
REGION=$region

if [ -n "$REGION" ]; then
  REGION="$REGION"
fi

VERSION="${BUILDKITE_COMMIT:0:7}"

json_string=$(jq -n \
  --arg ONEPORTAL_VERSION "$VERSION" \
  '$ARGS.named'
)

echo "$json_string" > version.json

echo "-- Compress gzip for javascript and stylesheet files"

mkdir -p compression
for i in $(find build/ -type f | grep -E "\.css$|\.js$"); \
do mkdir -p "$(dirname "compression/$i")"; gzip -9vc "$i" > "compression/$i"; rm -f "$i"; \
done

echo "-- Deploying version ${VERSION} on environment ${ENVIRONMENT} & region ${REGION}"
echo "-- S3 location ${MFE_S3_LOCATION}"
echo "-- S3 location branch ${MFE_S3_LOCATION_BRANCH}"
echo "-- BUILDKITE_BRANCH ${BUILDKITE_BRANCH}"


if [[ $BUILDKITE_BRANCH == "staging" || $BUILDKITE_BRANCH == "master" ]]; then
  echo "-- Deploy to s3 root bucket"
  aws s3 sync build/build${env}${AIRPORT} "${MFE_S3_LOCATION}" --region "${REGION}" --delete --exclude "*backup*" --exclude "feature-branch/*"
  aws s3 sync --content-encoding 'gzip' compression/build/build${env}${AIRPORT} "${MFE_S3_LOCATION}" --region "${REGION}"
  aws s3 cp "version.json" "${MFE_S3_LOCATION}"
else
  echo "-- Deploy to s3 feature branch folder"
  aws s3 sync build/build${env}${AIRPORT} "${MFE_S3_LOCATION_BRANCH}" --region "${REGION}" --delete --exclude "*backup*"
  aws s3 sync --content-encoding 'gzip' compression/build/build${env}${AIRPORT} "${MFE_S3_LOCATION_BRANCH}" --region "${REGION}"
fi


buildkite-agent annotate "Version: ${VERSION}" --context "version-number"

if [ "$ENVIRONMENT" = "dev" ]; then
  encoded_url_to_manifest=`rawurlencode ${VERSION}`
  buildkite-agent annotate "http://hub.stg.messagemedia.com?beta-features=on&${VERSION_ANNOTATION_CONFIG}=${encoded_url_to_manifest}" --context "${ENVIRONMENT}-feature-branch"
fi

echo "--- Create Invalidation on ${env} after releasing to ${env} ---"
grep_value="onesaas-${env}-${AIRPORT}-support.s3.${REGION}.amazonaws.com"
DISTRIBUTION_ID=`aws cloudfront list-distributions --output text --query "DistributionList.Items[*].[Origins.Items[0].DomainName,Id]" | grep $grep_value | cut -f2`
aws cloudfront create-invalidation --distribution-id="${DISTRIBUTION_ID}" --paths "/*"
