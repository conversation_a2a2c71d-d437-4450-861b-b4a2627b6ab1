#!/usr/bin/env bash

set -eu

echo "--- :npm: Installing packages"
npm install --legacy-peer-deps

echo "--- :js: Fetching branches origin/${BUILDKITE_BRANCH} and origin/${BUILDKITE_PULL_REQUEST_BASE_BRANCH}"
git fetch origin ${<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_BRANCH} && git fetch origin ${BUILDKITE_PULL_REQUEST_BASE_BRANCH}

echo "--- :book: Generating release documents"
node tools/release/generate-release-document.js \
  --jiraUser=${JIRA_USER} \
  --jiraSecret=${JIRA_SECRET} \
  --source="origin/${BUILDKITE_BRANCH}" \
  --destination="origin/${BUILDKITE_PULL_REQUEST_BASE_BRANCH}" \
  --slackToken=${SLACK_TOKEN} \

