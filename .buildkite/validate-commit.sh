#!/usr/bin/env bash

set -eu

if [[ $BUILDKITE_BRANCH == "master" ]]; then
  exit 0
fi

echo "+++ Validating commit message"
echo "Fetching origin/${BUILDKITE_BRANCH} and origin/master"
git fetch origin ${B<PERSON>LDKITE_BRANCH} && git fetch origin master

echo "Validating gitlog compare origin/${BUILDKITE_BRANCH} with origin/master"
node apps/hst-core/tools/release/validate-gitlog.js --source=origin/${BUILDKITE_BRANCH} --destination=origin/master
