set -e

echo "-- <PERSON><PERSON><PERSON><PERSON><PERSON>E_MESSAGE ${B<PERSON><PERSON><PERSON>ITE_MESSAGE}"

FEATURE_BRANCH_FOLDER=`echo ${BUILDKITE_MESSAGE}|cut -d "'" -f 2`
echo "-- FEATURE_BRANCH_FOLDER ${FEATURE_BRANCH_FOLDER}"

if [[ -n "${FEATURE_BRANCH_FOLDER:-}" ]]; then
  ENVIRONMENT=`aws ssm get-parameter --name /sinch/environment --query Parameter.Value --output text`
  AIRPORT=`aws ssm get-parameter --name /sinch/aws/airport --query Parameter.Value --output text`

  MFE_S3_LOCATION_BRANCH="s3://onesaas-${ENVIRONMENT}-${AIRPORT}-support/feature-branch/${FEATURE_BRANCH_FOLDER}"

  echo "-- S3 fodler to delete: ${MFE_S3_LOCATION_BRANCH}"

  aws s3 rm --recursive "${MFE_S3_LOCATION_BRANCH}"
fi

