#!/usr/bin/env bash

set -eu

echo "--- :js: Download mock API"
aws s3 sync "${MOCK_API_S3_LOCATION}/master" "mock-api" --delete

echo "--- :docker: Prepare environment"
docker run \
-v ~/.ssh:/root/.ssh \
-v ${SSH_AUTH_SOCK}:/ssh-agent \
-v ${PWD}:/code \
-v "${PWD}/cypress/screenshots":/code/cypress/screenshots \
-v "${PWD}/container-dist":/code/container-dist \
-v "${PWD}/build-functional-standalone":/code/build \
-v "${PWD}/mock-api":/code/mock-api \
-v "${PWD}/.buildkite":/code/.buildkite \
-e "SSH_AUTH_SOCK=/ssh-agent" \
-e "DEV_SERVER_PORT=3015" \
-e "MOCK_API_PORT=3000" \
-e "PERCY_TOKEN" \
-e "PERCY_ENABLE" \
-e "PERCY_PROJECT=MessageMedia" \
-e "PERCY_TARGET_BRANCH=staging" \
-e "BROWSER=CHROME" \
-e "DOCKER=true" \
-e "BUILDKITE" \
-e "BUILDKITE_COMMIT=${BUILDKITE_COMMIT_FULL}" \
-e "BUILDKITE_BRANCH" \
-e "BUILDKITE_PULL_REQUEST" \
-e "BUILDKITE_BUILD_ID" \
-e "CYPRESS_APP_HOSTNAME=localhost" \
-e "CYPRESS_RECORD_KEY" \
-e "CYPRESS_PROJECT_ID" \
-e "HUSKY" \
-w="/code" \
cypress/browsers:node-18.16.0-chrome-112.0.5615.121-1-ff-112.0.1-edge-112.0.1722.48-1 \
bash -c .buildkite/prestart-functional-test-standalone.sh