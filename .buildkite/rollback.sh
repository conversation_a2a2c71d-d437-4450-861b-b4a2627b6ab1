#!/usr/bin/env bash

set -eu

while [[ $# -gt 0 ]]; do
  case "$1" in
    --env)
      shift
      env="$1"
      ;;
    --region)
      shift
      region="$1"
      ;;
    *)
      echo "Invalid option: $1"
      exit 1
      ;;
  esac
  shift
done

case "${region}" in
  au) airport="syd";;
  us) airport="pdx";;
esac

MFE_S3_LOCATION="s3://sinchsaas-${env}-${airport}-mmgp-support"

bucket="${MFE_S3_LOCATION}"
backupBucket="${MFE_S3_LOCATION}/backup"

echo "+++ :s3: Rollback ${bucket} from ${backupBucket}"
aws s3 cp "${backupBucket}/static" "${bucket}/static" \
          --recursive \
          --metadata-directive REPLACE \
          --cache-control public,max-age=3600,immutable \
          --acl public-read

aws s3 cp "${backupBucket}/index.html" "${bucket}/index.html" \
          --cache-control max-age=0,s-maxage=600 \
          --acl public-read

aws s3 cp "${backupBucket}/manifest.json" "${bucket}/manifest.json" \
          --cache-control max-age=0,s-maxage=600 \
          --acl public-read
