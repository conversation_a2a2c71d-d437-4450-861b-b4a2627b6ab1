#!/usr/bin/env bash

set -eu
bef=`pwd`
cd "$(dirname "$0")/cloudformation"
after=`pwd`

echo "--- :s3: $bef $after"

echo "--- :s3:`ls`"

template="bucket-cache.yaml"
region="ap-southeast-2"


echo "--- :s3: Creating Cache Bucket"
#bucketExist=$(aws s3api head-bucket --bucket "sinchsaas-${1}-oneportal-module-cache" 2>&1 >/dev/null) || true

#if echo "${bucketExist}" | grep 'Not Found'; then
  #echo "--- Create a new bucket"
docker-compose -f docker-compose.yml \
  run --rm -e AWS_REGION="$region" stackup "oneportal-cache-stack" up \
  -t "$template" \
  --tags tags.yml
#fi