#!/usr/bin/env bash

die(){ print "${1}"; exit 1; }
print(){ echo " - ${1}"; }
wait(){
    print "Waiting for ${1}"
    node ./node_modules/.bin/wait-on -t 20000 "${1}" \
        || die "Failed to reach ${1}" \
        && print "Successfully reached ${1}"
}

echo "--- :npm: Install packages"
npm install --silent --legacy-peer-deps

echo "Start Standalone, Mock API"
npm run start:standalone-funtional 1>/dev/null 2>&1 &
node ./mock-api/main.js 1>/dev/null 2>&1 &

wait "http://localhost:${MOCK_API_PORT}"
wait "http://localhost:${DEV_SERVER_PORT}"

echo "+++ :hammer: Run MFE functional test"
if [[ ${BUILDKITE_BRANCH} =~ ^(staging|master|.*percy.*).* ]]; then
    npm run test:functional:percy
else
    npm run test:functional
fi
