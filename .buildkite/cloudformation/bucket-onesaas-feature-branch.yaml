AWSTemplateFormatVersion: '2010-09-09'
Description: 'Support frontend onesaas bucket'

Mappings:
  Account:
    'dev':
      id: '************'

    'qa':
      id: '************'

    'stg':
      id: '************'

    'prd':
      id: '************'

  DomainConfig:
    dev:
      envSubDomain: 'dev.'
      zoneid: 'Z103525831S9I9GX8MBUB'
      certArn: 'arn:aws:acm:us-east-1:************:certificate/985a0e09-e6ef-4ed5-bc03-73ab3b60e00c'
      syd:
        - 'syd.dev.support.saas.sinch.com'
      dub:
        - 'dub.dev.support.saas.sinch.com'
    qa:
      envSubDomain: 'qa.'
      zoneid: 'Z09536262GDJ9E560Y7FG'
      certArn: 'arn:aws:acm:us-east-1:************:certificate/1514d3c1-d450-4ea9-a49b-8c0bf8c46fe0'
      syd:
        - 'syd.qa.support.saas.sinch.com'
      dub:
        - 'dub.qa.support.saas.sinch.com'
    stg:
      envSubDomain: 'stg.'
      certArn: 'arn:aws:acm:us-east-1:************:certificate/850b8a7a-0253-43fc-98bf-1bff810f8ce3'
      zoneid: 'Z09665921TQ6FKUSMMD9F'
      syd:
        - 'syd.stg.support.saas.sinch.com'
      dub:
        - 'dub.stg.support.saas.sinch.com'
    prd:
      envSubDomain: ''
      certArn: 'arn:aws:acm:us-east-1:************:certificate/429a98bf-14ca-43a0-b55c-0f8f2e691bd0'
      zoneid: 'Z096598230FRMR24KQ4SW'
      syd:
        - 'syd.support.saas.sinch.com'
      dub:
        - 'dub.support.saas.sinch.com'

Parameters:
  Airport:
    Type: 'AWS::SSM::Parameter::Value<String>'
    Description: 'The Airport code, like syd'
    Default: '/sinch/aws/airport'

  Environment:
    Type: 'AWS::SSM::Parameter::Value<String>'
    Description: 'The environment, something like dev/qa/stg/prd'
    Default: '/sinch/environment'


Resources:
  SinchSaasSupportBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub 'onesaas-${Environment}-${Airport}-support'
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true
      WebsiteConfiguration:
        IndexDocument: index.html
        ErrorDocument: index.html
      LifecycleConfiguration:
        Rules:
          - Id: DeleteFeatureBranchFolderRule
            Prefix: 'feature-branch/'
            Status: Enabled
            ExpirationInDays: 1
    DeletionPolicy: Delete

  HSTAccessLogBucket:
    Type: "AWS::S3::Bucket"
    Properties:
      AccessControl: "LogDeliveryWrite"
      OwnershipControls:
        Rules:
          - ObjectOwnership: "BucketOwnerPreferred"
      BucketName: !Sub 'onesaas-access-logs-${Environment}-${Airport}-support'

  HSTCloudFrontLogBucketPolicy:
    Type: "AWS::S3::BucketPolicy"
    Properties:
      Bucket:
        Ref: "HSTAccessLogBucket"
      PolicyDocument:
        Statement:
          - Effect: "Allow"
            Principal:
              Service: "delivery.logs.amazonaws.com"
            Action: "s3:PutObject"
            Resource:
              Fn::Sub: "${HSTAccessLogBucket.Arn}/*"

  SinchSaasSupportBucketS3Policy:
    Type: AWS::S3::BucketPolicy
    Properties:
      Bucket:
        Ref: SinchSaasSupportBucket
      PolicyDocument:
        Statement:
          - Sid: PolicyForCloudFrontPrivateContent
            Effect: Allow
            Principal:
              Service: cloudfront.amazonaws.com
            Action: s3:GetObject
            Resource: !Sub 'arn:aws:s3:::onesaas-${Environment}-${Airport}-support/*'
            Condition:
              StringLike:
                AWS:SourceArn: !Sub ["arn:aws:cloudfront::${env}:distribution/*", {env: !FindInMap [ 'Account', !Ref Environment, 'id' ] } ]

  CloudFrontDistribution:
    Type: 'AWS::CloudFront::Distribution'
    Properties:
      DistributionConfig:
        Comment: 'HST OneSaas Support Website'
        Aliases: !FindInMap ['DomainConfig', !Ref Environment, !Ref Airport] # TODO: fix does not work for multi region
        ViewerCertificate:
          AcmCertificateArn: !FindInMap ['DomainConfig', !Ref Environment, 'certArn']
          SslSupportMethod: 'sni-only'
          MinimumProtocolVersion: 'TLSv1'
        DefaultRootObject: 'index.html'
        HttpVersion: 'http2'
        IPV6Enabled: false
        Logging:
          IncludeCookies: false
          Bucket: !Sub 'onesaas-access-logs-${Environment}-${Airport}-support.s3-ap-southeast-2.amazonaws.com'
          Prefix: 'cloudfront-onesaas-support'
        DefaultCacheBehavior:
          AllowedMethods:
            - 'GET'
            - 'HEAD'
          TargetOriginId: 'website-s3-bucket'
          ForwardedValues:
            QueryString: true
            Cookies:
              Forward: 'none'
          ViewerProtocolPolicy: 'redirect-to-https'
          Compress: true
          MinTTL: 0
          DefaultTTL: 60
          MaxTTL: 31536000
          FunctionAssociations:
            - EventType: 'viewer-request'
              FunctionARN: !GetAtt 'CloudFrontHSTReactFunctionSupport.FunctionMetadata.FunctionARN'
            - EventType: 'viewer-response'
              FunctionARN: !GetAtt 'HSTSharedResponseSecurityHeaderFunction.FunctionMetadata.FunctionARN'
        Enabled: true
        Origins:
          - Id: 'website-s3-bucket'
            DomainName: !Sub 'onesaas-${Environment}-${Airport}-support.s3.${AWS::Region}.amazonaws.com'
            S3OriginConfig:
              OriginAccessIdentity: ''
            OriginAccessControlId: !GetAtt 'CloudFrontHSTFrontendOriginAccessControl.Id'
        CacheBehaviors:
          - TargetOriginId: 'website-s3-bucket'
            PathPattern: '/feature-branch/*/static/*'
            ViewerProtocolPolicy: 'https-only'
            Compress: true
            AllowedMethods:
              - 'GET'
              - 'HEAD'
            ForwardedValues:
              QueryString: true
              Cookies:
                Forward: 'none'
            DefaultTTL: 3600
            CachePolicyId: !GetAtt 'CloudFrontHSTFrontendCachePolicy.Id'
          - TargetOriginId: 'website-s3-bucket'
            PathPattern: '/static/*'
            ViewerProtocolPolicy: 'https-only'
            Compress: true
            AllowedMethods:
              - 'GET'
              - 'HEAD'
            ForwardedValues:
              QueryString: true
              Cookies:
                Forward: 'none'
            DefaultTTL: 3600
            CachePolicyId: !GetAtt 'CloudFrontHSTFrontendCachePolicy.Id'

  CloudFrontHSTFrontendOriginAccessControl:
    Type: 'AWS::CloudFront::OriginAccessControl'
    Properties:
      OriginAccessControlConfig:
        Description: Origin Access Control for HST Frontend
        Name: !Sub 'cloudfront-hst-frontend-origin-access-control-${Airport}'
        OriginAccessControlOriginType: s3
        SigningBehavior: always
        SigningProtocol: sigv4

  CloudFrontHSTFrontendCachePolicy:
    Type: 'AWS::CloudFront::CachePolicy'
    Properties:
      CachePolicyConfig:
        Comment: 'CachePolicy for frontend'
        DefaultTTL: 300
        MaxTTL: 31536000
        MinTTL: 1
        Name: !Sub 'cloudfront-hst-frontend-cache-policy-${Airport}'
        ParametersInCacheKeyAndForwardedToOrigin:
          CookiesConfig:
            CookieBehavior: 'none'
          EnableAcceptEncodingBrotli: true
          EnableAcceptEncodingGzip: true
          HeadersConfig:
            HeaderBehavior: 'whitelist'
            Headers:
              - 'x-index-cache-key'
          QueryStringsConfig:
            QueryStringBehavior: 'none'

  CloudFrontHSTReactFunctionSupport:
    Type: 'AWS::CloudFront::Function'
    Properties:
      AutoPublish: true
      Name: !Sub 'cloudfront-hst-react-routing-handler-support-${Airport}'
      FunctionConfig:
        Comment: 'CloudFront react routing handler'
        Runtime: 'cloudfront-js-1.0'
      FunctionCode: |
        function handler(event) {
          var request = event.request;
          if (request.uri.includes('robots.txt') || request.uri.includes('version.json')) {
            return request;
          }

          var MATCHING_PATH = '/feature-branch'

          if (request.uri.includes(MATCHING_PATH)) {
            var pathArr = request.uri.split('/');
            var branch = pathArr[2]
            var resourceName = pathArr[pathArr.length - 1]
            if (resourceName.includes('.js')) {
                return request;
            }
            request.uri = '/feature-branch/' + branch + '/' + 'index.html'
            return request
          }

          request.uri = '/index.html';
          return request;
        }
  HSTSharedResponseSecurityHeaderFunction:
    Type: 'AWS::CloudFront::Function'
    Properties:
      AutoPublish: true
      Name: !Sub 'shared-response-security-header-handler-${Airport}'
      FunctionConfig:
        Comment: 'Shared response security header handler'
        Runtime: 'cloudfront-js-1.0'
      FunctionCode: |
        function handler(event) {
            var response = event.response;
            var headers = response.headers;

            // Header names MUST be lowercase
            // Set the x-frame-options header
            headers['x-frame-options'] = {value: 'DENY'};

            // Return response to viewers
            return response;
        }

  Route53RecordSet:
    Type: 'AWS::Route53::RecordSet'
    Properties:
      HostedZoneName: !Sub [ '${envSubDomain}support.saas.sinch.com.', { envSubDomain: !FindInMap [ 'DomainConfig', !Ref 'Environment', 'envSubDomain' ] } ]
      Name: !Sub [ '${Airport}.${envSubDomain}support.saas.sinch.com', { envSubDomain: !FindInMap [ 'DomainConfig', !Ref 'Environment', 'envSubDomain' ] } ]
      Type: A
      AliasTarget:
        DNSName: !GetAtt CloudFrontDistribution.DomainName
        HostedZoneId: Z2FDTNDATAQYW2 # CloudFront Hosted Zone ID
        EvaluateTargetHealth: false
