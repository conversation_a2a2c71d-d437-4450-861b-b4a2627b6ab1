AWSTemplateFormatVersion: '2010-09-09'
Description: 'Support Frontend Cache Bucket'

Mappings:
  Account:
    'qa':
      id: '************'

    'stg':
      id: '************'

    'prd':
      id: '************'

Parameters:
  Environment:
    Type: 'AWS::SSM::Parameter::Value<String>'
    Description: 'The environment, something like dev/qa/stg/prd'
    Default: '/sinch/environment'

Resources:
  SinchSaasSupportCacheBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub 'sinchsaas-${Environment}-oneportal-module-cache'
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true
    DeletionPolicy: Delete

  SinchSaasSupportCacheBucketPolicy:
    Type: "AWS::S3::BucketPolicy"
    Properties:
      Bucket:
        Ref: SinchSaasSupportCacheBucket
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: "Allow"
            Principal:
              AWS:
                - !FindInMap [Account, qa, id]
                - !FindInMap [Account, stg, id]
                - !FindInMap [Account, prd, id]
            Action: 
              - "s3:GetObject"
              - "s3:PutObject"
              - "s3:DeleteObject"
            Resource: 
              - !Sub '${SinchSaasSupportCacheBucket.Arn}/*'
              - !Sub '${SinchSaasSupportCacheBucket.Arn}'
          - Effect: "Allow"
            Principal:
              AWS:
                - !FindInMap [Account, qa, id]
                - !FindInMap [Account, stg, id]
                - !FindInMap [Account, prd, id]
            Action: "s3:ListBucket"
            Resource: !Sub '${SinchSaasSupportCacheBucket.Arn}'