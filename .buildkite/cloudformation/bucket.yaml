AWSTemplateFormatVersion: '2010-09-09'
Description: 'Support frontend bucket'

Mappings:
  Account:
    'dev':
      id: '************'

    'qa':
      id: '************'

    'stg':
      id: '************'

    'prd':
      id: '************'

  DomainConfig:
    dev:
      envSubDomain: 'dev.'
    qa:
      envSubDomain: 'qa.'
    stg:
      envSubDomain: 'stg.'
    prd:
      envSubDomain: ''

Parameters:
  Airport:
    Type: 'AWS::SSM::Parameter::Value<String>'
    Description: 'The Airport code, like syd'
    Default: '/sinch/aws/airport'

  Environment:
    Type: 'AWS::SSM::Parameter::Value<String>'
    Description: 'The environment, something like dev/qa/stg/prd'
    Default: '/sinch/environment'


Resources:
  SinchSaasSupportBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub 'sinchsaas-${Environment}-${Airport}-mmgp-support'
      PublicAccessBlockConfiguration:
        BlockPublicAcls: false
        BlockPublicPolicy: false
        IgnorePublicAcls: false
        RestrictPublicBuckets: false
      WebsiteConfiguration:
        IndexDocument: index.html
        ErrorDocument: index.html
    DeletionPolicy: Delete

  SinchSaasSupportBucketS3Policy:
    Type: AWS::S3::BucketPolicy
    Properties:
      Bucket:
        Ref: SinchSaasSupportBucket
      PolicyDocument:
        Statement:
          - Sid: PolicyForCloudFrontPrivateContent
            Effect: Allow
            Principal:
              Service: cloudfront.amazonaws.com
            Action: s3:GetObject
            Resource: !Sub 'arn:aws:s3:::sinchsaas-${Environment}-${Airport}-mmgp-support/*'
            Condition:
              StringLike:
                AWS:SourceArn: !Sub ["arn:aws:cloudfront::${env}:distribution/*", {env: !FindInMap [ 'Account', !Ref Environment, 'id' ] } ]
          - Sid: PolicyPublicForNginx
            Effect: Allow
            Principal: '*'
            Action: s3:GetObject
            Resource: !Sub 'arn:aws:s3:::sinchsaas-${Environment}-${Airport}-mmgp-support/*'
