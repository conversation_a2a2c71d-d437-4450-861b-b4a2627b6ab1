AWSTemplateFormatVersion: '2010-09-09'
Description: 'Support frontend onesaas certificate manager'

Resources:
  HSTFECertificateDev:
    Type: "AWS::CertificateManager::Certificate"
    Properties:
      DomainName: '*.dev.support.saas.sinch.com'
      ValidationMethod: DNS

  HSTFECertificateQa:
    Type: "AWS::CertificateManager::Certificate"
    Properties:
      DomainName: '*.qa.support.saas.sinch.com'
      ValidationMethod: DNS

  HSTFECertificateStg:
    Type: "AWS::CertificateManager::Certificate"
    Properties:
      DomainName: '*.stg.support.saas.sinch.com'
      ValidationMethod: DNS

  HSTFECertificatePrd:
    Type: "AWS::CertificateManager::Certificate"
    Properties:
      DomainName: '*.support.saas.sinch.com'
      ValidationMethod: DNS

Outputs:
  HSTFECertificateDev:
    Description: 'HST FE Certificate Dev'
    Value: !Ref 'HSTFECertificateDev'
    Export:
      Name: 'HSTFECertificateDev'

  HSTFECertificateQa:
    Description: 'HST FE Certificate Dev'
    Value: !Ref 'HSTFECertificateDev'
    Export:
      Name: 'HSTFECertificateQa'

  HSTFECertificateStg:
    Description: 'HST FE Certificate Dev'
    Value: !Ref 'HSTFECertificateDev'
    Export:
      Name: 'HSTFECertificateStg'

  HSTFECertificatePrd:
    Description: 'HST FE Certificate Dev'
    Value: !Ref 'HSTFECertificateDev'
    Export:
      Name: 'HSTFECertificatePrd'