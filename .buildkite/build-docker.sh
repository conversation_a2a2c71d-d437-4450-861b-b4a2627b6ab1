#!/usr/bin/env bash

set -eu

while [[ $# -gt 0 ]]; do
  case "$1" in
    --env)
      shift
      env="$1"
      ;;
    --airport)
      shift
      airport="$1"
      ;;
    *)
      echo "Invalid option: $1"
      exit 1
      ;;
  esac
  shift
done

echo '--- Prepare node modules'
aws s3 cp "s3://sinchsaas-${env}-oneportal-module-cache/node_modules.tar.gz" .
tar -xzf node_modules.tar.gz

if [[ $env != "functional" ]]; then
  command="BUILD_PATH=build${env}${airport} ENV_PATH=env/.env.${airport}.${env} nx build hst-core --skip-nx-cache"
fi

API_FEATURE_BRANCH="$(buildkite-agent meta-data get "api-feature-branch" --default '')"
echo "API_FEATURE_BRANCH: $API_FEATURE_BRANCH";

docker run \
-v ~/.ssh:/root/.ssh \
-v ${SSH_AUTH_SOCK}:/ssh-agent \
-v ${PWD}:/code \
-v /var/lib/buildkite-agent/.gitlab-token:/root/.gitlab-token \
-v /var/lib/buildkite-agent/.gitconfig:/root/.gitconfig \
-v /var/lib/buildkite-agent/.ssh/known_hosts:/root/.ssh/known_hosts \
-v /bin/buildkite-agent:/usr/bin/buildkite-agent \
-e "SSH_AUTH_SOCK=/ssh-agent" \
-e "BUILD_PATH" \
-e "HUSKY" \
-e "FEATURE_FLAGS" \
-e "API_FEATURE_BRANCH=${API_FEATURE_BRANCH}" \
-e "COMMIT_HASH=${BUILDKITE_COMMIT}" \
-e "BUILDKITE_BRANCH=${BUILDKITE_BRANCH}" \
-w="/code" \
cypress/browsers:node-18.16.1-chrome-114.0.5735.133-1-ff-114.0.2-edge-114.0.1823.51-1 \
bash -c "npm install -g nx && git fetch --all && $command && ls -l"
