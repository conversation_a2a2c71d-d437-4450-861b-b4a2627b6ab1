set -e

while [[ $# -gt 0 ]]; do
  case "$1" in
    --env)
      shift
      env="$1"
      ;;
    --region)
      shift
      region="$1"
      ;;
    --queue)
      shift
      queue="$1"
      ;;
    *)
      echo "Invalid option: $1"
      exit 1
      ;;
  esac
  shift
done

case "${env}" in
  dev) build_env="feature-branch-stg";;
  *) build_env=${env};;
esac


case "${region}" in
  au) airport="syd";;
  us) airport="pdx";;
esac


echo '--- :jshint: Build for'
echo "Env: $env";
echo "Region: $region";

# TODO: understand if we need to get the artifacts from previous step
# buildkite-agent artifact download "cypress/fixtures/featureSet/*" "cypress/fixtures/featureSet/"

PIPELINE="steps:
  - name: \":building_construction: :flag-$region: [$env] build for functional tests\"
    key: \"npm-build-functional-au-upload\"
    artifact_paths:
      - "build-functional-standalone/**/*"
      - "build-functional-standalone/*"
    command: 'bash .buildkite/build-docker.sh --env functional'
    env:
      FEATURE_FLAGS: false
      BUILD_PATH: \"build-functional-standalone\"
      COMMIT_HASH: \"${BUILDKITE_COMMIT}\"
    timeout_in_minutes: 20
    agents:
      queue: \"$queue\"
"

# Upload the new pipeline and add it to the current build
echo "$PIPELINE" | buildkite-agent pipeline upload

# bash -c "npm install --legacy-peer-deps && npm run build"
