#!/usr/bin/env bash

set -eu

echo "+++ :book: Announce release document"

echo "--- :js: Fetching branche origin/${BUILDKITE_BRANCH}"

git fetch origin ${<PERSON><PERSON><PERSON><PERSON>ITE_BRANCH}

git config user.email '<EMAIL>'
git config user.name 'BuildKite'

npx --yes --package=@sinch-smb/release-document@1.6.0 --userconfig=.npmrc tag-version \
--source=origin/${BUILDKITE_BRANCH} \
--jiraUser=${JIRA_USER} \
--jiraSecret=${JIRA_SECRET} \
--repoName="support-tool-frontend"
