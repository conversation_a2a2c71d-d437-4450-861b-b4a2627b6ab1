set -e

echo '--- Prepare node modules'
aws s3 cp "s3://sinchsaas-stg-oneportal-module-cache/node_modules.tar.gz" .
tar -xzf node_modules.tar.gz

echo '--- Testing'
docker run \
-v ~/.ssh:/root/.ssh \
-v ${SSH_AUTH_SOCK}:/ssh-agent \
-v ${PWD}:/code \
-e "SSH_AUTH_SOCK=/ssh-agent" \
-e "HUSKY" \
-w="/code" \
cypress/browsers:node-18.16.1-chrome-114.0.5735.133-1-ff-114.0.2-edge-114.0.1823.51-1 \
bash -c "npm install -g nx && nx test $1"
