set -e

while [[ $# -gt 0 ]]; do
  case "$1" in
    --env)
      shift
      env="$1"
      ;;
    --region)
      shift
      region="$1"
      ;;
    --queue)
      shift
      queue="$1"
      ;;
    *)
      echo "Invalid option: $1"
      exit 1
      ;;
  esac
  shift
done

case "${env}" in
  dev) build_env="feature-branch-stg";;
  *) build_env=${env};;
esac


case "${region}" in
  au) airport="syd";;
  eu) airport="dub";;
  us) airport="pdx";;
esac



case "${env}" in
  prd) compose_env="
            - \"BUILDKITE_BRANCH\"
            - \"COMMIT_HASH=${BUILDKITE_COMMIT}\"
            - \"MASTER_BRANCH=master\"
  ";;
  *) compose_env="
            - \"BUILDKITE_BRANCH\"
            - \"COMMIT_HASH=${BUILDKITE_COMMIT}\"
  "
esac


echo '--- :jshint: Build for'
echo "Env: $env";
echo "Region: $region";

PIPELINE="steps:
  - name: \":building_construction: :flag-$region: [$env] build main app\"
    artifact_paths:
      - "build${env}${airport}/**/*"
      - "build${env}${airport}/*"
    key: 'build-app-$env-$airport'
    command: 'bash .buildkite/build-docker.sh --airport $airport --env $env'
    env: "$compose_env"
    timeout_in_minutes: 20
    agents:
      queue: \"$queue\"
"

# NEED TO BE ADDED ONCE THE PLUGIN ISSUE IS FIXED.
#            GITLAB_NPM_TOKEN: \"/sinch/gitlab/access-token\"

# Upload the new pipeline and add it to the current build
echo "$PIPELINE" | buildkite-agent pipeline upload

# bash -c "npm install --legacy-peer-deps && npm run build"
