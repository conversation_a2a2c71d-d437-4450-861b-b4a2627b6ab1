#!/usr/bin/env bash

set -euo pipefail

type="$(buildkite-agent meta-data get "testType" --default all)"
messages="run the E2E tests for [${BUILDKITE_PIPELINE_SLUG}/${BUILDKITE_BRANCH}]"

role="true"
general="false"

case $type in
  roles)
    role="false"
    general="true"
  ;;
  all)
    role="false"
    general="false"
  ;;
  *)
    role="true"
    general="false"
  ;;
esac

# Create a pipeline with your trigger step
PIPELINE="steps:
  - trigger: \"git-support-tool-frontend-e2e-test\"
    label: \":hammer: run the E2E tests for [${BUILDKITE_PIPELINE_SLUG}/${BUILDKITE_BRANCH}]\"
    async: false
    build:
      branch: \"master\"
      env:
        DISABLE_STG_TESTS: ${general}
        DISABLE_PRD_TESTS: \"true\"
        DISABLE_ACTIVITY_STG_TESTS: \"true\"
        DISABLE_ACTIVITY_PRD_TESTS: \"true\"
        DISABLE_ROLES_STG_TESTS: \"${role}\"
        DISABLE_ROLES_PRD_TESTS: \"true\"
      message: \"${messages}\"
   "

echo "$PIPELINE" | buildkite-agent pipeline upload