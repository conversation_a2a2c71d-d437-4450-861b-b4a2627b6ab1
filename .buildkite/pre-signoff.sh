#!/usr/bin/env bash

set -eu

docker run \
-v ~/.ssh:/root/.ssh \
-v ${SSH_AUTH_SOCK}:/ssh-agent \
-v /var/lib/buildkite-agent/.gitlab-token:/root/.gitlab-token \
-v /var/lib/buildkite-agent/.gitconfig:/root/.gitconfig \
-v ${PWD}:/code \
-e "SSH_AUTH_SOCK=/ssh-agent" \
-e "BUILDKITE_BRANCH" \
-e "HUSKY" \
-e "JIRA_USER" \
-e "JIRA_SECRET" \
-e "DEPT_TECH_DEPLOYMENT_SIGN_OFF_WEBHOOK" \
-w="/code" \
cypress/browsers:node-18.16.1-chrome-114.0.5735.133-1-ff-114.0.2-edge-114.0.1823.51-1 \
bash -c "bash .buildkite/signoff.sh"