#!/usr/bin/env bash

set -ueo pipefail

# for pull request handling
IS_PULL_REQUEST=$([ "${BUILDKITE_PULL_REQUEST:-false}" != false ] && echo true || echo false)
IS_NOT_PULL_REQUEST=$([ "${BUILDKITE_PULL_REQUEST:-false}" != false ] && echo false || echo true)
export IS_PULL_REQUEST
export IS_NOT_PULL_REQUEST
export BUILDKITE_COMMIT_FULL=$(git rev-parse --verify HEAD)

# Disable husky on CI
export HUSKY="0"

# Gitlab npm auth
if [[ -n "${GITLAB_NPM_TOKEN:-}" ]]; then
  cat > "./.npmrc" << EOF
@sinch-smb:registry=https://gitlab.com/api/v4/projects/44330946/packages/npm/
//gitlab.com/api/v4/projects/44330946/packages/npm/:_authToken=${GITLAB_NPM_TOKEN}
@sinch:registry=https://gitlab.com/api/v4/packages/npm/
//gitlab.com/api/v4/packages/npm/:_authToken=${GITLAB_NPM_TOKEN}
EOF
fi