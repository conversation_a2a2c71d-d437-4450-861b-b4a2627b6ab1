#!/usr/bin/env bash

set -ueo pipefail

DEFAULT_REGION=ap-southeast-2

function exportParams () {
  echo "Export the $1 with the param name $2"
  value=$(aws ssm get-parameter \
                    --region "$DEFAULT_REGION" \
                    --with-decryption \
                    --output text \
                    --query Parameter.Value \
                    --name "$2")

  export "$1=${value}"
}

case $BUILDKITE_LABEL in
  "Download feature sets from S3")
    exportParams GITLAB_NPM_TOKEN "/buildkite/gitlab/npm/authtoken"
    ;;

  ":loudspeaker: Sign-off")
    exportParams GITLAB_NPM_TOKEN "/sinch/gitlab/access-token"
    exportParams JIRA_USER "/sinch/jira/user"
    exportParams JIRA_SECRET "/sinch/jira/secret"
    exportParams DEPT_TECH_DEPLOYMENT_SIGN_OFF_WEBHOOK "/blacklabel/slackwebhook/dept-tech-deployment-sign-off"
    ;;

  *)
    exportParams GITLAB_NPM_TOKEN "/sinch/gitlab/access-token"
    ;;
esac
