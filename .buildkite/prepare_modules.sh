#!/usr/bin/env bash

set -eu

filehash=`sha512sum package-lock.json | cut -f1 -d' '`
filepath="s3://sinchsaas-${1}-oneportal-module-cache/package-lock.json$filehash"
filecount=`aws s3 ls $filepath | wc -l`

if [[ ! filecount -eq 0 ]]; then
  echo "Module is available!" && exit 0
fi

docker run \
-v ~/.ssh:/root/.ssh \
-v ${SSH_AUTH_SOCK}:/ssh-agent \
-v ${PWD}:/code \
-e "SSH_AUTH_SOCK=/ssh-agent" \
-e "BUILD_PATH" \
-e "HUSKY" \
-w="/code" \
cypress/browsers:node-18.16.1-chrome-114.0.5735.133-1-ff-114.0.2-edge-114.0.1823.51-1 \
bash -c "bash .buildkite/cache-npm-packages.sh ${1}"
