#!/usr/bin/env bash

set -eu
bef=`pwd`
cd "$(dirname "$0")/cloudformation"
after=`pwd`

echo "--- :s3: $bef $after"

echo "--- :s3:`ls`"

if [[ $1 == "prd" ]]; then
    template="bucket-onesaas.yaml"
else
    template="bucket-onesaas-feature-branch.yaml"
fi

echo "--- :s3: Creating Vendor Specific Buckets [${1}] - [$2]"

docker-compose -f docker-compose.yml \
  run --rm -e AWS_REGION="$2" stackup "onesaas-support-bucket" up \
  -t "$template" \
  --tags tags.yml
