#!/bin/bash

# This is created manually as we need to have the certificate ARN when we create the CloudFront that already
# exists. And we can not use an ImportValue of an OutputParameter using a parameter. So that would force
# to do one CF distribution per region. Because certs are a one off creation this is a mandatory step before
# going to a region.

aws cloudformation create-stack --stack-name onesaas-cert-bucket --template-body .buildkite/cloudformation/onesaas-hst-certificate.yaml
